# Codebase Retrieval Result

**Query:** "Show me the exact section in sessionStore where feedback is applied to user message and AI response is added"

## Retrieved Code Sections:

### Key Sections Found:

1. **Feedback Application in generateAIResponse (Lines 512-529)**
```typescript
// Apply feedback to the user message if provided
if (data.feedback && userMessageId) {
  console.log('📊 Applying feedback to user message:', data.feedback);
  console.log('📊 Raw feedback score from edge function:', data.feedback.score, typeof data.feedback.score);
  const feedbackForMessage = {
    type: data.feedback.type,
    message: data.feedback.message,
    color: get().getFeedbackColor(data.feedback.type),
    score: data.feedback.score
  };
  console.log('📊 Feedback object being passed to updateMessageWithFeedback:', feedbackForMessage);
  
  await get().updateMessageWithFeedback(userMessageId, feedbackForMessage);
  
  // Update user score based on AI feedback
  const { updateScore } = useUserStore.getState();
  updateScore(data.feedback.score);
}
```

2. **AI Response Addition (Lines 531-533)**
```typescript
// Add AI message with the response from the API
console.log('🤖 Adding AI response from edge function to local state and database');
await get().addMessage(data.response, 'ai');
```

3. **updateMessageWithFeedback Function (Lines 622-650)**
```typescript
updateMessageWithFeedback: async (messageId: string, feedback: any) => {
  console.log('📊 Updating message with feedback:', { messageId, feedback });
  
  // Update local state first
  set(state => ({
    messages: state.messages.map(msg => 
      msg.id === messageId ? { ...msg, feedback } : msg
    )
  }));
  
  // Save feedback to database if message exists
  const { currentSession } = get();
  if (currentSession && !currentSession.isLocal) {
    try {
      console.log('💾 Saving feedback to database for message:', messageId);
      console.log('💾 Feedback score being saved:', feedback.score, typeof feedback.score);
      const scoreToSave = feedback.score !== undefined && feedback.score !== null ? feedback.score : 0;
      console.log('💾 Final score to save:', scoreToSave);
      await MessageService.addMessageFeedback(messageId, {
        type: feedback.type,
        message: feedback.message,
        score: scoreToSave
      });
      console.log('✅ Feedback saved to database successfully');
    } catch (error) {
      console.error('❌ Error saving feedback to database:', error);
    }
  }
}
```

4. **addMessage Function (Lines 398-466)**
```typescript
addMessage: async (content: string, sender: 'user' | 'ai', feedback?: any) => {
  console.log('💬 Adding message:', { content: content.substring(0, 50), sender, sessionId: get().currentSession?.id });
  const { currentSession } = get();
  if (!currentSession) {
    console.log('❌ No current session for message');
    return;
  }

  const messageId = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
  
  const newMessage: LocalMessage = {
    id: messageId,
    sessionId: currentSession.id,
    content,
    sender,
    timestamp: new Date(),
    feedback,
    isLocal: currentSession.isLocal
  };

  // Add to local state first
  console.log('📝 Adding message to local state');
  set(state => ({
    messages: [...state.messages, newMessage]
  }));

  console.log('💾 Session isLocal:', currentSession.isLocal);
  // Save to database if session is not local
  if (!currentSession.isLocal) {
    try {
      const messageToSave = {
        id: messageId,
        session_id: currentSession.id,
        content,
        sender,
        feedback_type: feedback?.type || null,
        feedback_message: feedback?.message || null,
        feedback_score: feedback?.score || null
      };
      console.log('🔄 Saving message to database with feedback:', {
        feedback,
        messageToSave: {
          ...messageToSave,
          content: messageToSave.content.substring(0, 30)
        }
      });
      await MessageService.createMessage(messageToSave);
      console.log('✅ Message saved to database:', messageId);
      
      // Update the message to mark it as saved
      set(state => ({
        messages: state.messages.map(msg => 
          msg.id === messageId ? { ...msg, isLocal: false } : msg
        )
      }));
    } catch (error) {
      console.error('❌ Error creating message in database:', error);
      // Message stays local
    }
  } else {
    console.log('📱 Session is local, message saved locally only');
  }
  
  return messageId; // Return the message ID for reference
}
```

## Summary:
The codebase retrieval found the exact sections where:
1. **Feedback is applied to user messages** in the `generateAIResponse` function (lines 512-529)
2. **AI responses are added** via the `addMessage` function call (line 533)
3. The **updateMessageWithFeedback** helper function that handles the feedback application (lines 622-650)
4. The **addMessage** function that handles adding both user and AI messages with optional feedback (lines 398-466)
