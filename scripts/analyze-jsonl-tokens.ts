#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to analyze token usage from a JSONL file containing API call data
 * Usage: npx tsx scripts/analyze-jsonl-tokens.ts --file "/path/to/file.jsonl" --date "2025-06-10"
 */
import { program } from 'commander';
import fs from 'fs';
import path from 'path';
import readline from 'readline';

// Define command line options
program
    .option('-f, --file <path>', 'Path to the JSONL file')
    .option('-d, --date <date>', 'Date to filter for (YYYY-MM-DD)')
    .option('-o, --output <path>', 'Output JSON file path', './token-analysis.json')
    .parse(process.argv);

const options = program.opts();

// Validate inputs
if (!options.file) {
    console.error('Please provide a JSONL file path with --file');
    process.exit(1);
}

if (!fs.existsSync(options.file)) {
    console.error(`File not found: ${options.file}`);
    process.exit(1);
}

// Parse the date if provided
let targetDate: string | null = null;
if (options.date) {
    targetDate = options.date;
    console.log(`Filtering for date: ${targetDate}`);
}

// Ensure output directory exists
const outputDir = path.dirname(options.output);
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

interface TokenUsage {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
    count: number;
}

interface TokenAnalysis {
    byModel: Record<string, TokenUsage>;
    byProvider: Record<string, TokenUsage>;
    byApp: Record<string, TokenUsage>;
    byHour: Record<string, TokenUsage>;
    topRequests: Array<{
        id: string;
        date: string;
        time: string;
        model: string;
        provider: string;
        app: string;
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    }>;
    totalUsage: TokenUsage & {
        uniqueModels: number;
        uniqueProviders: number;
        uniqueApps: number;
    };
}

// Initialize analysis object
const analysis: TokenAnalysis = {
    byModel: {},
    byProvider: {},
    byApp: {},
    byHour: {},
    topRequests: [],
    totalUsage: {
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0,
        count: 0,
        uniqueModels: 0,
        uniqueProviders: 0,
        uniqueApps: 0
    }
};

// Process the JSONL file
console.log(`Processing JSONL file: ${options.file}`);

// Create a readline interface to read the file line by line
const processFile = async () => {
    const fileStream = fs.createReadStream(options.file);
    const rl = readline.createInterface({
        input: fileStream,
        crlfDelay: Infinity
    });

    let lineCount = 0;
    let matchedCount = 0;

    for await (const line of rl) {
        lineCount++;
        
        try {
            // Parse the JSON line
            const data = JSON.parse(line);
            
            // Extract metadata from the JSON
            const metadata = data.heliconeMetadata;
            if (!metadata) continue;
            
            // Extract date information
            let date = '';
            let time = '';
            if (metadata.createdAt) {
                const createdAt = new Date(metadata.createdAt);
                date = createdAt.toISOString().split('T')[0]; // Format as YYYY-MM-DD
                time = createdAt.toISOString().split('T')[1].split('.')[0]; // Format as HH:MM:SS
                
                // Extract hour for hourly analysis
                const hour = time.split(':')[0];
                const hourKey = `${hour}:00`;
                
                // Skip if we're filtering by date and this row doesn't match
                if (targetDate && date !== targetDate) {
                    continue;
                }
                
                matchedCount++;
            } else {
                // No date found, skip
                continue;
            }
            
            // Extract token information
            let promptTokens = parseInt(metadata.promptTokens || '0');
            let completionTokens = parseInt(metadata.completionTokens || '0');
            let totalTokens = parseInt(metadata.totalTokens || '0');
            
            // If we have prompt and completion tokens but no total, calculate it
            if (totalTokens === 0 && (promptTokens > 0 || completionTokens > 0)) {
                totalTokens = promptTokens + completionTokens;
            }
            
            // Skip if no token information found
            if (totalTokens === 0) {
                continue;
            }
            
            // Extract model, provider, and app information
            const model = metadata.model || data.model || 'unknown';
            const provider = metadata.provider || 'unknown';
            const app = metadata.customProperties?.app || 'unknown';
            
            // Update analysis by model
            if (!analysis.byModel[model]) {
                analysis.byModel[model] = { promptTokens: 0, completionTokens: 0, totalTokens: 0, count: 0 };
            }
            analysis.byModel[model].promptTokens += promptTokens;
            analysis.byModel[model].completionTokens += completionTokens;
            analysis.byModel[model].totalTokens += totalTokens;
            analysis.byModel[model].count += 1;
            
            // Update analysis by provider
            if (!analysis.byProvider[provider]) {
                analysis.byProvider[provider] = { promptTokens: 0, completionTokens: 0, totalTokens: 0, count: 0 };
            }
            analysis.byProvider[provider].promptTokens += promptTokens;
            analysis.byProvider[provider].completionTokens += completionTokens;
            analysis.byProvider[provider].totalTokens += totalTokens;
            analysis.byProvider[provider].count += 1;
            
            // Update analysis by app
            if (!analysis.byApp[app]) {
                analysis.byApp[app] = { promptTokens: 0, completionTokens: 0, totalTokens: 0, count: 0 };
            }
            analysis.byApp[app].promptTokens += promptTokens;
            analysis.byApp[app].completionTokens += completionTokens;
            analysis.byApp[app].totalTokens += totalTokens;
            analysis.byApp[app].count += 1;
            
            // Update analysis by hour
            const hour = time.split(':')[0];
            const hourKey = `${hour}:00`;
            if (!analysis.byHour[hourKey]) {
                analysis.byHour[hourKey] = { promptTokens: 0, completionTokens: 0, totalTokens: 0, count: 0 };
            }
            analysis.byHour[hourKey].promptTokens += promptTokens;
            analysis.byHour[hourKey].completionTokens += completionTokens;
            analysis.byHour[hourKey].totalTokens += totalTokens;
            analysis.byHour[hourKey].count += 1;
            
            // Update total usage
            analysis.totalUsage.promptTokens += promptTokens;
            analysis.totalUsage.completionTokens += completionTokens;
            analysis.totalUsage.totalTokens += totalTokens;
            analysis.totalUsage.count += 1;
            
            // Add to top requests
            analysis.topRequests.push({
                id: metadata.requestId || 'unknown',
                date,
                time,
                model,
                provider,
                app,
                promptTokens,
                completionTokens,
                totalTokens
            });
            
        } catch (error) {
            console.error(`Error processing line ${lineCount}:`, error);
        }
    }
    
    console.log(`Processed ${lineCount} lines, matched ${matchedCount} records for date ${targetDate || 'any'}`);
    
    // Calculate unique counts
    analysis.totalUsage.uniqueModels = Object.keys(analysis.byModel).length;
    analysis.totalUsage.uniqueProviders = Object.keys(analysis.byProvider).length;
    analysis.totalUsage.uniqueApps = Object.keys(analysis.byApp).length;
    
    // Sort top requests by total tokens (descending)
    analysis.topRequests.sort((a, b) => b.totalTokens - a.totalTokens);
    
    // Limit to top 50 requests
    analysis.topRequests = analysis.topRequests.slice(0, 50);
    
    // Calculate costs
    const inputCost = (analysis.totalUsage.promptTokens / 1000000) * 3; // $3 per million input tokens
    const outputCost = (analysis.totalUsage.completionTokens / 1000000) * 15; // $15 per million output tokens
    const totalCost = inputCost + outputCost;
    
    // Add cost information to the analysis
    const analysisWithCost = {
        ...analysis,
        costs: {
            inputCost,
            outputCost,
            totalCost
        }
    };
    
    // Write analysis to output file
    fs.writeFileSync(
        options.output,
        JSON.stringify(analysisWithCost, null, 2)
    );
    
    console.log(`Analysis complete. Results written to ${options.output}`);
    console.log(`Total tokens processed: ${analysis.totalUsage.totalTokens.toLocaleString()}`);
    console.log(`Total cost: $${totalCost.toFixed(2)} ($${inputCost.toFixed(2)} input + $${outputCost.toFixed(2)} output)`);
    
    // Print top models by token usage
    console.log('\nTop Models by Token Usage:');
    Object.entries(analysis.byModel)
        .sort((a, b) => b[1].totalTokens - a[1].totalTokens)
        .slice(0, 5)
        .forEach(([model, usage]) => {
            console.log(`${model}: ${usage.totalTokens.toLocaleString()} tokens (${usage.count} calls)`);
        });
    
    // Print top providers by token usage
    console.log('\nTop Providers by Token Usage:');
    Object.entries(analysis.byProvider)
        .sort((a, b) => b[1].totalTokens - a[1].totalTokens)
        .slice(0, 5)
        .forEach(([provider, usage]) => {
            console.log(`${provider}: ${usage.totalTokens.toLocaleString()} tokens (${usage.count} calls)`);
        });
    
    // Print top apps by token usage
    console.log('\nTop Apps by Token Usage:');
    Object.entries(analysis.byApp)
        .sort((a, b) => b[1].totalTokens - a[1].totalTokens)
        .slice(0, 5)
        .forEach(([app, usage]) => {
            console.log(`${app}: ${usage.totalTokens.toLocaleString()} tokens (${usage.count} calls)`);
        });
    
    // Print hourly distribution
    console.log('\nHourly Token Usage:');
    Object.entries(analysis.byHour)
        .sort((a, b) => a[0].localeCompare(b[0])) // Sort by hour
        .forEach(([hour, usage]) => {
            console.log(`${hour}: ${usage.totalTokens.toLocaleString()} tokens (${usage.count} calls)`);
        });
};

// Run the analysis
processFile().catch(err => {
    console.error('Error processing file:', err);
    process.exit(1);
});
