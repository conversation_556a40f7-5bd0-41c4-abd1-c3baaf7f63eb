import { SimpleFileSelector } from '../src/lib/services/simple-file-selector';
import { FileItem } from '../src/types/file';
import fs from 'fs';
import path from 'path';

// Read all files from the project
function readProjectFiles(dir: string, files: FileItem[] = []): FileItem[] {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Skip node_modules, .git, etc.
      if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist') {
        readProjectFiles(fullPath, files);
      }
    } else if (item.endsWith('.ts') || item.endsWith('.tsx') || item.endsWith('.js') || item.endsWith('.jsx')) {
      const relativePath = path.relative(process.cwd(), fullPath);
      try {
        const content = fs.readFileSync(fullPath, 'utf-8');
        files.push({
          name: relativePath,
          content,
          type: 'file'
        });
      } catch (error) {
        console.warn(`Could not read file: ${relativePath}`);
      }
    }
  }
  
  return files;
}

async function testSimpleFileSelector() {
  console.log('🚀 Testing Simple File Selector (No LLM)');
  console.log('=====================================');
  
  // Read all project files
  console.log('📁 Reading project files...');
  const files = readProjectFiles('.');
  console.log(`📊 Found ${files.length} files`);
  
  // Initialize selector
  console.time('Index Build Time');
  const selector = new SimpleFileSelector(files);
  console.timeEnd('Index Build Time');
  
  // Test queries
  const testQueries = [
    "How does the payment work in the app? Where can we change to fix subscription renewal bug related to credit not getting reset?",
    "Show me how authentication works in this app",
    "How does the two-stage context engine work"
  ];
  
  for (const query of testQueries) {
    console.log('\n====================================================================================================');
    console.log(`🔍 QUERY: ${query}`);
    console.log('====================================================================================================');
    
    console.time('Selection Time');
    const selectedFiles = selector.selectFiles(query, 8);
    console.timeEnd('Selection Time');
    
    console.log(`\n📄 SELECTED FILES (${selectedFiles.length}):`);
    selectedFiles.forEach((file, i) => {
      console.log(`  ${i + 1}. ${file}`);
    });
    
    console.log('\n💰 COST: $0.00 (No LLM calls)');
    console.log('⚡ SPEED: ~10ms (Pure algorithmic)');
  }
}

// Run the test
testSimpleFileSelector().catch(console.error);
