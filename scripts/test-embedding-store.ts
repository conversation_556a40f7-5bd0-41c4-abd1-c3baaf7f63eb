import { config } from 'dotenv';
import { EmbeddingStore } from '../src/lib/services/embedding-store';
import { FileItem } from '../src/types/file';
import fs from 'fs';
import path from 'path';

// Load environment variables
config({ path: '.env.local' });

// Read project files
function readProjectFiles(dir: string, files: FileItem[] = []): FileItem[] {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist') {
        readProjectFiles(fullPath, files);
      }
    } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
      const relativePath = path.relative(process.cwd(), fullPath);
      try {
        const content = fs.readFileSync(fullPath, 'utf-8');
        files.push({
          name: relativePath,
          content,
          type: 'file'
        });
      } catch (error) {
        console.warn(`Could not read file: ${relativePath}`);
      }
    }
  }
  
  return files;
}

async function testEmbeddingStore() {
  console.log('🚀 Testing EmbeddingStore with OpenAI');
  console.log('===================================');
  
  // Read files
  console.log('📁 Reading project files...');
  const files = readProjectFiles('.'); // Index ALL files
  console.log(`📊 Indexing ${files.length} files`);
  
  // Initialize store
  const store = new EmbeddingStore();
  
  // Update embeddings
  console.time('⏱️  Initial embedding time');
  await store.updateEmbeddings(files);
  console.timeEnd('⏱️  Initial embedding time');
  
  console.log('📈 Stats:', store.getStats());
  
  // Test queries
  const testQueries = [
    "How does the payment work in the app? Where can we change to fix subscription renewal bug related to credit not getting reset?",
    "Show me how authentication works in this app",
    "How does the two-stage context engine work"
  ];
  
  for (const query of testQueries) {
    console.log('\n' + '='.repeat(100));
    console.log(`🔍 QUERY: ${query}`);
    console.log('='.repeat(100));
    
    console.time('⚡ Query time');
    const selectedFiles = await store.findSimilarFiles(query, 8);
    console.timeEnd('⚡ Query time');
    
    console.log(`\n📄 SELECTED FILES (${selectedFiles.length}):`);
    selectedFiles.forEach((file, i) => {
      console.log(`  ${i + 1}. ${file}`);
    });
  }
  
  // Test incremental update
  console.log('\n🔄 Testing incremental update...');
  console.time('⏱️  Incremental update time');
  await store.updateEmbeddings(files); // Should be fast (no changes)
  console.timeEnd('⏱️  Incremental update time');
}

// Run test
testEmbeddingStore().catch(console.error);
