import { ContextAwareBM25Embedder } from '../src/lib/services/context-aware-embedder';
import { FileItem } from '../src/types/file';
import fs from 'fs';
import path from 'path';

// Read project files
function readProjectFiles(rootDir: string): FileItem[] {
  const files: FileItem[] = [];
  
  function readDir(dir: string) {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      const relativePath = path.relative(rootDir, fullPath);
      
      // Skip common directories and files
      if (entry.name.startsWith('.') || 
          entry.name === 'node_modules' || 
          entry.name === 'dist' || 
          entry.name === 'build' ||
          entry.name === '.next') {
        continue;
      }
      
      if (entry.isDirectory()) {
        readDir(fullPath);
      } else if (entry.isFile()) {
        // Only include relevant file types
        const ext = path.extname(entry.name);
        if (['.ts', '.tsx', '.js', '.jsx', '.sql', '.json', '.md'].includes(ext)) {
          try {
            const content = fs.readFileSync(fullPath, 'utf-8');
            files.push({
              name: relativePath,
              content: content,
              type: 'file'
            });
          } catch (error) {
            console.warn(`⚠️ Could not read ${relativePath}:`, error);
          }
        }
      }
    }
  }
  
  readDir(rootDir);
  return files;
}

async function testTranslationQuery() {
  console.log('🌍 Testing Translation Files Query\n');
  
  try {
    // Read files
    console.log('📁 Reading project files...');
    const files = readProjectFiles('.');
    console.log(`📊 Searching through ${files.length} files\n`);

    // Initialize BM25 embedder
    const embedder = new ContextAwareBM25Embedder();

    // Index files
    console.log('⚡ Indexing files...');
    await embedder.indexFiles(files);
    console.log('✅ Indexing complete\n');

    // Create context for translation query
    const context = {
      recentMessages: [
        { content: "I need to add internationalization to the app", role: "user" },
        { content: "Let me help you set up translation files", role: "assistant" },
        { content: "Where should I put the translation files?", role: "user" }
      ],
      fileTree: files,
      logs: [],
      currentFile: "src/app/page.tsx"
    };

    // Test the translation query
    const userInput = "Show me where to plug the translation files";
    
    console.log(`🔍 Query: "${userInput}"`);
    console.log(`📍 Context: User asking about internationalization setup\n`);
    
    const results = await embedder.findSimilarFiles(userInput, context, 10);
    
    console.log('\n📋 Analysis of Results:');
    console.log('─'.repeat(60));
    
    if (results.length === 0) {
      console.log('❌ No translation-related files found in the codebase');
      console.log('\n💡 Recommendations for setting up translations:');
      console.log('1. Create a `locales/` or `translations/` directory');
      console.log('2. Add language files like `en.json`, `es.json`, etc.');
      console.log('3. Install i18n library (next-i18next, react-i18next, etc.)');
      console.log('4. Set up translation provider in your app');
    } else {
      console.log(`✅ Found ${results.length} potentially relevant files:\n`);

      // Categorize results
      const translationFiles = results.filter(f =>
        f.name.includes('i18n') ||
        f.name.includes('locale') ||
        f.name.includes('translation') ||
        f.name.includes('lang') ||
        f.content?.includes('i18n') ||
        f.content?.includes('translation') ||
        f.content?.includes('locale')
      );

      const configFiles = results.filter(f =>
        f.name.includes('config') ||
        f.name.includes('next.config') ||
        f.name.includes('package.json')
      );

      const componentFiles = results.filter(f =>
        f.name.includes('component') ||
        f.name.includes('.tsx') ||
        f.name.includes('.jsx')
      );
      
      if (translationFiles.length > 0) {
        console.log('🌍 Translation/i18n Files:');
        translationFiles.forEach((file, i) => {
          console.log(`  ${i + 1}. ${file.name}`);
        });
        console.log();
      }
      
      if (configFiles.length > 0) {
        console.log('⚙️ Configuration Files:');
        configFiles.forEach((file, i) => {
          console.log(`  ${i + 1}. ${file.name}`);
        });
        console.log();
      }
      
      if (componentFiles.length > 0) {
        console.log('🧩 Component Files (may need translation):');
        componentFiles.slice(0, 5).forEach((file, i) => {
          console.log(`  ${i + 1}. ${file.name}`);
        });
        if (componentFiles.length > 5) {
          console.log(`  ... and ${componentFiles.length - 5} more`);
        }
        console.log();
      }
    }
    
    // Check for existing i18n setup
    console.log('🔍 Checking for existing i18n setup:');
    console.log('─'.repeat(40));
    
    const packageJson = files.find(f => f.name === 'package.json');
    if (packageJson?.content) {
      const hasI18nDeps = packageJson.content.includes('i18n') || 
                         packageJson.content.includes('react-intl') ||
                         packageJson.content.includes('next-i18next');
      
      if (hasI18nDeps) {
        console.log('✅ Found i18n dependencies in package.json');
      } else {
        console.log('❌ No i18n dependencies found in package.json');
      }
    }
    
    const nextConfig = files.find(f => f.name.includes('next.config'));
    if (nextConfig?.content) {
      const hasI18nConfig = nextConfig.content.includes('i18n') || 
                           nextConfig.content.includes('locale');
      
      if (hasI18nConfig) {
        console.log('✅ Found i18n configuration in Next.js config');
      } else {
        console.log('❌ No i18n configuration found in Next.js config');
      }
    }
    
      // Provide recommendations
      console.log('\n💡 Recommendations:');
      console.log('─'.repeat(30));

      if (translationFiles.length === 0) {
      console.log('📁 Create translation structure:');
      console.log('   src/locales/en.json');
      console.log('   src/locales/es.json');
      console.log('   src/locales/fr.json');
      console.log();
      
      console.log('📦 Install i18n library:');
      console.log('   npm install next-i18next react-i18next');
      console.log();
      
      console.log('⚙️ Configure Next.js (next.config.js):');
      console.log('   module.exports = {');
      console.log('     i18n: {');
      console.log('       locales: ["en", "es", "fr"],');
      console.log('       defaultLocale: "en"');
      console.log('     }');
      console.log('   }');
      console.log();
      
      console.log('🔧 Set up translation provider in _app.tsx');
    } else {
      console.log('✅ You already have some translation setup');
      console.log('📝 Check the files above to see your current i18n structure');
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testTranslationQuery();
