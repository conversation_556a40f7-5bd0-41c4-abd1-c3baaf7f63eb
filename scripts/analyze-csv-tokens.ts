#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to analyze token usage from a CSV file containing API call data
 * Usage: npx tsx scripts/analyze-csv-tokens.ts --file "/path/to/file.csv" --date "2025-06-10"
 */
import { program } from 'commander';
import fs from 'fs';
import path from 'path';
import Papa from 'papaparse';

// Define command line options
program
    .option('-f, --file <path>', 'Path to the CSV file')
    .option('-d, --date <date>', 'Date to filter for (YYYY-MM-DD)')
    .option('-o, --output <path>', 'Output JSON file path', './token-analysis.json')
    .parse(process.argv);

const options = program.opts();

// Validate inputs
if (!options.file) {
    console.error('Please provide a CSV file path with --file');
    process.exit(1);
}

if (!fs.existsSync(options.file)) {
    console.error(`File not found: ${options.file}`);
    process.exit(1);
}

// Parse the date if provided
let targetDate: string | null = null;
if (options.date) {
    targetDate = options.date;
    console.log(`Filtering for date: ${targetDate}`);
}

// Ensure output directory exists
const outputDir = path.dirname(options.output);
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

interface TokenUsage {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
}

interface TokenAnalysis {
    byModel: Record<string, TokenUsage>;
    byType: Record<string, TokenUsage>;
    byDate: Record<string, TokenUsage>;
    topRequests: Array<{
        id: string;
        date: string;
        model: string;
        type: string;
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    }>;
    totalUsage: TokenUsage;
}

// Initialize analysis object
const analysis: TokenAnalysis = {
    byModel: {},
    byType: {},
    byDate: {},
    topRequests: [],
    totalUsage: {
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0
    }
};

// Process the CSV file
console.log(`Processing CSV file: ${options.file}`);

// Read the file as a stream for better memory handling with large files
const fileStream = fs.createReadStream(options.file);

// Debug flag to print detailed information about each row
const DEBUG = true;

// Parse CSV
Papa.parse(fileStream, {
    header: true,
    skipEmptyLines: true,
    complete: (results) => {
        console.log(`Parsed ${results.data.length} rows from CSV`);
        
        // Process each row
        results.data.forEach((row: any) => {
            try {
                // Parse JSON from schema and heliconeMetadata columns
                let schema = {};
                let metadata = {};
                
                try {
                    if (row.schema) {
                        schema = JSON.parse(row.schema);
                    }
                } catch (e) {
                    console.warn('Error parsing schema JSON');
                }
                
                try {
                    if (row.heliconeMetadata) {
                        metadata = JSON.parse(row.heliconeMetadata);
                    }
                } catch (e) {
                    console.warn('Error parsing heliconeMetadata JSON');
                }
                
                // Extract date information - search more extensively for date fields
                let date = '';
                let createdAt: Date | null = null;
                
                // Function to recursively search for date fields in an object
                const findDateFields = (obj: any, path: string = ''): string[] => {
                    if (!obj || typeof obj !== 'object') return [];
                    
                    let dateFields: string[] = [];
                    
                    for (const key in obj) {
                        const newPath = path ? `${path}.${key}` : key;
                        
                        // Check if this key looks like a date field
                        if (/date|time|created|timestamp/i.test(key)) {
                            const value = obj[key];
                            if (typeof value === 'string' || typeof value === 'number') {
                                dateFields.push(newPath);
                            }
                        }
                        
                        // Recursively search nested objects
                        if (obj[key] && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
                            dateFields = [...dateFields, ...findDateFields(obj[key], newPath)];
                        }
                    }
                    
                    return dateFields;
                };
                
                // Function to get a nested property by path
                const getNestedProperty = (obj: any, path: string): any => {
                    return path.split('.').reduce((prev, curr) => {
                        return prev && prev[curr] ? prev[curr] : null;
                    }, obj);
                };
                
                // Try to find date fields in metadata
                if (metadata) {
                    // First check common fields
                    if ((metadata as any).createdAt) {
                        try {
                            createdAt = new Date((metadata as any).createdAt);
                        } catch (e) {}
                    } else if ((metadata as any).created_at) {
                        try {
                            createdAt = new Date((metadata as any).created_at);
                        } catch (e) {}
                    } else if ((metadata as any).timestamp) {
                        try {
                            createdAt = new Date((metadata as any).timestamp);
                        } catch (e) {}
                    } else {
                        // Search for date fields
                        const dateFields = findDateFields(metadata);
                        for (const field of dateFields) {
                            const value = getNestedProperty(metadata, field);
                            if (value) {
                                try {
                                    createdAt = new Date(value);
                                    if (!isNaN(createdAt.getTime())) break;
                                } catch (e) {}
                            }
                        }
                    }
                }
                
                // If not found in metadata, try schema
                if (!createdAt && schema) {
                    // Check common fields
                    if ((schema as any).created_at) {
                        try {
                            createdAt = new Date((schema as any).created_at);
                        } catch (e) {}
                    } else if ((schema as any).created) {
                        try {
                            createdAt = new Date((schema as any).created);
                        } catch (e) {}
                    } else if ((schema as any).request && (schema as any).request.created_at) {
                        try {
                            createdAt = new Date((schema as any).request.created_at);
                        } catch (e) {}
                    } else if ((schema as any).request && (schema as any).request.created) {
                        try {
                            createdAt = new Date((schema as any).request.created);
                        } catch (e) {}
                    } else {
                        // Search for date fields
                        const dateFields = findDateFields(schema);
                        for (const field of dateFields) {
                            const value = getNestedProperty(schema, field);
                            if (value) {
                                try {
                                    createdAt = new Date(value);
                                    if (!isNaN(createdAt.getTime())) break;
                                } catch (e) {}
                            }
                        }
                    }
                }
                
                // If we found a valid date, format it
                if (createdAt && !isNaN(createdAt.getTime())) {
                    date = createdAt.toISOString().split('T')[0]; // Format as YYYY-MM-DD
                }
                
                // If we still don't have a date, check for Unix timestamps
                if (!date) {
                    // Look for Unix timestamp fields (seconds or milliseconds)
                    const checkUnixTimestamp = (obj: any) => {
                        if (!obj) return null;
                        
                        for (const key in obj) {
                            if (/time|created|timestamp/i.test(key) && typeof obj[key] === 'number') {
                                const timestamp = obj[key];
                                // Check if it's a reasonable Unix timestamp (between 2020 and 2030)
                                if (timestamp > 1577836800 && timestamp < 1893456000) { // 2020-01-01 to 2030-01-01
                                    return new Date(timestamp * 1000); // Seconds to milliseconds
                                } else if (timestamp > 1577836800000 && timestamp < 1893456000000) { // Already in milliseconds
                                    return new Date(timestamp);
                                }
                            }
                        }
                        return null;
                    };
                    
                    const timestampDate = checkUnixTimestamp(metadata) || checkUnixTimestamp(schema);
                    if (timestampDate) {
                        date = timestampDate.toISOString().split('T')[0];
                    }
                }
                
                // Debug date extraction
                if (DEBUG) {
                    console.log(`Row ID: ${row.id || 'unknown'}, Found date: ${date || 'NONE'}`);
                }
                
                // Skip if we're filtering by date and this row doesn't match
                // Be more flexible with date matching - check if the date contains the target date
                if (targetDate) {
                    if (!date) {
                        // No date found, skip
                        return;
                    }
                    
                    // Try exact match first
                    if (date !== targetDate) {
                        // If not exact match, check if the date contains the target date
                        // This handles cases where the date might be in a different format but contains the target date
                        if (!date.includes(targetDate.split('-').join('')) && 
                            !date.includes(targetDate)) {
                            return;
                        }
                    }
                }
                
                // Extract token information - search more extensively
                let promptTokens = 0;
                let completionTokens = 0;
                let totalTokens = 0;
                
                // Function to recursively search for token fields in an object
                const findTokenFields = (obj: any): any => {
                    if (!obj || typeof obj !== 'object') return null;
                    
                    // Check for common token usage patterns
                    if (obj.usage && typeof obj.usage === 'object') {
                        return obj.usage;
                    }
                    
                    if (obj.prompt_tokens !== undefined || 
                        obj.completion_tokens !== undefined || 
                        obj.total_tokens !== undefined) {
                        return obj;
                    }
                    
                    if (obj.promptTokens !== undefined || 
                        obj.completionTokens !== undefined || 
                        obj.totalTokens !== undefined) {
                        return obj;
                    }
                    
                    // Search in nested objects
                    for (const key in obj) {
                        if (obj[key] && typeof obj[key] === 'object') {
                            const result = findTokenFields(obj[key]);
                            if (result) return result;
                        }
                    }
                    
                    return null;
                };
                
                // Try to get token info from heliconeMetadata
                if (metadata) {
                    // Direct field access for common patterns
                    if ((metadata as any).promptTokens !== undefined) {
                        promptTokens = parseInt((metadata as any).promptTokens) || 0;
                    }
                    
                    if ((metadata as any).completionTokens !== undefined) {
                        completionTokens = parseInt((metadata as any).completionTokens) || 0;
                    }
                    
                    if ((metadata as any).totalTokens !== undefined) {
                        totalTokens = parseInt((metadata as any).totalTokens) || 0;
                    }
                    
                    // Search deeper if needed
                    if (totalTokens === 0) {
                        const tokenData = findTokenFields(metadata);
                        if (tokenData) {
                            promptTokens = parseInt(tokenData.prompt_tokens || tokenData.promptTokens || 0);
                            completionTokens = parseInt(tokenData.completion_tokens || tokenData.completionTokens || 0);
                            totalTokens = parseInt(tokenData.total_tokens || tokenData.totalTokens || 0);
                        }
                    }
                }
                
                // If no tokens found in metadata, try schema
                if (totalTokens === 0 && schema) {
                    // Look for usage data in schema
                    const tokenData = findTokenFields(schema);
                    if (tokenData) {
                        promptTokens = parseInt(tokenData.prompt_tokens || tokenData.promptTokens || 0);
                        completionTokens = parseInt(tokenData.completion_tokens || tokenData.completionTokens || 0);
                        totalTokens = parseInt(tokenData.total_tokens || tokenData.totalTokens || 0);
                    }
                }
                
                // If we have prompt and completion tokens but no total, calculate it
                if (totalTokens === 0 && (promptTokens > 0 || completionTokens > 0)) {
                    totalTokens = promptTokens + completionTokens;
                }
                
                // Debug token extraction
                if (DEBUG && totalTokens > 0) {
                    console.log(`Row ID: ${row.id || 'unknown'}, Tokens: ${totalTokens} (${promptTokens} prompt, ${completionTokens} completion)`);
                }
                
                // Skip if no token information found
                if (totalTokens === 0) {
                    return;
                }
                
                // Extract model and type information
                const model = row.model || 
                    ((schema as any).request && (schema as any).request.model) || 
                    'unknown';
                const type = row._type || 'unknown';
                
                // Update analysis by model
                if (!analysis.byModel[model]) {
                    analysis.byModel[model] = { promptTokens: 0, completionTokens: 0, totalTokens: 0 };
                }
                analysis.byModel[model].promptTokens += promptTokens;
                analysis.byModel[model].completionTokens += completionTokens;
                analysis.byModel[model].totalTokens += totalTokens;
                
                // Update analysis by type
                if (!analysis.byType[type]) {
                    analysis.byType[type] = { promptTokens: 0, completionTokens: 0, totalTokens: 0 };
                }
                analysis.byType[type].promptTokens += promptTokens;
                analysis.byType[type].completionTokens += completionTokens;
                analysis.byType[type].totalTokens += totalTokens;
                
                // Update analysis by date
                if (!analysis.byDate[date]) {
                    analysis.byDate[date] = { promptTokens: 0, completionTokens: 0, totalTokens: 0 };
                }
                analysis.byDate[date].promptTokens += promptTokens;
                analysis.byDate[date].completionTokens += completionTokens;
                analysis.byDate[date].totalTokens += totalTokens;
                
                // Update total usage
                analysis.totalUsage.promptTokens += promptTokens;
                analysis.totalUsage.completionTokens += completionTokens;
                analysis.totalUsage.totalTokens += totalTokens;
                
                // Add to top requests
                analysis.topRequests.push({
                    id: row.id || 'unknown',
                    date,
                    model,
                    type,
                    promptTokens,
                    completionTokens,
                    totalTokens
                });
                
            } catch (error) {
                console.error('Error processing row:', error);
            }
        });
        
        // Sort top requests by total tokens (descending)
        analysis.topRequests.sort((a, b) => b.totalTokens - a.totalTokens);
        
        // Limit to top 50 requests
        analysis.topRequests = analysis.topRequests.slice(0, 50);
        
        // Calculate costs
        const inputCost = (analysis.totalUsage.promptTokens / 1000000) * 3; // $3 per million input tokens
        const outputCost = (analysis.totalUsage.completionTokens / 1000000) * 15; // $15 per million output tokens
        const totalCost = inputCost + outputCost;
        
        // Add cost information to the analysis
        const analysisWithCost = {
            ...analysis,
            costs: {
                inputCost,
                outputCost,
                totalCost
            }
        };
        
        // Write analysis to output file
        fs.writeFileSync(
            options.output,
            JSON.stringify(analysisWithCost, null, 2)
        );
        
        console.log(`Analysis complete. Results written to ${options.output}`);
        console.log(`Total tokens processed: ${analysis.totalUsage.totalTokens.toLocaleString()}`);
        console.log(`Total cost: $${totalCost.toFixed(2)} ($${inputCost.toFixed(2)} input + $${outputCost.toFixed(2)} output)`);
        
        // Print top models by token usage
        console.log('\nTop Models by Token Usage:');
        Object.entries(analysis.byModel)
            .sort((a, b) => b[1].totalTokens - a[1].totalTokens)
            .slice(0, 5)
            .forEach(([model, usage]) => {
                console.log(`${model}: ${usage.totalTokens.toLocaleString()} tokens`);
            });
        
        // Print top request types by token usage
        console.log('\nTop Request Types by Token Usage:');
        Object.entries(analysis.byType)
            .sort((a, b) => b[1].totalTokens - a[1].totalTokens)
            .slice(0, 5)
            .forEach(([type, usage]) => {
                console.log(`${type}: ${usage.totalTokens.toLocaleString()} tokens`);
            });
    },
    error: (error) => {
        console.error('Error parsing CSV:', error);
        process.exit(1);
    }
});
