# Two-Stage Context Engine Testing Script

This script tests your improved two-stage context engine against the current codebase and provides detailed performance analysis.

## Usage

```bash
# Run the test script
npm run test:context-engine

# Or run directly with tsx
npx tsx scripts/test-two-stage-context-engine.ts
```

## What It Does

### 1. **Codebase Analysis**
- Recursively reads all files from the project root
- Applies .gitignore patterns and additional exclusions
- Filters out binary files, large files, and irrelevant content
- Converts files to the `FileItem[]` format expected by your engine

### 2. **File Exclusions**
The script automatically excludes:
- **From .gitignore**: `node_modules`, `.next`, `build`, `.env`, etc.
- **Binary files**: Images, videos, fonts, etc.
- **Generated files**: `types/generated`, `dist/`, etc.
- **Test files**: `*.test.*`, `*.spec.*`, `__tests__/`
- **Large files**: Files over 1MB

### 3. **Test Queries**
Runs the same queries you've been testing:

1. **Button Component Usage**: "Show me all the usages of the button component and how its used across the app. There's an issue causing it to not render."

2. **Tool Call Streaming**: "How does the tool call get streamed to the ui when generating code? Find the best place to make sure that we obfuscate the tool output streamed to the ui"

3. **Message Flow**: "Show me the addMessage function in sessionStore and how messages are added to the local state and database"

4. **Handle Send Message**: "Show me the handleSendMessage function in ConversationScreen and how it calls addMessage and generateAIResponse"

### 4. **Performance Metrics**
For each query, it tracks:
- ⏱️ **Execution time** (milliseconds)
- 📊 **Number of snippets** found
- 📏 **Total lines** returned
- 💰 **Estimated token count** (~5 tokens per line)
- 📋 **Additional files** suggested

### 5. **Detailed Results**
- Saves complete results to `results/` directory as JSON files
- Includes snippet previews, scores, and metadata
- Provides summary statistics across all queries

## Output Analysis

### Console Output
```
🚀 Testing Two-Stage Context Engine with Current Codebase

📁 Reading codebase files...
📊 Found 247 files to analyze
📊 Total lines of code: 45,231
📊 File types: typescript, javascript, json, markdown, css

🔍 TEST QUERY 1/4
📝 Query: Show me all the usages of the button component...
⏱️  Execution Time: 8,234ms
📊 Found 4 snippets
📏 Total lines returned: 287
💰 Estimated tokens: ~1,435 tokens
```

### JSON Results
Each query generates a detailed JSON file with:
```json
{
  "timestamp": "2025-01-12T10:30:00.000Z",
  "query": "Show me all the usages...",
  "executionTime": 8234,
  "snippetsCount": 4,
  "totalLines": 287,
  "snippets": [
    {
      "filePath": "src/components/ui/button.tsx",
      "type": "component",
      "score": 1.0,
      "lines": 156,
      "preview": "import React from 'react';\nimport { cn } from '@/lib/utils';\n..."
    }
  ],
  "additionalFiles": [...]
}
```

## Comparison with Augment's Codebase-Retrieval

Use this data to compare:

### **Performance Metrics**
- **Speed**: Your engine vs Augment's sub-second responses
- **Token Usage**: Your ~1,400 tokens vs Augment's optimized output
- **Relevance**: Quality of snippets returned

### **Output Quality**
- **Completeness**: Are function calls included when requested?
- **Editability**: Do snippets include necessary imports/types?
- **Precision**: Are the exact requested sections found?

### **Cost Analysis**
- **Token Budget**: Staying within 1000-line limit
- **Relevance vs Cost**: Quality trade-offs
- **Scalability**: Performance on larger codebases

## Expected Results

Based on your current improvements, you should see:

✅ **1000-line budget enforced** (never exceeded)
✅ **Complete editable snippets** (with imports/types)
✅ **Smart truncation** (removes low-relevance snippets)
✅ **Additional file suggestions** (for follow-up queries)
✅ **Significant cost reduction** (vs previous 33K+ tokens)

## Troubleshooting

### Common Issues
1. **"No relevant files found"**: Check if files are being excluded by patterns
2. **Large execution times**: Normal for first run, should improve with caching
3. **Missing .env.local**: Ensure OpenAI API keys are configured
4. **Permission errors**: Run with appropriate file system permissions

### Debug Mode
Add console logs to see:
- Which files are being processed
- LLM responses from both stages
- Token usage breakdown

## Next Steps

1. **Run the script** and analyze the results
2. **Compare with Augment's output** for the same queries
3. **Identify gaps** in relevance, speed, or cost
4. **Iterate on improvements** based on findings
5. **Test with different query types** to validate robustness

The goal is to achieve **75% cost reduction** while **maintaining or improving output quality** compared to your current system.
