#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to analyze message content from a JSONL file to identify high token usage causes
 * Usage: npx tsx scripts/analyze-message-content.ts --file "/path/to/file.jsonl" --date "2025-06-10"
 */
import { program } from 'commander';
import fs from 'fs';
import path from 'path';
import readline from 'readline';

// Define command line options
program
    .option('-f, --file <path>', 'Path to the JSONL file')
    .option('-d, --date <date>', 'Date to filter for (YYYY-MM-DD)')
    .option('-o, --output <path>', 'Output JSON file path', './message-analysis.json')
    .option('-t, --top <number>', 'Number of top token consumers to analyze in detail', '10')
    .parse(process.argv);

const options = program.opts();

// Validate inputs
if (!options.file) {
    console.error('Please provide a JSONL file path with --file');
    process.exit(1);
}

if (!fs.existsSync(options.file)) {
    console.error(`File not found: ${options.file}`);
    process.exit(1);
}

// Parse the date if provided
let targetDate: string | null = null;
if (options.date) {
    targetDate = options.date;
    console.log(`Filtering for date: ${targetDate}`);
}

// Ensure output directory exists
const outputDir = path.dirname(options.output);
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

// Number of top token consumers to analyze in detail
const topCount = parseInt(options.top) || 10;

interface MessageAnalysis {
    requestId: string;
    createdAt: string;
    totalTokens: number;
    promptTokens: number;
    completionTokens: number;
    model: string;
    provider: string;
    app: string;
    messageCount: number;
    userMessageCount: number;
    assistantMessageCount: number;
    systemMessageCount: number;
    toolMessageCount: number;
    largestMessageRole: string;
    largestMessageSize: number;
    totalMessageSize: number;
    messages: Array<{
        role: string;
        contentSize: number;
        contentPreview: string;
    }>;
}

// Process the JSONL file
console.log(`Processing JSONL file: ${options.file}`);

// Function to extract messages from various possible locations in the JSON structure
function extractMessages(data: any): Array<any> {
    let messages: Array<any> = [];
    
    // Try different paths where messages might be stored
    if (data.schema?.request?.messages) {
        messages = data.schema.request.messages;
    } else if (data.schema?.body?.messages) {
        messages = data.schema.body.messages;
    } else if (data.raw) {
        try {
            const rawData = JSON.parse(data.raw);
            if (rawData.messages) {
                messages = rawData.messages;
            }
        } catch (e) {
            // Ignore parsing errors for raw field
        }
    }
    
    // If we still don't have messages, search recursively
    if (messages.length === 0) {
        const findMessages = (obj: any): Array<any> => {
            if (!obj || typeof obj !== 'object') return [];
            
            if (Array.isArray(obj) && obj.length > 0 && 
                obj[0] && typeof obj[0] === 'object' && 
                (obj[0].role || obj[0].content)) {
                // This looks like a messages array
                return obj;
            }
            
            for (const key in obj) {
                if (key === 'messages' && Array.isArray(obj[key])) {
                    return obj[key];
                }
                
                if (obj[key] && typeof obj[key] === 'object') {
                    const result = findMessages(obj[key]);
                    if (result.length > 0) return result;
                }
            }
            
            return [];
        };
        
        messages = findMessages(data);
    }
    
    return messages;
}

// Function to get content size and preview
function getContentInfo(content: any): { size: number; preview: string } {
    if (!content) {
        return { size: 0, preview: '' };
    }
    
    if (typeof content === 'string') {
        return { 
            size: content.length,
            preview: content
        };
    }
    
    if (typeof content === 'object') {
        const contentStr = JSON.stringify(content);
        return { 
            size: contentStr.length,
            preview: contentStr
        };
    }
    
    return { size: 0, preview: '' };
}

// Create a readline interface to read the file line by line
const processFile = async () => {
    const fileStream = fs.createReadStream(options.file);
    const rl = readline.createInterface({
        input: fileStream,
        crlfDelay: Infinity
    });

    let lineCount = 0;
    let matchedCount = 0;
    let messageAnalyses: MessageAnalysis[] = [];

    for await (const line of rl) {
        lineCount++;
        
        try {
            // Parse the JSON line
            const data = JSON.parse(line);
            
            // Extract metadata from the JSON
            const metadata = data.heliconeMetadata;
            if (!metadata) continue;
            
            // Extract date information
            let date = '';
            if (metadata.createdAt) {
                const createdAt = new Date(metadata.createdAt);
                date = createdAt.toISOString().split('T')[0]; // Format as YYYY-MM-DD
                
                // Skip if we're filtering by date and this row doesn't match
                if (targetDate && date !== targetDate) {
                    continue;
                }
                
                matchedCount++;
            } else {
                // No date found, skip
                continue;
            }
            
            // Extract token information
            const promptTokens = parseInt(metadata.promptTokens || '0');
            const completionTokens = parseInt(metadata.completionTokens || '0');
            const totalTokens = parseInt(metadata.totalTokens || '0') || (promptTokens + completionTokens);
            
            // Skip if no token information found
            if (totalTokens === 0) {
                continue;
            }
            
            // Extract model, provider, and app information
            const model = metadata.model || data.model || 'unknown';
            const provider = metadata.provider || 'unknown';
            const app = metadata.customProperties?.app || 'unknown';
            
            // Extract messages
            const messages = extractMessages(data);
            
            // Analyze messages
            let userMessageCount = 0;
            let assistantMessageCount = 0;
            let systemMessageCount = 0;
            let toolMessageCount = 0;
            let largestMessageRole = '';
            let largestMessageSize = 0;
            let totalMessageSize = 0;
            
            const messageDetails = messages.map(msg => {
                const role = msg.role || 'unknown';
                const contentInfo = getContentInfo(msg.content);
                
                // Count by role
                if (role === 'user') userMessageCount++;
                else if (role === 'assistant') assistantMessageCount++;
                else if (role === 'system') systemMessageCount++;
                else if (role === 'tool') toolMessageCount++;
                
                // Track largest message
                if (contentInfo.size > largestMessageSize) {
                    largestMessageSize = contentInfo.size;
                    largestMessageRole = role;
                }
                
                // Add to total size
                totalMessageSize += contentInfo.size;
                
                return {
                    role,
                    contentSize: contentInfo.size,
                    contentPreview: contentInfo.preview
                };
            });
            
            // Create message analysis object
            const analysis: MessageAnalysis = {
                requestId: metadata.requestId || 'unknown',
                createdAt: metadata.createdAt || '',
                totalTokens,
                promptTokens,
                completionTokens,
                model,
                provider,
                app,
                messageCount: messages.length,
                userMessageCount,
                assistantMessageCount,
                systemMessageCount,
                toolMessageCount,
                largestMessageRole,
                largestMessageSize,
                totalMessageSize,
                messages: messageDetails
            };
            
            messageAnalyses.push(analysis);
            
        } catch (error) {
            console.error(`Error processing line ${lineCount}:`, error);
        }
    }
    
    console.log(`Processed ${lineCount} lines, matched ${matchedCount} records for date ${targetDate || 'any'}`);
    console.log(`Found ${messageAnalyses.length} records with token information`);
    
    // Sort by total tokens (descending)
    messageAnalyses.sort((a, b) => b.totalTokens - a.totalTokens);
    
    // Calculate summary statistics
    const totalRecords = messageAnalyses.length;
    const totalTokensSum = messageAnalyses.reduce((sum, item) => sum + item.totalTokens, 0);
    const avgTokensPerRequest = Math.round(totalTokensSum / totalRecords);
    
    const roleCounts = {
        user: messageAnalyses.reduce((sum, item) => sum + item.userMessageCount, 0),
        assistant: messageAnalyses.reduce((sum, item) => sum + item.assistantMessageCount, 0),
        system: messageAnalyses.reduce((sum, item) => sum + item.systemMessageCount, 0),
        tool: messageAnalyses.reduce((sum, item) => sum + item.toolMessageCount, 0)
    };
    
    const largestMessageRoles = messageAnalyses.reduce((acc, item) => {
        acc[item.largestMessageRole] = (acc[item.largestMessageRole] || 0) + 1;
        return acc;
    }, {} as Record<string, number>);
    
    // Create output object
    const output = {
        summary: {
            totalRequests: totalRecords,
            totalTokens: totalTokensSum,
            avgTokensPerRequest,
            roleCounts,
            largestMessageRoles
        },
        topTokenConsumers: messageAnalyses.slice(0, topCount),
        allRequests: messageAnalyses
    };
    
    // Write output to file
    fs.writeFileSync(
        options.output,
        JSON.stringify(output, null, 2)
    );
    
    console.log(`Analysis complete. Results written to ${options.output}`);
    
    // Print summary information
    console.log('\nSummary:');
    console.log(`Total Requests: ${totalRecords}`);
    console.log(`Total Tokens: ${totalTokensSum.toLocaleString()}`);
    console.log(`Average Tokens per Request: ${avgTokensPerRequest.toLocaleString()}`);
    
    console.log('\nMessage Role Counts:');
    Object.entries(roleCounts).forEach(([role, count]) => {
        console.log(`${role}: ${count}`);
    });
    
    console.log('\nLargest Message by Role:');
    Object.entries(largestMessageRoles).forEach(([role, count]) => {
        console.log(`${role}: ${count} requests`);
    });
    
    // Print top token consumers
    console.log('\nTop Token Consumers:');
    messageAnalyses.slice(0, 5).forEach((analysis, index) => {
        console.log(`\n#${index + 1}: ${analysis.totalTokens.toLocaleString()} tokens (${analysis.promptTokens.toLocaleString()} prompt, ${analysis.completionTokens.toLocaleString()} completion)`);
        console.log(`Request ID: ${analysis.requestId}`);
        console.log(`Time: ${analysis.createdAt}`);
        console.log(`Message Count: ${analysis.messageCount} (${analysis.userMessageCount} user, ${analysis.assistantMessageCount} assistant, ${analysis.systemMessageCount} system, ${analysis.toolMessageCount} tool)`);
        console.log(`Largest Message: ${analysis.largestMessageSize.toLocaleString()} chars (${analysis.largestMessageRole} role)`);
    });
};

// Run the analysis
processFile().catch(err => {
    console.error('Error processing file:', err);
    process.exit(1);
});
