#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test the two-stage context engine with the current codebase
 * Usage: npx tsx scripts/test-two-stage-context-engine.ts
 */

import * as fs from 'fs';
import * as path from 'path';
import { TwoStageLLMContextEngine } from '../src/lib/services/two-stage-context-engine';
import { FileItem } from '../src/types/file';

// Load environment variables
require('dotenv').config({path: ".env.local"});

/**
 * Get file extension and determine language
 */
function getLanguageFromPath(filePath: string): string {
  const ext = path.extname(filePath).toLowerCase();
  const languageMap: Record<string, string> = {
    '.ts': 'typescript',
    '.tsx': 'typescript',
    '.js': 'javascript',
    '.jsx': 'javascript',
    '.json': 'json',
    '.md': 'markdown',
    '.css': 'css',
    '.scss': 'scss',
    '.html': 'html',
    '.yml': 'yaml',
    '.yaml': 'yaml',
    '.sql': 'sql',
    '.py': 'python',
    '.java': 'java',
    '.kt': 'kotlin',
    '.swift': 'swift',
    '.go': 'go',
    '.rs': 'rust',
    '.php': 'php',
    '.rb': 'ruby',
    '.sh': 'bash',
    '.xml': 'xml',
    '.toml': 'toml',
    '.ini': 'ini',
    '.env': 'bash'
  };
  
  return languageMap[ext] || 'text';
}

/**
 * Check if file should be excluded based on .gitignore patterns
 */
function shouldExcludeFile(filePath: string): boolean {
  const excludePatterns = [
    // From .gitignore
    'node_modules',
    '.next',
    'out',
    'build',
    'coverage',
    '.DS_Store',
    '.env',
    '.vercel',
    '*.tsbuildinfo',
    'next-env.d.ts',
    '.idea',
    'zump',
    
    // Additional exclusions for context engine
    'assets/',
    '.mp3',
    '.mp4', 
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.svg',
    '.ico',
    '.woff',
    '.woff2',
    '.ttf',
    '.eot',
    'types/generated',
    'dist/',
    '.git/',
    '.yarn/',
    'coverage/',
    'test/',
    '__tests__/',
    '.test.',
    '.spec.',
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml'
  ];
  
  return excludePatterns.some(pattern => {
    if (pattern.endsWith('/')) {
      return filePath.includes(pattern);
    }
    if (pattern.startsWith('*.')) {
      return filePath.endsWith(pattern.substring(1));
    }
    if (pattern.startsWith('.') && pattern.length > 1) {
      return filePath.includes(pattern);
    }
    return filePath.includes(pattern);
  });
}

/**
 * Recursively read all files from a directory
 */
function readFilesRecursively(dir: string, baseDir: string = dir): FileItem[] {
  const files: FileItem[] = [];
  
  try {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      const relativePath = path.relative(baseDir, fullPath);
      
      // Skip if should be excluded
      if (shouldExcludeFile(relativePath)) {
        continue;
      }
      
      if (entry.isDirectory()) {
        // Recursively read subdirectory
        files.push(...readFilesRecursively(fullPath, baseDir));
      } else if (entry.isFile()) {
        try {
          const content = fs.readFileSync(fullPath, 'utf-8');
          const language = getLanguageFromPath(fullPath);
          
          // Only include text files that we can analyze
          if (content.length > 0 && content.length < 1000000) { // Skip very large files
            files.push({
              name: relativePath.replace(/\\/g, '/'), // Normalize path separators
              type: 'file',
              language,
              content,
              changes: 0
            });
          }
        } catch (error) {
          // Skip files that can't be read (binary files, permission issues, etc.)
          console.warn(`Skipping file ${relativePath}: ${error}`);
        }
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error);
  }
  
  return files;
}

/**
 * Default test queries to run against the context engine
 * Updated for magically.life codebase with diverse query types
 */
const defaultTestQueries = [
  {
    query: "Show me how the two-stage context engine works and how it selects relevant files for queries",
    reason: "Need to understand the context engine architecture for optimization"
  },
  {
    query: "Find all the chat tools and how they integrate with the streaming system. Show me the tool execution flow.",
    reason: "Need to understand tool architecture for adding new tools"
  },
  {
    query: "Show me how messages are stored and retrieved from the database, including the schema and queries",
    reason: "Need to understand data persistence for debugging message issues"
  },
  {
    query: "How does the deployment system work? Show me the deployment store and how it manages different platforms",
    reason: "Need to understand deployment architecture for adding new platforms"
  },
  {
    query: "Show me the subscription and billing system, including how features are gated and usage is tracked",
    reason: "Need to understand monetization system for implementing new pricing tiers"
  },
  {
    query: "Find the authentication system and how user sessions are managed across the application",
    reason: "Need to understand auth flow for implementing new login methods"
  },
  {
    query: "Show me how the file system and project management works, including file operations and state management",
    reason: "Need to understand project architecture for implementing new file features"
  },
  {
    query: "How does the AI streaming and response generation work? Show me the complete flow from user input to AI response",
    reason: "Need to understand AI integration for optimizing response quality"
  }
];

/**
 * Parse command line arguments
 */
function parseArguments() {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    return { useDefault: true, queries: defaultTestQueries };
  }

  // Check for help flag
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Usage: npx tsx scripts/test-two-stage-context-engine.ts [options]

Options:
  --query "your query here"     Run a single custom query
  --reason "reason here"        Provide reason for the query (optional)
  --help, -h                    Show this help message

Examples:
  # Run default test queries
  npx tsx scripts/test-two-stage-context-engine.ts

  # Run a single custom query
  npx tsx scripts/test-two-stage-context-engine.ts --query "Show me how authentication works"

  # Run with custom reason
  npx tsx scripts/test-two-stage-context-engine.ts --query "Show me auth flow" --reason "Need to debug login issues"
`);
    process.exit(0);
  }

  // Parse custom query
  const queryIndex = args.indexOf('--query');
  const reasonIndex = args.indexOf('--reason');

  if (queryIndex !== -1 && queryIndex + 1 < args.length) {
    const query = args[queryIndex + 1];
    const reason = reasonIndex !== -1 && reasonIndex + 1 < args.length
      ? args[reasonIndex + 1]
      : "Custom query for testing";

    return {
      useDefault: false,
      queries: [{ query, reason }]
    };
  }

  // Default to all test queries
  return { useDefault: true, queries: defaultTestQueries };
}

/**
 * Save results to a file for detailed analysis
 */
function saveResultsToFile(queryIndex: number, query: string, result: any, executionTime: number) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const fileName = `two-stage-engine-results-query-${queryIndex + 1}-${timestamp}.json`;
  const filePath = path.join(__dirname, '..', 'results', fileName);

  // Ensure results directory exists
  const resultsDir = path.dirname(filePath);
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir, { recursive: true });
  }

  const output = {
    timestamp: new Date().toISOString(),
    query,
    executionTime,
    snippetsCount: result.snippets.length,
    additionalFilesCount: result.additionalFiles.length,
    totalLines: result.snippets.reduce((sum: number, s: any) => sum + s.content.split('\n').length, 0),
    snippets: result.snippets.map((s: any) => ({
      filePath: s.filePath,
      type: s.type,
      context: s.context,
      score: s.score,
      lines: s.content.split('\n').length,
      startLine: s.startLine,
      endLine: s.endLine,
      symbols: s.symbols,
      // Include first 5 lines of content for preview
      preview: s.content.split('\n').slice(0, 5).join('\n') + (s.content.split('\n').length > 5 ? '\n...' : '')
    })),
    additionalFiles: result.additionalFiles,
    fullResults: result // Complete results for detailed analysis
  };

  fs.writeFileSync(filePath, JSON.stringify(output, null, 2));
  console.log(`💾 Results saved to: ${fileName}`);
}

/**
 * Main test function
 */
async function testTwoStageContextEngine() {
  console.log('🚀 Testing Two-Stage Context Engine with Current Codebase\n');

  // Parse command line arguments
  const { useDefault, queries } = parseArguments();

  if (useDefault) {
    console.log('📋 Running default test queries');
  } else {
    console.log('📋 Running custom query');
  }
  console.log('📋 Results will be saved for comparison with Augment\'s codebase-retrieval\n');

  // Read all files from the current directory
  console.log('📁 Reading codebase files...');
  const projectRoot = path.resolve(__dirname, '..');
  const files = readFilesRecursively(projectRoot);

  console.log(`📊 Found ${files.length} files to analyze`);
  console.log(`📊 Total lines of code: ${files.reduce((sum, f) => sum + f.content.split('\n').length, 0)}`);
  console.log(`📊 File types: ${[...new Set(files.map(f => f.language))].join(', ')}`);
  console.log(`📊 Average file size: ${Math.round(files.reduce((sum, f) => sum + f.content.split('\n').length, 0) / files.length)} lines\n`);

  // Initialize the context engine
  console.log('🔧 Initializing Two-Stage Context Engine...');
  const engine = new TwoStageLLMContextEngine(files);
  console.log('✅ Engine initialized\n');

  // Summary stats
  const summaryStats = {
    totalQueries: queries.length,
    totalExecutionTime: 0,
    totalSnippets: 0,
    totalLines: 0,
    averageExecutionTime: 0,
    averageSnippetsPerQuery: 0,
    averageLinesPerQuery: 0
  };

  // Run test queries
  for (let i = 0; i < queries.length; i++) {
    const { query, reason } = queries[i];

    console.log('='.repeat(100));
    console.log(`🔍 TEST QUERY ${i + 1}/${queries.length}`);
    console.log('='.repeat(100));
    console.log(`📝 Query: ${query}`);
    console.log(`📝 Reason: ${reason}\n`);

    try {
      const startTime = Date.now();
      const snippets = await engine.getRelevantSnippets(query, reason);
      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Create result object to match expected format
      const result = {
        snippets: snippets || [],
        additionalFiles: [] // Two-stage engine doesn't return additional files
      };

      // Update summary stats
      summaryStats.totalExecutionTime += executionTime;
      summaryStats.totalSnippets += result.snippets.length;
      const queryLines = result.snippets.reduce((sum, s) => sum + s.content.split('\n').length, 0);
      summaryStats.totalLines += queryLines;

      console.log(`⏱️  Execution Time: ${executionTime}ms`);
      console.log(`📊 Found ${result.snippets.length} snippets`);
      console.log(`📋 ${result.additionalFiles.length} additional files suggested`);
      console.log(`📏 Total lines returned: ${queryLines}`);
      console.log(`💰 Estimated tokens: ~${Math.round(queryLines * 5)} tokens\n`);

      // Display snippets summary
      if (result.snippets.length > 0) {
        console.log('📄 SNIPPETS FOUND:');
        result.snippets.forEach((snippet, index) => {
          const lines = snippet.content.split('\n').length;
          console.log(`  ${index + 1}. ${snippet.filePath} (${lines} lines, score: ${snippet.score?.toFixed(2) || 'N/A'})`);
          console.log(`     Type: ${snippet.type}, Context: ${snippet.context}`);
          console.log(`     Lines: ${snippet.startLine}-${snippet.endLine}`);
        });
      }

      // Display additional files
      if (result.additionalFiles.length > 0) {
        console.log('\n🔍 ADDITIONAL FILES SUGGESTED:');
        result.additionalFiles.forEach((file, index) => {
          console.log(`  ${index + 1}. ${file.fileName}`);
          console.log(`     Reason: ${file.reason}`);
          console.log(`     Suggested Query: "${file.suggestedQuery}"`);
        });
      }

      // Save detailed results
      saveResultsToFile(i, query, result, executionTime);

      console.log('\n');

    } catch (error) {
      console.error(`❌ Error executing query: ${error}`);
      console.log('\n');
    }
  }

  // Calculate and display summary statistics
  summaryStats.averageExecutionTime = summaryStats.totalExecutionTime / summaryStats.totalQueries;
  summaryStats.averageSnippetsPerQuery = summaryStats.totalSnippets / summaryStats.totalQueries;
  summaryStats.averageLinesPerQuery = summaryStats.totalLines / summaryStats.totalQueries;

  console.log('='.repeat(100));
  console.log('📊 SUMMARY STATISTICS');
  console.log('='.repeat(100));
  console.log(`🔢 Total Queries: ${summaryStats.totalQueries}`);
  console.log(`⏱️  Total Execution Time: ${summaryStats.totalExecutionTime}ms`);
  console.log(`📄 Total Snippets Found: ${summaryStats.totalSnippets}`);
  console.log(`📏 Total Lines Returned: ${summaryStats.totalLines}`);
  console.log(`💰 Total Estimated Tokens: ~${Math.round(summaryStats.totalLines * 5)}`);
  console.log(`⚡ Average Execution Time: ${Math.round(summaryStats.averageExecutionTime)}ms`);
  console.log(`📊 Average Snippets per Query: ${summaryStats.averageSnippetsPerQuery.toFixed(1)}`);
  console.log(`📏 Average Lines per Query: ${Math.round(summaryStats.averageLinesPerQuery)}`);
  console.log(`💰 Average Tokens per Query: ~${Math.round(summaryStats.averageLinesPerQuery * 5)}`);

  console.log('\n🎉 Testing completed!');
  console.log('📁 Check the results/ directory for detailed JSON outputs');
  console.log('🔍 Compare these results with Augment\'s codebase-retrieval tool performance');
}

// Run the test
if (require.main === module) {
  testTwoStageContextEngine()
    .then(() => {
      console.log('✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}
