import { config } from 'dotenv';
import { ContextAwareBM25Embedder } from '../src/lib/services/context-aware-embedder';
import { EmbeddingStore } from '../src/lib/services/embedding-store';
import { FileItem } from '../src/types/file';
import fs from 'fs';
import path from 'path';

// Load environment variables
config({ path: '.env.local' });

// Read project files (same as before)
function readProjectFiles(rootDir: string): FileItem[] {
  const files: FileItem[] = [];
  
  function readDir(dir: string) {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      const relativePath = path.relative(rootDir, fullPath);
      
      // Skip common directories and files
      if (entry.name.startsWith('.') || 
          entry.name === 'node_modules' || 
          entry.name === 'dist' || 
          entry.name === 'build' ||
          entry.name === '.next') {
        continue;
      }
      
      if (entry.isDirectory()) {
        readDir(fullPath);
      } else if (entry.isFile()) {
        // Only include relevant file types
        const ext = path.extname(entry.name);
        if (['.ts', '.tsx', '.js', '.jsx', '.sql', '.json', '.md'].includes(ext)) {
          try {
            const content = fs.readFileSync(fullPath, 'utf-8');
            files.push({
              name: relativePath,
              content: content,
              type: 'file'
            });
          } catch (error) {
            console.warn(`⚠️ Could not read ${relativePath}:`, error);
          }
        }
      }
    }
  }
  
  readDir(rootDir);
  return files;
}

async function testContextAwareEmbedder() {
  console.log('🧪 Testing Context-Aware BM25 Embedder vs OpenAI Embedder\n');
  
  try {
    // Read files
    console.log('📁 Reading project files...');
    const files = readProjectFiles('.');
    console.log(`📊 Testing with ${files.length} files\n`);

    // Initialize both embedders
    const bm25Embedder = new ContextAwareBM25Embedder();
    const openaiEmbedder = new EmbeddingStore();

    // Test scenarios with different contexts
    const testScenarios = [
      {
        name: "Payment Bug Fix",
        userInput: "Fix the credit reset bug in payment",
        context: {
          recentMessages: [
            { content: "User reported credits not resetting after payment", role: "user" },
            { content: "Let me check the LemonSqueezy integration", role: "assistant" },
            { content: "The checkout process seems to complete but credits don't update", role: "user" }
          ],
          fileTree: files,
          logs: [
            { message: "Payment webhook failed to process", type: "error" },
            { message: "Credit update transaction rolled back", type: "error" }
          ],
          currentFile: "src/app/api/checkout/route.ts"
        }
      },
      {
        name: "Auth Integration",
        userInput: "Add Google OAuth login",
        context: {
          recentMessages: [
            { content: "I want to add Google login to the app", role: "user" },
            { content: "We're using NextAuth.js for authentication", role: "assistant" }
          ],
          fileTree: files,
          currentFile: "src/app/(auth)/login/page.tsx"
        }
      },
      {
        name: "Database Schema",
        userInput: "Update user table schema",
        context: {
          recentMessages: [
            { content: "Need to add profile fields to users", role: "user" },
            { content: "I'll update the Supabase schema", role: "assistant" }
          ],
          fileTree: files,
          currentFile: "supabase/migrations/001_initial.sql"
        }
      },
      {
        name: "Navigation Bug",
        userInput: "Screen navigation not working",
        context: {
          recentMessages: [
            { content: "The app crashes when navigating to profile", role: "user" },
            { content: "Let me check the navigation setup", role: "assistant" }
          ],
          fileTree: files,
          currentFile: "src/components/navigation/TabNavigator.tsx"
        }
      }
    ];

    // Index files for BM25
    console.log('⚡ Indexing files for BM25...');
    const bm25IndexStart = Date.now();
    await bm25Embedder.indexFiles(files);
    const bm25IndexTime = Date.now() - bm25IndexStart;
    console.log(`✅ BM25 indexing completed in ${bm25IndexTime}ms\n`);

    // Index files for OpenAI (if API key available)
    let openaiIndexTime = 0;
    let openaiAvailable = false;
    if (process.env.OPENAI_API_KEY) {
      try {
        console.log('🔄 Indexing files for OpenAI...');
        const openaiIndexStart = Date.now();
        await openaiEmbedder.updateEmbeddings(files.slice(0, 20)); // Test with smaller set
        openaiIndexTime = Date.now() - openaiIndexStart;
        openaiAvailable = true;
        console.log(`✅ OpenAI indexing completed in ${openaiIndexTime}ms\n`);
      } catch (error) {
        console.log('⚠️ OpenAI API not available, testing BM25 only\n');
      }
    } else {
      console.log('⚠️ No OpenAI API key, testing BM25 only\n');
    }

    // Test each scenario
    for (const scenario of testScenarios) {
      console.log(`\n🎯 Testing: ${scenario.name}`);
      console.log(`📝 User Input: "${scenario.userInput}"`);
      console.log(`📍 Current File: ${scenario.context.currentFile || 'None'}`);
      
      // Test BM25
      console.log('\n🔍 BM25 Results:');
      const bm25Start = Date.now();
      const bm25Results = await bm25Embedder.findSimilarFiles(
        scenario.userInput, 
        scenario.context, 
        8
      );
      const bm25Time = Date.now() - bm25Start;
      
      // Test OpenAI (if available)
      if (openaiAvailable) {
        console.log('\n🤖 OpenAI Results:');
        const openaiStart = Date.now();
        const openaiResults = await openaiEmbedder.findSimilarFiles(scenario.userInput, 8);
        const openaiTime = Date.now() - openaiStart;
        
        // Compare results
        console.log('\n📊 Comparison:');
        console.log(`⚡ BM25: ${bm25Time}ms | 🤖 OpenAI: ${openaiTime}ms`);
        console.log(`🎯 BM25 found: ${bm25Results.length} files`);
        console.log(`🎯 OpenAI found: ${openaiResults.length} files`);
        
        // Check overlap
        const bm25Paths = new Set(bm25Results.map(f => f.name));
        const openaiPaths = new Set(openaiResults);
        const overlap = [...bm25Paths].filter(path => openaiPaths.has(path));
        console.log(`🔄 Overlap: ${overlap.length}/${Math.min(bm25Results.length, openaiResults.length)} files`);
        
        if (overlap.length > 0) {
          console.log(`✅ Common files: ${overlap.slice(0, 3).join(', ')}${overlap.length > 3 ? '...' : ''}`);
        }
      } else {
        console.log(`\n⚡ BM25 completed in ${bm25Time}ms`);
        console.log(`🎯 Found ${bm25Results.length} relevant files`);
      }
      
      console.log('\n' + '─'.repeat(80));
    }

    // Final statistics
    console.log('\n📈 Final Statistics:');
    console.log(`📁 Total files indexed: ${files.length}`);
    console.log(`⚡ BM25 indexing: ${bm25IndexTime}ms`);
    if (openaiAvailable) {
      console.log(`🤖 OpenAI indexing: ${openaiIndexTime}ms (${Math.round(openaiIndexTime/bm25IndexTime)}x slower)`);
    }
    
    const bm25Stats = bm25Embedder.getStats();
    console.log(`🧠 BM25 vocabulary: ${bm25Stats.vocabularySize} terms`);
    console.log(`📏 Average document length: ${bm25Stats.avgDocLength} tokens`);
    
    if (openaiAvailable) {
      const openaiStats = openaiEmbedder.getStats();
      console.log(`🤖 OpenAI embeddings: ${openaiStats.totalFiles} files`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testContextAwareEmbedder();
