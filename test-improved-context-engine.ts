// Test script to demonstrate the improved two-stage context engine
import { TwoStageLLMContextEngine } from './src/lib/services/two-stage-context-engine';
import { FileItem } from './src/types/file';

// Mock file data for testing
const mockFiles: FileItem[] = [
  {
    name: 'stores/sessionStore.ts',
    content: `
export class SessionStore {
  async addMessage(content: string, sender: 'user' | 'ai', feedback?: any) {
    const messageId = generateId();
    const newMessage = {
      id: messageId,
      content,
      sender,
      feedback
    };
    
    // Add to local state
    this.messages.push(newMessage);
    
    // Save to database
    await MessageService.createMessage(newMessage);
    
    return messageId;
  }
  
  async generateAIResponse(userMessage: string, userMessageId?: string) {
    // Call AI API
    const response = await aiService.generate(userMessage);
    
    // Apply feedback to user message
    if (response.feedback && userMessageId) {
      await this.updateMessageWithFeedback(userMessageId, response.feedback);
    }
    
    // Add AI response
    await this.addMessage(response.content, 'ai');
  }
}
    `,
    path: 'stores/sessionStore.ts'
  },
  {
    name: 'screens/ConversationScreen.tsx',
    content: `
export const ConversationScreen = () => {
  const { sessionStore } = useStores();
  
  const handleSendMessage = async (message: string) => {
    // Add user message
    const messageId = await sessionStore.addMessage(message, 'user');
    
    // Generate AI response
    await sessionStore.generateAIResponse(message, messageId);
  };
  
  return (
    <View>
      <MessageInput onSend={handleSendMessage} />
    </View>
  );
};
    `,
    path: 'screens/ConversationScreen.tsx'
  }
];

async function testImprovedEngine() {
  console.log('🚀 Testing Improved Two-Stage Context Engine\n');
  
  const engine = new TwoStageLLMContextEngine(mockFiles);
  
  // Test the same query that had issues before
  const query = "Show me the handleSendMessage function in ConversationScreen and how it calls addMessage and generateAIResponse";
  const reason = "Need to trace the message flow to understand why messages might be duplicated";
  
  console.log('📝 Query:', query);
  console.log('📝 Reason:', reason);
  console.log('\n' + '='.repeat(80) + '\n');
  
  try {
    const result = await engine.getRelevantSnippets(query, reason);
    
    console.log('✅ RESULTS:');
    console.log(`📊 Found ${result.snippets.length} snippets`);
    console.log(`📋 ${result.additionalFiles.length} additional files suggested\n`);
    
    // Display snippets
    result.snippets.forEach((snippet, index) => {
      console.log(`📄 Snippet ${index + 1}: ${snippet.filePath}`);
      console.log(`🎯 Type: ${snippet.type}, Score: ${snippet.score?.toFixed(2)}`);
      console.log(`📝 Context: ${snippet.context}`);
      console.log(`📍 Lines ${snippet.startLine}-${snippet.endLine}:`);
      console.log('```typescript');
      console.log(snippet.content);
      console.log('```\n');
    });
    
    // Display additional files
    if (result.additionalFiles.length > 0) {
      console.log('🔍 ADDITIONAL FILES OF INTEREST:');
      result.additionalFiles.forEach((file, index) => {
        console.log(`${index + 1}. ${file.fileName}`);
        console.log(`   Reason: ${file.reason}`);
        console.log(`   Suggested Query: "${file.suggestedQuery}"\n`);
      });
    }
    
    console.log('🎉 Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Demonstrate the improvements
console.log(`
🔧 IMPROVEMENTS IMPLEMENTED:

1. ✅ PRIMARY LLM QUERY GUIDANCE
   - Added specific query best practices to streamlined agent prompt
   - Examples of good vs bad queries
   - Guidance on using excludedFiles parameter

2. ✅ IMPROVED SECOND STAGE PROMPT
   - Fixed contradictions between "least comprehensive" vs "complete snippets"
   - Added query-specific completeness rules
   - Prioritizes showing actual function calls when asked

3. ✅ THREE-STAGE FILE DISCOVERY
   - Stage 1: Finds up to 15 files, splits into primary (8) and secondary (7)
   - Stage 2: Processes primary files, suggests secondary files
   - Stage 3: Smart truncation by removing low-relevance snippets, not content

4. ✅ SMART TRUNCATION
   - Hard 1000-line budget enforcement (never exceed)
   - Preserves high-relevance snippets completely
   - Suggests truncated snippets as follow-up queries
   - Maintains code integrity - never partial functions

5. ✅ ENHANCED OUTPUT FORMAT
   - Returns both snippets and additional file suggestions
   - Provides suggested queries for deeper exploration
   - Gives primary LLM a roadmap for follow-up queries

Expected Token Reduction: 75% while maintaining or improving relevance
`);

// Run the test if this file is executed directly
if (require.main === module) {
  testImprovedEngine();
}
