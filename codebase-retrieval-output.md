# Codebase Retrieval Output

## Query
Show me the current project structure, focusing on constants, contexts, types, and any existing color/theme systems. Also show me how colors are currently being used in components.

## Analysis Summary
- **Number of files found**: 9 files
- **Files identified**:
  1. `src/constants/index.ts`
  2. `src/constants/themes.ts`
  3. `src/constants/colors.ts`
  4. `src/types/theme.ts`
  5. `src/contexts/ThemeContext.tsx`
  6. `src/hooks/useThemeStyles.ts`
  7. `src/components/ui/ThemeSelector.tsx`
  8. `src/components/examples/ThemeShowcase.tsx`
  9. `src/utils/themeUtils.ts`
- **Approximate total lines of code**: 183+ lines (based on visible line numbers)

## Exact Output from Codebase Retrieval

The following code sections were retrieved:
Path: src/constants/index.ts
     1	// Storage keys with project ID for uniqueness
     2	export const STORAGE_KEYS = {
     3	  THEME: 'app_theme_mode_9afaf736',
     4	  USER_PREFERENCES: 'user_preferences_9afaf736',
     5	  JOURNEY_DATA: 'journey_data_9afaf736',
     6	  JOURNEY_STORE: 'journey_store_9afaf736',
     7	  WAYPOINT_STORE: 'waypoint_store_9afaf736',
     8	  AUTH_TOKEN: 'auth_token_9afaf736',
     9	  AUTH_STORE: 'auth_store_9afaf736',
    10	  APP_STORE: 'app_store_9afaf736',
    11	  FIRST_LAUNCH: 'first_launch_9afaf736',
    12	  LAST_ACTIVE_JOURNEY: 'last_active_journey_9afaf736',
    13	};
    14	
    15	// Re-export theme system
    16	export { lightTheme, darkTheme } from './themes';
    17	export { COLORS, JOURNEY_COLORS } from './colors';
...
    30	  { id: 'steelBlue', name: 'Steel Blue', value: '#4682B4', textColor: '#FFFFFF' },
    31	] as const;
    32	
    33	// App constants
    34	export const APP_CONFIG = {
    35	  PROJECT_ID: '9afaf736-bdc4-4ad5-bbc8-0dc37ac008ed',
    36	  VERSION: '1.0.0',
    37	  BUILD: '1',
    38	};
    39	
    40	// Theme-related exports
    41	export type { Theme, ThemeMode, ThemeContextType } from '../types/theme';
    42	export { useTheme, useThemeMode, useThemeColors, useIsDarkMode, useIsLightMode } from '../contexts/ThemeContext';
    43	export { themeUtils } from '../utils/themeUtils';
    44	export { useThemeStyles } from '../hooks/useThemeStyles';
...
Path: src/constants/themes.ts
...
     6	  
     7	  // Base colors for light theme
     8	  colors: {
     9	    background: {
    10	      primary: '#FFFFFF',      // Main app background
    11	      secondary: '#F8FAFC',    // Secondary surfaces
    12	      tertiary: '#F1F5F9',     // Subtle backgrounds
    13	      elevated: '#FFFFFF',     // Cards, modals
    14	    },
    15	    text: {
    16	      primary: '#0F172A',      // Main text
    17	      secondary: '#475569',    // Secondary text
    18	      muted: '#94A3B8',        // Subtle text, placeholders
    19	      inverse: '#FFFFFF',      // Text on dark backgrounds
    20	    },
    21	    border: {
    22	      primary: '#E2E8F0',      // Main borders
    23	      secondary: '#CBD5E1',    // Subtle borders
    24	      focus: '#e54b50',        // Focus rings (refined coral)
    25	    },
    26	    surface: {
    27	      card: '#FFFFFF',         // Card backgrounds
    28	      overlay: 'rgba(15, 23, 42, 0.8)', // Modal overlays
    29	      input: '#F8FAFC',        // Input backgrounds
    30	    },
    31	  },
    32	  
    33	  // Status colors (same for both themes)
    34	  status: {
    35	    error: '#EF4444',        // Error states
    36	    warning: '#F59E0B',      // Warning states
    37	    success: '#10B981',      // Success states
    38	    info: '#3B82F6',         // Info states
    39	  },
    40	  
    41	  // Primary app color (refined coral)
    42	  primary: {
    43	    default: '#e54b50',      // Improved contrast (4.7:1 ratio)
    44	    light: '#fc6267',        // Original coral as lighter variant
    45	    dark: '#cc3a3f',         // Darker for enhanced contrast
    46	    muted: '#fde2e4',        // Subtle backgrounds
    47	    contrast: '#FFFFFF',     // Always white text on coral
    48	  },
...
   162	  
   163	  // Base colors for dark theme
   164	  colors: {
   165	    background: {
   166	      primary: '#0F172A',      // Main app background
   167	      secondary: '#1E293B',    // Secondary surfaces
   168	      tertiary: '#334155',     // Subtle backgrounds
   169	      elevated: '#1E293B',     // Cards, modals
   170	    },
   171	    text: {
   172	      primary: '#F8FAFC',      // Main text
   173	      secondary: '#CBD5E1',    // Secondary text
   174	      muted: '#64748B',        // Subtle text, placeholders
   175	      inverse: '#0F172A',      // Text on light backgrounds
   176	    },
   177	    border: {
   178	      primary: '#334155',      // Main borders
   179	      secondary: '#475569',    // Subtle borders
   180	      focus: '#fd8a8e',        // Focus rings (lighter coral for dark)
   181	    },
   182	    surface: {
   183	      card: '#1E293B',         // Card backgrounds
   184	      overlay: 'rgba(0, 0, 0, 0.8)', // Modal overlays
   185	      input: '#334155',        // Input backgrounds
   186	    },
   187	  },
   188	  
   189	  // Status colors (same as light theme)
   190	  status: {
   191	    error: '#EF4444',
   192	    warning: '#F59E0B',
   193	    success: '#10B981',
   194	    info: '#3B82F6',
   195	  },
   196	  
   197	  // Primary app color adjusted for dark theme
   198	  primary: {
   199	    default: '#fd8a8e',      // Slightly lighter for dark backgrounds
   200	    light: '#fc6267',        // Original coral
   201	    dark: '#e5424a',         // Darker variant
   202	    muted: '#4a1a1c',        // Darker muted for dark theme
   203	    contrast: '#FFFFFF',     // White text
   204	  },
...
Path: src/constants/colors.ts
     1
     2	export const COLORS = {
     3	  // Primary palette
     4	  primary: '#2196F3',
     5	  success: '#10B981',
     6	  warning: '#F59E0B',
     7	  error: '#EF4444',
     8
     9	  // Journey colors
    10	  blue: '#2196F3',
    11	  green: '#10B981',
    12	  purple: '#8B5CF6',
    13	  orange: '#F97316',
    14	  red: '#EF4444',
    15	  teal: '#14B8A6',
    16	  pink: '#EC4899',
    17	  indigo: '#6366F1',
    18	  amber: '#F59E0B',
    19	  gray: '#6B7280',
    20
    21	  // Gray scale
    22	  gray50: '#F9FAFB',
    23	  gray100: '#F3F4F6',
    24	  gray200: '#E5E7EB',
    25	  gray400: '#9CA3AF',
    26	  gray500: '#6B7280',
    27	  gray700: '#374151',
    28	  gray900: '#1F2937',
    29
    30	  // Background
    31	  background: '#FFFFFF',
    32	  surface: '#F9FAFB'
    33	};
    34
    35	export const JOURNEY_COLORS = [
    36	  COLORS.blue,
    37	  COLORS.green,
    38	  COLORS.purple,
    39	  COLORS.orange,
    40	  COLORS.red,
    41	  COLORS.teal,
    42	  COLORS.pink,
    43	  COLORS.indigo,
    44	  COLORS.amber,
    45	  COLORS.gray
    46	];
...
Path: src/types/theme.ts
     1	// Theme System Types
     2	export type ThemeMode = 'light' | 'dark' | 'system';
     3
     4	// Base color structure for both light and dark themes
     5	export interface BaseColors {
     6	  background: {
     7	    primary: string;
     8	    secondary: string;
     9	    tertiary: string;
    10	    elevated: string;
    11	  };
    12	  text: {
     13	    primary: string;
    14	    secondary: string;
    15	    muted: string;
    16	    inverse: string;
    17	  };
    18	  border: {
    19	    primary: string;
    20	    secondary: string;
    21	    focus: string;
    22	  };
    23	  surface: {
    24	    card: string;
    25	    overlay: string;
    26	    input: string;
    27	  };
    28	}
    29
    30	// Status colors (same for both themes)
    31	export interface StatusColors {
    32	  error: string;
    33	  warning: string;
    34	  success: string;
    35	  info: string;
    36	}
    37
    38	// Primary app color variants
    39	export interface PrimaryColors {
    40	  default: string;
    41	  light: string;
    42	  dark: string;
    43	  muted: string;
    44	  contrast: string;
    45	}
...
   132
   133	// Theme context type
   134	export interface ThemeContextType {
   135	  theme: Theme;
   136	  themeMode: ThemeMode;
   137	  isLightMode: boolean;
   138	  isDarkMode: boolean;
   139	  toggleTheme: () => void;
   140	  setThemeMode: (mode: ThemeMode) => void;
   141
   142	  // Utility functions
   143	  getFocusStyle: (variant?: keyof FocusAlternatives) => object;
   144	  getFocusVisibleStyle: (variant?: keyof FocusAlternatives) => object;
   145	  getAccessibleTextColor: (backgroundColor: string) => string;
   146	  getJourneyColorVariant: (colorId: keyof JourneyColors, variant: keyof JourneyColorVariant) => string;
   147	  withOpacity: (color: string, opacityLevel: keyof OpacityLevels) => string;
   148	}
...
Path: src/contexts/ThemeContext.tsx
...
    79
    80	  const getAccessibleTextColor = (backgroundColor: string) => {
    81	    return themeUtils.getAccessibleTextColor(backgroundColor);
    82	  };
    83
    84	  const getJourneyColorVariant = (
    85	    colorId: keyof typeof theme.journey,
    86	    variant: keyof typeof theme.journey.coral
    87	  ) => {
    88	    return theme.journey[colorId][variant];
    89	  };
    90
    91	  const withOpacity = (color: string, opacityLevel: keyof typeof theme.opacity) => {
    92	    return themeUtils.withOpacity(color, theme.opacity[opacityLevel]);
    93	  };
    94
    95	  // Context value
    96	  const contextValue: ThemeContextType = {
    97	    theme,
    98	    themeMode,
    99	    isLightMode,
   100	    isDarkMode,
   101	    toggleTheme,
   102	    setThemeMode,
   103	    getFocusStyle,
   104	    getFocusVisibleStyle,
   105	    getAccessibleTextColor,
   106	    getJourneyColorVariant,
   107	    withOpacity,
   108	  };
   109
   110	  // Don't render until theme is loaded
   111	  if (isLoading) {
   112	    return null;
   113	  }
...
Path: src/hooks/useThemeStyles.ts
...
   196
   197	  // Utility functions
   198	  const utils = useMemo(() => ({
   199	    // Get journey color with variant
   200	    getJourneyColor: (colorId: keyof typeof theme.journey, variant: keyof typeof theme.journey.coral = 'default') => {
   201	      return theme.journey[colorId][variant];
   202	    },
   203
   204	    // Add opacity to any color
   205	    withOpacity: (color: string, opacity: keyof typeof theme.opacity) => {
   206	      return themeUtils.withOpacity(color, theme.opacity[opacity]);
   207	    },
   208
   209	    // Get accessible text color for background
   210	    getTextColor: (backgroundColor: string) => {
   211	      return themeUtils.getAccessibleTextColor(backgroundColor);
   212	    },
   213
   214	    // Get focus styles
   215	    getFocusStyle: (variant?: keyof typeof theme.focus.alternatives) => {
   216	      return themeUtils.getFocusStyle(theme, variant);
   217	    },
...
Path: src/components/ui/ThemeSelector.tsx
     1	import React from 'react';
     2	import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
     3	import { useTheme, useThemeMode } from '../../contexts/ThemeContext';
     4	import { ThemeMode } from '../../types/theme';
     5	import { Sun, Moon, Smartphone } from 'lucide-react-native';
     6
     7	interface ThemeSelectorProps {
     8	  variant?: 'horizontal' | 'vertical';
     9	  showLabels?: boolean;
    10	  size?: 'small' | 'medium' | 'large';
    11	}
    12
    13	export function ThemeSelector({
    14	  variant = 'horizontal',
    15	  showLabels = true,
    16	  size = 'medium'
    17	}: ThemeSelectorProps) {
    18	  const { theme } = useTheme();
    19	  const { themeMode, setThemeMode } = useThemeMode();
    20
    21	  const iconSize = size === 'small' ? 16 : size === 'medium' ? 20 : 24;
    22	  const buttonSize = size === 'small' ? 36 : size === 'medium' ? 44 : 52;
    23
    24	  const themeOptions: { mode: ThemeMode; icon: any; label: string }[] = [
    25	    { mode: 'light', icon: Sun, label: 'Light' },
    26	    { mode: 'dark', icon: Moon, label: 'Dark' },
    27	    { mode: 'system', icon: Smartphone, label: 'System' },
    28	  ];
...
    73
    74	  return (
    75	    <View style={styles.container}>
    76	      {themeOptions.map(({ mode, icon: Icon, label }) => {
    77	        const isActive = themeMode === mode;
    78
    79	        return (
    80	          <View key={mode} style={styles.optionContainer}>
    81	            <TouchableOpacity
    82	              style={[styles.button, isActive && styles.activeButton]}
    83	              onPress={() => handleThemeChange(mode)}
    84	              accessibilityRole="button"
    85	              accessibilityLabel={`Switch to ${label} theme`}
    86	              accessibilityState={{ selected: isActive }}
    87	            >
    88	              <Icon
    89	                size={iconSize}
    90	                color={isActive ? theme.primary.contrast : theme.colors.text.primary}
    91	              />
    92	            </TouchableOpacity>
    93	            {showLabels && (
    94	              <Text style={[styles.label, isActive && styles.activeLabel]}>
    95	                {label}
    96	              </Text>
    97	            )}
    98	          </View>
    99	        );
   100	      })}
   101	    </View>
   102	  );
   103	}
   104
   105	export default ThemeSelector;
...
Path: src/components/examples/ThemeShowcase.tsx
...
     7
     8	export function ThemeShowcase() {
     9	  const { theme, getJourneyColorVariant, withOpacity, getFocusStyle } = useTheme();
    10	  const [inputValue, setInputValue] = useState('');
    11	  const [selectedJourneyColor, setSelectedJourneyColor] = useState<keyof JourneyColors>('coral');
    12
    13	  const journeyColorOptions: { id: keyof JourneyColors; name: string }[] = [
    14	    { id: 'coral', name: 'Coral' },
    15	    { id: 'teal', name: 'Teal' },
    16	    { id: 'purple', name: 'Purple' },
    17	    { id: 'amber', name: 'Amber' },
    18	    { id: 'oceanBlue', name: 'Ocean Blue' },
    19	    { id: 'magenta', name: 'Magenta' },
    20	    { id: 'tangerine', name: 'Tangerine' },
    21	    { id: 'evergreen', name: 'Evergreen' },
    22	    { id: 'terraCotta', name: 'Terra Cotta' },
    23	    { id: 'steelBlue', name: 'Steel Blue' },
    24	  ];
...
    61	    colorSwatch: {
    62	      width: 60,
    63	      height: 60,
    64	      borderRadius: 8,
    65	      alignItems: 'center',
    66	      justifyContent: 'center',
    67	      borderWidth: 2,
    68	      borderColor: 'transparent',
    69	    },
    70	    selectedColorSwatch: {
    71	      borderColor: theme.colors.border.focus,
    72	    },
    73	    colorLabel: {
    74	      fontSize: 10,
    75	      fontWeight: '500',
    76	      textAlign: 'center',
    77	      marginTop: 2,
    78	    },
    79	    buttonRow: {
    80	      flexDirection: 'row',
    81	      gap: 12,
    82	      flexWrap: 'wrap',
    83	    },
    84	    primaryButton: {
    85	      backgroundColor: theme.primary.default,
    86	      paddingHorizontal: 20,
    87	      paddingVertical: 12,
    88	      borderRadius: 8,
    89	      alignItems: 'center',
    90	      justifyContent: 'center',
    91	      flexDirection: 'row',
    92	      gap: 8,
    93	      flex: 1,
    94	      minWidth: 120,
    95	    },
...
   238
   239	        {/* Journey Colors */}
   240	        <View style={styles.section}>
   241	          <Text style={styles.sectionTitle}>Journey Colors</Text>
   242	          <View style={styles.card}>
   243	            <View style={styles.colorGrid}>
   244	              {journeyColorOptions.map(({ id, name }) => {
   245	                const color = getJourneyColorVariant(id, 'default');
   246	                const isSelected = selectedJourneyColor === id;
   247
   248	                return (
   249	                  <TouchableOpacity
   250	                    key={id}
   251	                    onPress={() => setSelectedJourneyColor(id)}
   252	                    accessibilityRole="button"
   253	                    accessibilityLabel={`Select ${name} color`}
   254	                  >
   255	                    <View
   256	                      style={[
   257	                        styles.colorSwatch,
   258	                        { backgroundColor: color },
   259	                        isSelected && styles.selectedColorSwatch,
   260	                      ]}
   261	                    >
   262	                      <Text
   263	                        style={[
   264	                          styles.colorLabel,
   265	                          { color: getJourneyColorVariant(id, 'contrast') },
   266	                        ]}
   267	                      >
   268	                        {name}
   269	                      </Text>
   270	                    </View>
   271	                  </TouchableOpacity>
   272	                );
   273	              })}
   274	            </View>
   275	          </View>
   276	        </View>
...
Path: src/utils/themeUtils.ts
...
     2
     3	/**
     4	 * Theme utility functions for color manipulation and accessibility
     5	 */
     6	export const themeUtils = {
     7	  /**
     8	   * Convert hex color to RGB values
     9	   */
    10	  hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    11	    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    12	    return result ? {
    13	      r: parseInt(result[1], 16),
    14	      g: parseInt(result[2], 16),
    15	      b: parseInt(result[3], 16)
    16	    } : null;
    17	  },
    18
    19	  /**
    20	   * Calculate luminance of a color (for contrast calculations)
    21	   */
    22	  getLuminance(hex: string): number {
    23	    const rgb = this.hexToRgb(hex);
    24	    if (!rgb) return 0;
    25
    26	    const { r, g, b } = rgb;
    27	    const [rs, gs, bs] = [r, g, b].map(c => {
    28	      c = c / 255;
    29	      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    30	    });
    31
    32	    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
    33	  },
...
   126
   127	  /**
   128	   * Generate focus-visible style (keyboard only)
   129	   */
   130	  getFocusVisibleStyle(theme: Theme, variant?: keyof typeof theme.focus.alternatives): object {
   131	    return {
   132	      // This would be handled by the component's focus state
   133	      // React Native doesn't have :focus-visible, so components need to track keyboard vs touch
   134	      ...this.getFocusStyle(theme, variant),
   135	    };
   136	  },
   137
   138	  /**
   139	   * Generate button styles with theme
   140	   */
   141	  getButtonStyles(theme: Theme, variant: 'primary' | 'secondary' = 'primary') {
   142	    if (variant === 'primary') {
   143	      return {
   144	        backgroundColor: theme.primary.default,
   145	        color: theme.primary.contrast,
   146	        borderColor: 'transparent',
   147	      };
   148	    }
   149
   150	    return {
   151	      backgroundColor: 'transparent',
   152	      color: theme.colors.text.primary,
   153	      borderColor: theme.colors.border.primary,
   154	      borderWidth: 1,
   155	    };
   156	  },
   157
   158	  /**
   159	   * Generate input styles with theme
   160	   */
   161	  getInputStyles(theme: Theme) {
   162	    return {
   163	      backgroundColor: theme.colors.surface.input,
   164	      borderColor: theme.colors.border.primary,
   165	      color: theme.colors.text.primary,
   166	      placeholderTextColor: theme.colors.text.muted,
   167	    };
   168	  },
   169
   170	  /**
   171	   * Generate card styles with theme
   172	   */
   173	  getCardStyles(theme: Theme) {
   174	    return {
   175	      backgroundColor: theme.colors.surface.card,
   176	      borderColor: theme.colors.border.secondary,
   177	      shadowColor: theme.mode === 'dark' ? '#000000' : '#000000',
   178	      shadowOffset: { width: 0, height: 2 },
   179	      shadowOpacity: theme.mode === 'dark' ? 0.3 : 0.1,
   180	      shadowRadius: 4,
   181	      elevation: 2, // Android
   182	    };
   183	  },
...
