import { FileItem } from '@/types/file';
import path from 'path';

interface FileScore {
  filePath: string;
  score: number;
  reasons: string[];
}

interface FileKeywords {
  filePath: string;
  keywords: string[];
  fileType: string;
  importance: number;
}

export class SimpleFileSelector {
  private fileKeywords: Map<string, FileKeywords> = new Map();

  constructor(private files: FileItem[]) {
    this.buildIndex();
  }

  private buildIndex() {
    console.log('Building simple file index...');
    
    for (const file of this.files) {
      const keywords = this.extractKeywords(file);
      const fileType = this.determineFileType(file.name);
      const importance = this.calculateImportance(file);
      
      this.fileKeywords.set(file.name, {
        filePath: file.name,
        keywords,
        fileType,
        importance
      });
    }
    
    console.log(`Indexed ${this.fileKeywords.size} files`);
  }

  private extractKeywords(file: FileItem): string[] {
    const content = file.content || '';
    const fileName = file.name;
    const keywords = new Set<string>();

    // Extract from file name
    const nameWords = fileName
      .split(/[\/\-_\.]/)
      .filter(word => word.length > 2)
      .map(word => word.toLowerCase());
    nameWords.forEach(word => keywords.add(word));

    // Extract function names
    const functionMatches = content.match(/(?:function|const|export)\s+(\w+)/g) || [];
    functionMatches.forEach(match => {
      const name = match.split(/\s+/).pop()?.toLowerCase();
      if (name && name.length > 2) {
        keywords.add(name);
        // Split camelCase
        const camelWords = name.split(/(?=[A-Z])/).map(w => w.toLowerCase());
        camelWords.forEach(word => word.length > 2 && keywords.add(word));
      }
    });

    // Extract variable names
    const varMatches = content.match(/(?:const|let|var)\s+(\w+)/g) || [];
    varMatches.forEach(match => {
      const name = match.split(/\s+/).pop()?.toLowerCase();
      if (name && name.length > 2) {
        keywords.add(name);
      }
    });

    // Extract from comments
    const commentMatches = content.match(/\/\/\s*(.+)|\/\*\s*([\s\S]*?)\s*\*\//g) || [];
    commentMatches.forEach(comment => {
      const text = comment.replace(/\/\/|\/\*|\*\//g, '').toLowerCase();
      const words = text.split(/\s+/).filter(word => word.length > 3);
      words.forEach(word => keywords.add(word));
    });

    // Extract business terms
    const businessTerms = [
      'payment', 'subscription', 'credit', 'billing', 'checkout', 'webhook',
      'user', 'auth', 'login', 'register', 'password', 'reset',
      'database', 'schema', 'query', 'migration',
      'api', 'route', 'endpoint', 'service', 'handler',
      'component', 'screen', 'page', 'navigation',
      'store', 'state', 'context', 'provider'
    ];
    
    businessTerms.forEach(term => {
      if (content.toLowerCase().includes(term)) {
        keywords.add(term);
      }
    });

    return Array.from(keywords);
  }

  private determineFileType(fileName: string): string {
    const name = fileName.toLowerCase();
    
    if (name.includes('/api/')) return 'api_route';
    if (name.includes('webhook')) return 'webhook';
    if (name.includes('schema')) return 'schema';
    if (name.includes('service')) return 'service';
    if (name.includes('component')) return 'component';
    if (name.includes('screen') || name.includes('page')) return 'screen';
    if (name.includes('store') || name.includes('context')) return 'state';
    if (name.includes('config')) return 'config';
    if (name.includes('util') || name.includes('helper')) return 'util';
    if (name.includes('type') || name.endsWith('.d.ts')) return 'type';
    if (name.includes('hook')) return 'hook';
    
    return 'unknown';
  }

  private calculateImportance(file: FileItem): number {
    const content = file.content || '';
    let importance = 0;
    
    // File size (more content = more important)
    importance += Math.min(content.length / 1000, 10);
    
    // Number of exports
    const exportCount = (content.match(/export/g) || []).length;
    importance += exportCount * 2;
    
    // Number of imports
    const importCount = (content.match(/import/g) || []).length;
    importance += importCount;
    
    return importance;
  }

  selectFiles(query: string, maxFiles: number = 8): string[] {
    const queryTerms = this.parseQuery(query);
    const scores: FileScore[] = [];

    for (const [filePath, fileData] of this.fileKeywords) {
      const score = this.scoreFile(queryTerms, fileData);
      if (score.score > 0) {
        scores.push({
          filePath,
          score: score.score,
          reasons: score.reasons
        });
      }
    }

    // Sort by score and return top files
    scores.sort((a, b) => b.score - a.score);
    
    console.log('Top scored files:');
    scores.slice(0, maxFiles).forEach((item, i) => {
      console.log(`${i + 1}. ${item.filePath} (${item.score.toFixed(1)}) - ${item.reasons.join(', ')}`);
    });

    return scores.slice(0, maxFiles).map(s => s.filePath);
  }

  private parseQuery(query: string): string[] {
    const terms = query
      .toLowerCase()
      .split(/\s+/)
      .filter(term => term.length > 2)
      .map(term => term.replace(/[^\w]/g, ''));

    // Add domain-specific synonyms
    const expandedTerms = [...terms];

    // Payment domain
    if (terms.some(t => ['payment', 'billing', 'subscription', 'credit'].includes(t))) {
      expandedTerms.push('checkout', 'lemonsqueezy', 'webhook', 'billing', 'payment');
    }

    // Auth domain
    if (terms.some(t => ['auth', 'login', 'register', 'password', 'authentication'].includes(t))) {
      expandedTerms.push('auth', 'login', 'register', 'password', 'session', 'user');
    }

    return [...new Set(expandedTerms)]; // Remove duplicates
  }

  private scoreFile(queryTerms: string[], fileData: FileKeywords): { score: number; reasons: string[] } {
    let score = 0;
    const reasons: string[] = [];

    // MASSIVE boost for exact file name matches
    const fileName = path.basename(fileData.filePath, path.extname(fileData.filePath)).toLowerCase();
    const pathParts = fileData.filePath.toLowerCase().split('/');

    for (const term of queryTerms) {
      if (fileName.includes(term)) {
        score += 100;
        reasons.push(`filename match: ${term}`);
      }
      if (pathParts.some(part => part.includes(term))) {
        score += 50;
        reasons.push(`path match: ${term}`);
      }
    }

    // Domain-specific path boosts
    const filePath = fileData.filePath.toLowerCase();
    if (queryTerms.some(term => ['payment', 'subscription', 'credit', 'billing', 'checkout'].includes(term))) {
      if (filePath.includes('/api/checkout/') || filePath.includes('/api/subscription/') ||
          filePath.includes('/api/webhooks/') || filePath.includes('lemonsqueezy') ||
          filePath.includes('payment') || filePath.includes('billing')) {
        score += 200;
        reasons.push('payment domain path');
      }
    }

    if (queryTerms.some(term => ['auth', 'login', 'register', 'password'].includes(term))) {
      if (filePath.includes('/(auth)/') || filePath.includes('/auth/') ||
          filePath.includes('auth.') || filePath.includes('login') ||
          filePath.includes('register')) {
        score += 200;
        reasons.push('auth domain path');
      }
    }

    // Keyword overlap (reduced weight)
    const matchingKeywords = fileData.keywords.filter(keyword =>
      queryTerms.some(term => keyword.includes(term) || term.includes(keyword))
    );

    if (matchingKeywords.length > 0) {
      score += matchingKeywords.length * 5; // Reduced from 10
      reasons.push(`${matchingKeywords.length} keyword matches`);
    }

    // File type boost
    const fileTypeBoosts: Record<string, number> = {
      'api_route': 30,
      'webhook': 25,
      'service': 15,
      'schema': 10,
      'state': 8,
      'hook': 8,
      'component': 3,
      'config': 2
    };

    const boost = fileTypeBoosts[fileData.fileType] || 0;
    if (boost > 0) {
      score += boost;
      reasons.push(`${fileData.fileType} boost`);
    }

    // Importance weight (reduced)
    score += fileData.importance * 0.2;

    return { score, reasons };
  }
}
