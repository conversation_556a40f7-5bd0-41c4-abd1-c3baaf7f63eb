import { FileItem } from '@/types/file';

interface ProcessedDocument {
  original: FileItem;
  tokens: string[];
  termFreq: Map<string, number>;
  length: number;
}

interface ChatContext {
  recentMessages: Array<{ content: string; role: string }>;
  fileTree: FileItem[];
  logs?: Array<{ message: string; type: string }>;
  projectType?: string;
  currentFile?: string;
}

interface ScoredFile {
  file: FileItem;
  score: number;
  matchedTerms: string[];
}

/**
 * Context-Aware BM25 Embedder
 * 
 * Fast, local, zero-cost semantic search using BM25 algorithm
 * with intelligent context-aware query building.
 * 
 * Perfect for code search where keywords and structure matter.
 */
export class ContextAwareBM25Embedder {
  private documents: ProcessedDocument[] = [];
  private vocabulary: Set<string> = new Set();
  private avgDocLength: number = 0;
  private documentFreq: Map<string, number> = new Map();
  
  // BM25 parameters (tuned for code search)
  private readonly k1 = 1.2;  // Term frequency saturation
  private readonly b = 0.75;  // Length normalization

  constructor() {
    console.log('🔧 ContextAwareBM25Embedder initialized - Fast local search');
  }

  /**
   * Index files for BM25 search (instant for runtime use)
   */
  async indexFiles(files: FileItem[]): Promise<void> {
    const startTime = Date.now();
    
    // Process all documents
    this.documents = files.map(file => this.processDocument(file));
    
    // Calculate average document length
    this.avgDocLength = this.documents.reduce((sum, doc) => sum + doc.length, 0) / this.documents.length;
    
    // Build vocabulary and document frequency
    this.buildVocabulary();
    
    const duration = Date.now() - startTime;
    console.log(`✅ Indexed ${files.length} files in ${duration}ms`);
  }

  /**
   * Context-aware file search using BM25
   */
  async findSimilarFiles(
    userInput: string, 
    context: ChatContext, 
    maxFiles: number = 8
  ): Promise<FileItem[]> {
    const startTime = Date.now();
    
    // Build context-aware query
    const smartQuery = this.buildContextAwareQuery(userInput, context);
    console.log(`🧠 Smart query: "${smartQuery}"`);
    
    // Perform BM25 search
    const results = this.searchWithBM25(smartQuery, maxFiles);
    
    const duration = Date.now() - startTime;
    console.log(`🎯 Found ${results.length} files in ${duration}ms`);
    
    // Log top matches
    results.slice(0, Math.min(5, maxFiles)).forEach((result, i) => {
      console.log(`  ${i + 1}. ${result.file.name} (${result.score.toFixed(3)}) [${result.matchedTerms.join(', ')}]`);
    });
    
    return results.map(r => r.file);
  }

  /**
   * Build context-aware query from user input and chat context
   */
  private buildContextAwareQuery(userInput: string, context: ChatContext): string {
    const queryParts: string[] = [];
    
    // 1. Extract keywords from user input
    const userKeywords = this.extractKeywords(userInput);
    if (userKeywords) queryParts.push(userKeywords);
    
    // 2. Add recent conversation context
    const recentContext = this.getRecentContext(context.recentMessages);
    if (recentContext) queryParts.push(recentContext);
    
    // 3. Add project structure context
    const projectContext = this.getProjectContext(context.fileTree);
    if (projectContext) queryParts.push(projectContext);
    
    // 4. Add error/log context
    const errorContext = this.getErrorContext(context.logs);
    if (errorContext) queryParts.push(errorContext);
    
    // 5. Add current file context
    if (context.currentFile) {
      queryParts.push(this.getFileContext(context.currentFile));
    }
    
    return queryParts.join(' ').trim();
  }

  /**
   * Extract technical keywords from user input
   */
  private extractKeywords(input: string): string {
    // Technical terms that are important for code search
    const technicalTerms = input.match(/\b(auth|login|payment|api|component|screen|error|bug|fix|database|supabase|route|hook|store|state|navigation|ui|form|validation|subscription|checkout|credit|user|admin|oauth|google|github|email|password|token|session|middleware|edge|function|migration|schema|table|query|insert|update|delete|select|join|where|order|limit|offset|group|having|index|foreign|key|primary|unique|not|null|default|auto|increment|timestamp|varchar|text|json|boolean|integer|real|uuid)\b/gi);
    
    // File extensions and paths
    const fileTerms = input.match(/\b[\w-]+\.(ts|tsx|js|jsx|sql|json|md|css|scss|html|xml|yaml|yml)\b/gi);
    
    // Function/variable names (camelCase, snake_case, kebab-case)
    const codeTerms = input.match(/\b[a-zA-Z][a-zA-Z0-9_-]*[a-zA-Z0-9]\b/g);
    
    const allTerms = [
      ...(technicalTerms || []),
      ...(fileTerms || []),
      ...(codeTerms || []).filter(term => term.length > 2)
    ];
    
    return [...new Set(allTerms)].join(' ');
  }

  /**
   * Get context from recent messages
   */
  private getRecentContext(messages: Array<{ content: string; role: string }>): string {
    return messages
      .slice(-3) // Last 3 messages
      .map(m => this.extractKeywords(m.content))
      .filter(Boolean)
      .join(' ');
  }

  /**
   * Infer project context from file structure
   */
  private getProjectContext(files: FileItem[]): string {
    const context: string[] = [];
    
    // Check for common patterns
    const hasAuth = files.some(f => 
      f.name.includes('auth') || 
      f.content?.includes('NextAuth') || 
      f.content?.includes('signIn')
    );
    
    const hasPayment = files.some(f => 
      f.name.includes('payment') || 
      f.name.includes('checkout') || 
      f.content?.includes('LemonSqueezy') ||
      f.content?.includes('Stripe')
    );
    
    const hasSupabase = files.some(f => 
      f.content?.includes('supabase') || 
      f.content?.includes('createClient')
    );
    
    const hasNavigation = files.some(f => 
      f.name.includes('navigation') || 
      f.content?.includes('NavigationContainer') ||
      f.content?.includes('createStackNavigator')
    );
    
    const hasDatabase = files.some(f => 
      f.name.includes('schema') || 
      f.name.includes('migration') ||
      f.content?.includes('pgTable')
    );
    
    if (hasAuth) context.push('authentication login oauth session');
    if (hasPayment) context.push('payment subscription checkout billing');
    if (hasSupabase) context.push('supabase database client');
    if (hasNavigation) context.push('navigation routing screens');
    if (hasDatabase) context.push('database schema migration table');
    
    return context.join(' ');
  }

  /**
   * Extract context from error logs
   */
  private getErrorContext(logs?: Array<{ message: string; type: string }>): string {
    if (!logs || logs.length === 0) return '';
    
    return logs
      .filter(log => log.type === 'error')
      .slice(-3) // Last 3 errors
      .map(log => this.extractKeywords(log.message))
      .filter(Boolean)
      .join(' ');
  }

  /**
   * Get context from current file
   */
  private getFileContext(currentFile: string): string {
    // Extract meaningful parts from file path
    const pathParts = currentFile.split('/').filter(part => 
      part !== 'src' && part !== 'app' && part !== 'components'
    );
    
    return pathParts.join(' ').replace(/\.(ts|tsx|js|jsx)$/, '');
  }

  /**
   * Perform BM25 search
   */
  private searchWithBM25(query: string, limit: number): ScoredFile[] {
    const queryTerms = this.tokenize(query);
    
    const scores = this.documents.map((doc, index) => {
      const { score, matchedTerms } = this.calculateBM25Score(queryTerms, doc);
      return {
        file: doc.original,
        score,
        matchedTerms
      };
    });
    
    return scores
      .filter(result => result.score > 0) // Only return files with matches
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * Calculate BM25 score for a document
   */
  private calculateBM25Score(queryTerms: string[], document: ProcessedDocument): { score: number; matchedTerms: string[] } {
    let score = 0;
    const matchedTerms: string[] = [];
    
    for (const term of queryTerms) {
      const tf = document.termFreq.get(term) || 0;
      
      if (tf > 0) {
        matchedTerms.push(term);
        const df = this.documentFreq.get(term) || 0;
        const idf = Math.log((this.documents.length - df + 0.5) / (df + 0.5));
        
        score += idf * (tf * (this.k1 + 1)) / (tf + this.k1 * (1 - this.b + this.b * (document.length / this.avgDocLength)));
      }
    }
    
    return { score, matchedTerms };
  }

  /**
   * Process a document for BM25 indexing
   */
  private processDocument(file: FileItem): ProcessedDocument {
    // Combine file path and content for indexing
    const fullText = `${file.name} ${file.content || ''}`;
    const tokens = this.tokenize(fullText);
    
    // Calculate term frequencies
    const termFreq = new Map<string, number>();
    for (const token of tokens) {
      termFreq.set(token, (termFreq.get(token) || 0) + 1);
    }
    
    return {
      original: file,
      tokens,
      termFreq,
      length: tokens.length
    };
  }

  /**
   * Tokenize text for BM25 processing
   */
  private tokenize(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ') // Replace punctuation with spaces
      .split(/\s+/)
      .filter(token => token.length > 1) // Filter out single characters
      .filter(token => !this.isStopWord(token)); // Remove stop words
  }

  /**
   * Check if a word is a stop word (less relevant for search)
   */
  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
    ]);
    return stopWords.has(word);
  }

  /**
   * Build vocabulary and document frequency maps
   */
  private buildVocabulary(): void {
    this.vocabulary.clear();
    this.documentFreq.clear();
    
    for (const doc of this.documents) {
      const uniqueTerms = new Set(doc.tokens);
      
      for (const term of uniqueTerms) {
        this.vocabulary.add(term);
        this.documentFreq.set(term, (this.documentFreq.get(term) || 0) + 1);
      }
    }
  }

  /**
   * Get indexing statistics
   */
  getStats(): { totalFiles: number; vocabularySize: number; avgDocLength: number } {
    return {
      totalFiles: this.documents.length,
      vocabularySize: this.vocabulary.size,
      avgDocLength: Math.round(this.avgDocLength)
    };
  }
}
