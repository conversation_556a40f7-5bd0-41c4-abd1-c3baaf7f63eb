import type { InferSelectModel } from 'drizzle-orm';
import {
    pgTable,
    varchar,
    timestamp,
    json,
    uuid,
    text,
    primaryKey,
    foreignKey,
    boolean,
    integer, real,
    unique,
    index,
} from 'drizzle-orm/pg-core';

export const user = pgTable('User', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  email: varchar('email', { length: 64 }).notNull(),
  password: varchar('password', { length: 64 }),
  name: varchar(),
  firstName: varchar(),
  lastName: varchar(),
  linkedin: varchar('linkedin', { length: 255 }),
  provider: varchar('provider', { enum: ['credentials', 'google'] }).default('credentials'),
  isAnonymous: boolean('isAnonymous').default(false),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
});

export type User = InferSelectModel<typeof user>;

// Forward declare projects table to avoid circular references
export const projects = pgTable('Project', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  appName: text('appName'), // New field for project/app name
  slug: text('slug'), // URL-friendly identifier
  scheme: text('scheme'), // URL scheme for deep linking

  // App identifiers
  bundleIdentifier: text('bundleIdentifier'), // iOS bundle ID (com.company.app)
  packageName: text('packageName'), // Android package name

  isMigratedv1: boolean('isMigratedv1').default(false),
  // App assets
  icon: text('icon'), // URL to app icon
  splashImage: text('splashImage'), // URL to splash screen image
  primaryColor: text('primaryColor').default('#000000'), // Primary brand color

  // App configuration
  description: text('description'), // App store description
  privacyPolicyUrl: text('privacyPolicyUrl'),
  termsOfServiceUrl: text('termsOfServiceUrl'),

  // Ownership
  userId: uuid('userId')
    .notNull()
    .references(() => user.id, { onDelete: 'cascade' }),
  connectionId: uuid('connectionId')
    .references(() => connection.id, { onDelete: 'cascade' }),
  visibility: varchar('visibility', { enum: ['public', 'private', 'hidden'] })
    .notNull()
    .default('private'),

  // Timestamps
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),

  prompt: text('prompt'),
  initialUXGuidelines: text('initialUXGuidelines'),

  knowledgeBase: text('knowledgeBase'),
  aiGeneratedMemory: text('aiGeneratedMemory'),

  // Design preview fields
  approvedDesignHtml: text('approvedDesignHtml'),
  designChatId: uuid('designChatId').references(() => chat.id),

  // Supabase fields at project level
  supabaseProjectId: text('supabaseProjectId'),
  supabaseAnonKey: text('supabaseAnonKey'),
  supabaseServiceKey: text('supabaseServiceKey'),
});

export type Project = InferSelectModel<typeof projects>;

export const chat = pgTable('Chat', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  createdAt: timestamp('createdAt').notNull(),
  updatedAt: timestamp('updatedAt').notNull(),
  title: text('title').notNull(),
  userId: uuid('userId')
    .notNull()
    .references(() => user.id),
  projectId: uuid('projectId')
    .references(() => projects.id), // Reference to parent project
  visibility: varchar('visibility', { enum: ['public', 'private', 'hidden'] })
    .notNull()
    .default('private'),
  connectionId: uuid('connectionId')
    .references(() => connection.id),
  // Chat type to distinguish between app development and design preview
  type: varchar('type', { enum: ['app', 'design', 'admin', 'discuss'] }).notNull().default('app'),
  // Keep Supabase fields for backward compatibility but make them nullable
  supabaseProjectId: text('supabaseProjectId'),
  supabaseAnonKey: text('supabaseAnonKey'),
  supabaseServiceKey: text('supabaseServiceKey'),
  isInitialized: boolean('isInitialized').default(true),
  // Flag to indicate if the chat needs continuation due to model context limits
  needsContinuation: boolean('needsContinuation').default(false),
  // Design preview fields
  designHtml: text('designHtml'),
  designStatus: varchar('designStatus', { enum: ['initializing', 'generating', 'complete', 'error'] }),
  isDesignApproved: boolean('isDesignApproved').default(false),
}, (table) => {
  return {
    userIdIdx: index('chat_userId_idx').on(table.userId),
    projectIdIdx: index('chat_projectId_idx').on(table.projectId),
    userProjectIdx: index('chat_userId_projectId_idx').on(table.userId, table.projectId),
    updatedAtIdx: index('chat_updatedAt_idx').on(table.updatedAt)
  };
});

export type Chat = InferSelectModel<typeof chat>;

export const message = pgTable('Message', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  remoteProvider: varchar("remoteProvider"),
  remoteProviderId: varchar("remoteProviderId"),
  chatId: uuid('chatId')
    .notNull()
    .references(() => chat.id),
  projectId: uuid('projectId')
    .references(() => projects.id), // Reference to project
  role: varchar('role').notNull(),
  content: json('content').notNull(),
  componentContexts: json('componentContexts'),
  createdAt: timestamp('createdAt').notNull(),
  autoFixed: boolean('autoFixed').default(false),
  parts: json('parts'),
  version: integer('version').default(1),
  attachments: json('attachments'),
  hidden: boolean('hidden').default(false),
  userId: uuid('userId')
    .references(() => user.id),
  finishReason: varchar('finishReason'),
  parentUserMessageId: uuid('parentUserMessageId'),
  parentAssistantMessageId: uuid('parentAssistantMessageId'),
  isAssistantGroupHead: boolean('isAssistantGroupHead').default(false)
}, (table) => {
  return {
    chatIdIdx: index('message_chatId_idx').on(table.chatId),
    projectIdIdx: index('message_projectId_idx').on(table.projectId),
    userIdIdx: index('message_userId_idx').on(table.userId),
    chatCreatedAtIdx: index('message_chatId_createdAt_idx').on(table.chatId, table.createdAt),
    roleIdx: index('message_role_idx').on(table.role),
    parentAssistantMessageIdIdx: index('message_parentAssistantMessageId_idx').on(table.parentAssistantMessageId)
  };
});

export type Message = InferSelectModel<typeof message>;

export const vote = pgTable(
  'Vote',
  {
    chatId: uuid('chatId')
      .notNull()
      .references(() => chat.id),
    projectId: uuid('projectId')
      .references(() => projects.id), // Reference to project
    messageId: uuid('messageId')
      .notNull()
      .references(() => message.id),
    isUpvoted: boolean('isUpvoted').notNull(),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.chatId, table.messageId] }),
    };
  },
);

export type Vote = InferSelectModel<typeof vote>;

export const document = pgTable(
  'Document',
  {
    id: uuid('id').notNull().defaultRandom(),
    createdAt: timestamp('createdAt').notNull(),
    title: text('title').notNull(),
    content: text('content'),
    kind: varchar('text', { enum: ['text', 'code'] })
      .notNull()
      .default('text'),
    userId: uuid('userId')
      .notNull()
      .references(() => user.id),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.id, table.createdAt] }),
    };
  },
);

export type Document = InferSelectModel<typeof document>;

export const suggestion = pgTable(
  'Suggestion',
  {
    id: uuid('id').notNull().defaultRandom(),
    documentId: uuid('documentId').notNull(),
    documentCreatedAt: timestamp('documentCreatedAt').notNull(),
    originalText: text('originalText').notNull(),
    suggestedText: text('suggestedText').notNull(),
    description: text('description'),
    isResolved: boolean('isResolved').notNull().default(false),
    userId: uuid('userId')
      .notNull()
      .references(() => user.id),
    createdAt: timestamp('createdAt').notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.id] }),
    documentRef: foreignKey({
      columns: [table.documentId, table.documentCreatedAt],
      foreignColumns: [document.id, document.createdAt],
    }),
  }),
);

export type Suggestion = InferSelectModel<typeof suggestion>;

export const passwordResetTokens = pgTable('password_reset_tokens', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  token: text('token').notNull().unique(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

export type PasswordResetToken = InferSelectModel<typeof passwordResetTokens>;

// Connection status enum
export const ConnectionStatus = {
  PENDING: 'pending',
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  FAILED: 'failed'
} as const;

export type ConnectionStatus = typeof ConnectionStatus[keyof typeof ConnectionStatus];

// Base connection table
export const fileState = pgTable('FileState', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  chatId: uuid('chatId').notNull().references(() => chat.id),
  projectId: uuid('projectId').references(() => projects.id), // Reference to project
  sqlQuery: text('sqlQuery'),
  sqlStatus: varchar('sqlStatus', { enum: ['pending', 'executing', 'done', 'error'] }),
  sqlError: text('sqlError'),
  messageId: uuid('messageId').references(() => message.id),
  files: json('files').notNull(),
  dependencies: json('dependencies').notNull(),
  createdAt: timestamp('createdAt').notNull(),
  version: integer('version').default(1),
  isBaseCacheVersion: boolean('is_base_cache_version').default(false),
}, (table) => {
  return {
    chatIdIdx: index('fileState_chatId_idx').on(table.chatId),
    projectIdIdx: index('fileState_projectId_idx').on(table.projectId),
    messageIdIdx: index('fileState_messageId_idx').on(table.messageId),
    baseCacheVersionIdx: index('fileState_isBaseCacheVersion_idx').on(table.isBaseCacheVersion),
    chatBaseCacheIdx: index('fileState_chatId_isBaseCacheVersion_idx').on(table.chatId, table.isBaseCacheVersion),
    versionIdx: index('fileState_version_idx').on(table.version),
    chatMessageIdx: index('fileState_chatId_messageId_idx').on(table.chatId, table.messageId),
    createdAtIdx: index('fileState_createdAt_idx').on(table.createdAt)
  };
});

export type FileState = InferSelectModel<typeof fileState>;

// Screenshot state table for storing screenshot metadata
export const screenshotState = pgTable('ScreenshotState', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  projectId: uuid('projectId').references(() => projects.id),
  chatId: uuid('chatId'),
  messageId: uuid('messageId'),
  screenshots: json('screenshots').notNull(), // Array of screenshot objects with metadata
  createdAt: timestamp('createdAt').notNull().defaultNow(),
}, (table) => {
  return {
    projectIdIdx: index('screenshotState_projectId_idx').on(table.projectId),
    chatIdIdx: index('screenshotState_chatId_idx').on(table.chatId),
    messageIdIdx: index('screenshotState_messageId_idx').on(table.messageId),
    createdAtIdx: index('screenshotState_createdAt_idx').on(table.createdAt),
  };
});

export type ScreenshotState = InferSelectModel<typeof screenshotState>;

export const connection = pgTable('Connection', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  userId: uuid('userId')
    .notNull()
    .references(() => user.id, { onDelete: 'cascade' }),
  provider: varchar('provider', { length: 50 }).notNull(),
  status: varchar('status', { length: 50 })
    .notNull()
    .default(ConnectionStatus.PENDING),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
  lastSyncedAt: timestamp('lastSyncedAt'),
  metadata: json('metadata').$type<Record<string, any>>(),
});

export type Connection = InferSelectModel<typeof connection>;

export const tokenConsumption = pgTable('TokenConsumption', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  model: varchar('model', { length: 50 }).notNull(),
  totalTimeToken: real('totalTimeToken').notNull(),
  promptTokens: integer('promptTokens').notNull(),
  completionTokens: integer('completionTokens').notNull(),
  totalTokens: integer('totalTokens').notNull(),
  chatId: uuid('chatId')
    .notNull(),  // No foreign key as specified
  projectId: uuid('projectId')
    .notNull(),  // No foreign key as specified
  messageId: uuid('messageId')
    .notNull(),  // No foreign key as specified
  userId: uuid('userId')
    .notNull()
    .references(() => user.id, { onDelete: 'cascade' }),  // Add foreign key to User
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
  inputCost: real('inputCost').notNull(),  // Store as float
  outputCost: real('outputCost').notNull(),  // Store as float
  cachingDiscount: real('cachingDiscount'),
  cacheDiscountPercent: real('cacheDiscountPercent'),
  subtotal: real('subtotal'),
  totalCost: real('totalCost').notNull(),  // Store as float
  isAnonymous: boolean('isAnonymous').default(false),
  remoteProvider: varchar("remoteProvider"),
  remoteProviderId: varchar("remoteProviderId"),
  creditsConsumed: integer('creditsConsumed'),
  discountedCredits: integer('discountedCredits'),
  discountReason: varchar('discountReason', { length: 50 }),
  errorId: varchar('errorId'),
  discounted: boolean('discounted').default(false),
  isAutoFixed: boolean('isAutoFixed').default(false)
}, (table) => {
  return {
    userIdIdx: index('tokenConsumption_userId_idx').on(table.userId),
    chatIdIdx: index('tokenConsumption_chatId_idx').on(table.chatId),
    projectIdIdx: index('tokenConsumption_projectId_idx').on(table.projectId),
    userCreatedAtIdx: index('tokenConsumption_userId_createdAt_idx').on(table.userId, table.createdAt),
    isAnonymousIdx: index('tokenConsumption_isAnonymous_idx').on(table.isAnonymous)
  };
});

export type TokenConsumption = InferSelectModel<typeof tokenConsumption>;

// Update projects table to reference chat (already defined above)
export const projectChats = pgTable('ProjectChat', {
  projectId: uuid('projectId')
    .notNull()
    .references(() => projects.id),
  chatId: uuid('chatId')
    .notNull()
    .references(() => chat.id),
  isPrimary: boolean('isPrimary').default(true),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
});

export type ProjectChat = InferSelectModel<typeof projectChats>;

export const subscriptions = pgTable('Subscription', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  userId: varchar('userId', { length: 255 }).notNull(),
  status: varchar('status', { length: 50 }).notNull().default('inactive'),
  planId: varchar('planId', { length: 50 }).notNull().default('free'),
  subscriptionId: varchar('subscriptionId', { length: 255 }).unique(),
  credits: integer('credits').notNull().default(50), // Default to free tier credits
  creditsUsed: integer('creditsUsed').notNull().default(0),
  isActive: boolean('isActive').default(false),
  // LemonSqueezy fields
  lemonSqueezyCustomerId: varchar('lemonSqueezyCustomerId'),
  lemonSqueezySubscriptionId: varchar('lemonSqueezySubscriptionId'),
  lemonSqueezyOrderId: varchar('lemonSqueezyOrderId'),
  lemonSqueezyVariantId: varchar('lemonSqueezyVariantId'),
  provider: varchar('provider'),
  // Stripe fields (for compatibility)
  stripeCustomerId: varchar('stripeCustomerId'),
  stripeSubscriptionId: varchar('stripeSubscriptionId'),
  stripePriceId: varchar('stripePriceId'),
  stripeCurrentPeriodEnd: timestamp('stripeCurrentPeriodEnd'),
  stripeVariantId: timestamp('stripeVariantId'),
  // Billing cycle
  resetDate: timestamp('resetDate'),
  // Flags
  isDowngraded: boolean('isDowngraded').default(false),
  // Additional metadata (cancellation feedback, etc.)
  metadata: json('metadata').$type<Record<string, any>>(),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
}, (table) => {
  return {
    userIdIdx: index('subscription_userId_idx').on(table.userId),
    statusIdx: index('subscription_status_idx').on(table.status),
    userIdUpdatedAtIdx: index('subscription_userId_updatedAt_idx').on(table.userId, table.updatedAt),
    isActiveIdx: index('subscription_isActive_idx').on(table.isActive)
  };
});

export type Subscription = InferSelectModel<typeof subscriptions>;

export const fileEmbeddings = pgTable('FileEmbedding', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  projectId: varchar('projectId', { length: 255 }).notNull().default('default'),
  filePath: varchar('filePath', { length: 500 }).notNull(),
  contentHash: varchar('contentHash', { length: 64 }).notNull(),
  embedding: text('embedding').notNull(), // JSON string of embedding vector
  metadata: json('metadata').$type<{
    type: string;
    exports: string[];
    lineCount: number;
  }>(),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
}, (table) => {
  return {
    filePathUnique: unique('file_path_unique').on(table.filePath),
    projectIdIdx: index('file_embeddings_project_id_idx').on(table.projectId),
  };
});

export type FileEmbedding = InferSelectModel<typeof fileEmbeddings>;

export const projectConnections = pgTable('ProjectConnection', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  projectId: uuid('projectId')
    .notNull()
    .references(() => projects.id, { onDelete: 'cascade' }),
  provider: varchar('provider', { length: 50 }).notNull(),
  providerProjectId: text('providerProjectId').notNull(),
  providerProjectData: json('providerProjectData').$type<Record<string, any>>(),
  types: json('types').$type<{
    database: string;
    client: string;
    helper: string;
    projectRef: string;
    host: string;
    anonKey: string;
  }>(),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
});

export type ProjectConnection = InferSelectModel<typeof projectConnections>;

// APK Builds table for tracking Android build jobs
export const apkBuilds = pgTable('apk_builds', {
  id: uuid('id').primaryKey().notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: text('status').notNull(),
  apkUrl: text('apk_url'),
  error: text('error'),
  metadata: json('metadata').$type<Record<string, any>>(),
});

export type ApkBuild = InferSelectModel<typeof apkBuilds>;

// Deployments table for tracking all platform deployments
export const deployments = pgTable('deployments', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  projectId: uuid('projectId').references(() => projects.id), // Reference to project
  userId: uuid('userId').notNull().references(() => user.id),
  slug: text('slug').notNull(),
  platform: varchar('platform', { enum: ['web', 'android', 'ios'] }).notNull(),
  version: varchar('version', { length: 20 }).notNull(), // Semantic versioning (e.g., 1.0.0)
  versionCode: integer('versionCode'), // Auto-incrementing version number for platforms that require it
  status: varchar('status', {
    enum: ['queued', 'processing', 'completed', 'failed']
  }).notNull().default('queued'),
  url: text('url'), // URL where the app is deployed (web) or download link (APK)
  buildId: uuid('buildId').references(() => apkBuilds.id), // For Android/iOS builds
  fileStateId: uuid('fileStateId').references(() => fileState.id), // Reference to file structure
  metadata: json('metadata').$type<Record<string, any>>(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  error: text('error'),
}, (table) => {
  return {
    // Add a unique constraint on slug
    slugUnique: unique('slug_unique').on(table.slug),

    // Add a dedicated index on slug for faster lookups
    slugIdx: index('slug_idx').on(table.slug),

    // Add a composite index on slug and platform
    slugPlatformIdx: index('slug_platform_idx').on(table.slug, table.platform)
  };
});

export type Deployment = InferSelectModel<typeof deployments>;

export const stream = pgTable(
    'Stream',
    {
      id: uuid('id').notNull().defaultRandom(),
      chatId: uuid('chatId').notNull(),
      createdAt: timestamp('createdAt').notNull(),
    },
    (table) => ({
      pk: primaryKey({ columns: [table.id] })
    }),
);
export type Stream = InferSelectModel<typeof stream>;

// Design screens table for storing individual screen designs
export const designScreens = pgTable(
  'DesignScreen',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    chatId: uuid('chatId')
      .notNull()
      .references(() => chat.id, { onDelete: 'cascade' }),
    projectId: uuid('projectId')
      .notNull()
      .references(() => projects.id, { onDelete: 'cascade' }),
    name: text('name').notNull(),
    html: text('html').notNull(),
    order: real('order').notNull().default(0),
    status: varchar('status', { enum: ['starting', 'generating', 'complete', 'error'] })
      .notNull()
      .default('complete'),
    createdAt: timestamp('createdAt').notNull().defaultNow(),
    updatedAt: timestamp('updatedAt').notNull().defaultNow(),
  },
  (table) => ({
    chatIdIdx: index('design_screen_chatId_idx').on(table.chatId),
    projectIdIdx: index('design_screen_projectId_idx').on(table.projectId),
    orderIdx: index('design_screen_order_idx').on(table.order),
  }),
);
export type DesignScreen = InferSelectModel<typeof designScreens>;

// Temperature optimization metadata table
export const temperatureOptimization = pgTable('TemperatureOptimization', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  messageId: uuid('messageId')
    .notNull()
    .references(() => message.id, { onDelete: 'cascade' }),
  chatId: uuid('chatId')
    .notNull()
    .references(() => chat.id, { onDelete: 'cascade' }),
  projectId: uuid('projectId')
    .references(() => projects.id, { onDelete: 'cascade' }),
  userId: uuid('userId')
    .references(() => user.id, { onDelete: 'cascade' }),

  // Optimization results
  optimizedTemperature: real('optimizedTemperature').notNull(),
  selectedModel: varchar('selectedModel', { length: 100 }).notNull(),
  reasoning: text('reasoning').notNull(),
  contextFactors: json('contextFactors').$type<string[]>().notNull(),

  // User progression tracking (key metric for development platforms)
  userProgression: json('userProgression').$type<{
    isProgressing: boolean;
    isStuck: boolean;
    stuckSeverity?: "mild" | "moderate" | "severe";
  }>(),

  // Project complexity metrics
  fileCount: integer('fileCount').notNull().default(0),

  // Performance metrics
  optimizationDuration: integer('optimizationDuration'), // milliseconds
  wasSuccessful: boolean('wasSuccessful').notNull().default(true),
  errorMessage: text('errorMessage'),

  // Fallback info
  usedFallback: boolean('usedFallback').notNull().default(false),
  fallbackReason: varchar('fallbackReason', { length: 100 }),

  createdAt: timestamp('createdAt').notNull().defaultNow(),
}, (table) => ({
  messageIdIdx: index('temperature_optimization_messageId_idx').on(table.messageId),
  chatIdIdx: index('temperature_optimization_chatId_idx').on(table.chatId),
  projectIdIdx: index('temperature_optimization_projectId_idx').on(table.projectId),
  userIdIdx: index('temperature_optimization_userId_idx').on(table.userId),
  createdAtIdx: index('temperature_optimization_createdAt_idx').on(table.createdAt),
  temperatureIdx: index('temperature_optimization_temperature_idx').on(table.optimizedTemperature),
  successfulIdx: index('temperature_optimization_successful_idx').on(table.wasSuccessful),
}));

export type TemperatureOptimization = InferSelectModel<typeof temperatureOptimization>;

