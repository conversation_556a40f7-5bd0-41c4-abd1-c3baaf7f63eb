CREATE TABLE "FileEmbedding" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"projectId" varchar(255) DEFAULT 'default' NOT NULL,
	"filePath" varchar(500) NOT NULL,
	"contentHash" varchar(64) NOT NULL,
	"embedding" text NOT NULL,
	"metadata" json,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "file_path_unique" UNIQUE("filePath")
);
--> statement-breakpoint
CREATE INDEX "file_embeddings_project_id_idx" ON "FileEmbedding" USING btree ("projectId");