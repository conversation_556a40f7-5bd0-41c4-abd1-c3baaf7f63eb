import {action, makeAutoObservable, runInAction} from 'mobx';
import {RootStore} from './RootStore';
import {Snack, SnackListenerSubscription, SnackLogEvent} from 'snack-sdk';
import {FileItem} from '@/types/file';
import {DEFAULT_CODE} from '@/types/editor';
import {ProtocolErrorMessage, ProtocolIncomingMessage} from "snack-sdk/build/transports/Protocol";
import {createError} from "snack-sdk/build/utils";
import {addObject} from "snack-sdk/build/State";
import {base64ToFile} from "@/services/Uploads";


const SNACK_WEBPLAYER_URL = "https://storage.magically.life";

interface LocalSnackState {
    isLoading: boolean;
    error: string | null;
    webPreviewURL: string | null;
    url: string | null;
    deviceId: string | null;
    isFullscreen: boolean;
    platform: 'web' | 'ios' | 'android';
    isReady: boolean;
    webPreviewRef: {current: Window | null} | null;
    files: FileItem[];
    dependencies: Record<string, any>;
}

export class SnackStore {
    rootStore: RootStore;
    private snackInstances = new Map<string, {
        snack: Snack;
        stateListener: SnackListenerSubscription | undefined;
        logListener: SnackListenerSubscription | undefined;
    }>();

    private snackStates = new Map<string, LocalSnackState>();

    constructor(rootStore: RootStore) {
        this.rootStore = rootStore;
        makeAutoObservable(this, {
            rootStore: false
        }, {
            autoBind: true
        });
    }

    initSnack = (chatId: string, files: FileItem[], dependencies: Record<string, any>, webPreviewRef: {
        current: Window | null
    }, cleanupSnack: boolean): Promise<boolean> => {
        // console.log(`[SnackStore] Initializing snack for chatId: ${chatId}`, {
        //     filesCount: files.length,
        //     hasDependencies: !!dependencies,
        //     hasWebPreviewRef: !!webPreviewRef.current
        // });


        // console.log('Snack size', this.snackInstances.size, this.snackInstances.keys())
        return new Promise(async (resolve) => {
            if (cleanupSnack) {
                // Clean up existing instance if any
                this.cleanupSnack(chatId);
            }

            try {
                if (this.snackInstances.has(chatId)) {
                    // console.log(`Updating existing snack for chatId: ${chatId}`);
                    const instance = this.snackInstances.get(chatId);
                    if (instance) {
                        // Make sure it's online
                        instance.snack.setOnline(true);
                        // Update files if needed
                        if (files.length) {
                            instance.snack.updateFiles(this.convertToSnackFiles(files));
                            // instance.snack.updateDependencies(dependencies);
                            console.log('[DEBUG]: Send code changes through init')
                            instance.snack.sendCodeChanges();
                        }
                        resolve(true);
                    }
                } else {
                    // console.log(`[SnackStore] Create new Snack: ${chatId}`, this.snackInstances.size);
                    // console.log(`[SnackStore] Converting files for chatId: ${chatId}`, DEFAULT_CODE);
                    const snackFiles = this.convertToSnackFiles(files.length ? files: DEFAULT_CODE as FileItem[]);
                    const snack = new Snack({
                        files: snackFiles,
                        dependencies: this.addDependencies(dependencies, chatId),
                        verbose: false,
                        reloadTimeout: 10000,
                        sdkVersion: '52.0.0',
                        name: 'magically Generated App',
                        description: 'Generated with trymagically.com',
                        webPlayerURL: `${SNACK_WEBPLAYER_URL}/v2/53`,
                        webPreviewRef,
                    });

                    // @ts-ignore
                    snack.setState((state) => (
                            {
                                sdkVersion: "53.0.0",
                            }
                        )
                    );

                    this.snackInstances.set(chatId, {
                        snack,
                        stateListener: undefined,
                        logListener: undefined
                    });

                    console.log('snack -->', snack)
                    const updatedState = await snack.getStateAsync();
                    // console.log(`[SnackStore] Setting snack online for chatId: ${chatId}`);
                    snack.setOnline(true);

                    console.log('Expo URL:', updatedState.url)
                    // console.log(`[SnackStore] Adding state listener for chatId: ${chatId}`);
                    const stateListener = snack.addStateListener(async (state, prevState) => {
                        const localPrevState: LocalSnackState | undefined = this.snackStates.get(chatId);
                        if (!localPrevState || (localPrevState.url !== (state).url)) {
                            // console.log(`[SnackStore] State update for chatId: ${chatId}`, state);
                            this.updateSnackState(chatId, {
                                ...this.getInitialState(),
                                isReady: true,
                                isLoading: false,
                                error: null,
                                webPreviewURL: state.webPreviewURL || null,
                                url: state.url || null
                            });
                        }
                        // if (prevState && state.connectedClients !== prevState.connectedClients) {
                        //     for (const key in state.connectedClients) {
                        //         if (!prevState.connectedClients[key]) {
                        //             console.log('A client has connected! ', state.connectedClients[key]);
                        //         }
                        //     }
                        // }
                    });

                    // @ts-ignore
                    snack.onErrorMessageReceived = (connectedClientId: string, message: ProtocolErrorMessage) => {
                        console.log('connectedClientId', connectedClientId)
                        console.log('message', message)
                        try {
                            const json = JSON.parse(message.error);
                            const error = createError({
                                name: json.name || 'Error',
                                message: json.message || '',
                                fileName: json.fileName,
                                lineNumber: json.lineNumber || json.loc?.line || json.line,
                                columnNumber: json.columnNumber || json.loc?.column || json.column,
                                stack: json.stack,
                            });
                            console.log('error', error)
                            // @ts-ignore
                            snack.setState((state) => ({
                                connectedClients: addObject(state.connectedClients, connectedClientId, {
                                    ...state.connectedClients[connectedClientId],
                                    error,
                                    status: 'error',
                                }),
                            }));
                            const event: SnackLogEvent = {
                                type: "error",
                                // @ts-ignore
                                connectedClient: snack.state.connectedClients[connectedClientId],
                                message: JSON.stringify({cause: error.cause, message: error.message, lineNumber: error.lineNumber, columnNumber: error.columnNumber, fileName: error.fileName}),
                                // error?
                                // arguments: payload,
                            };

                            // @ts-ignore
                            snack.logListeners.forEach((listener) => listener(event));
                        } catch {
                            // @ts-ignore
                            snack.logger?.error('Failed to parse received error message', message);
                        }
                    }

                    // @ts-ignore
                    snack.onProtocolMessageReceived = (
                        transport: string,
                        connectedClientId: string,
                        message: ProtocolIncomingMessage,
                    ) => {
                        // Ignore messages from clients that are not setup yet or have disconnected
                        // @ts-ignore
                        if (!snack.state.connectedClients[connectedClientId]) {
                            return;
                        }
                        const session = this.rootStore.generatorStore.getActiveSession(chatId);

                        switch (message.type as any) {
                            case 'CONSOLE':
                                // @ts-ignore
                                snack.onConsoleMessageReceived(connectedClientId, message);
                                break;
                            case 'ERROR':
                                // @ts-ignore
                                snack.onErrorMessageReceived(connectedClientId, message);
                                break;
                            case 'STATUS_REPORT':
                                // @ts-ignore
                                snack.onStatusReportMessageReceived(connectedClientId, message);
                                break;

                            case "MAGICALLY_ERROR_FIX_REQUEST":
                                console.log('essage', message)
                                if(session ) {

                                    // console.log('file',file)
                                    runInAction(() => {
                                        // Add to component contexts in the session
                                        session.sendAutoFixError((message as any)?.error);
                                        // Set the current message (for backward compatibility)
                                        // session.setCurrentMessage(
                                        //     ``
                                        // )
                                    })
                                }
                                break;

                            case 'MAGICALLY_COMPONENT_SELECTED':
                                if(session ) {
                                    const {
                                        element,
                                        lineNumber,
                                        name,
                                        screenshot,
                                        sourceFile
                                    } = (message as any).data as any;
                                    // console.log('message', message)

                                    // console.log('file',file)
                                    runInAction(() => {
                                        // Add to component contexts in the session
                                        session.addComponentContext({
                                            componentName: name,
                                            element,
                                            sourceFile,
                                            lineNumber,
                                            screenshot
                                        });

                                        // Set the current message (for backward compatibility)
                                        // session.setCurrentMessage(
                                        //     ``
                                        // )
                                    })
                                }
                                break;

                            case "MAGICALLY_INSPECTION_MODE_CHANGED":
                                // console.log('MAGICALLY_INSPECTION_MODE_CHANGED', message)
                                break;
                            // @ts-ignore: CODE is echoed by pubsub, we ignore it
                            case 'CODE':
                                break;
                            // @ts-ignore: RELOAD_SNACK is echoed by pubsub, we ignore it
                            case 'RELOAD_SNACK':
                                break;
                            // @ts-ignore: REQUEST_STATUS is echoed by pubsub, we ignore it
                            case 'REQUEST_STATUS':
                                break;
                            default:
                                // @ts-ignore
                                snack.logger?.error('Invalid message received', transport, message);
                                break;
                        }
                    }


                    // console.log(`[SnackStore] Adding log listener for chatId: ${chatId}`);
                    const logListener = snack.addLogListener((logEvent) => {
                        const {type, message, connectedClient} = logEvent;
                        // console.log(`[SnackStore] Log event for chatId: ${chatId}`, logEvent);

                        if(type !== 'warn') {
                            // Add log to LogStore
                            this.rootStore.logStore.addLog(chatId, {
                                type: type as any,
                                message: typeof message === 'string' ? message : JSON.stringify(message),
                                source: 'console',
                                chatId: chatId,
                                platform: connectedClient?.platform,
                                clientId: connectedClient?.id
                            });
                        }

                        // Keep existing error handling
                        if (type === 'error') {
                            this.updateSnackState(chatId, {
                                error: message
                            });
                        }
                    });

                    this.snackInstances.set(chatId, {
                        snack,
                        stateListener: stateListener,
                        logListener: logListener
                    });

                    this.updateSnackState(chatId, {
                        ...this.getInitialState(),
                        isReady: true,
                        isLoading: false,
                        error: null,
                        webPreviewURL: updatedState.webPreviewURL || null,
                        url: updatedState.url || null,
                        webPreviewRef:webPreviewRef,
                        files: files,
                        dependencies: this.addDependencies(dependencies, chatId)
                    });

                    resolve(true)
                }
            } catch (err: any) {
                this.updateSnackState(chatId, {
                    ...this.getInitialState(),
                    isLoading: false,
                    error: err.message,
                    isReady: false
                });
                resolve(false);
            }
        });
    };

    @action
    destroySnack(chatId: string) {
        this.snackInstances.delete(chatId);
        this.snackStates.delete(chatId);
    }

    private getInitialState = (): LocalSnackState => ({
        isLoading: true,
        error: null,
        webPreviewURL: null,
        url: null,
        deviceId: null,
        isFullscreen: false,
        platform: 'web',
        isReady: false,
        dependencies: {},
        files: [],
        webPreviewRef: null,
    });



    @action
    updateFiles = async (chatId: string, files: FileItem[], dependencies: Record<string, any>) => {
        // console.log(`[SnackStore] Updating files for chatId: ${chatId}`, {
        //     filesCount: files.length,
        //     hasDependencies: !!dependencies,
        //     hasInstance: !!this.snackInstances.get(chatId),
        //     state: this.getSnackState(chatId)
        // });

        const instance = this.snackInstances.get(chatId);
        const state = this.getSnackState(chatId);
        if (!instance || !state?.isReady) {
            // console.log(`[SnackStore] Skipping file update for chatId: ${chatId} - instance or state not ready`);
            return;
        }

        state.files = files;
        state.dependencies = this.addDependencies(dependencies, chatId);
        const snackFiles = this.convertToSnackFiles(files);
        instance.snack.updateFiles(snackFiles);
        // instance.snack.updateDependencies(dependencies);
        console.log('[DEBUG]: Send code changes through updateFiles')
        instance.snack.sendCodeChanges();

        const updatedState = await instance?.snack.getStateAsync();
        console.log('updatedState after file update', updatedState)
        this.reloadSnack(chatId);
    };

    setDeviceId = (chatId: string, deviceId: string) => {
        this.updateSnackState(chatId, {deviceId});
    };

    setPlatform = (chatId: string, platform: 'web' | 'ios' | 'android') => {
        this.updateSnackState(chatId, {platform});
    };

    @action
    reloadSnack(chatId: string) {
        const instance = this.snackInstances.get(chatId);
        if (instance) {
            const state = this.getSnackState(chatId);
            this.rootStore.logStore.clearLogs(chatId);
            instance.snack.reloadConnectedClients();
            // this.initSnack(chatId, state?.files || [], state?.dependencies || {}, state?.dependencies as any, true);
        }
    }

    @action
    forceReloadSnack(chatId: string) {
        const instance = this.snackInstances.get(chatId);
        if (instance) {
            const state = this.getSnackState(chatId);

            // Clear logs first
            this.rootStore.logStore.clearLogs(chatId);

            // Get current state before cleanup
            const currentFiles = state?.files || [];
            const currentDependencies = this.addDependencies(state?.dependencies  || {}, chatId);
            const webPreviewRef = state?.webPreviewRef || { current: null };

            // Clean up the current instance
            this.cleanupSnack(chatId);

            // Wait a moment to ensure cleanup is complete
            setTimeout(() => {
                // Reinitialize with the same files and dependencies
                this.initSnack(chatId, currentFiles, currentDependencies, webPreviewRef, false);
                console.log(`Snack fully reloaded for chatId: ${chatId}`);
            }, 100);
        }
    }

    setFullscreen = (chatId: string, isFullscreen: boolean) => {
        this.updateSnackState(chatId, {isFullscreen});
    };

    @action
    setOnline(chatId: string) {
        // console.log(`[SNACK_WS] Setting online for chatId: ${chatId}`);
        const instance = this.snackInstances.get(chatId);
        if (instance) {
            // Force reconnection by going offline then online
            // console.log(`[SNACK_WS] Setting offline first for chatId: ${chatId}`);
            instance.snack.setOnline(false);
            const offlineTime = new Date().getTime();

            setTimeout(() => {
                const elapsedTime = new Date().getTime() - offlineTime;
                // console.log(`[SNACK_WS] Setting online after ${elapsedTime}ms for chatId: ${chatId}`);
                if (this.snackInstances.has(chatId)) {
                    instance.snack.setOnline(true);
                } else {
                    console.log(`[SNACK_WS] Instance no longer exists for chatId: ${chatId}`);
                }
            }, 50);
        } else {
            console.log(`[SNACK_WS] No instance found for chatId: ${chatId}`);
        }
    }

    @action
    setOffline(chatId: string) {
        // console.log(`[SNACK_WS] Setting offline for chatId: ${chatId}`);
        const instance = this.snackInstances.get(chatId);
        if (instance) {
            instance.snack.setOnline(false);
            console.log(`[SNACK_WS] Successfully set offline for chatId: ${chatId}`);
        } else {
            console.log(`[SNACK_WS] No instance found for chatId: ${chatId}`);
        }
    }

    @action
    cleanupAllSnacks() {
        // console.log(`[SNACK_WS] Cleaning up all Snack instances, count: ${this.snackInstances.size}`);
        // Get all chat IDs
        const chatIds = Array.from(this.snackInstances.keys());

        // Clean up each instance
        chatIds.forEach(chatId => {
            this.cleanupSnack(chatId);
        });

        // console.log(`[SNACK_WS] All Snack instances cleaned up`);
    }



    @action
    cleanupSnack(chatId: string) {
        // console.log(`[SNACK_WS] Starting cleanup for chatId: ${chatId}`);
        const instance = this.snackInstances.get(chatId);
        if (!instance) {
            // console.log(`[SNACK_WS] No instance found to clean up for chatId: ${chatId}`);
            return;
        }

        try {
            // First set offline to close connections
            // console.log(`[SNACK_WS] Setting offline during cleanup for chatId: ${chatId}`);
            instance.snack.setOnline(false);

            // Remove all listeners
            if (instance.stateListener) {
                // console.log(`[SNACK_WS] Removing state listener for chatId: ${chatId}`);
                instance.stateListener();
            }
            if (instance.logListener) {
                // console.log(`[SNACK_WS] Removing log listener for chatId: ${chatId}`);
                instance.logListener();
            }
        } catch (e) {
            console.error('[SNACK_WS] Error cleaning up Snack:', e);
        } finally {
            // Always remove from maps
            // console.log(`[SNACK_WS] Removing instance from maps for chatId: ${chatId}`);
            this.snackInstances.delete(chatId);
            this.snackStates.delete(chatId);
            // console.log(`[SNACK_WS] Cleanup complete for chatId: ${chatId}`);
        }
    }

    private convertToSnackFiles = (files: FileItem[]) => {
        return this.contextAwareTypeStripping(files).reduce((acc, file) => {
            acc[file.name] = {
                type: 'CODE' as const,
                contents: file.content
            };
            return acc;
        }, {} as Record<string, { type: 'CODE', contents: string }>);
    };

    private updateSnackState = (chatId: string, update: Partial<typeof this.snackStates extends Map<string, infer T> ? T : never>) => {
        const currentState = this.snackStates.get(chatId) || {
            isLoading: true,
            error: null,
            webPreviewURL: null,
            url: null
        };
        this.snackStates.set(chatId, {...currentState, ...update} as any);
    };

    getSnackState = (chatId: string): LocalSnackState | undefined => {
        return this.snackStates.get(chatId);
    };

    @action
    sendMessageToSnack(chatId: string, command: 'TOGGLE_INSPECTION_MODE' | 'SHOW_INSPECTOR' | 'HIDE_INSPECTOR') {
        const snackState = this.getSnackState(chatId);
        if (snackState && snackState.webPreviewRef) {
            console.log('Sending', snackState.webPreviewRef.current)
            const instance = this.snackInstances.get(chatId);

            snackState.webPreviewRef.current?.postMessage(JSON.stringify({
                type: 'MAGICALLY_VISUAL_EDITOR_COMMAND',
                command: command
            }), '*')

            // (document.querySelector('.snack-iframe')?).contentWindow.postMessage(JSON.stringify({
            //     type: 'MAGICALLY_VISUAL_EDITOR_COMMAND',
            //     command: "TOGGLE_INSPECTION_MODE"
            // }), '*')


            // document.querySelector('.snack-iframe').contentWindow.postMessage(JSON.stringify({
            //     type: 'MAGICALLY_VISUAL_EDITOR_COMMAND',
            //     command: command
            // }), '*')
        }
    }

    @action
    addDependencies(dependencies: Record<string, any>, chatId: string) {
        const upgradeVersions = {
            'react-native-svg': {version: '15.11.2'}
        }

        const mandatoryPackages = {
            'zustand/middleware': {version: '*'},
            'date-fns/locale': {version: '*'}
        }

        const upgradedDependencies = {...dependencies};
        const session = this.rootStore.generatorStore.getActiveSession(chatId);


        let needsUpdate = false;
        if(upgradedDependencies) {
            Object.entries(upgradedDependencies).forEach((dep) => {
                const name = dep[0];
                if (upgradeVersions[name] && upgradeVersions[name].version !== dep[1].version) {
                    upgradedDependencies[name] = upgradeVersions[name];
                    needsUpdate = true;
                }
            })
        }

        if(Object.entries(mandatoryPackages).length) {
            Object.entries(mandatoryPackages).forEach((dep) => {
                const name = dep[0];
                if (!upgradedDependencies[name]) {
                    upgradedDependencies[name] = dep[1];
                    needsUpdate = true;
                }
            })
        }

        if (needsUpdate) {
            session?.setDependencies(upgradedDependencies);
            this.updateSnackState(chatId, {dependencies: upgradedDependencies})
        }

        return upgradedDependencies;
    }


    contextAwareTypeStripping(files: FileItem[]) {
        return files;
        // We need to strip all the typescript from the files to ensure SystemJS in the snack environment does not trip up.
        // return files.map(file => {
        //     // We need to strip problematic TypeScript syntax from the files to be able to run on Expo Snack
        //     if (file.name.endsWith('.ts') || file.name.endsWith('.tsx')) {
        //         // Store string literals to prevent modifying them
        //         const stringLiterals: {[key: string]: string} = {};
        //         let stringCounter = 0;
        //
        //         // Replace string literals with placeholders
        //         let processedContent = file.content.replace(/('[^']*'|"[^"]*")/g, (match) => {
        //             const placeholder = `__STRING_PLACEHOLDER_${stringCounter}__`;
        //             stringLiterals[placeholder] = match;
        //             stringCounter++;
        //             return placeholder;
        //         });
        //
        //         // Now apply TypeScript transformations on the content without string literals
        //
        //         // Replace standalone type declarations with typeof to use 'any' type
        //         processedContent = processedContent.replace(/\btype\s+(\w+)\s*=\s*typeof\s+[\w\[\].]+\s*;/g, 'type $1 = any;');
        //
        //         // Replace standalone type declarations with union types to use 'any' type
        //         processedContent = processedContent.replace(/\btype\s+(\w+)\s*=\s*[\w]+(?:\s*\|\s*[\w]+)+\s*;/g, 'type $1 = any;');
        //
        //         // Replace typeof in type annotations (after colons)
        //         processedContent = processedContent.replace(/:\s*typeof\s+([\w\[\].]+)/g, ': any');
        //
        //         // Replace union types in type annotations (after colons)
        //         // Only match when not inside a string literal and when it's clearly a type annotation
        //         processedContent = processedContent.replace(/:\s*[\w]+(?:\s*\|\s*[\w]+)+(?=\s*[;,\)\}])/g, ': any');
        //
        //         // Fix any malformed 'any X.Y' patterns that might have been created
        //         processedContent = processedContent.replace(/:\s*any\s+([\w\[\].]+)/g, ': any');
        //
        //         // Restore string literals
        //         Object.keys(stringLiterals).forEach(placeholder => {
        //             processedContent = processedContent.replace(placeholder, stringLiterals[placeholder]);
        //         });
        //
        //         file.content = processedContent;
        //     }
        //
        //     return file;
        // });
    }
}
