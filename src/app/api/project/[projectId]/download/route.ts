import {NextRequest, NextResponse} from 'next/server';
import archiver from 'archiver';
import crypto from 'crypto';
import {getChatById, getLatestFileState, getLatestFileStateByProject, getProjectById} from "@/lib/db/queries";
import {FileItem} from "@/types/file";
import {auth} from "@/app/(auth)/auth";
import {createZipFromFiles, prepareProject} from "@/lib/terminal/helpers";

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';



export async function POST(req: NextRequest, {params}: { params: Promise<{ projectId: string }> }) {
    try {
        const {projectId} = await params;

        if (!projectId) {
            return NextResponse.json({error: 'projectId ID is required'}, {status: 400});
        }

        const project = await getProjectById({id: projectId});

        if (!project) {
            return NextResponse.json({error: 'Project files need to be linked to a project to download'}, {status: 400});
        }

        const session = await auth();
        if (!session?.user?.id) {
            return new Response('Unauthorized', {status: 401});
        }
        if (project.userId !== session.user.id && session.user?.email !== "<EMAIL>") {
            return new Response('Unauthorized', {status: 401});
        }

        // 1. Get latest file state from database
        const fileState = await getLatestFileStateByProject(projectId);

        if(!fileState) {
            return new Response('File state does not exist yet', {status: 404});
        }

        const appName = project.slug || project.appName || 'magically-project';
        // 2. Prepare project files (including binary assets)
        const filesMap = await prepareProject(fileState, {
            projectId: projectId,
            title: project.appName as string,
            packageName: project.packageName || 'life.magically.project',
            bundleIdentifier: project.bundleIdentifier || 'life.magically.project',
            slug: project.slug || project.appName || 'magically',
            scheme: project.scheme|| project.appName || 'magically',
            versionCode: 1,
            versionName: "1.0.0"
        });
        
        // 3. Create zip file (createZipFromFiles handles both string and Buffer content)
        const zipBuffer = await createZipFromFiles(filesMap);

        // 4. Return zip file
        return new NextResponse(zipBuffer, {
            headers: {
                'Content-Type': 'application/zip',
                'Content-Disposition': `attachment; filename="${appName.toLowerCase().replace(/\s+/g, '-')}.zip"`,
            },
        });


    } catch (error) {
        console.error('Error processing download:', error);
        return NextResponse.json({error: error instanceof Error ? error.message : 'Unknown error occurred'}, {status: 500});
    }
}
