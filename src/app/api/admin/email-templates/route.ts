import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';

// Admin guard middleware
async function adminGuard() {
  const session = await auth();

  // For development purposes, we're temporarily relaxing the authentication check
  // In production, uncomment the check below
  /*
  if (!session || !session.user || session.user.email !== '<EMAIL>') {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }
  */

  // For testing, we'll allow any authenticated user or even no authentication
  if (process.env.NODE_ENV === 'production' && (!session || !session.user)) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }

  return null; // No error, continue
}

// In a real application, these would be stored in the database
// For simplicity, we're using hardcoded templates
const EMAIL_TEMPLATES = [
  {
    id: '1',
    name: 'Welcome Back',
    subject: 'We miss you at Magically!',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>We've missed you!</h2>
        <p>Hi {{name}},</p>
        <p>We noticed it's been a while since you've used our AI coding assistant. We wanted to check in and see if there's anything we can help you with.</p>
        <p>Our platform has been updated with several new features that make coding even more efficient:</p>
        <ul>
          <li><strong>Improved code completion</strong> - More accurate suggestions based on your coding style</li>
          <li><strong>Enhanced debugging</strong> - Identify and fix issues faster</li>
          <li><strong>New language support</strong> - We've added more programming languages to our toolkit</li>
        </ul>
        <p>We'd love to have you back! Just click the button below to log in and see what's new.</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="https://trymagically.com/login" style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Log In Now</a>
        </div>
        <p>If you have any questions or need assistance, just reply to this email. We're here to help!</p>
        <p>Best regards,<br>The Magically Team</p>
      </div>
    `,
    createdAt: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'Pro Tips & Tricks',
    subject: 'Unlock Your Coding Potential: 3 Pro Tips',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Level Up Your Coding Skills</h2>
        <p>Hello {{name}},</p>
        <p>We want to help you get the most out of our AI coding assistant. Here are three pro tips that our power users love:</p>

        <div style="background-color: #f9fafb; padding: 15px; margin: 20px 0; border-left: 4px solid #4F46E5;">
          <h3 style="margin-top: 0;">1. Use Natural Language Commands</h3>
          <p>Instead of writing complex code from scratch, try describing what you want to achieve in plain English. For example, type "create a function that sorts an array of objects by date" and watch the magic happen!</p>
        </div>

        <div style="background-color: #f9fafb; padding: 15px; margin: 20px 0; border-left: 4px solid #4F46E5;">
          <h3 style="margin-top: 0;">2. Leverage Code Explanations</h3>
          <p>When you encounter complex code, highlight it and ask "explain this code" to get a detailed breakdown of what it does and how it works.</p>
        </div>

        <div style="background-color: #f9fafb; padding: 15px; margin: 20px 0; border-left: 4px solid #4F46E5;">
          <h3 style="margin-top: 0;">3. Use the Debugging Assistant</h3>
          <p>When you hit an error, copy the error message and ask our AI to help fix it. It will analyze the issue and suggest solutions to get your code running smoothly.</p>
        </div>

        <p>Ready to try these tips? Click below to jump back into coding:</p>

        <div style="text-align: center; margin: 30px 0;">
          <a href="https://trymagically.com/dashboard" style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Open trymagically.com</a>
        </div>

        <p>Happy coding!<br>The Magically Team</p>
      </div>
    `,
    createdAt: new Date().toISOString(),
  },
  {
    id: '3',
    name: 'Special Offer',
    subject: 'Limited Time: 20% Off Your Subscription Renewal',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #4F46E5;">Special Offer Just For You</h2>
        <p>Hi {{name}},</p>
        <p>We value your membership and want to make sure you continue to enjoy all the benefits of our AI coding assistant.</p>

        <div style="background-color: #f0f4ff; padding: 20px; margin: 25px 0; text-align: center; border-radius: 8px;">
          <h3 style="color: #4F46E5; margin-top: 0;">20% OFF YOUR NEXT BILLING CYCLE</h3>
          <p style="font-size: 18px;">Use code: <strong>WELCOME20</strong></p>
          <p style="font-size: 14px; color: #6b7280;">Offer expires in 7 days</p>
        </div>

        <p>With your subscription, you'll continue to enjoy:</p>
        <ul>
          <li>Unlimited AI code completions</li>
          <li>Advanced debugging assistance</li>
          <li>Code optimization suggestions</li>
          <li>Priority support</li>
          <li>Access to all new features</li>
        </ul>

        <p>To take advantage of this offer, simply click the button below and enter the code at checkout:</p>

        <div style="text-align: center; margin: 30px 0;">
          <a href="https://trymagically.com/billing" style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Renew Now with 20% Off</a>
        </div>

        <p style="font-size: 14px; color: #6b7280;">This offer is exclusive to you and cannot be combined with other promotions. The discount will be applied to your next billing cycle only.</p>

        <p>Thank you for being a valued member of our community!</p>
        <p>Best regards,<br>The Magically Team</p>
      </div>
    `,
    createdAt: new Date().toISOString(),
  }
];

export async function GET(request: NextRequest) {
  try {
    // Check admin access
    const authError = await adminGuard();
    if (authError) return authError;

    return NextResponse.json({ templates: EMAIL_TEMPLATES });
  } catch (error) {
    console.error('Error fetching email templates:', error);
    return NextResponse.json({ error: 'Failed to fetch email templates' }, { status: 500 });
  }
}
