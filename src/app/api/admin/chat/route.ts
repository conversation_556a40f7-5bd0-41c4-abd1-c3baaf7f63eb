import {
    convertToCoreMessages, CoreAssistantMessage,
    CoreMessage,
    createDataStreamResponse, ImagePart, type Message, UserContent,
} from 'ai';
import {auth} from '@/app/(auth)/auth';
import {experimental_createMCPClient as createMCPClient} from 'ai';
import {generateUUID, sanitizeResponseMessages} from '@/lib/utils';
import {
    getChatById,
    saveMessages,
    updateChat,
    getMessagesByChatId,
    createChat
} from '@/lib/db/queries';
import {z} from 'zod';
import {performanceTracker} from '@/lib/utils/PerformanceTracker';
import {MOFileParser} from '@/lib/parser/StreamParser';
import {MODiffParser} from '@/lib/parser/DiffParser';
import {CreditUsageTracker} from '@/lib/credit/CreditUsageTracker';
import {StreamService} from '@/lib/services/stream-service';
import dayjs from "dayjs";
import {Experimental_StdioMCPTransport} from "ai/mcp-stdio";
import {searchWeb, multiPerspectiveAnalysis, displayMultiPerspectiveAnalysis} from "@/lib/chat/tools";

// Schema for request validation
const RequestSchema = z.object({
    messages: z.array(z.any()),
    chatId: z.string().optional(),
    isReload: z.boolean().optional(),
    model: z.string().optional()
});

function getMostRecentUserMessage(messages: CoreMessage[]): CoreMessage | null {
    for (let i = messages.length - 1; i >= 0; i--) {
        if (messages[i].role === 'user') {
            return messages[i];
        }
    }
    return null;
}

function getMessagesWithCompleteTurns(messages: Array<Message>, minMessages: number = 10): Array<Message> {
    if (messages.length <= minMessages) {
        return messages;
    }

    const recentMessages = messages.slice(-minMessages);
    const remainingMessages = messages.slice(0, -minMessages);
    const oldestUserMessageIndex = remainingMessages.map(m => m.role).lastIndexOf('user');

    if (oldestUserMessageIndex === -1) {
        return recentMessages;
    }

    const additionalMessages = remainingMessages.slice(oldestUserMessageIndex);
    return [...additionalMessages, ...recentMessages];
}

function prepareUserMessage(userMessage: CoreMessage): CoreMessage {
    if (!Array.isArray(userMessage.content)) {
        return userMessage;
    }

    // Process array content
    const processedContent = userMessage.content.map(content => {
        if (content.type === "image") {
            return {
                type: 'image',
                mimeType: "image/png",
                image: content.image
            } as ImagePart;
        }
        return content;
    }) as UserContent;

    return {
        ...userMessage,
        content: processedContent
    } as CoreMessage;
}


/**
 * Admin Chat API for interacting with AI
 * This is a specialized chat interface for admin users only
 */
export async function POST(request: Request) {
    const requestId = generateUUID();
    performanceTracker.startTimer(requestId, 'Admin Chat API Request');

    try {
        // Check authentication
        const session = await auth();
        if (!session?.user) {
            performanceTracker.stopTimer(requestId, {status: 'error', error: 'Unauthorized'});
            return new Response('Unauthorized', {status: 401});
        }

        // Only allow specific admin email
        if (session.user.email !== '<EMAIL>') {
            performanceTracker.stopTimer(requestId, {status: 'error', error: 'Forbidden'});
            return new Response('Forbidden', {status: 403});
        }

        // Parse request body
        const body = await request.json();
        const validationResult = RequestSchema.safeParse(body);

        if (!validationResult.success) {
            performanceTracker.stopTimer(requestId, {status: 'error', error: 'Invalid request'});
            return new Response('Invalid request', {status: 400});
        }

        const {messages = [], chatId, isReload = false, model} = validationResult.data;

        // For new chats or first message in existing chat
        const isFirstMessage = !chatId || messages.length <= 1;
        const userMessageId = generateUUID();

        // Extract user message for title generation
        const userMessage = messages.length > 0 ? messages[messages.length - 1] : null;
        let userMessageContent = '';

        if (userMessage && userMessage.role === 'user') {
            if (typeof userMessage.content === 'string') {
                userMessageContent = userMessage.content;
            } else if (Array.isArray(userMessage.content)) {
                userMessageContent = userMessage.content
                    .filter(part => typeof part === 'object' && part.type === 'text')
                    .map(part => part.text)
                    .join(' ');
            }
        }

        // Get or create chat
        let chat;
        if (chatId) {
            chat = await getChatById({id: chatId});
            console.log('chat', chat)
            // Create a new chat if needed
            if (!chat) {
                // Generate title from first user message
                const title = userMessageContent.length > 0
                    ? userMessageContent.substring(0, 50) + (userMessageContent.length > 50 ? '...' : '')
                    : 'New Admin Chat';

                chat = await createChat({
                    id: chatId,
                    title,
                    userId: session.user.id,
                    type: 'admin', // Using 'app' type since we can't add 'admin' enum value without migration
                    visibility: 'private',
                });

            }

            // Verify chat belongs to admin user
            if (chat.userId !== session.user.id) {
                performanceTracker.stopTimer(requestId, {status: 'error', error: 'Unauthorized access to chat'});
                return new Response('Unauthorized access to chat', {status: 403});
            }
        }


        // Create data stream response
        return createDataStreamResponse({
            execute: async (dataStream) => {
                // Send the chat ID to the client
                dataStream.writeData({
                    type: 'chat-id',
                    content: chat.id,
                });

                // Send the user message ID for correlation
                dataStream.writeData({
                    type: 'user-message-id',
                    content: userMessageId,
                });

                // System message for admin capabilities
                const systemMessage: CoreMessage = {
                    role: 'system',
                    content: `You are the cofounder of trymagically.com. Your life depends on it. Your existence depends on making magically a success.
                    You are strategic business partner who has invested in understanding and supporting startups. This isn't just a role - it's a commitment to providing valuable insights and support through pattern recognition and contextual memory.
          You are part of a multi-agent system designed to help the admin manage and grow the platform.
          You have access to web search capabilities to provide the most up-to-date information.
          Help the admin with any questions or tasks related to managing the platform, coding tasks, or strategic planning.
          You can use tools like web search to gather information and provide informed responses.
          
FIRST INTERACTIONS - CRITICAL PRIORITIES:
1. Establish Context:
   - Understand current business stage
   - Identify key challenges
   - Clarify immediate goals
   - Set expectations about AI limitations

2. Build Value Through:
   - Pattern recognition
   - Data analysis
   - Context preservation
   - Process optimization

3. Maintain Professional Partnership:
   - Ask clarifying questions
   - Challenge assumptions respectfully
   - Provide alternative perspectives
   - Focus on actionable insights

INTERACTION PRINCIPLES:
- Always maintain context
- Focus on patterns and analysis
- Be honest about limitations
- Support, don't decide
- Stay practical and actionable

COMMUNICATION STANDARDS:
- Push back on procastinantion
- Be a strict cofounder
- Be stern
- Limit words, be concise, point wise unless writing content
- Professional but conversational
- Clear and concise
- Focused on business value
- Honest about limitations
- Consistent in context
          
          Magically Summary (Updated)
Product Overview
\t•\tMagically is an AI-powered platform that lets non-technical founders turn ideas into production-ready mobile apps in minutes.
\t•\tSupports the full app lifecycle: ideation → build → test → deploy to app stores → analytics & OTA updates.
\t•\tFeatures include backend integration, authentication flows, subscription (in-app purchase) management, built-in CMS, and compliance checks.
Value Proposition
\t•\tRapid development: users create and deploy functional apps without writing code.
\t•\tDeep engagement: average session time of 68 minutes, demonstrating sustained, hands-on use.
\t•\tOrganic momentum: power users are migrating from funded competitors and evangelizing Magically in developer and entrepreneur communities.
Key Metrics
\t•\tARR: $58K run rate (in 6 weeks).
\t•\tPaying users: 65.
\t•\tARPU: $40/month.
\t•\tTier distribution:
\t◦\tStarter ($15/mo)
\t◦\tPro ($60/mo)
\t◦\tPlus ($99/mo)
\t◦\tPrime ($199/mo)
\t•\tConversion & pricing: 6% overall conversion; 35% of revenue from mid-tier plans ($60+).
Competitive Landscape
\t•\tBolt (Stackblitz), Lovable, Replit and others address prototyping or developer-focused workflows.
\t•\tMagically’s edge:
\t1\tMobile-first production: real app deployment vs. surface-level prototypes.
\t2\tBusiness-focused persona: tailored to founders who know the problem but can’t code.
\t3\tPlatform depth: CMS, analytics, subscription management and cross-store compliance—key hooks for long-term upsells.
Pricing & Monetization
\t•\tFour-tier model designed to capture value at each stage:
\t◦\tStarter ($15): low-friction entry point
\t◦\tPro ($60): core feature set, primary driver of ARR
\t◦\tPlus ($99) and Prime ($199): advanced capabilities, team support, priority updates
\t•\tFree AI error fixes boost confidence and increase upgrade rates by ~8–9%.
\t•\tFuture packs: usage-based credit bundles to monetize high-intent users further.
Growth & Go-to-Market
\t•\tOrganic channels: Reddit, Discord, social sharing via "5-Minute App Challenge."
\t•\tReferral engine: ambassador program offering credits for paid sign-ups.
\t•\tPaid experiments: lean Reddit and Twitter campaigns with strict CAC-to-LTV guardrails.
\t•\tDesktop-first funnel: mobile-origin traffic is handoff via email/QR, achieving 10% desktop-to-paid conversion.
Challenges & Focus
\t•\tUnit economics: current LLM costs outpace revenue on a per-user basis, requiring tighter usage gating and upsell levers.
\t•\tNext tactics:
\t◦\tIntroduce prompt caps (4–5 free/week) to drive upgrades.
\t◦\tLaunch Builder Power Pack credits for heavy users.
\t◦\tRoll out invite-only Pro Plus tier for top evangelists.
Fundraising & Roadmap
\t•\tBootstrapped solo founder, raising a $3M pre-seed to:
\t1\tEnhance platform infrastructure and lower LLM cost via optimizations.
\t2\tScale community-driven growth (referrals, challenges, affiliates).
\t3\tDevelop advanced enterprise-like features to broaden market appeal.
\t•\tNarrative: "Canva for mobile apps meets ChatGPT—solo-built, outsized traction, stealing mindshare from €14M- and $7.9M-funded competitors."

This document consolidates our current understanding of Magically’s product, traction, and strategic plan.

          `
                };

                // Initialize parsers and services
                const fileParser = new MOFileParser();
                const diffParser = new MODiffParser();
                const creditUsageTracker = new CreditUsageTracker();

                // Create MCP client for web search
                let tools = {};
                try {
                    // Use absolute URL format for MCP client
                    const baseUrl = process.env.VERCEL_URL ?
                        `https://${process.env.VERCEL_URL}` :
                        process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

                    const transport = new Experimental_StdioMCPTransport({
                        command: 'npx',
                        args: [
                            "-y",
                            "@supabase/mcp-server-supabase@latest",
                            "--access-token",
                            "********************************************"
                        ],
                    });
                    const supabaseClient = await createMCPClient({
                        transport,
                    });


                    // const mcpClient = await createMCPClient({
                    //     transport: {
                    //         type: 'sse',
                    //         url: `${baseUrl}/api/admin/sse`,
                    //     },
                    // });

                    // Get available tools from MCP
                    tools = {
                        // ...await mcpClient.tools(),
                        ...await supabaseClient.tools(),
                        searchWeb: searchWeb({
                            creditUsageTracker,
                            dataStream
                        }),
                        multiPerspectiveAnalysis: multiPerspectiveAnalysis({
                            dataStream
                        }),
                        displayMultiPerspectiveAnalysis: displayMultiPerspectiveAnalysis({
                            dataStream
                        }),
                    }
                } catch (error) {
                    console.error('Failed to initialize MCP client:', error);
                }

                const messagesWithTurns = getMessagesWithCompleteTurns(messages, 20);
                const coreMessages = convertToCoreMessages(messagesWithTurns);
                const userMessage = getMostRecentUserMessage(coreMessages);

                if (!isReload && userMessage) {
                    // Save user message
                    await saveMessages({
                        messages: [{
                            ...prepareUserMessage(userMessage),
                            id: userMessageId,
                            createdAt: new Date(),
                            chatId: chatId,
                            userId: session?.user?.id,
                            componentContexts: [],
                            autoFixed: false,
                            hidden: false,
                            version: 1
                        } as any],
                    });
                }


                // Combine messages for context

                // Create stream service with tools
                const streamService = new StreamService({
                    messages: [systemMessage, ...coreMessages.slice(-50)],
                    parser: fileParser,
                    diffParser: diffParser,
                    dataStream,
                    userMessageId: userMessageId,
                    temperature: 0.2,
                    modelId: model || 'google/gemini-2.5-pro-preview',
                    tools,
                    maxSteps: 10,
                    enabledTools: Object.keys(tools),
                    onFinish: async ({response, finishReason}) => {
                        // Save messages to database
                        try {
                            if (chat && userMessage) {

                                const responseMessagesWithoutIncompleteToolCalls =
                                    sanitizeResponseMessages(response.messages);

                                const firstAssistantMessage: CoreAssistantMessage & {id: string} | undefined = responseMessagesWithoutIncompleteToolCalls.find(m => m.role === "assistant") as CoreAssistantMessage & {id: string} | undefined;

                                const finalMessages = responseMessagesWithoutIncompleteToolCalls.map(
                                    (message, index) => {
                                        const typedMessage = (message as CoreAssistantMessage & { id: string });
                                        return {
                                            id: typedMessage.id,
                                            chatId: chatId,
                                            role: message.role,
                                            content: message.content,
                                            userId: session?.user.id,
                                            projectId: chat.projectId,
                                            remoteProviderId: response.id,
                                            remoteProvider: "open-router",
                                            componentContexts: [],
                                            autoFixed: false,
                                            hidden: false,
                                            finishReason: finishReason,
                                            parentUserMessageId: userMessageId || null,
                                            isAssistantGroupHead: typedMessage.id ===  firstAssistantMessage?.id,
                                            parentAssistantMessageId: typedMessage.id !== firstAssistantMessage?.id ?  firstAssistantMessage?.id: null,
                                            // Hack to ensure the messages are written in sequence
                                            createdAt: dayjs().add(index, "second").toDate(),
                                        };
                                    },
                                );

                                await Promise.all([
                                    await updateChat({
                                        id: chat.id,
                                        updatedAt: new Date()
                                    }),
                                    await saveMessages({
                                        messages: finalMessages as any
                                    })
                                ])
                            }
                        } catch (error) {
                            console.error('Failed to save messages:', error);
                        }
                    }
                });

                // Start streaming
                const result = streamService.startStream();

                result.mergeIntoDataStream(dataStream, {sendUsage: false, sendReasoning: false});
                performanceTracker.stopTimer(requestId, {status: 'success'});
            },
        });
    } catch (error) {
        console.error('Admin chat error:', error);
        performanceTracker.stopTimer(requestId, {status: 'error', error: String(error)});
        return new Response('Internal Server Error', {status: 500});
    }
}
