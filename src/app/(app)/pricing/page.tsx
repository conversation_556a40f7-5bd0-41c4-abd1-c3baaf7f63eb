'use client';

import { useRouter, usePathname } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, Sparkles, Rocket, Building2, Loader2, Zap, CreditCard, BarChart3 } from 'lucide-react';
import { useSession, signIn } from 'next-auth/react';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import useSWR from 'swr';
import { fetcher } from '@/lib/utils';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { PricingFAQs } from '@/components/pricing/pricing-faqs';

import { PLANS } from '@/lib/subscription/plans';
import type { Plan as SubscriptionPlan, PlanTier } from '@/lib/subscription/plans';
import { trackSubscriptionEvent } from '@/lib/analytics/track';

interface PlanDisplay extends SubscriptionPlan {
  icon: React.ComponentType<{ className?: string }>;
  displayPrice: string;
}

const planIcons: { [K in PlanTier]: React.ComponentType<{ className?: string }> } = {
  anonymous: Sparkles,
  free: Sparkles,
  starter: Rocket,
  pro: Building2,
  plus: Building2,
  prime: Building2,
};

const displayPlans: PlanDisplay[] = PLANS.filter(plan => !plan.hidden).map(plan => ({
  ...plan,
  icon: planIcons[plan.tier],
  displayPrice: `$${plan.price}`
}));

interface SubscriptionStatus {
  isActive: boolean;
  planTier: PlanTier;
  planName: string;
  expiresAt?: string | null;
  credits: {
    total: number;
    used: number;
    remaining: number;
  };
}

export default function PricingPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
  const [userPlanTier, setUserPlanTier] = useState<PlanTier>('free');

  const pathname = usePathname();

  
  // Fetch subscription status using SWR
  const { data: subscriptionStatus, error: subscriptionError } = useSWR<SubscriptionStatus>(
    session?.user ? '/api/subscription/status' : null,
    fetcher
  );
  
  // Format large numbers with k suffix
  const formatNumber = (num: number) => {
    return num >= 1000 ? `${(num / 1000).toFixed(1)}k` : num.toString();
  };
  
  // Update userPlanTier when subscription status changes
  useEffect(() => {
    if (subscriptionStatus?.planTier) {
      setUserPlanTier(subscriptionStatus.planTier);
    }
  }, [subscriptionStatus]);
  
  // Track pricing page view
  useEffect(() => {
    // Track when pricing page is viewed
    trackSubscriptionEvent('PRICING_VIEWED', {
      current_plan: userPlanTier,
      entry_point: 'pricing_page'
    });
  }, [userPlanTier]);

  const handleUpgrade = async (planId: string) => {
    // Get plan details for tracking
    const planDetails = PLANS.find(plan => plan.id === planId);

    if (status !== "authenticated") {
      // Track unauthenticated upgrade attempt
      trackSubscriptionEvent('UPGRADE_INITIATED', {
        current_plan: 'anonymous',
        plan_type: planDetails?.tier || planId,
        entry_point: 'pricing_page',
        trigger_reason: 'unauthenticated'
      });
      
      // Redirect to login page with callback to pricing
      signIn(undefined, { callbackUrl: pathname as string });
      return;
    }
    try {
      setLoadingPlan(planId);
      
      // Track upgrade initiation
      trackSubscriptionEvent('UPGRADE_INITIATED', {
        current_plan: userPlanTier,
        plan_type: planDetails?.tier || planId,
        price: planDetails?.price || 0,
        currency: 'USD',
        entry_point: 'pricing_page'
      });
      
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          returnUrl: pathname,
          planId
        })
      });

      const data = await response.json();
      
      if (data.error) {
        // Track upgrade failure
        trackSubscriptionEvent('UPGRADE_FAILED', {
          current_plan: userPlanTier,
          plan_type: planDetails?.tier || planId,
          error_message: data.error
        });
        
        throw new Error(data.error);
      }

      // If this was an upgrade that was processed directly (no checkout URL)
      if (data.upgraded) {
        // Track successful upgrade
        trackSubscriptionEvent('UPGRADE_COMPLETED', {
          current_plan: userPlanTier,
          plan_type: planDetails?.tier || planId,
          price: planDetails?.price || 0,
          currency: 'USD'
        });
        
        toast.success(data.message || 'Plan upgraded successfully!');
        // Refresh to update UI with new plan
        window.location.reload();
        return;
      }
      
      // Redirect to LemonSqueezy checkout for new subscriptions
      window.location.href = data.url;
    } catch (error) {
      console.error('Checkout error:', error);
      
      // Track upgrade error
      trackSubscriptionEvent('UPGRADE_FAILED', {
        current_plan: userPlanTier,
        plan_type: planDetails?.tier || planId,
        error_message: error instanceof Error ? error.message : 'Unknown error'
      });
      
      toast.error('Failed to process upgrade');
      setLoadingPlan(null);
    }
  };

  const filteredDisplayPlans = displayPlans
      // Filter out plans that are lower tier than the user's current plan
      .filter(plan => {
        if (!session?.user) return true; // Show all plans for non-logged in users

        // Get indices to determine if plan is higher tier
        const userPlanIndex = PLANS.findIndex(p => p.tier === userPlanTier);
        const planIndex = PLANS.findIndex(p => p.tier === plan.tier);

        // Show current plan and higher tier plans only
        return planIndex >= userPlanIndex;
      });

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
      <div className="container py-16 mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-10 space-y-6">
          <div className="space-y-4">
            <h1 className="text-5xl font-bold bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
              Choose Your AI Superpower
            </h1>
            <h2 className="text-2xl font-medium text-accent/90">
              No hidden fees. No surprises.
            </h2>
          </div>
          <p className="text-lg text-muted-foreground/90 max-w-2xl mx-auto leading-relaxed">
            Transform your development workflow with AI-powered code generation, refactoring,
            and deployment — all in one platform.
          </p>
        </div>
        
        {/* Current Subscription Status */}
        {session?.user && subscriptionStatus && (
          <div className="max-w-md mx-auto mb-8 bg-card/80 backdrop-blur-sm border border-border/30 rounded-md p-4 shadow-sm">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-1.5">
                {subscriptionStatus.planTier === 'prime' ? (
                  <Sparkles className="h-4 w-4 text-yellow-400" />
                ) : subscriptionStatus.planTier === 'plus' ? (
                  <Sparkles className="h-4 w-4 text-yellow-400" />
                ) : subscriptionStatus.planTier === 'pro' ? (
                  <Zap className="h-4 w-4 text-purple-400" />
                ) : subscriptionStatus.planTier === 'starter' ? (
                  <CreditCard className="h-4 w-4 text-blue-400" />
                ) : (
                  <BarChart3 className="h-4 w-4 text-gray-400" />
                )}
                <span className="text-sm font-medium capitalize">{subscriptionStatus.planName}</span>
                <span className="text-xs px-1.5 py-0.5 bg-muted rounded-full ml-1">Current</span>
              </div>
              
              {subscriptionStatus.expiresAt && (
                <div className="text-xs text-muted-foreground">
                  Renews {new Date(subscriptionStatus.expiresAt).toLocaleDateString('en-US', {month: 'short', day: 'numeric'})}
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">Credits/Messages</span>
                <div className="font-medium">
                  <span className="text-sm">{formatNumber(subscriptionStatus.credits.remaining)}</span>
                  <span className="text-xs text-muted-foreground"> / {formatNumber(subscriptionStatus.credits.total)}</span>
                </div>
              </div>
              
              <Progress value={Math.min(100, Math.round((subscriptionStatus.credits.used / subscriptionStatus.credits.total) * 100))} className="h-1.5">
                <div 
                  className={`h-full ${subscriptionStatus.planTier === 'plus' ? 'bg-yellow-400' : 
                    subscriptionStatus.planTier === 'pro' ? 'bg-purple-400' : 
                    subscriptionStatus.planTier === 'starter' ? 'bg-blue-400' : 'bg-gray-400'}`} 
                  style={{ width: `${Math.min(100, Math.round((subscriptionStatus.credits.used / subscriptionStatus.credits.total) * 100))}%` }} 
                />
              </Progress>
              
              {subscriptionStatus.credits.remaining < subscriptionStatus.credits.total * 0.2 ? (
                <p className="text-xs text-yellow-500">
                  Running low on credits. Consider upgrading to a higher plan.
                </p>
              ) : null}
            </div>
          </div>
        )}

        <div className={`grid md:grid-cols-4 gap-5 mx-auto`}>
          {filteredDisplayPlans
            .map((plan) => (
            <Card 
              key={plan.name} 
              className={`flex flex-col relative backdrop-blur-sm bg-background/50 border-muted-foreground/20 hover:border-primary/30 transition-all duration-300 ${plan.tier === 'pro' ? 'md:-mt-4 md:mb-4' : ''}`}
            >
              {plan.isPopular && (
                <Badge 
                  className="absolute -top-3 left-1/2 -translate-x-1/2 px-4 py-1 bg-accent text-accent-foreground"
                >
                  Most Popular
                </Badge>
              )}
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <plan.icon className="h-6 w-6 text-primary" />
                  <CardTitle className="text-2xl capitalize">{plan.name}</CardTitle>
                </div>
                
              </CardHeader>
              <CardContent className="flex-1">
                <div className="mb-6">
                  <div className="flex items-baseline gap-1">
                    <span className="text-4xl font-bold">{plan.displayPrice}</span>
                    {plan.tier !== 'free' && <span className="text-muted-foreground">/month</span>}
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    {plan.operations} messages/month
                  </div>
                </div>
                <ul className="space-y-3">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-start gap-2">
                      <Check className="h-5 w-5 text-primary mt-0.5 shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button 
                  className="w-full h-12 text-base" 
                  variant={plan.tier === 'free' ? 'outline' : 'default'}
                  disabled={loadingPlan !== null || plan.tier === userPlanTier}
                  onClick={() => handleUpgrade(plan.id)}
                >
                  {loadingPlan === plan.id ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : plan.tier === userPlanTier ? (
                    <>Current Plan</>
                  ) : (
                    <>Upgrade to {plan.name}</>
                  )}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
        
        {/* Pricing FAQs */}
        <PricingFAQs />
      </div>
    </div>
  );
}
