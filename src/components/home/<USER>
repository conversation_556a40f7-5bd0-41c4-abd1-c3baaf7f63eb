'use client';

import { motion } from 'framer-motion';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ArrowR<PERSON>, Star, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';
import CreatorCount from "@/components/home/<USER>";
import { PLANS } from '@/lib/subscription/plans';
import {LinkButton} from "@/components/ui/link-button";

export function CTASection() {
  return (
    <section className="py-16 px-4 md:py-24 w-full relative overflow-hidden bg-gradient-to-br from-primary/10 to-accent/20">
      {/*<div className="absolute -top-40 -left-40 w-80 h-80 bg-primary/20 rounded-full blur-3xl"></div>*/}
      {/*<div className="absolute -bottom-40 -right-40 w-80 h-80 bg-accent/20 rounded-full blur-3xl"></div>*/}
      <div className="max-w-7xl mx-auto relative">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-accent/10 to-primary/10 rounded-3xl blur-3xl opacity-30" />
        
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className={cn(
            "rounded-3xl overflow-hidden border border-white/20 shadow-xl",
            "bg-gradient-to-br from-background/40 to-background/30",
            "backdrop-blur-xl p-8 md:p-12 relative"
          )}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Ready to bring your app idea to life?
              </h2>
              <p className="text-muted-foreground mb-6 md:text-lg">
                Join thousands of mobile entrepreneurs who have already built amazing apps with magically.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  size="lg" 
                  className="bg-accent hover:bg-accent/90 text-accent-foreground"
                >
                  Get Started Free
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                {/*<Button */}
                {/*  size="lg" */}
                {/*  variant="outline"*/}
                {/*  className="border-primary/20 hover:bg-primary/5"*/}
                {/*>*/}
                {/*  See Examples*/}
                {/*</Button>*/}
              </div>
              
              <div className="mt-6 flex items-center">
                <div className="flex -space-x-2">
                  <CreatorCount/>

                </div>
                {/*<div className="ml-4">*/}
                {/*  <div className="flex items-center">*/}
                {/*    {[1, 2, 3, 4, 5].map((i) => (*/}
                {/*      <Star */}
                {/*        key={i} */}
                {/*        className="h-4 w-4 fill-amber-400/80 stroke-amber-400/80" */}
                {/*      />*/}
                {/*    ))}*/}
                {/*  </div>*/}
                {/*</div>*/}
              </div>
            </div>
            
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 rounded-xl blur-xl opacity-20" />
              <div className={cn(
                "rounded-xl overflow-hidden border border-white/20",
                "bg-background/30 backdrop-blur-xl p-6 relative shadow-lg"
              )}>
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center">
                    <div className="h-10 w-10 rounded-full bg-primary flex items-center justify-center text-primary-foreground">
                      <Zap className="h-5 w-5" />
                    </div>
                    <div className="ml-3">
                      <h4 className="font-medium">magically Starter + 3 other plans</h4>
                      {/*<p className="text-xs text-muted-foreground">Most popular</p>*/}
                    </div>
                  </div>
                  {/*<div className="bg-accent/10 text-accent rounded-full px-3 py-1 text-xs font-medium">*/}
                  {/*  15% Extra Credits*/}
                  {/*</div>*/}
                </div>
                
                <div className="mb-4">
                  <div className="flex items-end">
                    <span className="text-3xl font-bold">${PLANS.find(p => p.tier === 'starter')?.price}</span>
                    <span className="text-muted-foreground ml-1">/month</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    <span className="font-medium">{Math.round(PLANS.find(p => p.tier === 'starter')!.operations)}</span> credits included
                  </p>
                </div>
                
                <ul className="space-y-2 mb-6">
                  {
                    PLANS.find(p => p.tier === 'starter')!.features.map((feature, i) => (
                    <li key={i} className="flex items-center">
                      <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center mr-2">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M10 3L4.5 8.5L2 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <LinkButton href={'/pricing'} className="w-full bg-primary hover:bg-primary/90 text-primary-foreground">
                  Explore all plans
                  <ArrowRight className="ml-2 h-4 w-4" />
                </LinkButton>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
