'use client';

import Link from 'next/link';
import {
  Github,
  HelpCircle,
  FileText,
  Lock,
  DollarSign,
  MessageCircle,
  Linkedin,
  TwitterIcon,
  XIcon
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import MagicallyLogo from '@/components/logo';
import { DiscordIcon } from '@/components/icons/discord';
import {useSession} from "next-auth/react";

export function Footer() {

  const { data: session, status } = useSession();

  return (
    <footer className="bg-black py-16 md:py-20 w-full">
      <div className="container mx-auto px-4 md:px-6 text-sm text-muted-foreground">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-x-8 gap-y-10 pb-12 border-b border-gray-800">
          {/* Column 1: Company & Logo */}
          <div className="flex flex-col space-y-4 mb-4 sm:mb-0 items-center sm:items-start">
            <div className="mb-2">
              <MagicallyLogo asLink/>
            </div>
            <p className="text-sm leading-relaxed opacity-70 text-center sm:text-left mx-8 md:mx-0">
              Build beautiful apps without code using AI. Turn your idea into reality in minutes.
            </p>
            <div className="flex space-x-4 pt-2">
              <Link
                  href="https://discord.gg/Cpda56yVYY"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center hover:text-white transition-colors"
                  aria-label="Discord"
              >
                <DiscordIcon className="h-5 w-5" />
              </Link>
              <Link 
                href="https://github.com/magically-life"
                target="_blank"
                rel="noopener noreferrer" 
                className="flex items-center hover:text-white transition-colors"
                aria-label="GitHub"
              >
                <Github className="h-5 w-5" />
              </Link>
              <Link 
                href="https://www.linkedin.com/company/magically-life"
                target="_blank"
                rel="noopener noreferrer" 
                className="flex items-center hover:text-white transition-colors"
                aria-label="LinkedIn"
              >
                <Linkedin className="h-5 w-5" />
              </Link>
              <Link
                href="https://x.com/magically_life"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center hover:text-white transition-colors"
                aria-label="Twitter"
              >
                <XIcon className="h-5 w-5" />
              </Link>
            </div>
            <a href="https://www.producthunt.com/products/magically-2?embed=true&utm_source=badge-featured&utm_medium=badge&utm_source=badge-magically&#0045;2" target="_blank"><img src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=977625&theme=light&t=1749754814360" alt="magically - Build&#0032;mobile&#0032;app&#0032;businesses&#0032;without&#0032;coding&#0032;using&#0032;AI | Product Hunt" style={{width: 250, height: 54}} width="250" height="54" /></a>
          </div>
          
          {/* Column 2: Product */}
          <div className="flex flex-col space-y-3 items-center sm:items-start">
            <h3 className="text-white text-base font-medium mb-2">Product</h3>
            <Link href="/pricing" className="hover:text-white transition-colors">
              Pricing
            </Link>
            <Link href="/faq" className="hover:text-white transition-colors">
              FAQ
            </Link>
            <Link href="/contact" className="hover:text-white transition-colors">
              Contact
            </Link>
          </div>
          
          {/* Column 3: Resources */}
          <div className="flex flex-col space-y-3 items-center sm:items-start">
            <h3 className="text-white text-base font-medium mb-2">Resources</h3>
            <Link 
              href="https://checkout.trymagically.com/affiliates"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-white transition-colors"
            >
              Affiliates
            </Link>
            <Link 
              href="https://discord.gg/Cpda56yVYY"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-white transition-colors"
            >
              Community
            </Link>
            <Link href="/terms" className="hover:text-white transition-colors">
              Terms
            </Link>
            <Link href="/privacy" className="hover:text-white transition-colors">
              Privacy
            </Link>
          </div>
          
          {/* Column 4: Company Address */}
          <div className="flex flex-col space-y-3 items-center sm:items-start">
            <h3 className="text-white text-base font-medium mb-2">Company</h3>
            <address className="not-italic text-sm leading-relaxed opacity-70 text-center sm:text-left">
              magicalai Inc.<br />
              2261 Market Street STE 85290<br />
              San Francisco, CA 94114
            </address>
          </div>
        </div>
        
        {/* Copyright section */}
        <div className="py-8 flex flex-col md:flex-row justify-between items-center text-center md:text-left">
          <p className="text-xs opacity-60 mb-4 md:mb-0">
            © {new Date().getFullYear()} magicalai Inc. All rights reserved.
          </p>
          <div className="flex justify-center md:justify-end space-x-6">
            <Link href="/terms" className="text-xs opacity-60 hover:opacity-100 transition-opacity">
              Terms of Service
            </Link>
            <Link href="/privacy" className="text-xs opacity-60 hover:opacity-100 transition-opacity">
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
