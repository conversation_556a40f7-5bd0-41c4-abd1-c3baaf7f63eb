File: constants/colors.ts (Lines 2-33)
Type: unknown
Symbols: COLORS

export const COLORS = {
// Primary palette
primary: '#2196F3',
success: '#10B981',
warning: '#F59E0B',
error: '#EF4444',

// Journey colors
blue: '#2196F3',
green: '#10B981',
purple: '#8B5CF6',
orange: '#F97316',
red: '#EF4444',
teal: '#14B8A6',
pink: '#EC4899',
indigo: '#6366F1',
amber: '#F59E0B',
gray: '#6B7280',

// Gray scale
gray50: '#F9FAFB',
gray100: '#F3F4F6',
gray200: '#E5E7EB',
gray400: '#9CA3AF',
gray500: '#6B7280',
gray700: '#374151',
gray900: '#1F2937',

// Background
background: '#FFFFFF',
surface: '#F9FAFB'
};

---

File: screens/CreateJourneyScreen.tsx (Lines 22-185)
Type: component
Symbols: CreateJourneyScreen

export default function CreateJourneyScreen({ navigation, route }: CreateJourneyScreenProps) {
const journeyId = route.params?.journeyId;
const isEditing = !!journeyId;

const getJourneyById = useJourneyStore(state => state.getJourneyById);
const existingJourney = isEditing ? getJourneyById(journeyId) : null;

const [title, setTitle] = useState(existingJourney?.title || '');
const [description, setDescription] = useState(existingJourney?.description || '');
const [selectedColor, setSelectedColor] = useState(existingJourney?.color || COLORS.primary);
const [status, setStatus] = useState<'active' | 'done'>(existingJourney?.status || 'active');
const [startDate] = useState(existingJourney?.startDate || new Date());

const createJourney = useJourneyStore(state => state.createJourney);
const updateJourney = useJourneyStore(state => state.updateJourney);

const isValid = title.trim().length > 0;

const handleSubmit = () => {
if (!isValid) return;

    if (isEditing && journeyId) {
      updateJourney(journeyId, {
        title: title.trim(),
        description: description.trim() || undefined,
        color: selectedColor,
        status
      });
    } else {
      createJourney({
        title: title.trim(),
        description: description.trim() || undefined,
        color: selectedColor,
        startDate,
        status: 'active'
      });
    }

    navigation.goBack();
};



return (
<View style={styles.container}>
<View style={styles.header}>
<Pressable
style={styles.closeButton}
onPress={() => navigation.goBack()}
>
<X size={24} color={COLORS.gray700} />
</Pressable>
<Text style={styles.headerTitle}>{isEditing ? 'Edit Journey' : 'Create Journey'}</Text>
<View style={styles.placeholder} />
</View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.label}>
            Title <Text style={styles.required}>*</Text>
          </Text>
          <TextInput
            style={styles.input}
            placeholder="Enter journey title"
            value={title}
            onChangeText={setTitle}
            placeholderTextColor={COLORS.gray400}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder="Describe your journey..."
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
            placeholderTextColor={COLORS.gray400}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Start Date</Text>
          <View style={styles.dateInput}>
            <Text style={styles.dateText}>{formatDisplayDate(startDate)}</Text>
            <Calendar size={20} color={COLORS.gray400} />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Journey Color</Text>
          <View style={styles.colorGrid}>
            {JOURNEY_COLORS.map((color, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.colorOption,
                  { backgroundColor: color },
                  selectedColor === color && styles.selectedColor
                ]}
                onPress={() => setSelectedColor(color)}
              >
                {selectedColor === color && (
                  <Check size={20} color="#FFFFFF" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {isEditing && (
          <View style={styles.section}>
            <Text style={styles.label}>Status</Text>
            <View style={styles.statusContainer}>
              <TouchableOpacity
                style={[
                  styles.statusOption,
                  status === 'active' && styles.selectedStatus
                ]}
                onPress={() => setStatus('active')}
              >
                <Text style={[
                  styles.statusText,
                  status === 'active' && styles.selectedStatusText
                ]}>Active</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.statusOption,
                  status === 'done' && styles.selectedStatus
                ]}
                onPress={() => setStatus('done')}
              >
                <Text style={[
                  styles.statusText,
                  status === 'done' && styles.selectedStatusText
                ]}>Done</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.createButton,
            { backgroundColor: selectedColor },
            !isValid && styles.disabledButton
          ]}
          onPress={handleSubmit}
          disabled={!isValid}
        >
          <Check size={20} color="#FFFFFF" />
          <Text style={styles.createButtonText}>{isEditing ? 'Save Changes' : 'Create Journey'}</Text>
        </TouchableOpacity>
      </View>
    </View>
);
}

---

File: screens/JourneyDetailScreen.tsx (Lines 50-561)
Type: component
Symbols: JourneyDetailScreen

export default function JourneyDetailScreen({ navigation, route }: JourneyDetailScreenProps) {
const { journeyId } = route.params;
const [activeTab, setActiveTab] = useState<'timeline' | 'path' | 'insights'>('timeline');
const [showJourneyMenu, setShowJourneyMenu] = useState(false);
const [showWaypointMenu, setShowWaypointMenu] = useState<string | null>(null);
const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
const [showDeleteJourneyModal, setShowDeleteJourneyModal] = useState(false);
const [showDeleteWaypointModal, setShowDeleteWaypointModal] = useState<string | null>(null);
const [editingWaypoint, setEditingWaypoint] = useState<string | null>(null);

const journey = useJourneyStore(state => state.getJourneyById(journeyId));
const deleteJourney = useJourneyStore(state => state.deleteJourney);
const updateJourney = useJourneyStore(state => state.updateJourney);
const getWaypointsByJourney = useWaypointStore(state => state.getWaypointsByJourney);
const updateWaypoint = useWaypointStore(state => state.updateWaypoint);
const deleteWaypoint = useWaypointStore(state => state.deleteWaypoint);
const waypoints = getWaypointsByJourney(journeyId);

if (!journey) {
navigation.goBack();
return null;
}

const handleEditJourney = () => {
navigation.navigate('CreateJourney', { journeyId });
};

const handleDeleteJourney = () => {
setShowDeleteJourneyModal(true);
};

const confirmDeleteJourney = () => {
deleteJourney(journeyId);
setShowDeleteJourneyModal(false);
navigation.goBack();
};

const handleEditWaypoint = (waypointId: string) => {
setEditingWaypoint(waypointId);
};

const handleDeleteWaypoint = (waypointId: string) => {
setShowDeleteWaypointModal(waypointId);
};

const confirmDeleteWaypoint = (waypointId: string) => {
const waypoint = waypoints.find(w => w.id === waypointId);
if (waypoint) {
deleteWaypoint(waypointId);
// Update journey counts
updateJourney(journeyId, {
waypointCount: journey.waypointCount - 1,
milestoneCount: journey.milestoneCount - (waypoint.isMilestone ? 1 : 0),
updatedAt: new Date()
});
}
setShowDeleteWaypointModal(null);
};

const handleUpdateWaypoint = (data: {
content: string;
date: Date;
sentiment: number;
impact: 'low' | 'medium' | 'high';
isMilestone: boolean;
}) => {
if (editingWaypoint) {
const oldWaypoint = waypoints.find(w => w.id === editingWaypoint);
updateWaypoint(editingWaypoint, data);

      // Update milestone count if changed
      if (oldWaypoint && oldWaypoint.isMilestone !== data.isMilestone) {
        updateJourney(journeyId, {
          milestoneCount: journey.milestoneCount + (data.isMilestone ? 1 : -1),
          updatedAt: new Date()
        });
      }
    }
    setEditingWaypoint(null);
};

// Journey Menu Options
const journeyOptions: DropdownOption[] = [
{
label: 'Edit Journey',
icon: <Edit size={20} color={COLORS.gray700} />,
onPress: handleEditJourney
},
{
label: 'Delete Journey',
icon: <Trash2 size={20} color={COLORS.error} />,
onPress: handleDeleteJourney,
destructive: true
}
];

// Waypoint Menu Options
const getWaypointOptions = (waypointId: string): DropdownOption[] => [
{
label: 'Edit Waypoint',
icon: <Edit size={20} color={COLORS.gray700} />,
onPress: () => handleEditWaypoint(waypointId)
},
{
label: 'Delete Waypoint',
icon: <Trash2 size={20} color={COLORS.error} />,
onPress: () => handleDeleteWaypoint(waypointId),
destructive: true
}
];

// Handle menu button press with position tracking
const handleJourneyMenuPress = (event: any) => {
const { pageX, pageY } = event.nativeEvent;
setMenuPosition({ x: pageX, y: pageY });
setShowJourneyMenu(true);
};

const handleWaypointMenuPress = (waypointId: string, event: any) => {
const { pageX, pageY } = event.nativeEvent;
setMenuPosition({ x: pageX, y: pageY });
setShowWaypointMenu(waypointId);
};



const getSentimentBolts = (sentiment: number) => {
const count = Math.ceil((Math.abs(sentiment) / 100) * 3);
return Array(Math.max(1, count)).fill(0).map((_, i) => (
<Zap
key={i}
size={12}
color={sentiment >= 0 ? COLORS.primary : COLORS.gray400}
fill={sentiment >= 0 ? COLORS.primary : 'none'}
/>
));
};

const getImpactBar = (impact: string) => {
const width = impact === 'low' ? '33%' : impact === 'medium' ? '66%' : '100%';
return (
<View style={styles.impactBarContainer}>
<View style={[styles.impactBar, { width }]} />
</View>
);
};

const renderTabContent = () => {
switch (activeTab) {
case 'timeline':
return (
<ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
{waypoints.map((waypoint) => (
<View
key={waypoint.id}
style={[
styles.waypointCard,
waypoint.isMilestone && styles.milestoneCard
]}
>
<View style={styles.waypointHeader}>
<View style={styles.waypointHeaderLeft}>
<Text style={styles.waypointDate}>
{formatDate(waypoint.date).toUpperCase()}
</Text>
{waypoint.isMilestone && (
<View style={styles.milestoneButton}>
<Text style={styles.milestoneText}>Milestone</Text>
</View>
)}
</View>
<TouchableOpacity
style={styles.waypointMenuButton}
onPress={(event) => {
Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
handleWaypointMenuPress(waypoint.id, event);
}}
>
<MoreVertical size={16} color={COLORS.gray500} />
</TouchableOpacity>
</View>

                <Text style={styles.waypointContent}>
                  {waypoint.content}
                </Text>
                
                <View style={styles.waypointMeta}>
                  <View style={styles.sentimentContainer}>
                    {getSentimentBolts(waypoint.sentiment)}
                  </View>
                  
                  <View style={styles.impactContainer}>
                    <Text style={styles.impactLabel}>–</Text>
                    {getImpactBar(waypoint.impact)}
                    <Text style={styles.impactLabel}>+</Text>
                  </View>
                </View>
              </View>
            ))}
          </ScrollView>
        );
        
      case 'path':
        return (
          <View style={styles.pathContainer}>
            <View style={styles.pathVisualization}>
              <View style={styles.timelineBar}>
                {['Jul 2024', 'Aug 2024', 'Sep 2024', 'Oct 2024', 'Nov 2024'].map((month, index) => (
                  <Text key={index} style={styles.timelineLabel}>{month}</Text>
                ))}
              </View>
              
              <View style={styles.pathArea}>
                {/* Simplified path visualization */}
                {waypoints.slice(0, 8).map((waypoint, index) => {
                  const x = (index / 7) * 100;
                  const y = 50 + (waypoint.sentiment / 200) * 40; // Convert sentiment to y position
                  return (
                    <View
                      key={waypoint.id}
                      style={[
                        styles.pathPoint,
                        {
                          left: `${x}%`,
                          top: `${y}%`,
                          backgroundColor: waypoint.isMilestone ? '#FFD700' : journey.color
                        },
                        waypoint.isMilestone && styles.milestonePoint
                      ]}
                    />
                  );
                })}
              </View>
            </View>
            
            <View style={styles.playbackControls}>
              <View style={styles.progressBar}>
                <View style={[styles.progress, { backgroundColor: journey.color }]} />
              </View>
              
              <View style={styles.controls}>
                <TouchableOpacity style={styles.controlButton}>
                  <SkipBack size={20} color={COLORS.gray700} />
                </TouchableOpacity>
                <TouchableOpacity style={styles.controlButton}>
                  <ChevronLeft size={20} color={COLORS.gray700} />
                </TouchableOpacity>
                <TouchableOpacity style={[styles.playButton, { backgroundColor: journey.color }]}>
                  <Play size={20} color="#FFFFFF" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.controlButton}>
                  <ChevronRight size={20} color={COLORS.gray700} />
                </TouchableOpacity>
                <TouchableOpacity style={styles.controlButton}>
                  <SkipForward size={20} color={COLORS.gray700} />
                </TouchableOpacity>
              </View>
            </View>
            
            {waypoints[0] && (
              <View style={styles.pathWaypointCard}>
                <Text style={styles.pathWaypointDate}>
                  {formatDate(waypoints[0].date)}
                </Text>
                <Text style={styles.pathWaypointTitle}>
                  {waypoints[0].content.length > 50
                    ? waypoints[0].content.substring(0, 50) + '...'
                    : waypoints[0].content}
                </Text>
                <TouchableOpacity style={styles.pathWaypointButton}>
                  <MoreHorizontal size={16} color={COLORS.gray500} />
                </TouchableOpacity>
              </View>
            )}
          </View>
        );
        
      case 'insights':
        const positiveWaypoints = waypoints.filter(w => w.sentiment > 20).length;
        const milestones = waypoints.filter(w => w.isMilestone).length;
        const positivePercentage = waypoints.length > 0 ? Math.round((positiveWaypoints / waypoints.length) * 100) : 0;
        const daysActive = waypoints.length > 0 ? Math.ceil((Date.now() - new Date(waypoints[waypoints.length - 1].date).getTime()) / (1000 * 60 * 60 * 24)) : 0;
        
        return (
          <ScrollView style={styles.insightsContent} showsVerticalScrollIndicator={false}>
            <View style={styles.metricsGrid}>
              <View style={styles.metricCard}>
                <Text style={styles.metricNumber}>{waypoints.length}</Text>
                <Text style={styles.metricLabel}>Waypoints</Text>
              </View>
              
              <View style={styles.metricCard}>
                <View style={styles.metricHeader}>
                  <Text style={styles.metricNumber}>{milestones}</Text>
                  <Star size={16} color="#FFD700" fill="#FFD700" />
                </View>
                <Text style={styles.metricLabel}>Milestones</Text>
              </View>
              
              <View style={styles.metricCard}>
                <Text style={[styles.metricNumber, { color: COLORS.primary }]}>
                  {positivePercentage}%
                </Text>
                <Text style={styles.metricLabel}>Positive</Text>
              </View>
              
              <View style={styles.metricCard}>
                <Text style={styles.metricNumber}>{daysActive}</Text>
                <Text style={styles.metricLabel}>Days Active</Text>
              </View>
            </View>
            
            <View style={styles.chartSection}>
              <Text style={styles.chartTitle}>Sentiment Distribution</Text>
              <View style={styles.donutChart}>
                <Text style={[styles.donutPercentage, { color: COLORS.primary }]}>
                  {positivePercentage}%
                </Text>
                <Text style={styles.donutLabel}>Positive</Text>
              </View>
              
              <View style={styles.chartLegend}>
                <View style={styles.legendItem}>
                  <View style={[styles.legendDot, { backgroundColor: COLORS.primary }]} />
                  <Text style={styles.legendText}>{positiveWaypoints}</Text>
                  <Text style={styles.legendLabel}>Positive</Text>
                </View>
                <View style={styles.legendItem}>
                  <View style={[styles.legendDot, { backgroundColor: COLORS.gray400 }]} />
                  <Text style={styles.legendText}>{waypoints.length - positiveWaypoints - 1}</Text>
                  <Text style={styles.legendLabel}>Neutral</Text>
                </View>
                <View style={styles.legendItem}>
                  <View style={[styles.legendDot, { backgroundColor: COLORS.error }]} />
                  <Text style={styles.legendText}>1</Text>
                  <Text style={styles.legendLabel}>Challenge</Text>
                </View>
              </View>
            </View>
            
            <Text style={styles.monthlyTitle}>Monthly Progress</Text>
            
            <View style={styles.insightCard}>
              <TrendingUp size={20} color={COLORS.primary} />
              <View style={styles.insightContent}>
                <Text style={styles.insightTitle}>Strong Momentum</Text>
                <Text style={styles.insightText}>
                  You've maintained consistent growth with October being your most productive month. Keep this rhythm!
                </Text>
              </View>
            </View>
          </ScrollView>
        );
        
      default:
        return null;
    }
};

return (
<View style={styles.container}>
<View style={[styles.header, { borderBottomColor: journey.color }]}>
<View style={styles.headerTop}>
<TouchableOpacity
style={styles.backButton}
onPress={() => navigation.goBack()}
>
<ArrowLeft size={24} color={COLORS.gray700} />
</TouchableOpacity>

          <View style={styles.headerCenter}>
            <View style={styles.journeyInfo}>
              <View style={[styles.colorCircle, { backgroundColor: journey.color }]} />
              <Text style={styles.journeyTitle}>{journey.title}</Text>
            </View>
            <View style={styles.journeyMeta}>
              <Flag size={14} color={COLORS.gray500} />
              <Text style={styles.metaText}>{journey.waypointCount}</Text>
              <Text style={styles.metaText}>Since {formatDate(journey.startDate)}</Text>
            </View>
          </View>
          
          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.actionButton}>
              <Search size={20} color={COLORS.gray500} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Filter size={20} color={COLORS.gray500} />
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={(event) => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                handleJourneyMenuPress(event);
              }}
            >
              <MoreVertical size={20} color={COLORS.gray500} />
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.tabBar}>
          {[
            { key: 'timeline', label: 'Timeline', icon: Flag },
            { key: 'path', label: 'Path', icon: TrendingUp },
            { key: 'insights', label: 'Insights', icon: TrendingUp }
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tab,
                activeTab === tab.key && { borderBottomColor: journey.color }
              ]}
              onPress={() => setActiveTab(tab.key as any)}
            >
              <tab.icon
                size={16}
                color={activeTab === tab.key ? journey.color : COLORS.gray500}
              />
              <Text
                style={[
                  styles.tabText,
                  activeTab === tab.key && { color: journey.color }
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      <View style={styles.content}>
        {renderTabContent()}
      </View>
      
      <TouchableOpacity
        style={[styles.fab, { backgroundColor: journey.color }]}
        onPress={() => navigation.navigate('AddWaypoint', { journeyId })}
      >
        <Plus size={24} color="#FFFFFF" />
      </TouchableOpacity>

      {/* Journey Dropdown Menu */}
      <DropdownMenu
        visible={showJourneyMenu}
        onClose={() => setShowJourneyMenu(false)}
        options={journeyOptions}
        position={menuPosition}
      />

      {/* Waypoint Dropdown Menu */}
      <DropdownMenu
        visible={!!showWaypointMenu}
        onClose={() => setShowWaypointMenu(null)}
        options={showWaypointMenu ? getWaypointOptions(showWaypointMenu) : []}
        position={menuPosition}
      />

      {/* Delete Journey Confirmation */}
      <ConfirmationModal
        isVisible={showDeleteJourneyModal}
        title="Delete Journey"
        message={`Are you sure you want to delete "${journey.title}"? This will also delete all waypoints and cannot be undone.`}
        confirmText="Delete Journey"
        onConfirm={confirmDeleteJourney}
        onCancel={() => setShowDeleteJourneyModal(false)}
        destructive
      />

      {/* Delete Waypoint Confirmation */}
      <ConfirmationModal
        isVisible={!!showDeleteWaypointModal}
        title="Delete Waypoint"
        message="Are you sure you want to delete this waypoint? This action cannot be undone."
        confirmText="Delete Waypoint"
        onConfirm={() => showDeleteWaypointModal && confirmDeleteWaypoint(showDeleteWaypointModal)}
        onCancel={() => setShowDeleteWaypointModal(null)}
        destructive
      />

      {/* Edit Waypoint Modal */}
      <Modal
        visible={!!editingWaypoint}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.editModalContainer}>
          <View style={styles.editModalHeader}>
            <Pressable 
              style={styles.closeButton}
              onPress={() => setEditingWaypoint(null)}
            >
              <X size={24} color={COLORS.gray700} />
            </Pressable>
            <Text style={styles.editModalTitle}>Edit Waypoint</Text>
            <View style={styles.placeholder} />
          </View>
          
          {editingWaypoint && (
            <WaypointForm
              initialData={waypoints.find(w => w.id === editingWaypoint)}
              onSubmit={handleUpdateWaypoint}
              buttonText="Save Changes"
              journeyColor={journey.color}
            />
          )}
        </View>
      </Modal>
    </View>
);
}

---

File: screens/SettingsScreen.tsx (Lines 32-216)
Type: component
Symbols: SettingsScreen

export default function SettingsScreen({ navigation }: SettingsScreenProps) {
const { theme, setTheme } = useAppStore();

const themeOptions = [
{ key: 'light', label: 'Light' },
{ key: 'dark', label: 'Dark' },
{ key: 'system', label: 'System' }
];

const renderSectionHeader = (title: string) => (
<Text style={styles.sectionHeader}>{title}</Text>
);

const renderSettingItem = (
icon: any,
title: string,
subtitle?: string,
onPress?: () => void,
rightElement?: React.ReactNode
) => (
<TouchableOpacity
style={styles.settingItem}
onPress={onPress}
disabled={!onPress}
>
<View style={styles.settingIcon}>
{React.createElement(icon, { size: 20, color: COLORS.gray700 })}
</View>
<View style={styles.settingContent}>
<Text style={styles.settingTitle}>{title}</Text>
{subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
</View>
{rightElement || (onPress && (
<ChevronRight size={16} color={COLORS.gray400} />
))}
</TouchableOpacity>
);

return (
<View style={styles.container}>
<View style={styles.header}>
<TouchableOpacity
style={styles.backButton}
onPress={() => navigation.goBack()}
>
<ArrowLeft size={24} color={COLORS.gray700} />
</TouchableOpacity>
<Text style={styles.headerTitle}>Settings</Text>
<View style={styles.placeholder} />
</View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderSectionHeader('Account')}
        <View style={styles.section}>
          <Text style={styles.accountDescription}>
            Sign in to sync your journeys across devices and backup your data
          </Text>
          
          {renderSettingItem(
            User,
            'Continue with Google',
            undefined,
            () => {
              // Handle Google sign in
            }
          )}
          
          {renderSettingItem(
            User,
            'Continue with Email',
            undefined,
            () => {
              // Handle email sign in
            }
          )}
        </View>

        {renderSectionHeader('Appearance')}
        <View style={styles.section}>
          {renderSettingItem(
            Palette,
            'Theme',
            undefined,
            undefined,
            <View style={styles.themeSelector}>
              {themeOptions.map((option) => (
                <Pressable
                  key={option.key}
                  style={[
                    styles.themeOption,
                    theme === option.key && styles.selectedTheme
                  ]}
                  onPress={() => setTheme(option.key)}
                >
                  <Text
                    style={[
                      styles.themeText,
                      theme === option.key && styles.selectedThemeText
                    ]}
                  >
                    {option.label}
                  </Text>
                </Pressable>
              ))}
            </View>
          )}
        </View>

        {renderSectionHeader('Notifications')}
        <View style={styles.section}>
          {renderSettingItem(
            Bell,
            'Notification Settings',
            undefined,
            () => {
              // Handle notification settings
            }
          )}
        </View>

        {renderSectionHeader('Support')}
        <View style={styles.section}>
          {renderSettingItem(
            HelpCircle,
            'Help & FAQ',
            undefined,
            () => {
              // Handle help
            }
          )}
          
          {renderSettingItem(
            Shield,
            'Privacy Policy',
            undefined,
            () => {
              // Handle privacy policy
            }
          )}
          
          {renderSettingItem(
            FileText,
            'Terms of Service',
            undefined,
            () => {
              // Handle terms
            }
          )}
        </View>

        {renderSectionHeader('Data & Storage')}
        <View style={styles.section}>
          {renderSettingItem(
            Database,
            'Data Management',
            undefined,
            () => {
              // Handle data management
            }
          )}
        </View>

        {renderSectionHeader('About')}
        <View style={styles.section}>
          {renderSettingItem(
            Info,
            'Version',
            'v1.0.0',
            undefined
          )}
          
          {renderSettingItem(
            Globe,
            'waymarkerapp.com',
            undefined,
            () => {
              // Handle website link
            },
            <ExternalLink size={16} color={COLORS.gray400} />
          )}
        </View>
      </ScrollView>
    </View>
);
}

---

File: screens/JourneyListScreen.tsx (Lines 21-125)
Type: component
Symbols: JourneyListScreen

export default function JourneyListScreen({ navigation }: JourneyListScreenProps) {
const journeys = useJourneyStore(state => state.journeys);
const waypoints = useWaypointStore(state => state.waypoints);



const renderJourneyCard = (journey: any) => {
// Calculate entry and milestone counts for this journey
const journeyWaypoints = waypoints.filter(waypoint => waypoint.journeyId === journey.id);
const entryCount = journeyWaypoints.length;
const milestoneCount = journeyWaypoints.filter(waypoint => waypoint.isMilestone).length;

    return (
      <Pressable
        key={journey.id}
        style={styles.journeyCard}
        onPress={() => navigation.navigate('JourneyDetail', { journeyId: journey.id })}
      >
        <View style={styles.cardContent}>
          <View style={styles.journeyInfo}>
            <View style={[styles.colorCircle, { backgroundColor: journey.color }]} />
            <Text style={styles.journeyTitle}>{journey.title}</Text>
          </View>
          
          <View style={styles.metadataRow}>
            <View style={styles.metricsGroup}>
              <View style={[styles.metricItem, { marginRight: 12 }]}>
                <Flag size={14} color="#6B7280" style={{ marginRight: 4 }} />
                <Text style={styles.metadataText}>{entryCount}</Text>
              </View>
              <View style={styles.metricItem}>
                <Star size={14} color="#6B7280" style={{ marginRight: 4 }} />
                <Text style={styles.metadataText}>{milestoneCount}</Text>
              </View>
            </View>
            <Text style={styles.metadataText}>{formatTimeAgo(journey.updatedAt)}</Text>
          </View>
        </View>
      </Pressable>
    );
};

return (
<View style={styles.container}>
<View style={styles.header}>
<Text style={styles.headerTitle}>waymarker.</Text>
<View style={styles.headerActions}>
<TouchableOpacity
style={styles.actionButton}
onPress={() => {
// Handle search
}}
>
<Search size={20} color={COLORS.gray700} />
</TouchableOpacity>
<TouchableOpacity
style={styles.actionButton}
onPress={() => {
// Handle filter
}}
>
<Filter size={20} color={COLORS.gray700} />
</TouchableOpacity>
<TouchableOpacity
style={styles.actionButton}
onPress={() => navigation.navigate('Settings')}
>
<Settings size={20} color={COLORS.gray700} />
</TouchableOpacity>
</View>
</View>

      {journeys.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyTitle}>Start Your Journey</Text>
          <Text style={styles.emptySubtitle}>
            Create your first journey and begin tracking meaningful moments and milestones.
          </Text>
          <TouchableOpacity
            style={styles.emptyButton}
            onPress={() => navigation.navigate('CreateJourney')}
          >
            <Plus size={20} color="#FFFFFF" />
            <Text style={styles.emptyButtonText}>Create Journey</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.journeyList}
          showsVerticalScrollIndicator={false}
        >
          {journeys.map(renderJourneyCard)}
        </ScrollView>
      )}

      <TouchableOpacity
        style={styles.fab}
        onPress={() => navigation.navigate('CreateJourney')}
      >
        <Plus size={24} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
);
}

---

File: components/form/SentimentSelector.tsx (Lines 12-78)
Type: component
Symbols: SentimentSelector

export default function SentimentSelector({ value, onChange, journeyColor }: SentimentSelectorProps) {
// Convert sentiment value to dot index (0-8)
const getSelectedDotIndex = () => {
// Map -100 to 100 range to 0-8 range
const normalized = (value + 100) / 200; // 0 to 1
return Math.round(normalized * 8); // 0 to 8
};

// Convert dot index to sentiment value
const getDotValue = (index: number) => {
// Map 0-8 range to -100 to 100 range
return (index / 8) * 200 - 100;
};

const selectedIndex = getSelectedDotIndex();

const getSentimentLabel = (value: number) => {
if (value >= 60) return 'Very Positive';
if (value >= 20) return 'Positive';
if (value > -20) return 'Neutral';
if (value > -60) return 'Challenging';
return 'Very Challenging';
};

const renderDots = () => {
const dots = [];
for (let i = 0; i < 9; i++) {
const isSelected = i === selectedIndex;
const dotValue = getDotValue(i);

      dots.push(
        <Pressable
          key={i}
          style={[
            styles.dot,
            isSelected && styles.selectedDot,
            {
              backgroundColor: isSelected ? journeyColor : COLORS.gray400
            }
          ]}
          onPress={() => onChange(dotValue)}
          hitSlop={{ top: 10, bottom: 10, left: 4, right: 4 }}
        />
      );
    }
    return dots;
};

return (
<View style={styles.wrapper}>
<View style={styles.container}>
<Minus size={20} color={COLORS.gray500} strokeWidth={2.5} />

        <View style={styles.dotsContainer}>
          <View style={styles.line} />
          {renderDots()}
        </View>
        
        <Plus size={20} color={COLORS.gray500} strokeWidth={2.5} />
      </View>
      
      <Text style={styles.label}>
        {getSentimentLabel(value)}
      </Text>
    </View>
);
}

---

File: components/form/ImpactPicker.tsx (Lines 11-96)
Type: component
Symbols: ImpactPicker

export default function ImpactPicker({ value, onChange, journeyColor }: ImpactPickerProps) {
const getIconStyle = (level: 'low' | 'medium' | 'high') => {
const baseStyle = styles.impactOption;
if (level === 'low') {
return [baseStyle, { width: 24 }]; // Single circle width
} else if (level === 'medium') {
return [baseStyle, { width: 36, marginLeft: 8 }]; // Double circle width + visual gap
} else {
return [baseStyle, { width: 48, marginLeft: 8 }]; // Triple circle width + visual gap
}
};

const renderOption = (level: 'low' | 'medium' | 'high') => {
const isSelected = value === level;

    return (
      <View style={getIconStyle(level)}>
        <Pressable
          key={level}
          style={styles.impactTouchable}
          onPress={() => onChange(level)}
          hitSlop={{ top: 4, bottom: 4, left: 4, right: 4 }}
        >
          <View style={styles.impactCircleWrapper}>
          {/* Level 1 (Low Impact) - Single Circle */}
          {level === 'low' && (
            <View style={[
              styles.impactCircle,
              {
                borderColor: COLORS.gray400,
                backgroundColor: isSelected ? journeyColor : 'transparent'
              }
            ]} />
          )}
          
          {/* Level 2 (Medium Impact) - Double Circles */}
          {level === 'medium' && (
            <>
              <View style={[
                styles.impactCircle,
                {
                  borderColor: COLORS.gray400,
                  backgroundColor: isSelected ? journeyColor : 'transparent'
                }
              ]} />
              <View style={[
                styles.impactCircleOuter,
                { borderColor: COLORS.gray400 }
              ]} />
            </>
          )}
          
          {/* Level 3 (High Impact) - Triple Circles */}
          {level === 'high' && (
            <>
              <View style={[
                styles.impactCircle,
                {
                  borderColor: COLORS.gray400,
                  backgroundColor: isSelected ? journeyColor : 'transparent'
                }
              ]} />
              <View style={[
                styles.impactCircleOuter,
                { borderColor: COLORS.gray400 }
              ]} />
              <View style={[
                styles.impactCircleOutermost,
                { borderColor: COLORS.gray400 }
              ]} />
            </>
          )}
        </View>
      </Pressable>
    </View>
    );
};

return (
<View style={styles.container}>
{renderOption('low')}
{renderOption('medium')}
{renderOption('high')}
</View>
);
}

---

File: components/form/MilestoneToggle.tsx (Lines 12-38)
Type: component
Symbols: MilestoneToggle

export default function MilestoneToggle({ value, onChange, journeyColor }: MilestoneToggleProps) {
return (
<Pressable
style={styles.container}
onPress={() => onChange(!value)}
hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
>
<View
style={[
styles.circle,
{
backgroundColor: value ? journeyColor : 'transparent',
borderColor: value ? journeyColor : COLORS.gray200,
borderWidth: 2
}
]}
>
<Star
size={24}
color={value ? '#FFFFFF' : COLORS.gray400}
fill={value ? '#FFFFFF' : 'none'}
/>
</View>

    </Pressable>
);
}

---

File: components/form/DatePicker.tsx (Lines 22-208)
Type: component
Symbols: DatePicker

export default function DatePicker({ value, onChange, journeyColor, placeholder }: DatePickerProps) {
const [isModalVisible, setIsModalVisible] = useState(false);
const today = new Date();
const [selectedYear, setSelectedYear] = useState(today.getFullYear());
const [selectedMonth, setSelectedMonth] = useState(today.getMonth());
const [selectedDay, setSelectedDay] = useState(today.getDate());

const months = [
'January', 'February', 'March', 'April', 'May', 'June',
'July', 'August', 'September', 'October', 'November', 'December'
];



const getDaysInMonth = (year: number, month: number) => {
return new Date(year, month + 1, 0).getDate();
};

const getFirstDayOfMonth = (year: number, month: number) => {
return new Date(year, month, 1).getDay();
};

const handleDateSelect = () => {
const newDate = new Date(selectedYear, selectedMonth, selectedDay);
onChange(newDate);
setIsModalVisible(false);
};

const handleCancel = () => {
// Reset to current value with safety checks
const safeValue = value instanceof Date && !isNaN(value.getTime()) ? value : new Date();
setSelectedYear(safeValue.getFullYear());
setSelectedMonth(safeValue.getMonth());
setSelectedDay(safeValue.getDate());
setIsModalVisible(false);
};

const renderCalendar = () => {
const daysInMonth = getDaysInMonth(selectedYear, selectedMonth);
const firstDay = getFirstDayOfMonth(selectedYear, selectedMonth);
const days = [];
const today = new Date();

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<View key={`empty-${i}`} style={styles.emptyDay} />);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const currentDate = new Date(selectedYear, selectedMonth, day);
      const isFuture = currentDate > today;
      const isSelected = day === selectedDay;
      const isDisabled = isFuture;
      
      days.push(
        <Pressable
          key={day}
          style={[
            styles.dayButton,
            isSelected && !isDisabled && { backgroundColor: journeyColor },
            isDisabled && styles.disabledDay
          ]}
          onPress={() => !isDisabled && setSelectedDay(day)}
          disabled={isDisabled}
        >
          <Text
            style={[
              styles.dayText,
              isSelected && !isDisabled && { color: '#FFFFFF' },
              isDisabled && { color: COLORS.gray300 }
            ]}
          >
            {day}
          </Text>
        </Pressable>
      );
    }

    return days;
};

const navigateMonth = (direction: 'prev' | 'next') => {
if (direction === 'prev') {
if (selectedMonth === 0) {
setSelectedMonth(11);
setSelectedYear(selectedYear - 1);
} else {
setSelectedMonth(selectedMonth - 1);
}
} else {
if (selectedMonth === 11) {
setSelectedMonth(0);
setSelectedYear(selectedYear + 1);
} else {
setSelectedMonth(selectedMonth + 1);
}
}

    // Adjust day if it doesn't exist in the new month
    const daysInNewMonth = getDaysInMonth(
      direction === 'prev' && selectedMonth === 0 ? selectedYear - 1 : 
      direction === 'next' && selectedMonth === 11 ? selectedYear + 1 : selectedYear,
      direction === 'prev' && selectedMonth === 0 ? 11 : 
      direction === 'next' && selectedMonth === 11 ? 0 : 
      direction === 'prev' ? selectedMonth - 1 : selectedMonth + 1
    );
    
    if (selectedDay > daysInNewMonth) {
      setSelectedDay(daysInNewMonth);
    }
};

return (
<>
<Pressable
style={styles.dateInput}
onPress={() => setIsModalVisible(true)}
>
<Text style={styles.dateText}>
{placeholder || formatDatePicker(value)}
</Text>
<Calendar size={20} color={COLORS.gray400} />
</Pressable>

      <Modal
        visible={isModalVisible}
        transparent
        animationType="fade"
        onRequestClose={handleCancel}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Date</Text>
            </View>

            <View style={styles.calendarHeader}>
              <Pressable
                style={styles.navButton}
                onPress={() => navigateMonth('prev')}
              >
                <ChevronLeft size={20} color={COLORS.gray700} />
              </Pressable>
              
              <Text style={styles.monthYearText}>
                {months[selectedMonth]} {selectedYear}
              </Text>
              
              <Pressable
                style={styles.navButton}
                onPress={() => navigateMonth('next')}
              >
                <ChevronRight size={20} color={COLORS.gray700} />
              </Pressable>
            </View>

            <View style={styles.weekDaysHeader}>
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                <Text key={day} style={styles.weekDayText}>
                  {day}
                </Text>
              ))}
            </View>

            <View style={styles.calendar}>
              {renderCalendar()}
            </View>

            <View style={styles.modalActions}>
              <Pressable style={styles.cancelButton} onPress={handleCancel}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </Pressable>
              
              <Pressable
                style={[styles.selectButton, { backgroundColor: journeyColor }]}
                onPress={handleDateSelect}
              >
                <Text style={styles.selectButtonText}>Select</Text>
              </Pressable>
            </View>
          </View>
        </View>
      </Modal>
    </>
);
}

---

File: constants/colors.ts (Lines 35-46)
Type: unknown
Symbols: JOURNEY_COLORS

export const JOURNEY_COLORS = [
COLORS.blue,
COLORS.green,
COLORS.purple,
COLORS.orange,
COLORS.red,
COLORS.teal,
COLORS.pink,
COLORS.indigo,
COLORS.amber,
COLORS.gray
];

---

File: screens/CreateJourneyScreen.tsx (Lines 15-16)
Type: unknown
Symbols: COLORS and JOURNEY_COLORS import

import { COLORS, JOURNEY_COLORS } from '../constants/colors';
import { formatDisplayDate } from '../utils/dateUtils';

---

File: screens/JourneyDetailScreen.tsx (Lines 34-34)
Type: unknown
Symbols: COLORS import

import { COLORS } from '../constants/colors';

---

File: screens/SettingsScreen.tsx (Lines 26-26)
Type: unknown
Symbols: COLORS import

import { COLORS } from '../constants/colors';

---

File: screens/JourneyListScreen.tsx (Lines 14-14)
Type: unknown
Symbols: COLORS import

import { COLORS } from '../constants/colors';

---

File: components/form/SentimentSelector.tsx (Lines 4-4)
Type: unknown
Symbols: COLORS import

import { COLORS } from '../../constants/colors';

---

File: components/form/ImpactPicker.tsx (Lines 3-3)
Type: unknown
Symbols: COLORS import

import { COLORS } from '../../constants/colors';

---

File: components/form/MilestoneToggle.tsx (Lines 4-4)
Type: unknown
Symbols: COLORS import

import { COLORS } from '../../constants/colors';

---

File: components/form/DatePicker.tsx (Lines 12-13)
Type: unknown
Symbols: COLORS import

import { COLORS } from '../../constants/colors';
import { formatDatePicker } from '../../utils/dateUtils';

---

File: screens/AddWaypointScreen.tsx (Lines 11-12)
Type: unknown
Symbols: COLORS import

import { COLORS } from '../constants/colors';
import WaypointForm from '../components/forms/WaypointForm';

---

File: screens/AddWaypointScreen.tsx (Lines 23-76)
Type: component
Symbols: AddWaypointScreen

export default function AddWaypointScreen({ navigation, route }: AddWaypointScreenProps) {
const { journeyId } = route.params;

const createWaypoint = useWaypointStore(state => state.createWaypoint);
const updateJourney = useJourneyStore(state => state.updateJourney);
const journey = useJourneyStore(state => state.getJourneyById(journeyId));

const handleSubmit = (data: {
content: string;
date: Date;
sentiment: number;
impact: 'low' | 'medium' | 'high';
isMilestone: boolean;
}) => {
if (!journey) return;

    createWaypoint({
      journeyId,
      ...data
    });

    // Update journey counts
    updateJourney(journeyId, {
      waypointCount: journey.waypointCount + 1,
      milestoneCount: journey.milestoneCount + (data.isMilestone ? 1 : 0),
      updatedAt: new Date()
    });

    navigation.goBack();
};



return (
<View style={styles.container}>
<View style={styles.header}>
<Pressable
style={styles.closeButton}
onPress={() => navigation.goBack()}
>
<X size={24} color={COLORS.gray700} />
</Pressable>
<Text style={styles.headerTitle}>New Waypoint</Text>
<View style={styles.placeholder} />
</View>

      <WaypointForm
        onSubmit={handleSubmit}
        buttonText="Add Waypoint"
        journeyColor={journey?.color || COLORS.primary}
      />
    </View>
);
}