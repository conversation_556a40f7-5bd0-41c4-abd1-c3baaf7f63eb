{"timestamp": "2025-06-14T17:23:37.792Z", "query": "Show me the subscription and billing system, including how features are gated and usage is tracked", "executionTime": 13683, "snippetsCount": 11, "additionalFilesCount": 0, "totalLines": 1513, "snippets": [{"filePath": "src/lib/subscription.ts", "type": "type", "context": "Defines the subscription status structure including credits and usage tracking, essential for understanding subscription and billing system.", "score": 1, "lines": 11, "startLine": 7, "endLine": 17, "symbols": ["SubscriptionStatus"], "preview": "interface SubscriptionStatus {\n  subscriptionId?: string;\n  isActive: boolean;\n  plan: PlanTier;\n  status: 'active'| 'inactive'| 'cancelled' | 'expired' | 'past_due';\n..."}, {"filePath": "src/lib/subscription.ts", "type": "util", "context": "Core function to check subscription status, credits, usage, and gating based on user type and subscription state.", "score": 1, "lines": 83, "startLine": 19, "endLine": 101, "symbols": ["checkSubscriptionStatus"], "preview": "export async function checkSubscriptionStatus(userId: string, isAnonymous?: boolean): Promise<SubscriptionStatus> {\n  // Get appropriate plan based on user type\n  const freePlan = PLANS.find(plan => plan.tier === \"free\");\n  const anonymousPlan = PLANS.find(plan => plan.tier === \"anonymous\");\n  \n..."}, {"filePath": "src/lib/subscription.ts", "type": "util", "context": "Function to check message sending limits, usage tracking, and gating based on daily and total credits.", "score": 1, "lines": 70, "startLine": 103, "endLine": 172, "symbols": ["checkMessageLimit"], "preview": "export async function checkMessageLimit(userId: string, isAnonymous: boolean) {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n\n  // Get subscription status first (which already has creditsUsed for paid plans)\n..."}, {"filePath": "src/lib/subscription/plans.ts", "type": "type", "context": "Defines plan tiers, features, and plan structure including allowed features and limits, critical for feature gating and billing.", "score": 1, "lines": 32, "startLine": 1, "endLine": 32, "symbols": ["PlanTier, FeatureId, Plan"], "preview": "export type PlanTier = 'free' | 'starter' | 'pro' | 'plus' | 'prime' | 'anonymous';\n\n// Feature identifiers for access control\nexport type FeatureId = \n  | 'code_download'\n..."}, {"filePath": "src/lib/subscription/plans.ts", "type": "unknown", "context": "Defines all subscription plans with features, limits, and allowed features, directly related to subscription and billing system.", "score": 1, "lines": 177, "startLine": 34, "endLine": 210, "symbols": ["PLANS"], "preview": "export const PLANS: Plan[] = [\n  {\n    id: 'anonymous',\n    name: 'Anonymous',\n    tier: 'anonymous',\n..."}, {"filePath": "src/lib/subscription/plans.ts", "type": "util", "context": "Utility functions for retrieving plan details and checking feature access, essential for feature gating and billing logic.", "score": 1, "lines": 23, "startLine": 212, "endLine": 234, "symbols": ["getPlanByTier, getOperationsByTier, calculateOveragePrice, isFeatureAllowed, getAllowedFeatures"], "preview": "export const getPlanByTier = (tier: PlanTier): Plan => {\n  return PLANS.find(p => p.tier === tier) || PLANS[0];\n};\n\nexport const getOperationsByTier = (tier: PlanTier): number => {\n..."}, {"filePath": "src/components/subscription/subscription-management.tsx", "type": "component", "context": "Main UI component for subscription and billing management, showing usage, gating, upgrade and cancellation flows.", "score": 1, "lines": 368, "startLine": 41, "endLine": 408, "symbols": ["SubscriptionManagement"], "preview": "export function SubscriptionManagement() {\n  const router = useRouter();\n  const [showExitInterview, setShowExitInterview] = useState(false);\n  const [isUpgrading, setIsUpgrading] = useState(false);\n\n..."}, {"filePath": "src/components/subscription/subscription-indicator.tsx", "type": "component", "context": "UI component showing subscription usage and gating status, relevant for understanding usage tracking and feature gating display.", "score": 0.9, "lines": 233, "startLine": 38, "endLine": 270, "symbols": ["SubscriptionIndicator"], "preview": "export function SubscriptionIndicator({ className, variant = 'default' }: { \n  className?: string;\n  variant?: 'default' | 'compact' | 'sidebar';\n}) {\n  const router = useRouter();\n..."}, {"filePath": "src/components/subscription-status.tsx", "type": "component", "context": "Component showing subscription usage status and gating logic, including click handlers for upgrade or login, relevant for usage tracking and gating.", "score": 0.8, "lines": 168, "startLine": 9, "endLine": 176, "symbols": ["SubscriptionStatus"], "preview": "export function SubscriptionStatus() {\n  const { subscription, isLoading } = useSubscription();\n  const { generatorStore } = useStores();\n  const isMobile = useMediaQuery('(max-width: 768px)');\n\n..."}, {"filePath": "src/components/subscription/exit-interview.tsx", "type": "component", "context": "Component handling subscription cancellation feedback and cancellation API call, part of subscription lifecycle management.", "score": 0.6, "lines": 342, "startLine": 36, "endLine": 377, "symbols": ["ExitInterview"], "preview": "export function ExitInterview({\n  open,\n  onOpenChange,\n  subscriptionId,\n  onCancellationComplete\n..."}, {"filePath": "src/components/subscription/exit-interview.tsx", "type": "type", "context": "Defines props for exit interview component related to subscription cancellation feedback.", "score": 0.3, "lines": 6, "startLine": 21, "endLine": 26, "symbols": ["ExitInterviewProps"], "preview": "interface ExitInterviewProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  subscriptionId: string;\n  onCancellationComplete?: () => void;\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/subscription.ts", "content": "interface SubscriptionStatus {\n  subscriptionId?: string;\n  isActive: boolean;\n  plan: PlanTier;\n  status: 'active'| 'inactive'| 'cancelled' | 'expired' | 'past_due';\n  expiresAt: Date | null;\n  credits: number;        // Now represents total messages allowed\n  creditsUsed: number;    // Now represents messages used\n  creditsRemaining: number; // Now represents messages remaining\n  expiryDate?: string;\n}", "startLine": 7, "endLine": 17, "type": "type", "symbols": ["SubscriptionStatus"], "score": 1, "context": "Defines the subscription status structure including credits and usage tracking, essential for understanding subscription and billing system.", "includesImports": false}, {"filePath": "src/lib/subscription.ts", "content": "export async function checkSubscriptionStatus(userId: string, isAnonymous?: boolean): Promise<SubscriptionStatus> {\n  // Get appropriate plan based on user type\n  const freePlan = PLANS.find(plan => plan.tier === \"free\");\n  const anonymousPlan = PLANS.find(plan => plan.tier === \"anonymous\");\n  \n  // Skip subscription check for anonymous users\n  if (isAnonymous) {\n    // For anonymous users, count messages from token consumption\n    const result = await db\n      .select({\n        messageCount: sql<number>`COUNT(*)`\n      })\n      .from(tokenConsumption)\n      .where(\n        eq(tokenConsumption.userId, userId)\n      );\n\n    // Ensure we have a number by explicitly converting\n    const messagesUsed = Number(result[0]?.messageCount || 0);\n    const anonMessageLimit = anonymousPlan?.operations || 100;\n    \n    return {\n      isActive: false,\n      status: 'active',\n      plan: 'anonymous',\n      expiresAt: null,\n      credits: anonMessageLimit,  // Now represents message limit\n      creditsUsed: messagesUsed,  // Now represents messages used\n      creditsRemaining: Math.max(0, anonMessageLimit - messagesUsed)\n    };\n  }\n  \n  // For authenticated users, check if they have an active subscription\n  const userSubscription = await db\n    .select()\n    .from(subscriptions)\n    .where(and(\n        eq(subscriptions.userId, userId),\n        eq(subscriptions.isActive, true)\n    ))\n    .orderBy(desc(subscriptions.updatedAt))\n    .limit(1)\n    .then(results => results[0]);\n\n  if (!userSubscription || !userSubscription.isActive) {\n    // For free tier users, count messages from token consumption\n    const result = await db\n      .select({\n        messageCount: sql<number>`COUNT(*)`\n      })\n      .from(tokenConsumption)\n      .where(\n        eq(tokenConsumption.userId, userId)\n      );\n\n    // Ensure we have a number by explicitly converting\n    const messagesUsed = Number(result[0]?.messageCount || 0);\n    const freeMessageLimit = freePlan?.operations || 50;\n\n    return {\n      isActive: false,\n      plan: 'free',\n      status: 'active',\n      expiresAt: null,\n      credits: freeMessageLimit, // Free tier message limit\n      creditsUsed: messagesUsed, // Messages used\n      creditsRemaining: Math.max(0, freeMessageLimit - messagesUsed)\n    };\n  }\n\n  // User has an active subscription - use subscription.creditsUsed as source of truth for message count\n  return {\n    expiryDate: userSubscription.resetDate ? userSubscription.resetDate.toISOString() : undefined,\n    isActive: !!userSubscription.isActive,\n    subscriptionId: userSubscription.id,\n    status: userSubscription.status as any,\n    plan: userSubscription.planId as PlanTier,\n    expiresAt: userSubscription.resetDate, // Use resetDate as the expiration date\n    credits: userSubscription.credits,      // Now represents message limit\n    creditsUsed: userSubscription.creditsUsed || 0, // Now represents messages used\n    creditsRemaining: Math.max(0, userSubscription.credits - (userSubscription.creditsUsed || 0))\n  };\n}", "startLine": 19, "endLine": 101, "type": "util", "symbols": ["checkSubscriptionStatus"], "score": 1, "context": "Core function to check subscription status, credits, usage, and gating based on user type and subscription state.", "includesImports": false}, {"filePath": "src/lib/subscription.ts", "content": "export async function checkMessageLimit(userId: string, isAnonymous: boolean) {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n\n  // Get subscription status first (which already has creditsUsed for paid plans)\n  const subscription = await checkSubscriptionStatus(userId, isAnonymous);\n  \n  // For consistency, use the creditsUsed from subscription status (now represents messages)\n  const messagesUsed = subscription.creditsUsed;\n\n  // Get plan details\n  const plan = getPlanByTier(subscription.plan);\n  \n  // Get daily limit from plan\n  const dailyLimit = plan.dailyLimit;\n  \n  // Check if plan has a daily limit (not -1)\n  if (dailyLimit > 0) {\n    // Count messages used today\n    const todayResult = await db\n      .select({\n        todayMessageCount: sql<number>`COUNT(*)`\n      })\n      .from(tokenConsumption)\n      .where(\n        and(\n          eq(tokenConsumption.userId, userId),\n          gte(tokenConsumption.createdAt, today)\n        )\n      );\n    \n    // Ensure we have a number by explicitly converting\n    const todayMessagesUsed = Number(todayResult[0]?.todayMessageCount || 0);\n    const dailyRemaining = Math.max(0, dailyLimit - todayMessagesUsed);\n    \n    // Check both daily and monthly message limits\n    return {\n      canSendMessage: todayMessagesUsed < dailyLimit && messagesUsed < subscription.credits,\n      remaining: Math.min(\n        dailyRemaining,\n        Math.max(0, subscription.credits - messagesUsed)\n      ),\n      limit: subscription.credits,\n      dailyLimit: dailyLimit,\n      dailyRemaining: dailyRemaining,\n      isPro: subscription.plan !== 'free' && subscription.plan !== 'anonymous',\n      isAnonymous: subscription.plan === 'anonymous',\n      planTier: subscription.plan,\n      totalCredits: subscription.credits,      // Now represents total message limit\n      creditsUsed: messagesUsed,               // Now represents messages used\n      creditsRemaining: Math.max(0, subscription.credits - messagesUsed) // Messages remaining\n    };\n  }\n\n  // Plans without daily limit (dailyLimit = -1)\n  return {\n    canSendMessage: subscription.creditsRemaining > 0,\n    remaining: subscription.creditsRemaining,\n    limit: subscription.credits,\n    isPro: subscription.plan !== 'free' && subscription.plan !== 'anonymous',\n    isAnonymous: subscription.plan === 'anonymous',\n    planTier: subscription.plan,\n    totalCredits: subscription.credits,      // Now represents total message limit\n    creditsUsed: subscription.creditsUsed,   // Now represents messages used\n    creditsRemaining: subscription.creditsRemaining, // Messages remaining\n    // Include dailyLimit as -1 to indicate unlimited\n    dailyLimit: -1,\n    dailyRemaining: -1\n  };\n}", "startLine": 103, "endLine": 172, "type": "util", "symbols": ["checkMessageLimit"], "score": 1, "context": "Function to check message sending limits, usage tracking, and gating based on daily and total credits.", "includesImports": false}, {"filePath": "src/lib/subscription/plans.ts", "content": "export type PlanTier = 'free' | 'starter' | 'pro' | 'plus' | 'prime' | 'anonymous';\n\n// Feature identifiers for access control\nexport type FeatureId = \n  | 'code_download'\n  | 'deployment_web'\n  | 'deployment_android'\n  | 'deployment_ios'\n  | 'push_notifications'\n  | 'over_the_air_updates'\n  | 'advanced_code_generation'\n  | 'version_control'\n  | 'custom_integrations'\n  | 'usage_analytics'\n  | 'priority_support'\n  | 'advanced_agent_mode';\n\nexport interface Plan {\n  id: string;\n  name: string;\n  tier: PlanTier;\n  hidden?: boolean;\n  dailyLimit: number;\n  messageLimit: number;\n  dailyMessageLimit: number;\n  price: number;\n  operations: number;\n  features: string[];\n  limitations?: string[];\n  isPopular?: boolean;\n  allowedFeatures: FeatureId[];\n}", "startLine": 1, "endLine": 32, "type": "type", "symbols": ["PlanTier, FeatureId, Plan"], "score": 1, "context": "Defines plan tiers, features, and plan structure including allowed features and limits, critical for feature gating and billing.", "includesImports": false}, {"filePath": "src/lib/subscription/plans.ts", "content": "export const PLANS: Plan[] = [\n  {\n    id: 'anonymous',\n    name: 'Anonymous',\n    tier: 'anonymous',\n    price: 0,\n    operations: 2,\n    hidden: true,\n    dailyLimit: 2,\n    messageLimit: -1,\n    dailyMessageLimit: -1,\n    features: [\n      'Single File Edits',\n      'Community Support',\n      'Web Preview'\n    ],\n    limitations: [\n      'No Code Download',\n      'No Web, Android or iOS Deployments',\n      'No Custom Integrations',\n      'Basic Agent Mode Only',\n      'No Project Save'\n    ],\n    allowedFeatures: []\n  },\n  {\n    id: 'free',\n    name: 'Free',\n    tier: 'free',\n    price: 0,\n    operations: 10,\n    dailyLimit: 10,\n    hidden: true,\n    messageLimit: -1,\n    dailyMessageLimit: -1,\n    features: [\n      'Single File Edits',\n      'Community Support',\n      'Web Preview'\n    ],\n    limitations: [\n      'No Code Download',\n      'No Web, Android or iOS Deployments',\n      'No Custom Integrations',\n      'Basic Agent Mode Only'\n    ],\n    allowedFeatures: [\n      'deployment_web'\n    ]\n  },\n  {\n    id: 'starter',\n    name: 'Starter',\n    tier: 'starter',\n    price: 25,\n    operations: 100,\n    hidden: false,\n    messageLimit: -1,\n    dailyMessageLimit: -1,\n    dailyLimit: -1,\n    features: [\n      '1 credit = ~upto 10 tasks',\n      'Higher limits',\n      'Code Download',\n      'Android and iOS one click to stores',\n      'Access to new features',\n      'Community Support'\n    ],\n    limitations: [\n    ],\n    allowedFeatures: [\n      'deployment_web',\n      'deployment_android',\n      'deployment_ios',\n      'code_download',\n      'push_notifications',\n      'over_the_air_updates'\n    ]\n  },\n  {\n    id: 'pro',\n    name: 'Pro',\n    tier: 'pro',\n    price: 60,\n    operations: 240,\n    hidden: false,\n    messageLimit: -1,\n    dailyMessageLimit: -1,\n    dailyLimit: -1,\n    isPopular: true,\n    features: [\n      '1 credit = ~upto 10 tasks',\n      'Higher limits',\n      'Code Download',\n      'Android and iOS one click to stores',\n      'Advanced Code Generation',\n      // 'Version Control Integration',\n      'Email Support'\n    ],\n    allowedFeatures: [\n      'code_download',\n      'deployment_web',\n      'deployment_android',\n      'deployment_ios',\n      'push_notifications',\n      'over_the_air_updates',\n      'advanced_code_generation',\n      'version_control',\n      'advanced_agent_mode'\n    ]\n  },\n  {\n    id: 'plus',\n    name: 'Plus',\n    tier: 'plus',\n    price: 99,\n    operations: 400,\n    hidden: false,\n    messageLimit: -1,\n    dailyMessageLimit: -1,\n    dailyLimit: -1,\n    features: [\n      '1 credit = ~upto 10 tasks',\n      'Free auto error fixes',\n      'Everything in Pro',\n      'Priority Support',\n      'Custom Integrations',\n      'Usage Analytics'\n    ],\n    allowedFeatures: [\n      'code_download',\n      'deployment_web',\n      'deployment_android',\n      'deployment_ios',\n      'push_notifications',\n      'over_the_air_updates',\n      'advanced_code_generation',\n      'version_control',\n      'advanced_agent_mode',\n      'custom_integrations',\n      'usage_analytics',\n      'priority_support'\n    ]\n  },\n  {\n    id: 'prime',\n    name: 'Prime',\n    tier: 'prime',\n    price: 199,\n    operations: 800,\n    hidden: false,\n    messageLimit: -1,\n    dailyMessageLimit: -1,\n    dailyLimit: -1,\n    features: [\n      '1 credit = ~upto 10 tasks',\n      'Everything in Plus',\n      'Priority Support',\n      '1-on-1 Consulting (10 hours per month)',\n      'App store deployment support'\n    ],\n    allowedFeatures: [\n      'code_download',\n      'deployment_web',\n      'deployment_android',\n      'deployment_ios',\n      'push_notifications',\n      'over_the_air_updates',\n      'advanced_code_generation',\n      'version_control',\n      'advanced_agent_mode',\n      'custom_integrations',\n      'usage_analytics',\n      'priority_support'\n    ]\n  }\n];", "startLine": 34, "endLine": 210, "type": "unknown", "symbols": ["PLANS"], "score": 1, "context": "Defines all subscription plans with features, limits, and allowed features, directly related to subscription and billing system.", "includesImports": false}, {"filePath": "src/lib/subscription/plans.ts", "content": "export const getPlanByTier = (tier: PlanTier): Plan => {\n  return PLANS.find(p => p.tier === tier) || PLANS[0];\n};\n\nexport const getOperationsByTier = (tier: PlanTier): number => {\n  return getPlanByTier(tier).operations;\n};\n\nexport const calculateOveragePrice = (operations: number): number => {\n  // $0.10 per operation for overage\n  return operations * 0.10;\n};\n\n// Helper function to check if a feature is allowed for a given plan tier\nexport const isFeatureAllowed = (planTier: PlanTier, featureId: FeatureId): boolean => {\n  const plan = getPlanByTier(planTier);\n  return plan.allowedFeatures.includes(featureId);\n};\n\n// Get all allowed features for a plan tier\nexport const getAllowedFeatures = (planTier: PlanTier): FeatureId[] => {\n  return getPlanByTier(planTier).allowedFeatures;\n};", "startLine": 212, "endLine": 234, "type": "util", "symbols": ["getPlanByTier, getOperationsByTier, calculateOveragePrice, isFeatureAllowed, getAllowedFeatures"], "score": 1, "context": "Utility functions for retrieving plan details and checking feature access, essential for feature gating and billing logic.", "includesImports": false}, {"filePath": "src/components/subscription/subscription-management.tsx", "content": "export function SubscriptionManagement() {\n  const router = useRouter();\n  const [showExitInterview, setShowExitInterview] = useState(false);\n  const [isUpgrading, setIsUpgrading] = useState(false);\n\n  const { data: status, error, isLoading, mutate } = useSWR<SubscriptionStatus>(\n    '/api/subscription/status',\n    fetcher,\n    {\n      refreshInterval: 60000, // Refresh every minute\n      revalidateOnFocus: true,\n    }\n  );\n\n  // Format large numbers with k suffix\n  const formatNumber = (num: number) => {\n    return num >= 1000 ? `${(num / 1000).toFixed(1)}k` : num.toString();\n  };\n\n  const getPlanIcon = (plan: PlanTier) => {\n    switch (plan) {\n      case 'prime':\n        return <Sparkles className=\"h-5 w-5 text-yellow-400\" />;\n      case 'plus':\n        return <Sparkles className=\"h-5 w-5 text-yellow-400\" />;\n      case 'pro':\n        return <Zap className=\"h-5 w-5 text-purple-400\" />;\n      case 'starter':\n        return <Rocket className=\"h-5 w-5 text-blue-400\" />;\n      case 'free':\n        return <CreditCardIcon className=\"h-5 w-5 text-gray-400\" />;\n      default:\n        return <CreditCardIcon className=\"h-5 w-5 text-gray-400\" />;\n    }\n  };\n\n  const getPlanColor = (plan: PlanTier) => {\n    switch (plan) {\n      case 'prime':\n        return 'bg-yellow-400';\n      case 'plus':\n        return 'bg-yellow-400';\n      case 'pro':\n        return 'bg-purple-400';\n      case 'starter':\n        return 'bg-blue-400';\n      default:\n        return 'bg-gray-400';\n    }\n  };\n\n  const handleViewPlans = () => {\n    router.push('/pricing');\n  };\n\n  const handleUpgrade = async (planId: string) => {\n    try {\n      setIsUpgrading(true);\n\n      // Get plan details for tracking\n      const planDetails = PLANS.find(plan => plan.id === planId);\n\n      // Track upgrade initiation\n      trackSubscriptionEvent('UPGRADE_INITIATED', {\n        current_plan: status?.planTier || 'unknown',\n        plan_type: planDetails?.tier || planId,\n        price: planDetails?.price || 0,\n        currency: 'USD',\n        entry_point: 'subscription_management'\n      });\n\n      const response = await fetch('/api/checkout', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          returnUrl: '/subscription',\n          planId\n        })\n      });\n\n      const data = await response.json();\n\n      if (data.error) {\n        // Track upgrade failure\n        trackSubscriptionEvent('UPGRADE_FAILED', {\n          current_plan: status?.planTier || 'unknown',\n          plan_type: planDetails?.tier || planId,\n          // Using any to bypass type checking for custom properties\n          ...(data.error ? { error_message: data.error } : {})\n        });\n\n        throw new Error(data.error);\n      }\n\n      // Track upgrade initiated (instead of checkout initiated which isn't in the allowed event types)\n      trackSubscriptionEvent('UPGRADE_INITIATED', {\n        current_plan: status?.planTier || 'unknown',\n        plan_type: planDetails?.tier || planId,\n        entry_point: 'subscription_management'\n      });\n\n      // Redirect to checkout URL\n      if (data.url) {\n        window.location.href = data.url;\n      } else {\n        throw new Error('No checkout URL returned');\n      }\n    } catch (error) {\n      console.error('Error upgrading plan:', error);\n    } finally {\n      setIsUpgrading(false);\n    }\n  };\n\n  // Format date to readable format\n  const formatDate = (dateString?: string) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center py-10\">\n        <div className=\"animate-pulse flex flex-col items-center gap-4\">\n          <div className=\"h-8 w-40 bg-muted rounded\"></div>\n          <div className=\"h-40 w-full max-w-md bg-muted rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !status) {\n    return (\n      <Alert variant=\"destructive\" className=\"max-w-3xl mx-auto my-8\">\n        <AlertCircle className=\"h-4 w-4\" />\n        <AlertTitle>Error</AlertTitle>\n        <AlertDescription>\n          Unable to load subscription information. Please try refreshing the page.\n        </AlertDescription>\n      </Alert>\n    );\n  }\n\n  const usagePercentage = Math.min(100, Math.round((status.credits.used / status.credits.total) * 100));\n  const planDetails = PLANS.find(plan => plan.tier === status.planTier);\n\n  // Get color based on usage percentage\n  const getUsageColor = (percentage: number) => {\n    if (percentage >= 90) return 'bg-red-500';\n    if (percentage >= 75) return 'bg-amber-500';\n    return getPlanColor(status.planTier);\n  };\n\n  return (\n    <div className=\"space-y-8 max-w-4xl mx-auto\">\n      <div className=\"space-y-2\">\n        <h1 className=\"text-3xl font-bold\">Subscription Management</h1>\n        <p className=\"text-muted-foreground\">Manage your subscription and usage details</p>\n      </div>\n\n      {/* Current Plan Card */}\n      <Card className=\"overflow-hidden\">\n        <CardHeader className=\"bg-muted/50\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-2\">\n              {getPlanIcon(status.planTier)}\n              <CardTitle className=\"capitalize\">{status.planName} Plan</CardTitle>\n            </div>\n            <Badge\n              variant={status.status === 'active' ? 'default' :\n                      status.status === 'cancelled' ? 'destructive' :\n                      status.status === 'past_due' ? 'destructive' : 'outline'}\n            >\n              {status.status === 'active' ? 'Active' :\n               status.status === 'cancelled' ? 'Cancelled' :\n               status.status === 'past_due' ? 'Past Due' : 'Inactive'}\n            </Badge>\n          </div>\n        </CardHeader>\n        <CardContent className=\"pt-6 space-y-6\">\n          {/* Subscription Status Information */}\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            <div className=\"space-y-4\">\n              <div>\n                <h3 className=\"text-sm font-medium text-muted-foreground mb-1\">Subscription Details</h3>\n                <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                  <div>Plan:</div>\n                  <div className=\"font-medium capitalize\">{status.planName}</div>\n\n                  <div>Status:</div>\n                  <div className=\"font-medium capitalize\">{status.status || 'Unknown'}</div>\n\n                  <div>Price:</div>\n                  <div className=\"font-medium\">${planDetails?.price || 0}/month</div>\n\n                  {status.currentPeriodEnd && (\n                    <>\n                      <div>Current Period Ends:</div>\n                      <div className=\"font-medium\">{formatDate(status.currentPeriodEnd)}</div>\n                    </>\n                  )}\n\n                  {status.status === 'cancelled' && status.cancelAt && (\n                    <>\n                      <div>Access Until:</div>\n                      <div className=\"font-medium\">{formatDate(status.cancelAt)}</div>\n                    </>\n                  )}\n                </div>\n              </div>\n\n              {status.status === 'cancelled' && (\n                <Alert>\n                  <CalendarClock className=\"h-4 w-4\" />\n                  <AlertTitle>Subscription Cancelled</AlertTitle>\n                  <AlertDescription>\n                    Your subscription has been cancelled but you still have access until {formatDate(status.cancelAt || status.currentPeriodEnd)}.\n                  </AlertDescription>\n                </Alert>\n              )}\n            </div>\n\n            {/* Usage Information */}\n            <div className=\"space-y-4\">\n              <div>\n                <h3 className=\"text-sm font-medium text-muted-foreground mb-1\">Usage</h3>\n                <div className=\"flex items-center justify-between mb-1\">\n                  <span className=\"text-sm\">Messages</span>\n                  <div>\n                    <span className=\"text-sm font-medium\">{formatNumber(status.credits.remaining)}</span>\n                    <span className=\"text-xs text-muted-foreground\"> / {formatNumber(status.credits.total)}</span>\n                  </div>\n                </div>\n                <Progress value={usagePercentage} className=\"h-2\">\n                  <div className={`h-full ${getUsageColor(usagePercentage)}`} style={{ width: `${usagePercentage}%` }} />\n                </Progress>\n                {usagePercentage >= 90 && (\n                  <p className=\"text-xs text-red-500 mt-1\">\n                    Very low on messages. Consider upgrading your plan.\n                  </p>\n                )}\n                {usagePercentage >= 75 && usagePercentage < 90 && (\n                  <p className=\"text-xs text-amber-500 mt-1\">\n                    Running low on messages.\n                  </p>\n                )}\n              </div>\n\n              {status.dailyLimit && status.dailyLimit > 0 && (\n                <div>\n                  <div className=\"flex items-center justify-between mb-1\">\n                    <span className=\"text-sm\">Daily Limit</span>\n                    <div>\n                      <span className=\"text-sm font-medium\">{status.dailyRemaining}</span>\n                      <span className=\"text-xs text-muted-foreground\"> / {status.dailyLimit}</span>\n                    </div>\n                  </div>\n                  <Progress\n                    value={Math.min(100, Math.round(((status.dailyLimit - (status.dailyRemaining || 0)) / status.dailyLimit) * 100))}\n                    className=\"h-2\"\n                  >\n                    <div\n                      className={`h-full ${getPlanColor(status.planTier)}`}\n                      style={{ width: `${Math.min(100, Math.round(((status.dailyLimit - (status.dailyRemaining || 0)) / status.dailyLimit) * 100))}%` }}\n                    />\n                  </Progress>\n                </div>\n              )}\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Plan Features */}\n          <div>\n            <h3 className=\"text-sm font-medium text-muted-foreground mb-2\">Plan Features</h3>\n            <ul className=\"grid grid-cols-1 md:grid-cols-2 gap-2\">\n              {planDetails?.features.map((feature, index) => (\n                <li key={index} className=\"flex items-start gap-2\">\n                  <CheckCircle className=\"h-4 w-4 text-green-500 mt-0.5\" />\n                  <span className=\"text-sm\">{feature}</span>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </CardContent>\n        <CardFooter className=\"flex justify-between items-center bg-muted/30 border-t\">\n          <div className=\"flex flex-col sm:flex-row gap-3 w-full justify-end\">\n            {status.status === 'active' && (\n              <>\n                {/* Upgrade button with better styling */}\n                <Button \n                  variant=\"default\" \n                  size=\"sm\"\n                  className=\"sm:w-auto px-6 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white\"\n                  onClick={handleViewPlans}\n                >\n                  <Zap className=\"mr-2 h-4 w-4\" />\n                  Upgrade Plan\n                </Button>\n                \n                {/* Cancel subscription button with subtle styling */}\n                <Button \n                  variant=\"ghost\" \n                  size=\"sm\"\n                  className=\"sm:w-auto px-6 text-gray-500 hover:text-gray-700 hover:bg-gray-100/50\"\n                  onClick={() => setShowExitInterview(true)}\n                  disabled={!status.subscriptionId}\n                >\n                  Cancel Plan\n                </Button>\n              </>\n            )}\n            \n            {status.status === 'cancelled' && (\n              <Button \n                variant=\"default\" \n                size=\"sm\"\n                className=\"sm:w-auto px-6 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white\"\n                onClick={handleViewPlans}\n              >\n                <CreditCard className=\"mr-2 h-4 w-4\" />\n                Resubscribe\n              </Button>\n            )}\n            \n            {(status.status === 'expired' || status.status === 'past_due') && (\n              <Button \n                variant=\"default\" \n                size=\"sm\"\n                className=\"sm:w-auto px-6 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white\"\n                onClick={handleViewPlans}\n              >\n                <CreditCard className=\"mr-2 h-4 w-4\" />\n                Renew Subscription\n              </Button>\n            )}\n          </div>\n        </CardFooter>\n      </Card>\n\n      {/* Exit Interview Modal with improved integration */}\n      {status.subscriptionId && (\n        <ExitInterview\n          open={showExitInterview}\n          onOpenChange={(open) => {\n            setShowExitInterview(open);\n            if (!open) {\n              // Refresh data when modal is closed\n              setTimeout(() => mutate(), 500);\n            }\n          }}\n          subscriptionId={status.subscriptionId}\n          onCancellationComplete={() => {\n            // Refresh data after cancellation\n            mutate();\n          }}\n        />\n      )}\n    </div>\n  );\n}", "startLine": 41, "endLine": 408, "type": "component", "symbols": ["SubscriptionManagement"], "score": 1, "context": "Main UI component for subscription and billing management, showing usage, gating, upgrade and cancellation flows.", "includesImports": false}, {"filePath": "src/components/subscription/subscription-indicator.tsx", "content": "export function SubscriptionIndicator({ className, variant = 'default' }: { \n  className?: string;\n  variant?: 'default' | 'compact' | 'sidebar';\n}) {\n  const router = useRouter();\n  const [open, setOpen] = useState(false);\n  \n  const { data: status, error, isLoading } = useSWR<SubscriptionStatus>(\n    '/api/subscription/status',\n    fetcher,\n    {\n      refreshInterval: 60000, // Refresh every minute\n      revalidateOnFocus: true,\n    }\n  );\n  \n  // Format large numbers with k suffix\n  const formatNumber = (num: number) => {\n    return num >= 1000 ? `${(num / 1000).toFixed(1)}k` : num.toString();\n  };\n\n  const getPlanIcon = (plan: PlanTier) => {\n    switch (plan) {\n      case 'prime':\n      return <Sparkles className=\"h-4 w-4 text-yellow-400\" />;\n      case 'plus':\n        return <Sparkles className=\"h-4 w-4 text-yellow-400\" />;\n      case 'pro':\n        return <Zap className=\"h-4 w-4 text-purple-400\" />;\n      case 'starter':\n        return <CreditCard className=\"h-4 w-4 text-blue-400\" />;\n      default:\n        return <CreditCard className=\"h-4 w-4 text-gray-400\" />;\n    }\n  };\n\n  const getPlanColor = (plan: PlanTier) => {\n    switch (plan) {\n      case 'prime':\n        return 'bg-yellow-400';\n      case 'plus':\n        return 'bg-yellow-400';\n      case 'pro':\n        return 'bg-purple-400';\n      case 'starter':\n        return 'bg-blue-400';\n      default:\n        return 'bg-gray-400';\n    }\n  };\n\n  const handleViewSubscription = () => {\n    if(status?.isPro) {\n      router.push('/subscription');\n    } else {\n      router.push('/pricing');\n    }\n    setOpen(false);\n  };\n\n  if (isLoading) {\n    return (\n      <Button variant=\"ghost\" size=\"sm\" className={cn(\"h-8 gap-1\", className)} disabled>\n        <BarChart3 className=\"h-4 w-4\" />\n        <span className=\"text-xs\">Loading...</span>\n      </Button>\n    );\n  }\n\n  if (error || !status) {\n    return (\n      <Button variant=\"ghost\" size=\"sm\" className={cn(\"h-8 gap-1\", className)} onClick={handleViewSubscription}>\n        <CreditCard className=\"h-4 w-4\" />\n        <span className=\"text-xs\">Subscription</span>\n      </Button>\n    );\n  }\n\n  const usagePercentage = Math.min(100, Math.round((status.credits.used / status.credits.total) * 100));\n  \n  // Get color based on usage percentage\n  const getUsageColor = (percentage: number) => {\n    if (percentage >= 90) return 'bg-red-500';\n    if (percentage >= 80) return 'bg-amber-500';\n    return getPlanColor(status.planTier);\n  };\n  \n  // Compact version (just icon and credits remaining)\n  if (variant === 'compact') {\n    return (\n      <Tooltip>\n        <TooltipTrigger asChild>\n          <Button \n            variant=\"ghost\" \n            size=\"sm\" \n            className={cn(\"h-7 px-2 gap-1 rounded-full border border-border/30\", className)}\n            onClick={handleViewSubscription}\n          >\n            {getPlanIcon(status.planTier)}\n            <span className=\"text-xs font-medium\">{formatNumber(status.credits.remaining)}</span>\n          </Button>\n        </TooltipTrigger>\n        <TooltipContent side=\"right\" className=\"p-2 text-xs\">\n          <div className=\"font-medium\">{formatNumber(status.credits.remaining)} / {formatNumber(status.credits.total)} credits</div>\n          <div className=\"text-muted-foreground capitalize\">{status.planName} Plan</div>\n          {status.dailyLimit && status.dailyLimit > 0 && (\n            <div className=\"text-muted-foreground mt-1\">\n              Daily: {status.dailyRemaining} / {status.dailyLimit}\n            </div>\n          )}\n        </TooltipContent>\n      </Tooltip>\n    );\n  }\n\n  // Sidebar version (vertical layout)\n  if (variant === 'sidebar') {\n    return (\n      <div \n        className={cn(\"flex flex-col gap-0.5 px-2 py-1 cursor-pointer hover:bg-muted/30 rounded transition-colors\", className)}\n        onClick={handleViewSubscription}\n      >\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-1\">\n            <div className=\"w-3.5 h-3.5\">{getPlanIcon(status.planTier)}</div>\n            <span className=\"text-[10px] font-medium capitalize\">{status.planName}</span>\n          </div>\n          <ChevronRight className=\"h-2.5 w-2.5 opacity-60\" />\n        </div>\n        <div className=\"space-y-0.5\">\n          <div className=\"flex items-center justify-between text-[9px] text-muted-foreground\">\n            <span>Messages</span>\n            <span>{formatNumber(status.credits.remaining)} / {formatNumber(status.credits.total)}</span>\n          </div>\n          <Progress value={usagePercentage} className=\"h-0.5\">\n            <div className={`h-full ${getUsageColor(usagePercentage)}`} style={{ width: `${usagePercentage}%` }} />\n          </Progress>\n          \n          {status.dailyLimit && status.dailyLimit > 0 && (\n            <div className=\"flex items-center justify-between text-[9px] text-muted-foreground mt-0.5\">\n              <span>Daily</span>\n              <span>{status.dailyRemaining} / {status.dailyLimit}</span>\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  // Default version (popover with details)\n  return (\n    <Popover open={open} onOpenChange={setOpen}>\n      <PopoverTrigger asChild>\n        <Button \n          variant=\"outline\" \n          size=\"sm\" \n          className={cn(\"h-7 px-2 gap-1.5 rounded-md border-border/40\", className)}\n        >\n          {getPlanIcon(status.planTier)}\n          <span className=\"text-xs font-medium\">{formatNumber(status.credits.remaining)}</span>\n        </Button>\n      </PopoverTrigger>\n      <PopoverContent className=\"w-64 p-3\" align=\"end\">\n        <div \n          className=\"space-y-2.5 cursor-pointer\" \n          onClick={() => {\n            setOpen(false);\n            handleViewSubscription();\n          }}\n        >\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-1.5\">\n              {getPlanIcon(status.planTier)}\n              <span className=\"text-sm font-medium capitalize\">{status.planName}</span>\n            </div>\n            <Button \n              variant=\"ghost\" \n              size=\"sm\" \n              className=\"h-6 px-2 text-xs\" \n              onClick={(e) => {\n                e.stopPropagation();\n                setOpen(false);\n                handleViewSubscription();\n              }}\n            >\n              Manage\n            </Button>\n          </div>\n          \n          <div className=\"space-y-0.5\">\n            <div className=\"flex items-center justify-between text-xs\">\n              <span className=\"text-muted-foreground\">Message</span>\n              <span className=\"font-medium\">{formatNumber(status.credits.remaining)} / {formatNumber(status.credits.total)}</span>\n            </div>\n            <Progress value={usagePercentage} className=\"h-1.5\">\n              <div className={`h-full ${getUsageColor(usagePercentage)}`} style={{ width: `${usagePercentage}%` }} />\n            </Progress>\n            {usagePercentage >= 90 && (\n              <p className=\"text-[10px] text-red-500 pt-0.5\">\n                Very low on messages\n              </p>\n            )}\n            {usagePercentage >= 80 && usagePercentage < 90 && (\n              <p className=\"text-[10px] text-amber-500 pt-0.5\">\n                Running low on messages\n              </p>\n            )}\n            \n            {status.dailyLimit && status.dailyLimit > 0 && (\n              <div className=\"flex items-center justify-between text-xs mt-2\">\n                <span className=\"text-muted-foreground\">Daily Limit</span>\n                <span className=\"font-medium\">{status.dailyRemaining} / {status.dailyLimit}</span>\n              </div>\n            )}\n          </div>\n          \n          {status.planTier !== 'plus' && (\n            <Button \n              className=\"w-full text-xs h-7 mt-1\" \n              onClick={(e) => {\n                e.stopPropagation();\n                setOpen(false);\n                handleViewSubscription();\n              }}\n            >\n              Upgrade Plan\n            </Button>\n          )}\n        </div>\n      </PopoverContent>\n    </Popover>\n  );\n}", "startLine": 38, "endLine": 270, "type": "component", "symbols": ["SubscriptionIndicator"], "score": 0.9, "context": "UI component showing subscription usage and gating status, relevant for understanding usage tracking and feature gating display.", "includesImports": false}, {"filePath": "src/components/subscription-status.tsx", "content": "export function SubscriptionStatus() {\n  const { subscription, isLoading } = useSubscription();\n  const { generatorStore } = useStores();\n  const isMobile = useMediaQuery('(max-width: 768px)');\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n        <Loader2 className=\"h-3 w-3 animate-spin\" />\n        Loading...\n      </div>\n    );\n  }\n\n\n  if (!subscription) {\n    return null;\n  }\n\n  posthog.setPersonProperties({\n    plan: subscription?.planTier,\n    isPaidUser: subscription?.isPro,\n    messageLimit: subscription?.messageLimit,\n    messagesRemaining: subscription?.messagesRemaining\n  })\n\n  // Calculate usage percentages for different limits\n  \n  // 1. Calculate credits usage (replacing the legacy message limit)\n  // Use credits.total and credits.remaining from the subscription API\n  // Ensure values are non-negative\n  const safeCreditsRemaining = Math.max(0, subscription.credits?.remaining || 0);\n  const usedCredits = subscription.credits?.total - safeCreditsRemaining;\n  const creditsPercentageUsed = (usedCredits / subscription.credits?.total) * 100;\n  \n  // 2. Calculate daily limit usage if applicable\n  const hasDailyLimit = subscription.dailyLimit > 0;\n  // Ensure values are non-negative\n  const safeDailyRemaining = Math.max(0, subscription.dailyRemaining || 0);\n  const usedDailyCredits = hasDailyLimit ? (subscription.dailyLimit - safeDailyRemaining) : 0;\n  const dailyPercentageUsed = hasDailyLimit ? (usedDailyCredits / subscription.dailyLimit) * 100 : 0;\n  \n  // 3. Determine which limit to display based on user type and limits\n  const shouldShowDailyLimit = () => {\n    // For free and anonymous users, prioritize daily limit unless daily is left and monthly is less than 10%\n    if (!subscription.isPro) {\n      // If daily limit exists and monthly is critical (less than 10% remaining)\n      const isMonthlyLimitCritical = (subscription.credits?.remaining / subscription.credits?.total) * 100 < 10;\n      \n      // Show monthly limit only when daily limit is not depleted AND monthly limit is critical\n      return !(subscription.dailyRemaining > 0 && isMonthlyLimitCritical);\n    }\n    \n    // For Pro users, show the most restrictive limit\n    return hasDailyLimit && dailyPercentageUsed > creditsPercentageUsed;\n  };\n  \n  const showingDailyLimit = shouldShowDailyLimit();\n  \n  // 4. Select the most relevant percentage to display\n  const percentageUsed = showingDailyLimit ? dailyPercentageUsed : creditsPercentageUsed;\n  \n  // 5. Check if we should show the bar (for all users, including Pro)\n  const shouldShowBar = percentageUsed >= 80;\n  \n  // 6. Don't show anything for Pro users unless they're close to a limit\n  if (subscription.isPro && !shouldShowBar) {\n    return null;\n  }\n\n  // 7. Handle click based on user type\n  const handleClick = () => {\n    if (subscription.isAnonymous) {\n      // For anonymous users, show login dialog\n      generatorStore.toggleLoginDialog(true);\n    } else {\n      // For authenticated users, show upgrade dialog\n      generatorStore.setUsageLimit(\n        subscription.credits?.total || 0,\n        subscription.credits?.remaining || 0\n      );\n      generatorStore.toggleUpgradeDialog(true);\n    }\n  }\n\n  // Determine the appropriate message based on which limit we're showing\n  const getMessage = () => {\n    if (showingDailyLimit) {\n      return {\n        label: 'Daily:',\n        value: safeDailyRemaining <= 0 \n          ? 'You have run out of messages for the day' \n          : `${safeDailyRemaining} message${safeDailyRemaining !== 1 ? 's' : ''} left`\n      };\n    } else {\n      return {\n        label: 'Monthly:',\n        value: safeCreditsRemaining <= 0 \n          ? 'You have run out of messages for the month' \n          : `${safeCreditsRemaining} message${safeCreditsRemaining !== 1 ? 's' : ''} left`\n      };\n    }\n  };\n  \n  // Get the sign-in message for anonymous users (separate from credit display)\n  const getSignInMessage = () => {\n    return isMobile ? 'Sign in' : 'Sign in to save your project';\n  };\n\n  return (\n    <div className=\"flex items-center gap-2 text-xs bg-background/50 px-2.5 py-1.5 rounded-lg border border-border/50\">\n      <div className=\"flex items-center gap-2 flex-1\">\n        <div className=\"flex-1 min-w-[140px]\">\n          <div className=\"flex flex-col gap-0.5\">\n            <div className=\"flex items-center gap-1.5\">\n              {/* Display the credits remaining with clear label */}\n              {(showingDailyLimit && safeDailyRemaining <= 0) || \n               (!showingDailyLimit && safeCreditsRemaining <= 0) ? (\n                <span className=\"text-red-500 font-medium\">{getMessage().value}</span>\n              ) : (\n                <>\n                  <span className=\"font-medium text-muted-foreground\">{getMessage().label}</span>\n                  <span className=\"text-muted-foreground\">{getMessage().value}</span>\n                </>\n              )}\n              {/* Show sign-in message for anonymous users */}\n              {subscription.isAnonymous && (\n                <span className=\"ml-1 text-accent\">(Sign in to save your work)</span>\n              )}\n            </div>\n          </div>\n          {/* Only show progress bar if not on mobile */}\n          <div className=\"h-1 w-full bg-muted/30 rounded-full overflow-hidden mt-1.5 hidden md:flex\">\n            <div \n              className={cn(\n                \"h-full transition-all duration-500\",\n                percentageUsed >= 90 ? \"bg-red-500\" :\n                percentageUsed >= 80 ? \"bg-amber-500\" : \"bg-primary\"\n              )}\n              style={{ width: `${percentageUsed}%` }}\n            />\n          </div>\n        </div>\n      </div>\n      <Button\n        variant=\"ghost\"\n        size=\"xs\"\n        className={cn(\n          \"font-medium\",\n          percentageUsed >= 90\n            ? \"text-red-500 hover:text-red-500 hover:bg-red-500/10\"\n            : \"text-accent hover:text-accent hover:bg-accent/10\"\n        )}\n        onClick={handleClick}\n      >\n        {/* Show icon for mobile anonymous users to save space */}\n        {subscription.isAnonymous ? (\n          <>\n            <LogIn className=\"h-3 w-3 mr-1\" />\n            Sign in\n          </>\n        ) : (\n          'Upgrade'\n        )}\n      </Button>\n    </div>\n  );\n}", "startLine": 9, "endLine": 176, "type": "component", "symbols": ["SubscriptionStatus"], "score": 0.8, "context": "Component showing subscription usage status and gating logic, including click handlers for upgrade or login, relevant for usage tracking and gating.", "includesImports": false}, {"filePath": "src/components/subscription/exit-interview.tsx", "content": "export function ExitInterview({\n  open,\n  onOpenChange,\n  subscriptionId,\n  onCancellationComplete\n}: ExitInterviewProps) {\n  const router = useRouter();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [currentStep, setCurrentStep] = useState(0);\n  const [feedback, setFeedback] = useState<CancellationFeedback>({\n    signupGoal: '',\n    stopReason: '',\n    recommendScore: 5,\n    recommendReason: ''\n  });\n  \n  const [wantsFeedbackCall, setWantsFeedbackCall] = useState(false);\n  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n\n  const steps = [\n    {\n      title: \"What was your main goal when you signed up?\",\n      key: \"signupGoal\",\n      options: [\n        { value: \"build_mvp\", label: \"Build an MVP to test my idea\" },\n        { value: \"create_production_app\", label: \"Create a production app for my business\" },\n        { value: \"learn_no_code\", label: \"Learn if no-code could work for me\" },\n        { value: \"replace_current_process\", label: \"Replace my current app development process\" },\n        { value: \"other\", label: \"Other\" }\n      ]\n    },\n    {\n      title: \"What stopped you from achieving that goal?\",\n      key: \"stopReason\",\n      options: [\n        { value: \"too_complex\", label: \"App building process was too complex\" },\n        { value: \"didnt_work\", label: \"Generated app didn't work as expected\" },\n        { value: \"missing_features\", label: \"Missing features I needed\" },\n        { value: \"too_expensive\", label: \"Too expensive for the value\" },\n        { value: \"better_alternative\", label: \"Found a better alternative\" },\n        { value: \"other\", label: \"Other\" }\n      ]\n    },\n    {\n      title: \"How likely are you to recommend Magically?\",\n      key: \"recommendScore\",\n      subKey: \"recommendReason\",\n      placeholder: \"What would make you more likely to recommend us?\"\n    },\n    {\n      title: \"Would you like to schedule a feedback call?\",\n      key: \"feedbackCall\",\n      description: \"Schedule a quick call to provide detailed feedback and get a refund (up to $15 for Magically Starter) after the call. This helps us improve the overall experience.\"\n    }\n  ];\n\n  const isCurrentStepValid = useCallback(() => {\n    const currentStepData = steps[currentStep];\n    if (currentStepData.key === 'signupGoal' || currentStepData.key === 'stopReason') {\n      return !!feedback[currentStepData.key as keyof CancellationFeedback];\n    }\n    return true;\n  }, [currentStep, feedback, steps]);\n\n  const handleNext = useCallback(() => {\n    if (!isCurrentStepValid()) {\n      // Optionally, add user feedback here (e.g., toast, shake animation)\n      return;\n    }\n    if (currentStep < steps.length - 1) {\n      setCurrentStep(currentStep + 1);\n    }\n  }, [currentStep, steps, isCurrentStepValid, setCurrentStep]);\n\n  const handlePrevious = useCallback(() => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1);\n    }\n  }, [currentStep, setCurrentStep]);\n\n  // Effect for cleaning up the timeout if the component unmounts\n  useEffect(() => {\n    return () => {\n      if (debounceTimeoutRef.current) {\n        clearTimeout(debounceTimeoutRef.current);\n      }\n    };\n  }, []);\n\n  const handleSubmit = useCallback(async () => {\n    if (isSubmitting) {\n      return; // Prevent multiple submissions if already processing\n    }\n    setIsSubmitting(true); // Set loading state immediately for UI feedback\n\n    // Clear any existing debounce timeout\n    if (debounceTimeoutRef.current) {\n      clearTimeout(debounceTimeoutRef.current);\n    }\n\n    // Set a new debounce timeout\n    debounceTimeoutRef.current = setTimeout(async () => {\n      try {\n        const response = await fetch('/api/subscription/cancel', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          subscriptionId,\n          feedback: {\n            ...feedback,\n            // Include feedback call preference in the metadata\n            requestedFeedbackCall: wantsFeedbackCall\n          }\n        }),\n      });\n\n      if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.message || 'Failed to cancel subscription');\n      }\n\n      const result = await response.json();\n\n      if (onCancellationComplete) {\n        onCancellationComplete();\n      }\n\n      toast.success('Subscription cancelled successfully.');\n\n      onOpenChange(false); // Close the dialog\n      router.refresh();    // Refresh page data\n\n      // Reset internal component state\n      setCurrentStep(0);\n      setFeedback({ signupGoal: '', stopReason: '', recommendScore: 5, recommendReason: '' });\n      setWantsFeedbackCall(false);\n\n      } catch (error) {\n        console.error('Error during API call:', error);\n        // Ensure alert is shown for API call errors within the timeout\n        toast.error('An unexpected error occurred while cancelling. Please try again.');\n      } finally {\n        setIsSubmitting(false); // Reset loading state after API call attempt\n      }\n    }, 1000); // 1-second debounce period\n  }, [isSubmitting, subscriptionId, feedback, wantsFeedbackCall, onOpenChange, onCancellationComplete, router, setCurrentStep, setFeedback, setWantsFeedbackCall]);\n\n  // Success state\n  if (currentStep === steps.length) {\n    return (\n      <Dialog open={open} onOpenChange={onOpenChange}>\n        <DialogContent className=\"sm:max-w-[400px] p-0 overflow-hidden\">\n          <div className=\"flex flex-col items-center justify-center text-center p-8 space-y-4\">\n            <div className=\"rounded-full bg-green-50 p-3\">\n              <CheckCircle2 className=\"h-8 w-8 text-green-500\" />\n            </div>\n            <DialogTitle className=\"text-xl font-medium\">Subscription Cancelled</DialogTitle>\n            <DialogDescription>\n              You'll have access until the end of your billing period.\n            </DialogDescription>\n          </div>\n        </DialogContent>\n      </Dialog>\n    );\n  }\n\n  // Error state\n  if (currentStep === steps.length + 1) {\n    return (\n      <Dialog open={open} onOpenChange={onOpenChange}>\n        <DialogContent className=\"sm:max-w-[400px] p-0 overflow-hidden\">\n          <div className=\"flex flex-col items-center justify-center text-center p-8 space-y-4\">\n            <div className=\"rounded-full bg-red-50 p-3\">\n              <AlertCircle className=\"h-8 w-8 text-red-500\" />\n            </div>\n            <DialogTitle className=\"text-xl font-medium\">Something went wrong</DialogTitle>\n            <DialogDescription>\n              We couldn't cancel your subscription. Please try again.\n            </DialogDescription>\n            <Button \n              variant=\"outline\" \n              onClick={() => setCurrentStep(0)}\n              className=\"mt-2\"\n            >\n              Try Again\n            </Button>\n          </div>\n        </DialogContent>\n      </Dialog>\n    );\n  }\n\n  const step = steps[currentStep];\n  \n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"sm:max-w-[450px] p-0 overflow-hidden\">\n        <DialogHeader className=\"p-6 pb-3 border-b\">\n          <DialogTitle className=\"text-lg font-medium\">Cancel Subscription</DialogTitle>\n          <DialogDescription className=\"text-sm\">\n            We're sorry to see you go. Your feedback helps us improve.\n          </DialogDescription>\n        </DialogHeader>\n        \n        <div className=\"p-6\">\n          <h3 className=\"text-base font-medium mb-4\">{step.title}</h3>\n          \n          {(step.key === 'signupGoal' || step.key === 'stopReason') && (\n            <RadioGroup \n              value={feedback[step.key]}\n              onValueChange={(value) => setFeedback({...feedback, [step.key]: value})}\n              className=\"space-y-2.5\"\n            >\n              {step.options?.map((option) => (\n                <div \n                  key={option.value} \n                  className=\"flex items-center space-x-2 rounded-md border p-3 hover:bg-muted/20 transition-colors\"\n                >\n                  <RadioGroupItem value={option.value} id={`${step.key}-${option.value}`} />\n                  <Label htmlFor={`${step.key}-${option.value}`} className=\"flex-grow cursor-pointer\">\n                    {option.label}\n                  </Label>\n                </div>\n              ))}\n            </RadioGroup>\n          )}\n          \n          {step.key === 'recommendScore' && (\n            <div className=\"space-y-6\">\n              <div className=\"space-y-4\">\n                <div className=\"flex justify-between text-sm text-muted-foreground\">\n                  <span>Not likely</span>\n                  <span>Very likely</span>\n                </div>\n                <div className=\"px-1\">\n                  <Slider\n                    value={[feedback.recommendScore]}\n                    min={0}\n                    max={10}\n                    step={1}\n                    onValueChange={(value) => setFeedback({...feedback, recommendScore: value[0]})}\n                    className=\"my-4\"\n                  />\n                </div>\n                <div className=\"flex justify-between px-1\">\n                  {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (\n                    <span \n                      key={num} \n                      className={`w-6 h-6 flex items-center justify-center text-xs rounded-full \n                        ${feedback.recommendScore === num ? 'bg-primary text-primary-foreground' : 'text-muted-foreground'}`}\n                    >\n                      {num}\n                    </span>\n                  ))}\n                </div>\n              </div>\n              \n              <div className=\"pt-4\">\n                <Label htmlFor=\"recommend-reason\" className=\"text-sm font-medium mb-2 block\">\n                  Why did you give this score?\n                </Label>\n                <Textarea\n                  id=\"recommend-reason\"\n                  placeholder={step.placeholder}\n                  value={feedback.recommendReason || ''}\n                  onChange={(e) => setFeedback({...feedback, recommendReason: e.target.value})}\n                  className=\"resize-none h-24\"\n                />\n              </div>\n            </div>\n          )}\n          \n          {step.key === 'feedbackCall' && (\n            <div className=\"space-y-6\">\n              <p className=\"text-sm text-muted-foreground\">{step.description}</p>\n              \n              <div className=\"space-y-4\">\n                <RadioGroup \n                  value={wantsFeedbackCall ? 'yes' : 'no'}\n                  onValueChange={(value) => setWantsFeedbackCall(value === 'yes')}\n                  className=\"space-y-3\"\n                >\n                  <div className=\"flex items-center space-x-2 rounded-md border p-3 hover:bg-muted/20 transition-colors\">\n                    <RadioGroupItem value=\"yes\" id=\"feedback-call-yes\" />\n                    <Label htmlFor=\"feedback-call-yes\" className=\"flex-grow cursor-pointer\">\n                      Yes, I'd like to schedule a call and get a refund\n                    </Label>\n                  </div>\n                  <div className=\"flex items-center space-x-2 rounded-md border p-3 hover:bg-muted/20 transition-colors\">\n                    <RadioGroupItem value=\"no\" id=\"feedback-call-no\" />\n                    <Label htmlFor=\"feedback-call-no\" className=\"flex-grow cursor-pointer\">\n                      No, I'll skip the call\n                    </Label>\n                  </div>\n                </RadioGroup>\n                \n                {wantsFeedbackCall && (\n                  <div className=\"mt-4 p-4 bg-muted/20 rounded-md\">\n                    <h4 className=\"font-medium mb-2\">Schedule a Feedback Call</h4>\n                    <p className=\"text-sm mb-3\">After clicking the link below, you'll be able to choose a time that works for you.</p>\n                    <a \n                      href=\"https://calendly.com/magically-feedback/exit-interview\"\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full\"\n                    >\n                      Schedule Call & Get Refund\n                    </a>\n                    <p className=\"text-xs text-muted-foreground mt-2\">Your refund (up to $15 for Magically Starter) will be processed after the call.</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n        \n        <DialogFooter className=\"flex justify-between p-6 border-t bg-muted/10\">\n          <Button \n            variant=\"ghost\" \n            onClick={currentStep === 0 ? () => onOpenChange(false) : handlePrevious}\n            disabled={isSubmitting}\n          >\n            {currentStep === 0 ? 'Cancel' : 'Back'}\n          </Button>\n          \n          <Button \n            variant={currentStep === steps.length - 1 ? \"destructive\" : \"default\"}\n            onClick={currentStep === steps.length - 1 ? handleSubmit : handleNext}\n            disabled={isSubmitting || (currentStep === steps.length - 1 ? !isCurrentStepValid() : false)}\n          >\n            {currentStep === steps.length - 1 ? \n              (isSubmitting ? \"Processing...\" : \"Confirm Cancellation\") : \n              \"Continue\"\n            }\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  );\n}", "startLine": 36, "endLine": 377, "type": "component", "symbols": ["ExitInterview"], "score": 0.6, "context": "Component handling subscription cancellation feedback and cancellation API call, part of subscription lifecycle management.", "includesImports": false}, {"filePath": "src/components/subscription/exit-interview.tsx", "content": "interface ExitInterviewProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  subscriptionId: string;\n  onCancellationComplete?: () => void;\n}", "startLine": 21, "endLine": 26, "type": "type", "symbols": ["ExitInterviewProps"], "score": 0.3, "context": "Defines props for exit interview component related to subscription cancellation feedback.", "includesImports": false}], "additionalFiles": []}}