{"timestamp": "2025-06-14T17:32:49.198Z", "query": "Find all the chat tools and how they integrate with the streaming system. Show me the tool execution flow.", "executionTime": 13287, "snippetsCount": 4, "additionalFilesCount": 0, "totalLines": 945, "snippets": [{"filePath": "src/lib/services/stream-service.ts", "type": "unknown", "context": "This class manages the streaming system and integrates tool calls within the streaming flow, including handling tool-call and tool-result chunks, tool call cost tracking, and tool call repair logic. It is central to understanding the tool execution flow in the streaming system.", "score": 1, "lines": 273, "startLine": 110, "endLine": 382, "symbols": ["StreamService"], "preview": "export class StreamService {\n    private config: StreamServiceConfig;\n    private toolCallCosts: ToolCallCost[] = [];\n\n    constructor(config: StreamServiceConfig) {\n..."}, {"filePath": "src/components/base/chat.tsx", "type": "component", "context": "This React component uses the chat tools and handles tool calls via the onToolCall callback, showing how chat tools integrate with the chat UI and streaming system. It includes handling of tool calls like clientTesting and manages tool results.", "score": 0.9, "lines": 149, "startLine": 41, "endLine": 189, "symbols": ["Cha<PERSON>"], "preview": "const Chat = observer(({\n                           id,\n                           initialMessages,\n                           isReadonly,\n                           initialPrompt,\n..."}, {"filePath": "src/components/base/data-stream-handler.tsx", "type": "component", "context": "This component handles the data stream from the chat, processing different delta types including tool-call and tool-result, user-message-id, ai-message-id, and file operations. It shows how streaming data is handled and integrated with chat tools.", "score": 0.9, "lines": 262, "startLine": 73, "endLine": 334, "symbols": ["DataStreamHandler"], "preview": "export function DataStreamHandler({id, operationChange, onStreamingComplete, onFileOpen, onContinuationFlagChange, onUpdateUserMessage}: {\n    id: string,\n    operationChange: (codeBlock: CodeBlock) => any;\n    onFileOpen: (fileName: string) => any;\n    onContinuationFlagChange?: (needsContinuation: boolean) => void;\n..."}, {"filePath": "src/lib/chat/tools/multi-perspective-analysis.tool.ts", "type": "util", "context": "This is a representative chat tool that integrates with the streaming system by streaming multi-perspective analysis results via dataStream.writeData. It demonstrates how a tool executes and streams results back to the client.", "score": 0.8, "lines": 261, "startLine": 55, "endLine": 315, "symbols": ["multiPerspectiveAnalysis"], "preview": "export const multiPerspectiveAnalysis = ({\n  dataStream,\n}: {\n  dataStream: DataStreamWriter;\n}) => {\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/services/stream-service.ts", "content": "export class StreamService {\n    private config: StreamServiceConfig;\n    private toolCallCosts: ToolCallCost[] = [];\n\n    constructor(config: StreamServiceConfig) {\n        this.config = config;\n    }\n\n    /**\n     * Start the text streaming process\n     * @returns The result of the streamText call\n     */\n    public startStream() {\n        const {\n            messages,\n            parser,\n            diffParser,\n            dataStream,\n            userMessageId,\n            abortSignal,\n            agentModeEnabled = false,\n            isFirstMessage = false,\n            onFinish,\n            tools = {},\n            temperature = 0,\n            maxSteps = 10,\n            generateMessageId = () => crypto.randomUUID(),\n            isSupabaseConnected = false,\n            enabledTools = [],\n            enableValidation,\n            transformChunking = \"line\",\n            modelId,\n            isConnectingSupabase\n        } = this.config;\n        let streamModelId = modelId\n        if(!modelId) {\n            streamModelId = \"anthropic/claude-sonnet-4\"\n            if (!isFirstMessage) {\n                if(agentModeEnabled) {\n                    // streamModelId = 'anthropic/claude-3.7-sonnet';\n                    streamModelId = 'anthropic/claude-sonnet-4';\n                    // streamModelId = 'openai/gpt-4.1';\n                } else {\n                    streamModelId = \"anthropic/claude-sonnet-4\"\n                }\n            }\n            if(isConnectingSupabase) {\n                streamModelId = \"anthropic/claude-sonnet-4\"\n            }\n        }\n\n        return streamText({\n            model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),\n            maxSteps,\n            experimental_activeTools: enabledTools,\n            experimental_toolCallStreaming: true,\n            toolCallStreaming: true,\n            temperature,\n            messages: messages.filter(m => !!m),\n            experimental_transform: smoothStream({chunking: transformChunking}),\n            maxRetries: 3,\n            abortSignal,\n            experimental_continueSteps: true,\n            onError: (error) => {\n                console.log('Error', error);\n                throw error;\n            },\n            onStepFinish: async (stepResult) => {\n                // console.log('stepResult', stepResult)\n                const body = await fetchFromOpenRouter(stepResult.response.id);\n                // console.log('Step result body', body)\n                \n                // Track tool call cost if data is available\n                if (body && stepResult.response.id) {\n                    const toolCallCost: ToolCallCost = {\n                        id: stepResult.response.id,\n                        model: body.data.model || streamModelId,\n                        tokens_prompt: body.data.tokens_prompt || 0,\n                        tokens_completion: body.data.tokens_completion || 0,\n                        native_tokens_prompt: body.data.native_tokens_prompt,\n                        native_tokens_completion: body.data.native_tokens_completion,\n                        total_cost: body.data.total_cost || 0,\n                        cache_discount: body.data.cache_discount,\n                        provider_name: body.data.provider_name,\n                        timestamp: new Date()\n                    };\n                    console.log('toolCallCost', toolCallCost)\n                    this.toolCallCosts.push(toolCallCost);\n                }\n            },\n            onChunk: ({chunk}) => {\n                if (chunk.type === \"text-delta\") {\n                    try {\n                        // First run the file parser\n                        const afterFileParser = parser.parse(userMessageId, chunk.textDelta);\n\n                        // Then run the diff parser on the output of the file parser\n                        diffParser.parse(userMessageId, afterFileParser);\n                    } catch (e) {\n                        console.error('Error in chunk processing', e);\n                    }\n                } else if (chunk.type === \"tool-call\") {\n                    // Send tool call information to the client\n                    dataStream.writeData({\n                        type: 'tool-call',\n                        content: {\n                            toolCallId: chunk.toolCallId,\n                            toolName: chunk.toolName,\n                            args: chunk.args\n                        }\n                    });\n                } else if (chunk.type === \"tool-result\") {\n                    // Send tool result information to the client\n                    dataStream.writeData({\n                        type: 'tool-result',\n                        content: {\n                            toolCallId: chunk.toolCallId,\n                            toolName: chunk.toolName,\n                            result: chunk.result\n                        }\n                    });\n                }\n            },\n            experimental_generateMessageId: generateMessageId,\n            experimental_repairToolCall: async ({\n                                                    toolCall,\n                                                    tools,\n                                                    error,\n                                                    messages,\n                                                    system,\n                                                }) => {\n                console.log('In repair', error);\n                const result = await generateText({\n                    model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),\n                    system,\n                    messages: [\n                        ...messages,\n                        {\n                            role: 'assistant',\n                            content: [\n                                {\n                                    type: 'tool-call',\n                                    toolCallId: toolCall.toolCallId,\n                                    toolName: toolCall.toolName,\n                                    args: toolCall.args,\n                                },\n                            ],\n                        },\n                        {\n                            role: 'tool' as const,\n                            content: [\n                                {\n                                    type: 'tool-result',\n                                    toolCallId: toolCall.toolCallId,\n                                    toolName: toolCall.toolName,\n                                    result: error.message,\n                                },\n                            ],\n                        },\n                    ],\n                    tools,\n                });\n\n                const newToolCall = result.toolCalls.find(\n                    newToolCall => newToolCall.toolName === toolCall.toolName,\n                );\n\n                return newToolCall != null\n                    ? {\n                        toolCallType: 'function' as const,\n                        toolCallId: toolCall.toolCallId,\n                        toolName: toolCall.toolName,\n                        args: JSON.stringify(newToolCall.args),\n                    }\n                    : null;\n            },\n            tools,\n            onFinish: async (params) => {\n                if (onFinish) {\n                    await onFinish({...params, enableValidation});\n                }\n            },\n        });\n    }\n\n    /**\n     * Sanitize response messages by removing incomplete tool calls\n     * @param messages The messages to sanitize\n     * @returns Sanitized messages\n     */\n    public static sanitizeResponseMessages(messages: AIMessage[]): AIMessage[] {\n        return messages.filter(message => {\n            // Filter out messages that are just incomplete tool calls\n            if (message.role === 'assistant' && Array.isArray(message.content)) {\n                const hasOnlyToolCalls = message.content.every(\n                    part => part.type === 'tool-call'\n                );\n\n                if (hasOnlyToolCalls) {\n                    return false;\n                }\n            }\n            return true;\n        });\n    }\n\n    /**\n     * Get the aggregated cost and token usage for all tool calls\n     * @returns Summary of token usage and costs\n     */\n    public getToolCallCostSummary() {\n        if (this.toolCallCosts.length === 0) {\n            return {\n                count: 0,\n                total_tokens_prompt: 0,\n                total_tokens_completion: 0,\n                total_cost: 0,\n                total_cache_discount: 0,\n                net_cost: 0,\n                tool_calls: []\n            };\n        }\n        \n        const total_tokens_prompt = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_prompt || 0), 0);\n        const total_tokens_completion = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_completion || 0), 0);\n        const total_cost = this.toolCallCosts.reduce((sum, call) => sum + (call.total_cost || 0), 0);\n        const total_cache_discount = this.toolCallCosts.reduce((sum, call) => sum + (call.cache_discount || 0), 0);\n        \n        return {\n            count: this.toolCallCosts.length,\n            total_tokens_prompt: round(total_tokens_prompt, 0),\n            total_tokens_completion: round(total_tokens_completion, 0),\n            total_tokens: round(total_tokens_prompt + total_tokens_completion, 0),\n            total_cost: round(total_cost, 6),\n            total_cache_discount: round(total_cache_discount, 6),\n            net_cost: round(total_cost + total_cache_discount, 6),\n            tool_calls: this.toolCallCosts\n        };\n    }\n    \n    /**\n     * Get a simple formatted string representation of the tool call costs\n     * @returns Formatted string with cost summary\n     */\n    public getToolCallCostReport(): string {\n        const summary = this.getToolCallCostSummary();\n        \n        if (summary.count === 0) {\n            return \"No tool calls recorded.\";\n        }\n        \n        return `Tool Call Summary:\n- Total Calls: ${summary.count}\n- Prompt Tokens: ${summary.total_tokens_prompt}\n- Completion Tokens: ${summary.total_tokens_completion}\n- Total Tokens: ${summary.total_tokens}\n- Total Cost: $${summary.total_cost}\n- Cache Discount: $${summary.total_cache_discount}\n- Net Cost: $${summary.net_cost}`;\n    }\n    \n    /**\n     * Create a new instance with updated configuration\n     * @param updatedConfig The updated configuration\n     * @returns A new StreamService instance\n     */\n    public withConfig(updatedConfig: Partial<StreamServiceConfig>): StreamService {\n        return new StreamService({\n            ...this.config,\n            ...updatedConfig,\n        });\n    }\n}", "startLine": 110, "endLine": 382, "type": "unknown", "symbols": ["StreamService"], "score": 1, "context": "This class manages the streaming system and integrates tool calls within the streaming flow, including handling tool-call and tool-result chunks, tool call cost tracking, and tool call repair logic. It is central to understanding the tool execution flow in the streaming system.", "includesImports": false}, {"filePath": "src/components/base/chat.tsx", "content": "const Chat = observer(({\n                           id,\n                           initialMessages,\n                           isReadonly,\n                           initialPrompt,\n                           onLoadingChange,\n                           projectId,\n                           runLastUserMessage,\n                           onInitialRunInitialized,\n                           hasMoreMessages,\n                           totalUserMessages\n                       }: {\n    id: string;\n    initialMessages: Array<UIMessage>;\n    initialPrompt?: string;\n    isReadonly: boolean;\n    onLoadingChange?: (isLoading: boolean) => any;\n    projectId: string;\n    runLastUserMessage: boolean;\n    onInitialRunInitialized: () => any;\n    hasMoreMessages?: boolean;\n    totalUserMessages?: number;\n}) => {\n    const {mutate} = useSWRConfig();\n\n    const {anonymousId, hasReachedLimit, incrementChatCount, storeChat, getStoredChat} = useAnonymousSession();\n    const {status: authStatus} = useSession();\n\n    const {generatorStore, integrationStore, logStore, snackStore} = useStores();\n    const session = generatorStore.getActiveSession(id);\n    const [integrationOpen, setIntegrationOpen] = useState(false);\n    const [isSecretOpen, setIsSecretOpen] = useState(false);\n    const [secretNames, setSecretNames] = useState<string[] | null>(null);\n    const [droppedFiles, setDroppedFiles]= useState<File[]>();\n    const [selectMode, setSelectMode] = useState(false);\n    const [sqlDialogOpen, setSqlDialogOpen] = useState(false);\n    // Define a proper type for SQL queries\n    type SqlQuery = {\n        query: string;\n        source: string;\n        table: string;\n    };\n    const [pendingSqlQueries, setPendingSqlQueries] = useState<SqlQuery[]>([]);\n    const [analysisResult, setAnalysisResult] = useState<{ analysis: CodeChangeAnalysis, message: string } | null>(null);\n    const [isAnalysisDialogOpen, setIsAnalysisDialogOpen] = useState(false);\n    const [isLoadingPrevious, setIsLoadingPrevious] = useState(false);\n    const [isLoadAllDialogOpen, setIsLoadAllDialogOpen] = useState(false);\n    const [hasMoreLocalOverride, setHasMoreLocalOverride] = useState<boolean | undefined>(undefined);\n    if (!session) {\n        return;\n    }\n\n    const {agentModeEnabled} = useAgentModeSettings();\n\n    // Track when a chat is started or resumed\n    useEffect(() => {\n        const isNewChat = initialMessages.length <= 1; // Only system message or empty\n\n        if (isNewChat) {\n            trackMessageEvent('CHAT_STARTED', {\n                chat_id: id,\n                project_id: projectId\n            });\n        } else {\n            trackMessageEvent('CHAT_RESUMED', {\n                chat_id: id,\n                time_since_last_activity: 0, // We don't have this info yet, but could add it\n                session_message_count: initialMessages.length\n            });\n        }\n    }, [id, initialMessages.length, projectId]);\n\n    const {\n        messages,\n        setMessages,\n        handleSubmit,\n        input,\n        setInput,\n        append,\n        isLoading,\n        stop,\n        reload,\n        data,\n        status,\n        experimental_resume,\n        addToolResult\n    } = useChat({\n        id,\n        keepLastMessageOnError: false,\n        experimental_prepareRequestBody: (options: {\n            id: string;\n            messages: UIMessage[];\n            requestData?: JSONValue;\n            requestBody?: object;\n        }) => {\n            options.messages = options.messages.map(message => {\n                message.parts = message.parts.filter(part => {\n                    if(part.type === \"tool-invocation\") {\n                        return part.toolInvocation.state !== \"partial-call\"\n                    }\n                    return true\n                })\n                return message\n            }).slice(-20);\n            return {\n                files: session.fileTree.length ? session.fileTree : DEFAULT_CODE,\n                activeFile: session.activeFile,\n                dependencies: session.dependencies,\n                linkSupabaseProjectId: integrationStore.currentSelectedProjectId,\n                linkSupabaseConnection: integrationStore.getConnection(\"supabase\")?.id,\n                projectId: session.projectId,\n                logs: logStore.getLogs(id),\n                agentModeEnabled: agentModeEnabled,\n                messages: options.messages,\n                id,\n                ...options.requestBody\n            }\n        },\n        headers: anonymousId ? {\n            'x-anonymous-id': anonymousId,\n            'x-chat-count': hasReachedLimit ? '2' : '1',\n        } : {},\n        initialMessages,\n        generateId: () => {\n            return generateUUID();\n        },\n        sendExtraMessageFields: true,\n        streamProtocol: \"data\",\n        onToolCall: async ({toolCall}) => {\n            if(toolCall.toolName === \"clientTesting\") {\n                console.log('[Chat] Client testing tool call received:', toolCall);\n                if (session) {\n                    // Start client testing with the tool call parameters\n                    session.startClientTesting(\n                        toolCall.toolCallId,\n                        (toolCall.args as any)?.featuresToTest || '',\n                        (toolCall.args as any)?.expectations || '',\n                        (toolCall.args as any)?.reason || ''\n                    );\n\n                    console.log('[Chat] Client testing started, waiting for user interaction');\n                    // For human-in-the-loop tools, we don't return anything here\n                    // The tool result will be added via addToolResult when user completes testing\n                    // This prevents the connection from closing prematurely\n                }\n                // Don't return anything for human-in-the-loop tools\n                // The result will be provided later via addToolResult\n            }\n        },", "startLine": 41, "endLine": 189, "type": "component", "symbols": ["Cha<PERSON>"], "score": 0.9, "context": "This React component uses the chat tools and handles tool calls via the onToolCall callback, showing how chat tools integrate with the chat UI and streaming system. It includes handling of tool calls like clientTesting and manages tool results.", "includesImports": false}, {"filePath": "src/components/base/data-stream-handler.tsx", "content": "export function DataStreamHandler({id, operationChange, onStreamingComplete, onFileOpen, onContinuationFlagChange, onUpdateUserMessage}: {\n    id: string,\n    operationChange: (codeBlock: CodeBlock) => any;\n    onFileOpen: (fileName: string) => any;\n    onContinuationFlagChange?: (needsContinuation: boolean) => void;\n    onUpdateUserMessage?: (message: string) => void;\n    onStreamingComplete?: () => void;\n}) {\n    const {data: dataStream, messages, setMessages} = useChat({id, streamProtocol: \"text\"});\n    const {setUserMessageIdFromServer} = useUserMessageId();\n    const {setAIMessageIdFromServer} = useAIMessageId();\n    const {setBlock} = useBlock();\n    const lastProcessedIndex = useRef(-1);\n    const [currentOperation, setCurrentOperation] = useState<CodeBlock>({type: null, fileName: '', content: ''});\n\n    useEffect(() => {\n        if (!dataStream?.length) return;\n\n        const newDeltas = dataStream.slice(lastProcessedIndex.current + 1);\n        lastProcessedIndex.current = dataStream.length - 1;\n\n        (newDeltas as DataStreamDelta[]).forEach((delta: DataStreamDelta) => {\n            if (delta.type === 'user-message-id') {\n                setUserMessageIdFromServer(delta.content as string);\n                return;\n            }\n            if (delta.type === 'ai-message-id') {\n                console.log('AI message id', delta.content);\n                onStreamingComplete && onStreamingComplete();\n                setAIMessageIdFromServer(delta.content as string);\n                // setMessages(messages => {\n                //     messages[messages.length - 1].id = delta.content as string;\n                //     return [...messages];\n                // })\n                return;\n            }\n            if (delta.type === \"open-file\") {\n                onFileOpen((delta.content as FileOpenBlock).filename)\n                return;\n            }\n            \n            if (delta.type === \"file-move\") {\n                const moveOp = delta.content as FileMoveBlock;\n                toast.success(\n                    `File moved from ${moveOp.sourcePath} to ${moveOp.targetPath}`,\n                    {\n                        duration: 4000,\n                        position: \"top-right\",\n                        icon: \"📂\",\n                    }\n                );\n                return;\n            }\n\n            if (delta.type === \"messages-update\") {\n                console.log('messages-update', delta.content, typeof delta.content)\n                // setMessages(messages => {\n                //     (delta.content as MessagesUpdate).messages.reverse().forEach((m, index) => {\n                //         // if (messages[messages.length - 1 - index].id.includes(\"msg-\")) {\n                //         if(messages[messages.length - 1 - index]) {\n                //             messages[messages.length - 1 - index].id = m?.id;\n                //             // @ts-ignore\n                //             messages[messages.length - 1 - index].parentUserMessageId = m?.parentUserMessageId;\n                //             // @ts-ignore\n                //             messages[messages.length - 1 - index].parentAssistantMessageId = m?.parentAssistantMessageId;\n                //             // @ts-ignore\n                //             messages[messages.length - 1 - index].isAssistantGroupHead = m?.isAssistantGroupHead;\n                //         }\n                //         // }\n                //     })\n                //\n                //     // messages = messages.map(message => {\n                //     //     // if (message.id.includes(\"msg-\")) {\n                //     //         // find the corresponding user message\n                //     //         if (message.role === \"user\") {\n                //     //             (delta.content as MessagesUpdate).messages.find(m => m.role === \"user\");\n                //     //         }\n                //     //     // }\n                //     //     return message;\n                //     // })\n                //     return messages;\n                // })\n                return;\n            }\n            \n            if (delta.type === \"credit-usage-event\") {\n                const creditEvent = delta.content as CreditUsageEvent;\n                if (creditEvent.type === \"error-fix\") {\n                    toast.success(\"Error fixing doesn't consume credits! Keep your app running smoothly.\", {\n                        duration: 4000,\n                        position: \"top-right\",\n                        className: \"bg-gradient-to-r from-green-400 to-green-600\"\n                    });\n                } else if (creditEvent.type === \"auto-fix\") {\n                    toast.success(\"I automatically fixed a small issue for you!\", {\n                        description: \"Your app should now work correctly.\",\n                        duration: 4000,\n                        position: \"top-right\",\n                        className: \"bg-gradient-to-r from-green-400 to-green-600\"\n                    });\n                }\n                return;\n            }\n            \n            if (delta.type === \"continuation-flag\") {\n                console.log('delta', JSON.stringify(delta))\n                const continuationEvent = delta.content as ContinuationFlagEvent;\n                console.log(`[DataStreamHandler] Continuation flag: ${continuationEvent?.needsContinuation}`);\n                if (onContinuationFlagChange) {\n                    onContinuationFlagChange(!!continuationEvent?.needsContinuation);\n                }\n                return;\n            }\n\n            if (delta.type === \"update-user-message\") {\n                if(onUpdateUserMessage) {\n                    onUpdateUserMessage((delta.content as UpdateUserMessage)?.userMessage)\n                }\n                return;\n            }\n            \n            // Handle validation status messages\n            if (delta.type === \"validating-output\" || delta.type === \"validated-output\") {\n                console.log('delta.type', delta.type)\n                const validationMessage = delta.content as ValidationMessage;\n                \n                // Create a special validation message with a distinct visual style\n                const validationContent = delta.type === \"validating-output\" ?\n                    `🔍 ${validationMessage.message}` :\n                    `✅ ${validationMessage.message}`;\n                \n                // Add the validation message to the chat as a UIMessage (from 'ai' package)\n                const newMessage: UIMessage = {\n                    id: `validation-${Date.now()}`,\n                    role: 'system',\n                    content: validationContent\n                };\n                \n                // Update messages with the new validation message\n                setMessages([...messages, newMessage]);\n                return;\n            }\n\n            if (delta.type === 'file-operation') {\n                const op: CodeBlock = delta.content as CodeBlock;\n                setCurrentOperation({\n                    type: op.type,\n                    fileName: op.fileName || op.absolutePath || '',\n                    absolutePath: op.absolutePath || op.fileName || '',\n                    content: op.content || ''\n                });\n            }\n\n\n            // const content = delta.content as string;\n\n            // // Detect file operations\n            // if (content.includes('<create_file name=\"')) {\n            //     const fileName = content.match(/name=\"([^\"]+)\"/)?.[1];\n            //     setCurrentOperation({\n            //         type: 'create',\n            //         fileName: fileName || '',\n            //         content: ''\n            //     });\n            // } else if (content.includes('<edit_file name=\"')) {\n            //     const fileName = content.match(/name=\"([^\"]+)\"/)?.[1];\n            //     setCurrentOperation({\n            //         type: 'edit',\n            //         fileName: fileName || '',\n            //         content: ''\n            //     });\n            // } else if (content.includes('</create_file>') || content.includes('</edit_file>')) {\n            //     setCurrentOperation({type: null, fileName: '', content: ''});\n            // } else if (currentOperation.type) {\n            //     // Accumulate content\n            //     setCurrentOperation(prev => ({\n            //         ...prev,\n            //         content: prev.content + content\n            //     }));\n            // }\n\n            setBlock((draftBlock) => {\n                if (!draftBlock) {\n                    return {...initialBlockData, status: 'streaming'};\n                }\n\n                switch (delta.type) {\n                    case 'id':\n                        return {\n                            ...draftBlock,\n                            documentId: delta.content as string,\n                            status: 'streaming',\n                        };\n\n                    case 'title':\n                        return {\n                            ...draftBlock,\n                            title: delta.content as string,\n                            status: 'streaming',\n                        };\n\n                    case 'kind':\n                        return {\n                            ...draftBlock,\n                            kind: delta.content as BlockKind,\n                            status: 'streaming',\n                        };\n\n                    case 'text-delta':\n                        return {\n                            ...draftBlock,\n                            content: draftBlock.content + (delta.content as string),\n                            isVisible:\n                                draftBlock.status === 'streaming' &&\n                                draftBlock.content.length > 400 &&\n                                draftBlock.content.length < 450\n                                    ? true\n                                    : draftBlock.isVisible,\n                            status: 'streaming',\n                        };\n\n                    case 'code-delta':\n                        return {\n                            ...draftBlock,\n                            content: delta.content as string,\n                            isVisible:\n                                draftBlock.status === 'streaming' &&\n                                draftBlock.content.length > 300 &&\n                                draftBlock.content.length < 310\n                                    ? true\n                                    : draftBlock.isVisible,\n                            status: 'streaming',\n                        };\n\n                    case 'clear':\n                        return {\n                            ...draftBlock,\n                            content: '',\n                            status: 'streaming',\n                        };\n\n                    case 'finish':\n                        return {\n                            ...draftBlock,\n                            status: 'idle',\n                        };\n\n                    default:\n                        return draftBlock;\n                }\n            });\n        });\n    }, [dataStream, setBlock, setUserMessageIdFromServer, setAIMessageIdFromServer]);\n\n    useEffect(() => {\n        if (operationChange) {\n            operationChange(currentOperation);\n        }\n    }, [currentOperation]);\n\n    return null;\n}", "startLine": 73, "endLine": 334, "type": "component", "symbols": ["DataStreamHandler"], "score": 0.9, "context": "This component handles the data stream from the chat, processing different delta types including tool-call and tool-result, user-message-id, ai-message-id, and file operations. It shows how streaming data is handled and integrated with chat tools.", "includesImports": false}, {"filePath": "src/lib/chat/tools/multi-perspective-analysis.tool.ts", "content": "export const multiPerspectiveAnalysis = ({\n  dataStream,\n}: {\n  dataStream: DataStreamWriter;\n}) => {\n  return tool({\n    description: 'Analyze a topic from multiple perspectives using different AI personas to reach a more comprehensive and balanced understanding',\n    parameters: multiPerspectiveAnalysisSchema,\n    execute: async ({ topic, context = '', focusAreas = [], requiredPersonas = [], analysisDepth = 'standard', discussionRounds = 3 }) => {\n      try {\n        // Stream the initial setup information\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `# Multi-Perspective Analysis: ${topic}\\n\\nInitiating analysis with multiple perspectives...\\n\\n`\n        });\n\n        // Select which personas to use\n        const selectedPersonaNames = requiredPersonas.length > 0 ? requiredPersonas : personaList;\n        \n        if (selectedPersonaNames.length === 0) {\n          throw new Error('No valid personas selected for analysis');\n        }\n\n        // Determine token allocation based on analysis depth\n        let maxTokensPerPersona;\n        switch (analysisDepth) {\n          case 'brief':\n            maxTokensPerPersona = 500;\n            break;\n          case 'comprehensive':\n            maxTokensPerPersona = 1500;\n            break;\n          case 'standard':\n          default:\n            maxTokensPerPersona = 1000;\n        }\n\n        // Track all contributions to the discussion\n        type Contribution = {\n          persona: PersonaName;\n          content: string;\n          round: number;\n          replyTo?: PersonaName[];\n        };\n\n        const contributions: Contribution[] = [];\n        \n        // Initialize the discussion with the topic introduction\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `## Multi-Perspective Discussion\\n\\n`\n        });\n\n        // PHASE 1: Initial perspectives from each persona\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `### Round 1: Initial Perspectives\\n\\n`\n        });\n\n        // Generate initial perspectives from each persona\n        for (const personaName of selectedPersonaNames) {\n          dataStream.writeData({\n            type: 'multi-perspective-update',\n            content: `#### ${personaName}:\\n\\n`\n          });\n\n          // Create the prompt for this persona\n          const personaPrompt = `\nYou are the \"${personaName}\" persona with the following characteristics:\n${personaDescriptions[personaName]}\n\nAnalyze the following topic from your unique perspective:\nTopic: ${topic}\n${context ? `Context: ${context}` : ''}\n${focusAreas.length > 0 ? `Focus Areas: ${focusAreas.join(', ')}` : ''}\n\nProvide your analysis in a clear, concise manner that reflects your perspective. Be insightful but concise.\n`;\n\n          // Generate the persona's perspective\n          const result = streamText({\n            model: customModel(\"anthropic/claude-sonnet-4\"),\n            temperature: 0.7,\n            messages: [\n              {\n                role: 'system',\n                content: personaPrompt\n              }\n            ]\n          });\n          \n          let perspective = '';\n          for await (const chunk of result.textStream) {\n            perspective += chunk;\n          }\n\n          // Add this perspective to the contributions\n          contributions.push({\n            persona: personaName,\n            content: perspective,\n            round: 1\n          });\n\n          dataStream.writeData({\n            type: 'multi-perspective-update',\n            content: `${perspective}\\n\\n`\n          });\n        }\n\n        // PHASE 2: Interactive discussion rounds\n        // Each round, personas respond to previous contributions\n        for (let round = 2; round <= discussionRounds; round++) {\n          dataStream.writeData({\n            type: 'multi-perspective-update',\n            content: `### Round ${round}: Developing the Discussion\\n\\n`\n          });\n\n          // Get all contributions from the previous round\n          const previousRoundContributions = contributions.filter(c => c.round === round - 1);\n          \n          // Each persona responds to the previous round\n          for (const personaName of selectedPersonaNames) {\n            // Get all previous contributions except this persona's own contribution\n            const relevantContributions = previousRoundContributions\n              .filter(c => c.persona !== personaName);\n              \n            if (relevantContributions.length === 0) continue;\n            \n            dataStream.writeData({\n              type: 'multi-perspective-update',\n              content: `#### ${personaName}:\\n\\n`\n            });\n\n            // Format the previous contributions for the prompt\n            const previousContributionsText = relevantContributions\n              .map(c => `${c.persona}: ${c.content}`)\n              .join('\\n\\n');\n\n            // Create a prompt that encourages building on previous ideas\n            const dialoguePrompt = `\nYou are the \"${personaName}\" persona with the following characteristics:\n${personaDescriptions[personaName]}\n\nYou're participating in round ${round} of a discussion on \"${topic}\".\n\nHere are the most recent contributions from other participants:\n\n${previousContributionsText}\n\nBased on your unique perspective and the discussion so far:\n1. Respond to at least 2 specific points made by others\n2. Build upon or challenge ideas in a constructive way\n3. Introduce a new insight or question that advances the discussion\n4. Maintain your distinct perspective while seeking common ground\n\nBe concise but insightful. Your goal is to deepen the collective understanding.\n`;\n\n            // Generate the persona's response\n            const result = streamText({\n              model: customModel(\"anthropic/claude-sonnet-4\"),\n              temperature: 0.7,\n              messages: [\n                {\n                  role: 'system',\n                  content: dialoguePrompt\n                }\n              ]\n            });\n            \n            let response = '';\n            for await (const chunk of result.textStream) {\n              response += chunk;\n            }\n\n            // Add this response to the contributions\n            contributions.push({\n              persona: personaName,\n              content: response,\n              round: round,\n              replyTo: relevantContributions.map(c => c.persona)\n            });\n\n            dataStream.writeData({\n              type: 'multi-perspective-update',\n              content: `${response}\\n\\n`\n            });\n          }\n        }\n\n        // PHASE 3: Generate synthesis and consensus\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `## Synthesis and Consensus\\n\\n`\n        });\n\n        // Compile all contributions for synthesis\n        const allContent = contributions\n          .map(c => `Round ${c.round} - ${c.persona}: ${c.content}`)\n          .join('\\n\\n');\n\n        // Create the synthesis prompt\n        const synthesisPrompt = `\nYou are a neutral synthesizer tasked with finding common ground and key insights from multiple perspectives on the topic: \"${topic}\"\n\nHere are all the perspectives and dialogue exchanges:\n\n${allContent}\n\nPlease provide:\n1. A summary of key points of agreement across perspectives\n2. Important points of disagreement or tension\n3. Unique insights contributed by each perspective\n4. A balanced synthesis that incorporates the strongest elements from each viewpoint\n5. Remaining questions or areas for further exploration\n\nYour synthesis should be fair to all perspectives while highlighting the most valuable insights from each.\n`;\n\n        // Generate the synthesis\n        const synthesisResult = streamText({\n          model: customModel(\"anthropic/claude-sonnet-4\"),\n          temperature: 0.7,\n          messages: [\n            {\n              role: 'system',\n              content: synthesisPrompt\n            }\n          ]\n        });\n        \n        let synthesis = '';\n        for await (const chunk of synthesisResult.textStream) {\n          synthesis += chunk;\n        }\n\n        // Synthesis is built in the streaming loop above\n\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: synthesis\n        });\n\n        // Return the complete analysis results\n        return {\n          topic,\n          contributions,\n          synthesis,\n          result: 'success'\n        };\n      } catch (error) {\n        console.error('Error in multi-perspective analysis:', error);\n        dataStream.writeData({\n          type: 'error',\n          error: error instanceof Error ? error.message : 'Failed to complete multi-perspective analysis'\n        });\n        throw error;\n      }\n    }\n  });\n};", "startLine": 55, "endLine": 315, "type": "util", "symbols": ["multiPerspectiveAnalysis"], "score": 0.8, "context": "This is a representative chat tool that integrates with the streaming system by streaming multi-perspective analysis results via dataStream.writeData. It demonstrates how a tool executes and streams results back to the client.", "includesImports": false}], "additionalFiles": []}}