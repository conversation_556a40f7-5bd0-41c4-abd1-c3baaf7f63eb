{"timestamp": "2025-06-14T17:35:37.987Z", "query": "How does the deployment system work? Show me the deployment store and how it manages different platforms", "executionTime": 14143, "snippetsCount": 2, "additionalFilesCount": 0, "totalLines": 471, "snippets": [{"filePath": "src/stores/DeploymentStore.ts", "type": "type", "context": "These types define the deployment platforms, feature statuses, deployment statuses, and the Deployment interface which are fundamental to understanding the deployment system and how it manages different platforms.", "score": 1, "lines": 18, "startLine": 5, "endLine": 22, "symbols": ["DeploymentPlatform, PlatformFeatureStatus, DeploymentStatus, Deployment"], "preview": "export type DeploymentPlatform = 'web' | 'android' | 'ios';\n\nexport type PlatformFeatureStatus = 'available' | 'coming_soon' | 'beta';\nexport type DeploymentStatus = 'idle' | 'queued' | 'processing' | 'deploying' | 'completed' | 'success' | 'failed';\n\n..."}, {"filePath": "src/stores/DeploymentStore.ts", "type": "unknown", "context": "The DeploymentStore class contains the core logic for managing deployments, including platform features, deployment state, deployment methods, status updates, polling for build status, and loading deployments from the database. It directly addresses how the deployment system works and manages different platforms.", "score": 1, "lines": 453, "startLine": 24, "endLine": 476, "symbols": ["DeploymentStore"], "preview": "export class DeploymentStore {\n  rootStore: RootStore;\n\n  @observable deploymentDialogOpen = false;\n  @observable selectedPlatform: DeploymentPlatform = 'android';\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/stores/DeploymentStore.ts", "content": "export type DeploymentPlatform = 'web' | 'android' | 'ios';\n\nexport type PlatformFeatureStatus = 'available' | 'coming_soon' | 'beta';\nexport type DeploymentStatus = 'idle' | 'queued' | 'processing' | 'deploying' | 'completed' | 'success' | 'failed';\n\nexport interface Deployment {\n  id: string;\n  buildId?: string;\n  platform: DeploymentPlatform;\n  url?: string;\n  slug?: string; // Subdomain for web deployments (slug.web.magically.life)\n  status: DeploymentStatus;\n  version: string;\n  createdAt: Date;\n  error?: string;\n  fileStateId?: string;\n  dbRecordId?: string; // Reference to the database record ID\n}", "startLine": 5, "endLine": 22, "type": "type", "symbols": ["DeploymentPlatform, PlatformFeatureStatus, DeploymentStatus, Deployment"], "score": 1, "context": "These types define the deployment platforms, feature statuses, deployment statuses, and the Deployment interface which are fundamental to understanding the deployment system and how it manages different platforms.", "includesImports": false}, {"filePath": "src/stores/DeploymentStore.ts", "content": "export class DeploymentStore {\n  rootStore: RootStore;\n\n  @observable deploymentDialogOpen = false;\n  @observable selectedPlatform: DeploymentPlatform = 'android';\n  @observable isDeploying = false;\n\n  // Platform features availability\n  @observable platformFeatures = {\n    web: {\n      deploy: 'available' as PlatformFeatureStatus\n    },\n    android: {\n      deploy: 'available' as PlatformFeatureStatus,\n      apkDownload: 'available' as PlatformFeatureStatus,\n      playStore: 'coming_soon' as PlatformFeatureStatus\n    },\n    ios: { deploy: 'coming_soon' as PlatformFeatureStatus }\n  };\n  \n  // Map of projectId to list of deployments\n  @observable deployments = new Map<string, Deployment[]>();\n\n  constructor(rootStore: RootStore) {\n    this.rootStore = rootStore;\n    this.deployments = new Map<string, Deployment[]>();\n    makeAutoObservable(this);\n  }\n\n  @action\n  toggleDeploymentDialog(open: boolean) {\n    this.deploymentDialogOpen = open;\n  }\n\n  @action\n  setSelectedTarget(platform: DeploymentPlatform) {\n    this.selectedPlatform = platform;\n  }\n\n  @action\n  async deployProject(projectId: string, platform: DeploymentPlatform) {\n    // This method is now used for publishing projects\n    // Web deployment is now available, so we don't need to check for coming_soon status\n    \n    if (platform === 'ios' && this.platformFeatures.ios.deploy === 'coming_soon') {\n      // Show notification for coming soon feature\n      if (this.rootStore.notificationStore) {\n        this.rootStore.notificationStore.showNotification({\n          title: 'iOS Publishing',\n          message: 'Publishing to iOS is coming soon!',\n          type: 'info',\n          duration: 3000\n        });\n      }\n      return { status: 'coming_soon' };\n    }\n    \n    if (this.isDeploying) {\n      // Show notification for already deploying\n      if (this.rootStore.notificationStore) {\n        this.rootStore.notificationStore.showNotification({\n          title: 'Publishing in Progress',\n          message: 'Your app is already being published. Please wait.',\n          type: 'warning',\n          duration: 3000\n        });\n      }\n      return { status: 'already_deploying' };\n    }\n    \n    this.isDeploying = true;\n    \n    try {\n      // Show notification for starting deployment\n      if (this.rootStore.notificationStore) {\n        this.rootStore.notificationStore.showNotification({\n          title: 'Publishing Started',\n          message: `Publishing your app to ${platform}...`,\n          type: 'info',\n          duration: 3000\n        });\n      }\n      \n      // Get the latest version number for this project\n      const latestDeployments = this.getDeployments(projectId);\n      const latestVersion = latestDeployments.length > 0 \n        ? latestDeployments.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0].version\n        : '1.0.0';\n      \n      // Increment the version number (patch by default)\n      const newVersion = incrementVersion(latestVersion, 'patch');\n\n      // Create a new deployment record\n      const deployment: Deployment = {\n        id: crypto.randomUUID(),\n        platform: platform,\n        status: 'queued',\n        version: newVersion,\n        createdAt: new Date(),\n      };\n      \n      // Add to deployments map\n      const projectDeployments = this.getDeployments(projectId);\n      this.deployments.set(projectId, [...projectDeployments, deployment]);\n      \n      // Call the API to deploy the project\n      const response = await fetch(`/api/project/${projectId}/deploy`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          projectId,\n          platform: platform, // Ensure we're using the correct parameter name (platform instead of platform)\n          version: newVersion,\n          deploymentId: deployment.id\n        }),\n      });\n      \n      const data = await response.json();\n      \n      if (!response.ok) {\n        throw new Error(data.error || 'Deployment failed');\n      }\n      \n      // Update with database record ID\n      if (data.dbRecordId) {\n        deployment.dbRecordId = data.dbRecordId;\n      }\n      \n      // If this is a build that needs status polling (android)\n      if (data.buildId) {\n        // Update the deployment record with the build ID and status\n        this.updateDeploymentStatus(\n            projectId,\n          deployment.id, \n          data.status as DeploymentStatus, \n          data.url,\n          undefined,\n          data.buildId,\n          data.fileStateId\n        );\n        \n        // Start polling for build status\n        this.pollBuildStatus(projectId, deployment.id, data.buildId);\n        \n        return { \n          status: 'queued', \n          deploymentId: deployment.id,\n          version: newVersion\n        };\n      }\n      \n      // For immediate deployments or coming soon features\n      if (data.status === 'coming_soon') {\n        this.updateDeploymentStatus(projectId, deployment.id, 'completed', undefined);\n        return { status: 'coming_soon' };\n      }\n      \n      // For successful deployments\n      this.updateDeploymentStatus(\n          projectId,\n        deployment.id, \n        'success', \n        data.url,\n        undefined,\n        undefined,\n        data.fileStateId\n      );\n      \n      return { \n        status: 'success', \n        url: data.url,\n        version: newVersion\n      };\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      console.error('Deployment error:', error);\n      \n      return { \n        status: 'error',\n        error: errorMessage \n      };\n    } finally {\n      this.isDeploying = false;\n    }\n  }\n\n  @action\n  private updateDeploymentStatus(\n      projectId: string,\n    deploymentId: string, \n    status: DeploymentStatus, \n    url?: string, \n    error?: string,\n    buildId?: string,\n    fileStateId?: string\n  ) {\n    const projectDeployments = this.deployments.get(projectId) || [];\n    const updatedDeployments = projectDeployments.map(deployment => {\n      if (deployment.id === deploymentId) {\n        const updatedDeployment = {\n          ...deployment,\n          status,\n          error,\n          buildId: buildId || deployment.buildId,\n          fileStateId: fileStateId || deployment.fileStateId\n        };\n        \n        if (url) updatedDeployment.url = url;\n        \n        // Update the database record if we have a dbRecordId\n        // if (deployment.dbRecordId) {\n        //   this.updateDeploymentInDatabase(deployment.dbRecordId, projectId, status, url, error);\n        // }\n        \n        return updatedDeployment;\n      }\n      return deployment;\n    });\n    \n    this.deployments.set(projectId, updatedDeployments);\n  }\n\n  @action\n  getDeployments(projectId: string): Deployment[] {\n    if (!this.deployments) {\n      this.deployments = new Map<string, Deployment[]>();\n    }\n    return this.deployments.get(projectId) || [];\n  }\n\n  @action\n  isPlatformAvailable(platform: DeploymentPlatform): boolean {\n    return this.platformFeatures[platform]?.deploy === 'available';\n  }\n\n  @action\n  getLatestDeployment(projectId: string, platform: DeploymentPlatform): Deployment | undefined {\n    const deployments = this.getDeployments(projectId);\n    return deployments\n      .filter(d => d.platform === platform)\n      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0];\n  }\n  \n  @action\n  getLatestDeploymentUrl(projectId: string, platform: DeploymentPlatform): string | undefined {\n    const latest = this.getLatestDeployment(projectId, platform);\n    return latest?.url;\n  }\n  \n  @action\n  getDeploymentStatus(projectId: string, platform: DeploymentPlatform): DeploymentStatus | undefined {\n    const latest = this.getLatestDeployment(projectId, platform);\n    return latest?.status;\n  }\n  \n  // Poll for build status updates\n  private async pollBuildStatus(projectId: string, deploymentId: string, buildId: string) {\n    const maxAttempts = 60; // Maximum number of polling attempts (5 minutes with 5-second intervals)\n    const interval = 5000; // Poll every 5 seconds\n    let attempts = 0;\n    \n    // Log polling start\n    console.log(`Starting build status polling for buildId: ${buildId}, deploymentId: ${deploymentId}`);\n    \n    const checkStatus = async () => {\n      try {\n        // Check if we've reached max attempts\n        if (attempts >= maxAttempts) {\n          console.log(`Polling timed out after ${maxAttempts} attempts for buildId: ${buildId}`);\n          this.updateDeploymentStatus(projectId, deploymentId, 'failed', undefined, 'Build timed out');\n          return;\n        }\n        \n        // Get the current deployment\n        const projectDeployments = this.deployments.get(projectId) || [];\n        const deployment = projectDeployments.find(d => d.id === deploymentId);\n        \n        // If deployment doesn't exist or is already in a final state, stop polling\n        if (!deployment || ['success', 'completed', 'failed'].includes(deployment.status)) {\n          console.log(`Stopping polling for buildId: ${buildId} - deployment status: ${deployment?.status || 'not found'}`);\n          return;\n        }\n        \n        console.log(`Polling attempt ${attempts + 1} for buildId: ${buildId}`);\n        \n        // Fetch build status\n        const response = await fetch(`/api/project/${projectId}/deploy/status?buildId=${buildId}`);\n        \n        if (!response.ok) {\n          console.error(`Error response from status API: ${response.status} ${response.statusText}`);\n          attempts++;\n          setTimeout(checkStatus, interval);\n          return;\n        }\n        \n        const data = await response.json();\n        \n        // Update deployment status based on build status\n        runInAction(() => {\n          if (data.status === 'completed') {\n            this.updateDeploymentStatus(\n                projectId,\n              deploymentId, \n              'completed', // Match the status in the database schema\n              data.apkUrl || data.url\n            );\n            \n            // Show success notification\n            if (data.apkUrl) {\n              // Use the rootStore to show a notification if available\n              if (this.rootStore.notificationStore) {\n                this.rootStore.notificationStore.showNotification({\n                  title: 'Build Completed',\n                  message: 'Your APK is ready for download',\n                  type: 'success',\n                  duration: 5000\n                });\n              }\n            }\n          } else if (data.status === 'failed') {\n            this.updateDeploymentStatus(\n                projectId,\n              deploymentId, \n              'failed', \n              undefined, \n              data.error || 'Build failed'\n            );\n            \n            // Show error notification\n            if (this.rootStore.notificationStore) {\n              this.rootStore.notificationStore.showNotification({\n                title: 'Build Failed',\n                message: data.error || 'Build process failed',\n                type: 'error',\n                duration: 5000\n              });\n            }\n          } else {\n            // Still in progress, update status and continue polling\n            this.updateDeploymentStatus(\n                projectId,\n              deploymentId, \n              data.status as DeploymentStatus\n            );\n            \n            attempts++;\n            setTimeout(checkStatus, interval);\n          }\n        });\n      } catch (error) {\n        console.error('Error polling build status:', error);\n        attempts++;\n        setTimeout(checkStatus, interval);\n      }\n    };\n    \n    // Start polling\n    checkStatus();\n  }\n  \n  // Update deployment record in the database\n  private async updateDeploymentInDatabase(\n    dbRecordId: string,\n    projectId: string,\n    status: string,\n    url?: string,\n    error?: string\n  ) {\n    try {\n      console.log(`Updating deployment in database: ID=${dbRecordId}, status=${status}`);\n      \n      const response = await fetch(`/api/project/${projectId}/deploy/update`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          id: dbRecordId, // Make sure we're using the correct parameter name\n          status,\n          url,\n          error,\n        }),\n      });\n      \n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(`API returned ${response.status}: ${JSON.stringify(errorData)}`);\n      }\n      \n      console.log(`Successfully updated deployment ${dbRecordId} to status: ${status}`);\n    } catch (error) {\n      console.error('Failed to update deployment in database:', error);\n    }\n  }\n  \n  // Load deployments from the database\n  @action\n  async loadDeploymentsFromDatabase(projectId: string) {\n    try {\n      console.log(`Loading deployments for projectId: ${projectId}`);\n      \n      const response = await fetch(`/api/project/${projectId}/deploy/list`);\n      \n      if (!response.ok) {\n        const errorText = await response.text().catch(() => 'Unknown error');\n        throw new Error(`Failed to load deployments: ${response.status} ${errorText}`);\n      }\n      \n      const data = await response.json();\n      \n      console.log(`Loaded ${data.deployments?.length || 0} deployments from database`);\n      \n      // Convert database records to Deployment objects\n      const deployments: Deployment[] = (data.deployments || []).map((record: any) => ({\n        id: record.id, // Use the database ID as the local ID\n        dbRecordId: record.id,\n        platform: record.platform as DeploymentPlatform,\n        status: record.status as DeploymentStatus,\n        version: record.version,\n        url: record.url,\n        slug: record.slug, // Add slug for web deployments\n        buildId: record.buildId,\n        fileStateId: record.fileStateId,\n        createdAt: new Date(record.createdAt || record.created_at),\n        error: record.error,\n      }));\n      \n      // Update the deployments map\n      this.deployments.set(projectId, deployments);\n      \n      // We're no longer automatically polling for in-progress builds\n      // This prevents excessive API calls\n      \n      return deployments;\n    } catch (error) {\n      console.error('Error loading deployments:', error);\n      \n      // Show error notification\n      if (this.rootStore.notificationStore) {\n        this.rootStore.notificationStore.showNotification({\n          title: 'Error Loading Deployments',\n          message: error instanceof Error ? error.message : 'Failed to load deployments',\n          type: 'error',\n          duration: 5000\n        });\n      }\n      \n      return [];\n    }\n  }\n}", "startLine": 24, "endLine": 476, "type": "unknown", "symbols": ["DeploymentStore"], "score": 1, "context": "The DeploymentStore class contains the core logic for managing deployments, including platform features, deployment state, deployment methods, status updates, polling for build status, and loading deployments from the database. It directly addresses how the deployment system works and manages different platforms.", "includesImports": false}], "additionalFiles": []}}