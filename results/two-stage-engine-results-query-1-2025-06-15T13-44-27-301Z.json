{"timestamp": "2025-06-15T13:44:27.302Z", "query": "Find the credit reset bug causing the subscription to not reset properly. Show me the subscription management, billing system, and credit tracking code.", "executionTime": 15394, "snippetsCount": 6, "additionalFilesCount": 0, "totalLines": 141, "snippets": [{"filePath": "src/lib/subscription/credit-usage.ts", "type": "util", "context": "This function updates credit usage for a user subscription, including counting usage in the current billing cycle and updating the subscription record. It is central to credit reset and usage tracking.", "score": 1, "lines": 58, "startLine": 12, "endLine": 69, "symbols": ["updateCreditUsage"], "preview": "export async function updateCreditUsage(userId: string, operations: CreditOperation[]) {\n    console.log(`[Messages] Updating message usage for user ${userId}`);\n    \n    // Get the latest active subscription\n    const subscription = await db\n..."}, {"filePath": "src/lib/db/schema.ts", "type": "type", "context": "The subscriptions table schema defines fields like credits, creditsUsed, resetDate, and status which are critical for understanding subscription credit reset behavior.", "score": 0.9, "lines": 37, "startLine": 378, "endLine": 414, "symbols": ["subscriptions table schema"], "preview": "export const subscriptions = pgTable('Subscription', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  userId: varchar('userId', { length: 255 }).notNull(),\n  status: varchar('status', { length: 50 }).notNull().default('inactive'),\n  planId: varchar('planId', { length: 50 }).notNull().default('free'),\n..."}, {"filePath": "src/lib/db/queries.ts", "type": "util", "context": "Function to fetch subscription by ID, useful for inspecting subscription state during credit reset.", "score": 0.8, "lines": 5, "startLine": 1529, "endLine": 1533, "symbols": ["getSubscriptionById"], "preview": "export async function getSubscriptionById(id: string): Promise<typeof subscriptionsSchema.$inferSelect | undefined> {\n  try {\n    const result = await db.select().from(subscriptionsSchema).where(eq(subscriptionsSchema.id, id)).limit(1);\n    return result[0];\n  } catch (error) {"}, {"filePath": "src/lib/credit/CreditUsageTracker.ts", "type": "util", "context": "This method tracks credit operations which are relevant to credit usage and reset logic.", "score": 0.7, "lines": 8, "startLine": 18, "endLine": 25, "symbols": ["CreditUsageTracker.trackOperation"], "preview": "    trackOperation(type: CreditOperationType, count: number = 1) {\n        const existingOp = this.operations.find(op => op.type === type);\n        if (existingOp) {\n            existingOp.count += count;\n        } else {\n..."}, {"filePath": "src/lib/db/queries.ts", "type": "util", "context": "Function updates subscription status and metadata on cancellation, including potentially updating resetDate which may affect credit reset logic.", "score": 0.7, "lines": 29, "startLine": 1539, "endLine": 1567, "symbols": ["updateSubscriptionOnCancel"], "preview": "export async function updateSubscriptionOnCancel(\n  id: string,\n  isActive: boolean,\n  newMetadata: Record<string, any>,\n  currentMetadata?: Record<string, any> | null\n..."}, {"filePath": "src/lib/credit/CreditUsageTracker.ts", "type": "util", "context": "This method clears tracked credit operations, potentially relevant to resetting credits.", "score": 0.6, "lines": 4, "startLine": 80, "endLine": 83, "symbols": ["CreditUsageTracker.clear"], "preview": "    clear() {\n        this.operations = [];\n        this.discountedOperations = [];\n    }"}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/subscription/credit-usage.ts", "content": "export async function updateCreditUsage(userId: string, operations: CreditOperation[]) {\n    console.log(`[Messages] Updating message usage for user ${userId}`);\n    \n    // Get the latest active subscription\n    const subscription = await db\n        .select()\n        .from(subscriptions)\n        .where(eq(subscriptions.userId, userId))\n        .orderBy(desc(subscriptions.updatedAt));\n\n    if (!subscription || subscription.length === 0) {\n        console.log(`[Messages] No subscription found for user ${userId}`);\n        return 0;\n    }\n\n    const sub = subscription.find(sub => sub.status === 'active') || subscription[0];\n    if (!sub) {\n        console.log(`[Messages] No active subscription found for user ${userId}`);\n        return 0;\n    }\n    \n    // Get the billing cycle start date\n    const cycleStartDate = sub.createdAt;\n    console.log(`[Messages] Current billing cycle started at: ${cycleStartDate}`);\n    \n    // Count the number of messages (token consumption records) in the current billing cycle\n    const messageCountResult = await db\n        .select({\n            messageCount: sql<number>`COUNT(*)`\n        })\n        .from(tokenConsumption)\n        .where(\n            and(\n                eq(tokenConsumption.userId, userId),\n                // Use a safer approach that converts dates to ISO strings\n                sql`${tokenConsumption.createdAt} >= ${cycleStartDate instanceof Date ? cycleStartDate.toISOString() : cycleStartDate}`,\n                ne(tokenConsumption.discounted, true)\n            ),\n        );\n    \n    const messagesUsed = messageCountResult[0]?.messageCount || 0;\n    console.log(`[Messages] Total messages used in current cycle: ${messagesUsed}`);\n    \n    // Update the subscription with the message count\n    // Note: We're still using the creditsUsed field for now, but it now represents messages used\n    await db.transaction(async (tx) => {\n        await tx\n            .update(subscriptions)\n            .set({\n                creditsUsed: messagesUsed, // This now represents messages used, not credits\n                updatedAt: new Date(),\n            })\n            .where(eq(subscriptions.id, sub.id));\n    });\n    \n    console.log(`[Messages] Updated subscription ${sub.id} with ${messagesUsed} messages used`);\n    return messagesUsed;\n}", "startLine": 12, "endLine": 69, "type": "util", "symbols": ["updateCreditUsage"], "score": 1, "context": "This function updates credit usage for a user subscription, including counting usage in the current billing cycle and updating the subscription record. It is central to credit reset and usage tracking.", "includesImports": false}, {"filePath": "src/lib/db/schema.ts", "content": "export const subscriptions = pgTable('Subscription', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  userId: varchar('userId', { length: 255 }).notNull(),\n  status: varchar('status', { length: 50 }).notNull().default('inactive'),\n  planId: varchar('planId', { length: 50 }).notNull().default('free'),\n  subscriptionId: varchar('subscriptionId', { length: 255 }).unique(),\n  credits: integer('credits').notNull().default(50), // Default to free tier credits\n  creditsUsed: integer('creditsUsed').notNull().default(0),\n  isActive: boolean('isActive').default(false),\n  // LemonSqueezy fields\n  lemonSqueezyCustomerId: varchar('lemonSqueezyCustomerId'),\n  lemonSqueezySubscriptionId: varchar('lemonSqueezySubscriptionId'),\n  lemonSqueezyOrderId: varchar('lemonSqueezyOrderId'),\n  lemonSqueezyVariantId: varchar('lemonSqueezyVariantId'),\n  provider: varchar('provider'),\n  // Stripe fields (for compatibility)\n  stripeCustomerId: varchar('stripeCustomerId'),\n  stripeSubscriptionId: varchar('stripeSubscriptionId'),\n  stripePriceId: varchar('stripePriceId'),\n  stripeCurrentPeriodEnd: timestamp('stripeCurrentPeriodEnd'),\n  stripeVariantId: timestamp('stripeVariantId'),\n  // Billing cycle\n  resetDate: timestamp('resetDate'),\n  // Flags\n  isDowngraded: boolean('isDowngraded').default(false),\n  // Additional metadata (cancellation feedback, etc.)\n  metadata: json('metadata').$type<Record<string, any>>(),\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n}, (table) => {\n  return {\n    userIdIdx: index('subscription_userId_idx').on(table.userId),\n    statusIdx: index('subscription_status_idx').on(table.status),\n    userIdUpdatedAtIdx: index('subscription_userId_updatedAt_idx').on(table.userId, table.updatedAt),\n    isActiveIdx: index('subscription_isActive_idx').on(table.isActive)\n  };\n});", "startLine": 378, "endLine": 414, "type": "type", "symbols": ["subscriptions table schema"], "score": 0.9, "context": "The subscriptions table schema defines fields like credits, creditsUsed, resetDate, and status which are critical for understanding subscription credit reset behavior.", "includesImports": false}, {"filePath": "src/lib/db/queries.ts", "content": "export async function getSubscriptionById(id: string): Promise<typeof subscriptionsSchema.$inferSelect | undefined> {\n  try {\n    const result = await db.select().from(subscriptionsSchema).where(eq(subscriptionsSchema.id, id)).limit(1);\n    return result[0];\n  } catch (error) {", "startLine": 1529, "endLine": 1533, "type": "util", "symbols": ["getSubscriptionById"], "score": 0.8, "context": "Function to fetch subscription by ID, useful for inspecting subscription state during credit reset.", "includesImports": false}, {"filePath": "src/lib/credit/CreditUsageTracker.ts", "content": "    trackOperation(type: CreditOperationType, count: number = 1) {\n        const existingOp = this.operations.find(op => op.type === type);\n        if (existingOp) {\n            existingOp.count += count;\n        } else {\n            this.operations.push({ type, count });\n        }\n    }", "startLine": 18, "endLine": 25, "type": "util", "symbols": ["CreditUsageTracker.trackOperation"], "score": 0.7, "context": "This method tracks credit operations which are relevant to credit usage and reset logic.", "includesImports": false}, {"filePath": "src/lib/db/queries.ts", "content": "export async function updateSubscriptionOnCancel(\n  id: string,\n  isActive: boolean,\n  newMetadata: Record<string, any>,\n  currentMetadata?: Record<string, any> | null\n) {\n  try {\n    const combinedMetadata = { ...currentMetadata, ...newMetadata };\n    const endsAtFromMeta = newMetadata.endsAt;\n    const updates = {\n      status: 'cancelled',\n      isActive: isActive,\n      metadata: combinedMetadata,\n      updatedAt: new Date(),\n    }\n    const resetDateValue = endsAtFromMeta;\n    if (resetDateValue) {\n      updates[resetDate] = resetDateValue;\n    }\n\n    await db\n      .update(subscriptionsSchema)\n      .set(updates as any)\n      .where(eq(subscriptionsSchema.id, id));\n  } catch (error) {\n    console.error('Failed to update subscription on cancel in database:', error);\n    throw error;\n  }\n}", "startLine": 1539, "endLine": 1567, "type": "util", "symbols": ["updateSubscriptionOnCancel"], "score": 0.7, "context": "Function updates subscription status and metadata on cancellation, including potentially updating resetDate which may affect credit reset logic.", "includesImports": false}, {"filePath": "src/lib/credit/CreditUsageTracker.ts", "content": "    clear() {\n        this.operations = [];\n        this.discountedOperations = [];\n    }", "startLine": 80, "endLine": 83, "type": "util", "symbols": ["CreditUsageTracker.clear"], "score": 0.6, "context": "This method clears tracked credit operations, potentially relevant to resetting credits.", "includesImports": false}], "additionalFiles": []}}