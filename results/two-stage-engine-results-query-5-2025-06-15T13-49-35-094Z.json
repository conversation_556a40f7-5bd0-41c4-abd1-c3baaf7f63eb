{"timestamp": "2025-06-15T13:49:35.094Z", "query": "Show me the subscription and billing system, including how features are gated and usage is tracked", "executionTime": 18266, "snippetsCount": 10, "additionalFilesCount": 0, "totalLines": 745, "snippets": [{"filePath": "src/lib/credit/CreditUsageTracker.ts", "type": "unknown", "context": "This class tracks credit operations and discounted operations, which is essential for usage tracking in the subscription and billing system.", "score": 1, "lines": 76, "startLine": 9, "endLine": 84, "symbols": ["CreditUsageTracker"], "preview": "export class CreditUsageTracker {\n    private operations: CreditOperation[] = [];\n    private discountedOperations: DiscountedOperation[] = [];\n\n    /**\n..."}, {"filePath": "src/lib/subscription.ts", "type": "util", "context": "This function checks the subscription status of a user, including their plan, credits, and usage, which is core to the subscription and billing system.", "score": 1, "lines": 83, "startLine": 19, "endLine": 101, "symbols": ["checkSubscriptionStatus"], "preview": "export async function checkSubscriptionStatus(userId: string, isAnonymous?: boolean): Promise<SubscriptionStatus> {\n  // Get appropriate plan based on user type\n  const freePlan = PLANS.find(plan => plan.tier === \"free\");\n  const anonymousPlan = PLANS.find(plan => plan.tier === \"anonymous\");\n  \n..."}, {"filePath": "src/lib/subscription.ts", "type": "util", "context": "This function checks the message sending limits based on subscription status and plan limits, directly related to feature gating and usage tracking.", "score": 1, "lines": 70, "startLine": 103, "endLine": 172, "symbols": ["checkMessageLimit"], "preview": "export async function checkMessageLimit(userId: string, isAnonymous: boolean) {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n\n  // Get subscription status first (which already has creditsUsed for paid plans)\n..."}, {"filePath": "src/lib/subscription/plans.ts", "type": "type", "context": "These types define the plan tiers, features, and plan structure, which are fundamental to understanding feature gating and subscription tiers.", "score": 1, "lines": 32, "startLine": 1, "endLine": 32, "symbols": ["PlanTier, FeatureId, Plan"], "preview": "export type PlanTier = 'free' | 'starter' | 'pro' | 'plus' | 'prime' | 'anonymous';\n\n// Feature identifiers for access control\nexport type FeatureId = \n  | 'code_download'\n..."}, {"filePath": "src/lib/subscription/plans.ts", "type": "unknown", "context": "This constant defines the subscription plans, their features, limits, and allowed features, which is critical for feature gating and billing.", "score": 1, "lines": 177, "startLine": 34, "endLine": 210, "symbols": ["PLANS"], "preview": "export const PLANS: Plan[] = [\n  {\n    id: 'anonymous',\n    name: 'Anonymous',\n    tier: 'anonymous',\n..."}, {"filePath": "src/lib/subscription/plans.ts", "type": "util", "context": "This helper function checks if a feature is allowed for a given plan tier, directly related to feature gating.", "score": 1, "lines": 4, "startLine": 226, "endLine": 229, "symbols": ["isFeatureAllowed"], "preview": "export const isFeatureAllowed = (planTier: PlanTier, featureId: FeatureId): boolean => {\n  const plan = getPlanByTier(planTier);\n  return plan.allowedFeatures.includes(featureId);\n};"}, {"filePath": "src/lib/db/schema.ts", "type": "unknown", "context": "This database schema defines the tokenConsumption table which tracks usage metrics and credits consumed, essential for usage tracking in billing.", "score": 1, "lines": 42, "startLine": 319, "endLine": 360, "symbols": ["tokenConsumption"], "preview": "export const tokenConsumption = pgTable('TokenConsumption', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  model: varchar('model', { length: 50 }).notNull(),\n  totalTimeToken: real('totalTimeToken').notNull(),\n  promptTokens: integer('promptTokens').notNull(),\n..."}, {"filePath": "src/lib/db/schema.ts", "type": "unknown", "context": "This database schema defines the subscriptions table with fields for plan, credits, status, and billing metadata, core to the subscription and billing system.", "score": 1, "lines": 37, "startLine": 378, "endLine": 414, "symbols": ["subscriptions"], "preview": "export const subscriptions = pgTable('Subscription', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  userId: varchar('userId', { length: 255 }).notNull(),\n  status: varchar('status', { length: 50 }).notNull().default('inactive'),\n  planId: varchar('planId', { length: 50 }).notNull().default('free'),\n..."}, {"filePath": "src/lib/db/queries.ts", "type": "util", "context": "This function in queries.ts duplicates the subscription status check logic, showing how subscription and usage data is queried and used for gating.", "score": 1, "lines": 154, "startLine": 19, "endLine": 172, "symbols": ["checkSubscriptionStatus"], "preview": "  stream, screenshotState,\n  temperatureOptimization, type TemperatureOptimization,\n} from './schema';\nimport { apkBuilds, deployments, subscriptions as subscriptionsSchema } from './schema';\nimport {PgSelectBase, PgSelectWithout} from \"drizzle-orm/pg-core\";\n..."}, {"filePath": "src/lib/db/queries.ts", "type": "util", "context": "This function checks message limits based on subscription and daily limits, showing usage gating logic in database queries.", "score": 1, "lines": 70, "startLine": 103, "endLine": 172, "symbols": ["checkMessageLimit"], "preview": "    const hashedPassword = password ? hashSync(password, genSaltSync(10)) : null;\n    const now = new Date();\n\n    const [createdUser] = await db\n      .insert(user)\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/credit/CreditUsageTracker.ts", "content": "export class CreditUsageTracker {\n    private operations: CreditOperation[] = [];\n    private discountedOperations: DiscountedOperation[] = [];\n\n    /**\n     * Track a credit operation that will be charged to the user\n     * @param type The type of operation\n     * @param count The number of operations (default: 1)\n     */\n    trackOperation(type: CreditOperationType, count: number = 1) {\n        const existingOp = this.operations.find(op => op.type === type);\n        if (existingOp) {\n            existingOp.count += count;\n        } else {\n            this.operations.push({ type, count });\n        }\n    }\n\n    /**\n     * Track a discounted operation that won't be charged to the user\n     * @param type The type of operation\n     * @param reason The reason for the discount (e.g., 'error_fixing')\n     * @param count The number of operations (default: 1)\n     */\n    trackDiscountedOperation(type: CreditOperationType, reason: string, count: number = 1) {\n        const existingOp = this.discountedOperations.find(op => op.type === type && op.reason === reason);\n        if (existingOp) {\n            existingOp.count += count;\n        } else {\n            this.discountedOperations.push({ type, count, reason });\n        }\n    }\n\n    /**\n     * Get all tracked operations that will be charged\n     */\n    getOperations(): CreditOperation[] {\n        return [...this.operations];\n    }\n\n    /**\n     * Get all discounted operations that won't be charged\n     */\n    getDiscountedOperations(): DiscountedOperation[] {\n        return [...this.discountedOperations];\n    }\n\n    /**\n     * Get the total number of credits consumed (charged operations only)\n     */\n    getCreditConsumption(): number {\n        return this.operations.reduce((total, op) => total + op.count, 0);\n    }\n\n    /**\n     * Get the total number of credits saved through discounts\n     */\n    getDiscountedCreditCount(): number {\n        return this.discountedOperations.reduce((total, op) => total + op.count, 0);\n    }\n\n    /**\n     * Get the total potential credit consumption (charged + discounted)\n     */\n    getTotalPotentialConsumption(): number {\n        return this.getCreditConsumption() + this.getDiscountedCreditCount();\n    }\n\n    /**\n     * Clear all tracked operations\n     */\n    clear() {\n        this.operations = [];\n        this.discountedOperations = [];\n    }\n}", "startLine": 9, "endLine": 84, "type": "unknown", "symbols": ["CreditUsageTracker"], "score": 1, "context": "This class tracks credit operations and discounted operations, which is essential for usage tracking in the subscription and billing system.", "includesImports": false}, {"filePath": "src/lib/subscription.ts", "content": "export async function checkSubscriptionStatus(userId: string, isAnonymous?: boolean): Promise<SubscriptionStatus> {\n  // Get appropriate plan based on user type\n  const freePlan = PLANS.find(plan => plan.tier === \"free\");\n  const anonymousPlan = PLANS.find(plan => plan.tier === \"anonymous\");\n  \n  // Skip subscription check for anonymous users\n  if (isAnonymous) {\n    // For anonymous users, count messages from token consumption\n    const result = await db\n      .select({\n        messageCount: sql<number>`COUNT(*)`\n      })\n      .from(tokenConsumption)\n      .where(\n        eq(tokenConsumption.userId, userId)\n      );\n\n    // Ensure we have a number by explicitly converting\n    const messagesUsed = Number(result[0]?.messageCount || 0);\n    const anonMessageLimit = anonymousPlan?.operations || 100;\n    \n    return {\n      isActive: false,\n      status: 'active',\n      plan: 'anonymous',\n      expiresAt: null,\n      credits: anonMessageLimit,  // Now represents message limit\n      creditsUsed: messagesUsed,  // Now represents messages used\n      creditsRemaining: Math.max(0, anonMessageLimit - messagesUsed)\n    };\n  }\n  \n  // For authenticated users, check if they have an active subscription\n  const userSubscription = await db\n    .select()\n    .from(subscriptions)\n    .where(and(\n        eq(subscriptions.userId, userId),\n        eq(subscriptions.isActive, true)\n    ))\n    .orderBy(desc(subscriptions.updatedAt))\n    .limit(1)\n    .then(results => results[0]);\n\n  if (!userSubscription || !userSubscription.isActive) {\n    // For free tier users, count messages from token consumption\n    const result = await db\n      .select({\n        messageCount: sql<number>`COUNT(*)`\n      })\n      .from(tokenConsumption)\n      .where(\n        eq(tokenConsumption.userId, userId)\n      );\n\n    // Ensure we have a number by explicitly converting\n    const messagesUsed = Number(result[0]?.messageCount || 0);\n    const freeMessageLimit = freePlan?.operations || 50;\n\n    return {\n      isActive: false,\n      plan: 'free',\n      status: 'active',\n      expiresAt: null,\n      credits: freeMessageLimit, // Free tier message limit\n      creditsUsed: messagesUsed, // Messages used\n      creditsRemaining: Math.max(0, freeMessageLimit - messagesUsed)\n    };\n  }\n\n  // User has an active subscription - use subscription.creditsUsed as source of truth for message count\n  return {\n    expiryDate: userSubscription.resetDate ? userSubscription.resetDate.toISOString() : undefined,\n    isActive: !!userSubscription.isActive,\n    subscriptionId: userSubscription.id,\n    status: userSubscription.status as any,\n    plan: userSubscription.planId as PlanTier,\n    expiresAt: userSubscription.resetDate, // Use resetDate as the expiration date\n    credits: userSubscription.credits,      // Now represents message limit\n    creditsUsed: userSubscription.creditsUsed || 0, // Now represents messages used\n    creditsRemaining: Math.max(0, userSubscription.credits - (userSubscription.creditsUsed || 0))\n  };\n}", "startLine": 19, "endLine": 101, "type": "util", "symbols": ["checkSubscriptionStatus"], "score": 1, "context": "This function checks the subscription status of a user, including their plan, credits, and usage, which is core to the subscription and billing system.", "includesImports": false}, {"filePath": "src/lib/subscription.ts", "content": "export async function checkMessageLimit(userId: string, isAnonymous: boolean) {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n\n  // Get subscription status first (which already has creditsUsed for paid plans)\n  const subscription = await checkSubscriptionStatus(userId, isAnonymous);\n  \n  // For consistency, use the creditsUsed from subscription status (now represents messages)\n  const messagesUsed = subscription.creditsUsed;\n\n  // Get plan details\n  const plan = getPlanByTier(subscription.plan);\n  \n  // Get daily limit from plan\n  const dailyLimit = plan.dailyLimit;\n  \n  // Check if plan has a daily limit (not -1)\n  if (dailyLimit > 0) {\n    // Count messages used today\n    const todayResult = await db\n      .select({\n        todayMessageCount: sql<number>`COUNT(*)`\n      })\n      .from(tokenConsumption)\n      .where(\n        and(\n          eq(tokenConsumption.userId, userId),\n          gte(tokenConsumption.createdAt, today)\n        )\n      );\n    \n    // Ensure we have a number by explicitly converting\n    const todayMessagesUsed = Number(todayResult[0]?.todayMessageCount || 0);\n    const dailyRemaining = Math.max(0, dailyLimit - todayMessagesUsed);\n    \n    // Check both daily and monthly message limits\n    return {\n      canSendMessage: todayMessagesUsed < dailyLimit && messagesUsed < subscription.credits,\n      remaining: Math.min(\n        dailyRemaining,\n        Math.max(0, subscription.credits - messagesUsed)\n      ),\n      limit: subscription.credits,\n      dailyLimit: dailyLimit,\n      dailyRemaining: dailyRemaining,\n      isPro: subscription.plan !== 'free' && subscription.plan !== 'anonymous',\n      isAnonymous: subscription.plan === 'anonymous',\n      planTier: subscription.plan,\n      totalCredits: subscription.credits,      // Now represents total message limit\n      creditsUsed: messagesUsed,               // Now represents messages used\n      creditsRemaining: Math.max(0, subscription.credits - messagesUsed) // Messages remaining\n    };\n  }\n\n  // Plans without daily limit (dailyLimit = -1)\n  return {\n    canSendMessage: subscription.creditsRemaining > 0,\n    remaining: subscription.creditsRemaining,\n    limit: subscription.credits,\n    isPro: subscription.plan !== 'free' && subscription.plan !== 'anonymous',\n    isAnonymous: subscription.plan === 'anonymous',\n    planTier: subscription.plan,\n    totalCredits: subscription.credits,      // Now represents total message limit\n    creditsUsed: subscription.creditsUsed,   // Now represents messages used\n    creditsRemaining: subscription.creditsRemaining, // Messages remaining\n    // Include dailyLimit as -1 to indicate unlimited\n    dailyLimit: -1,\n    dailyRemaining: -1\n  };\n}", "startLine": 103, "endLine": 172, "type": "util", "symbols": ["checkMessageLimit"], "score": 1, "context": "This function checks the message sending limits based on subscription status and plan limits, directly related to feature gating and usage tracking.", "includesImports": false}, {"filePath": "src/lib/subscription/plans.ts", "content": "export type PlanTier = 'free' | 'starter' | 'pro' | 'plus' | 'prime' | 'anonymous';\n\n// Feature identifiers for access control\nexport type FeatureId = \n  | 'code_download'\n  | 'deployment_web'\n  | 'deployment_android'\n  | 'deployment_ios'\n  | 'push_notifications'\n  | 'over_the_air_updates'\n  | 'advanced_code_generation'\n  | 'version_control'\n  | 'custom_integrations'\n  | 'usage_analytics'\n  | 'priority_support'\n  | 'advanced_agent_mode';\n\nexport interface Plan {\n  id: string;\n  name: string;\n  tier: PlanTier;\n  hidden?: boolean;\n  dailyLimit: number;\n  messageLimit: number;\n  dailyMessageLimit: number;\n  price: number;\n  operations: number;\n  features: string[];\n  limitations?: string[];\n  isPopular?: boolean;\n  allowedFeatures: FeatureId[];\n}", "startLine": 1, "endLine": 32, "type": "type", "symbols": ["PlanTier, FeatureId, Plan"], "score": 1, "context": "These types define the plan tiers, features, and plan structure, which are fundamental to understanding feature gating and subscription tiers.", "includesImports": false}, {"filePath": "src/lib/subscription/plans.ts", "content": "export const PLANS: Plan[] = [\n  {\n    id: 'anonymous',\n    name: 'Anonymous',\n    tier: 'anonymous',\n    price: 0,\n    operations: 2,\n    hidden: true,\n    dailyLimit: 2,\n    messageLimit: -1,\n    dailyMessageLimit: -1,\n    features: [\n      'Single File Edits',\n      'Community Support',\n      'Web Preview'\n    ],\n    limitations: [\n      'No Code Download',\n      'No Web, Android or iOS Deployments',\n      'No Custom Integrations',\n      'Basic Agent Mode Only',\n      'No Project Save'\n    ],\n    allowedFeatures: []\n  },\n  {\n    id: 'free',\n    name: 'Free',\n    tier: 'free',\n    price: 0,\n    operations: 10,\n    dailyLimit: 10,\n    hidden: true,\n    messageLimit: -1,\n    dailyMessageLimit: -1,\n    features: [\n      'Single File Edits',\n      'Community Support',\n      'Web Preview'\n    ],\n    limitations: [\n      'No Code Download',\n      'No Web, Android or iOS Deployments',\n      'No Custom Integrations',\n      'Basic Agent Mode Only'\n    ],\n    allowedFeatures: [\n      'deployment_web'\n    ]\n  },\n  {\n    id: 'starter',\n    name: 'Starter',\n    tier: 'starter',\n    price: 25,\n    operations: 100,\n    hidden: false,\n    messageLimit: -1,\n    dailyMessageLimit: -1,\n    dailyLimit: -1,\n    features: [\n      '1 credit = ~upto 10 tasks',\n      'Higher limits',\n      'Code Download',\n      'Android and iOS one click to stores',\n      'Access to new features',\n      'Community Support'\n    ],\n    limitations: [\n    ],\n    allowedFeatures: [\n      'deployment_web',\n      'deployment_android',\n      'deployment_ios',\n      'code_download',\n      'push_notifications',\n      'over_the_air_updates'\n    ]\n  },\n  {\n    id: 'pro',\n    name: 'Pro',\n    tier: 'pro',\n    price: 60,\n    operations: 240,\n    hidden: false,\n    messageLimit: -1,\n    dailyMessageLimit: -1,\n    dailyLimit: -1,\n    isPopular: true,\n    features: [\n      '1 credit = ~upto 10 tasks',\n      'Higher limits',\n      'Code Download',\n      'Android and iOS one click to stores',\n      'Advanced Code Generation',\n      // 'Version Control Integration',\n      'Email Support'\n    ],\n    allowedFeatures: [\n      'code_download',\n      'deployment_web',\n      'deployment_android',\n      'deployment_ios',\n      'push_notifications',\n      'over_the_air_updates',\n      'advanced_code_generation',\n      'version_control',\n      'advanced_agent_mode'\n    ]\n  },\n  {\n    id: 'plus',\n    name: 'Plus',\n    tier: 'plus',\n    price: 99,\n    operations: 400,\n    hidden: false,\n    messageLimit: -1,\n    dailyMessageLimit: -1,\n    dailyLimit: -1,\n    features: [\n      '1 credit = ~upto 10 tasks',\n      'Free auto error fixes',\n      'Everything in Pro',\n      'Priority Support',\n      'Custom Integrations',\n      'Usage Analytics'\n    ],\n    allowedFeatures: [\n      'code_download',\n      'deployment_web',\n      'deployment_android',\n      'deployment_ios',\n      'push_notifications',\n      'over_the_air_updates',\n      'advanced_code_generation',\n      'version_control',\n      'advanced_agent_mode',\n      'custom_integrations',\n      'usage_analytics',\n      'priority_support'\n    ]\n  },\n  {\n    id: 'prime',\n    name: 'Prime',\n    tier: 'prime',\n    price: 199,\n    operations: 800,\n    hidden: false,\n    messageLimit: -1,\n    dailyMessageLimit: -1,\n    dailyLimit: -1,\n    features: [\n      '1 credit = ~upto 10 tasks',\n      'Everything in Plus',\n      'Priority Support',\n      '1-on-1 Consulting (10 hours per month)',\n      'App store deployment support'\n    ],\n    allowedFeatures: [\n      'code_download',\n      'deployment_web',\n      'deployment_android',\n      'deployment_ios',\n      'push_notifications',\n      'over_the_air_updates',\n      'advanced_code_generation',\n      'version_control',\n      'advanced_agent_mode',\n      'custom_integrations',\n      'usage_analytics',\n      'priority_support'\n    ]\n  }\n];", "startLine": 34, "endLine": 210, "type": "unknown", "symbols": ["PLANS"], "score": 1, "context": "This constant defines the subscription plans, their features, limits, and allowed features, which is critical for feature gating and billing.", "includesImports": false}, {"filePath": "src/lib/subscription/plans.ts", "content": "export const isFeatureAllowed = (planTier: PlanTier, featureId: FeatureId): boolean => {\n  const plan = getPlanByTier(planTier);\n  return plan.allowedFeatures.includes(featureId);\n};", "startLine": 226, "endLine": 229, "type": "util", "symbols": ["isFeatureAllowed"], "score": 1, "context": "This helper function checks if a feature is allowed for a given plan tier, directly related to feature gating.", "includesImports": false}, {"filePath": "src/lib/db/schema.ts", "content": "export const tokenConsumption = pgTable('TokenConsumption', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  model: varchar('model', { length: 50 }).notNull(),\n  totalTimeToken: real('totalTimeToken').notNull(),\n  promptTokens: integer('promptTokens').notNull(),\n  completionTokens: integer('completionTokens').notNull(),\n  totalTokens: integer('totalTokens').notNull(),\n  chatId: uuid('chatId')\n    .notNull(),  // No foreign key as specified\n  projectId: uuid('projectId')\n    .notNull(),  // No foreign key as specified\n  messageId: uuid('messageId')\n    .notNull(),  // No foreign key as specified\n  userId: uuid('userId')\n    .notNull()\n    .references(() => user.id, { onDelete: 'cascade' }),  // Add foreign key to User\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n  inputCost: real('inputCost').notNull(),  // Store as float\n  outputCost: real('outputCost').notNull(),  // Store as float\n  cachingDiscount: real('cachingDiscount'),\n  cacheDiscountPercent: real('cacheDiscountPercent'),\n  subtotal: real('subtotal'),\n  totalCost: real('totalCost').notNull(),  // Store as float\n  isAnonymous: boolean('isAnonymous').default(false),\n  remoteProvider: varchar(\"remoteProvider\"),\n  remoteProviderId: varchar(\"remoteProviderId\"),\n  creditsConsumed: integer('creditsConsumed'),\n  discountedCredits: integer('discountedCredits'),\n  discountReason: varchar('discountReason', { length: 50 }),\n  errorId: varchar('errorId'),\n  discounted: boolean('discounted').default(false),\n  isAutoFixed: boolean('isAutoFixed').default(false)\n}, (table) => {\n  return {\n    userIdIdx: index('tokenConsumption_userId_idx').on(table.userId),\n    chatIdIdx: index('tokenConsumption_chatId_idx').on(table.chatId),\n    projectIdIdx: index('tokenConsumption_projectId_idx').on(table.projectId),\n    userCreatedAtIdx: index('tokenConsumption_userId_createdAt_idx').on(table.userId, table.createdAt),\n    isAnonymousIdx: index('tokenConsumption_isAnonymous_idx').on(table.isAnonymous)\n  };\n});", "startLine": 319, "endLine": 360, "type": "unknown", "symbols": ["tokenConsumption"], "score": 1, "context": "This database schema defines the tokenConsumption table which tracks usage metrics and credits consumed, essential for usage tracking in billing.", "includesImports": false}, {"filePath": "src/lib/db/schema.ts", "content": "export const subscriptions = pgTable('Subscription', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  userId: varchar('userId', { length: 255 }).notNull(),\n  status: varchar('status', { length: 50 }).notNull().default('inactive'),\n  planId: varchar('planId', { length: 50 }).notNull().default('free'),\n  subscriptionId: varchar('subscriptionId', { length: 255 }).unique(),\n  credits: integer('credits').notNull().default(50), // Default to free tier credits\n  creditsUsed: integer('creditsUsed').notNull().default(0),\n  isActive: boolean('isActive').default(false),\n  // LemonSqueezy fields\n  lemonSqueezyCustomerId: varchar('lemonSqueezyCustomerId'),\n  lemonSqueezySubscriptionId: varchar('lemonSqueezySubscriptionId'),\n  lemonSqueezyOrderId: varchar('lemonSqueezyOrderId'),\n  lemonSqueezyVariantId: varchar('lemonSqueezyVariantId'),\n  provider: varchar('provider'),\n  // Stripe fields (for compatibility)\n  stripeCustomerId: varchar('stripeCustomerId'),\n  stripeSubscriptionId: varchar('stripeSubscriptionId'),\n  stripePriceId: varchar('stripePriceId'),\n  stripeCurrentPeriodEnd: timestamp('stripeCurrentPeriodEnd'),\n  stripeVariantId: timestamp('stripeVariantId'),\n  // Billing cycle\n  resetDate: timestamp('resetDate'),\n  // Flags\n  isDowngraded: boolean('isDowngraded').default(false),\n  // Additional metadata (cancellation feedback, etc.)\n  metadata: json('metadata').$type<Record<string, any>>(),\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n}, (table) => {\n  return {\n    userIdIdx: index('subscription_userId_idx').on(table.userId),\n    statusIdx: index('subscription_status_idx').on(table.status),\n    userIdUpdatedAtIdx: index('subscription_userId_updatedAt_idx').on(table.userId, table.updatedAt),\n    isActiveIdx: index('subscription_isActive_idx').on(table.isActive)\n  };\n});", "startLine": 378, "endLine": 414, "type": "unknown", "symbols": ["subscriptions"], "score": 1, "context": "This database schema defines the subscriptions table with fields for plan, credits, status, and billing metadata, core to the subscription and billing system.", "includesImports": false}, {"filePath": "src/lib/db/queries.ts", "content": "  stream, screenshotState,\n  temperatureOptimization, type TemperatureOptimization,\n} from './schema';\nimport { apkBuilds, deployments, subscriptions as subscriptionsSchema } from './schema';\nimport {PgSelectBase, PgSelectWithout} from \"drizzle-orm/pg-core\";\nimport {BlockKind} from \"@/components/base/block\";\nimport dayjs from \"dayjs\";\n\n// Import the singleton database client\nimport { db } from './db';\n\n// Utility function for retrying database operations\nasync function withRetry<T>(\n  operation: () => Promise<T>,\n  maxRetries = 3,\n  initialDelay = 300\n): Promise<T> {\n  let lastError;\n\n  for (let attempt = 0; attempt < maxRetries; attempt++) {\n    try {\n      return await operation();\n    } catch (error) {\n      console.log(`Retry attempt ${attempt + 1}/${maxRetries} failed:`, error);\n      lastError = error;\n\n      // Don't wait on the last attempt\n      if (attempt < maxRetries - 1) {\n        await new Promise(resolve => setTimeout(resolve, initialDelay * Math.pow(2, attempt)));\n      }\n    }\n  }\n\n  throw lastError;\n}\n\nexport async function getUser(email: string): Promise<Array<User>> {\n  try {\n    return await db.select().from(user).where(eq(user.email, email));\n  } catch (error) {\n    console.error('Failed to get user from database');\n    throw error;\n  }\n}\n\nexport async function getUserById(id: string): Promise<User | undefined> {\n  try {\n    const users = await db.select().from(user).where(eq(user.id, id));\n    return users[0];\n  } catch (error) {\n    console.error('Failed to get user by ID from database');\n    throw error;\n  }\n}\n\nexport async function createOrFetchAnonUser(id: string): Promise<User> {\n  try {\n    // Try to find existing anonymous user\n    const existingUser = await getUserById(id);\n    if (existingUser) {\n      return existingUser;\n    }\n\n    // Create new anonymous user\n    const [newUser] = await db.insert(user).values({\n      id,\n      email: `anon-${id}@temp.magically.life`,\n      isAnonymous: true,\n    }).returning();\n\n    return newUser;\n  } catch (error) {\n    console.error('Failed to create/fetch anonymous user');\n    throw error;\n  }\n}\n\nexport async function createUser(\n  email: string,\n  password: string,\n  name: string,\n  provider: 'credentials' | 'google' = 'credentials'\n): Promise<User> {\n  try {\n    const hashedPassword = password ? hashSync(password, genSaltSync(10)) : null;\n    const now = new Date();\n\n    const [createdUser] = await db\n      .insert(user)\n      .values({\n        email,\n        password: hashedPassword,\n        name,\n        provider,\n        createdAt: now,\n        updatedAt: now\n      })\n      .returning();\n\n    return createdUser;\n  } catch (error) {\n    console.error('Failed to create user in database', error);\n    throw error;\n  }\n}\n\nexport async function saveChat({\n  id,\n  userId,\n  projectId,\n  title,\n  updatedAt,\n  isInitialized,\n  type\n}: {\n  id: string;\n  userId: string;\n  projectId?: string;\n  title: string;\n  updatedAt: Date,\n  isInitialized?: boolean,\n  type?: 'app' | 'design' | 'discuss'\n}) {\n  try {\n    const chats = await db.insert(chat).values({\n      id,\n      createdAt: new Date(),\n      updatedAt,\n      userId,\n      projectId,\n      title,\n      isInitialized,\n      type: type || 'app' // Default to 'app' if not specified\n    }).returning();\n    return chats[0];\n  } catch (error) {\n    console.error('Failed to save chat in database', error);\n    throw error;\n  }\n}\n\nexport async function markChatAsInitialized({ id }: { id: string }) {\n  try {\n    await db\n        .update(chat)\n        .set({\n          isInitialized: true,\n        })\n        .where(eq(chat.id, id));\n  } catch (error) {\n    console.error('Failed to update chat in database', error);\n  }\n}\n", "startLine": 19, "endLine": 172, "type": "util", "symbols": ["checkSubscriptionStatus"], "score": 1, "context": "This function in queries.ts duplicates the subscription status check logic, showing how subscription and usage data is queried and used for gating.", "includesImports": false}, {"filePath": "src/lib/db/queries.ts", "content": "    const hashedPassword = password ? hashSync(password, genSaltSync(10)) : null;\n    const now = new Date();\n\n    const [createdUser] = await db\n      .insert(user)\n      .values({\n        email,\n        password: hashedPassword,\n        name,\n        provider,\n        createdAt: now,\n        updatedAt: now\n      })\n      .returning();\n\n    return createdUser;\n  } catch (error) {\n    console.error('Failed to create user in database', error);\n    throw error;\n  }\n}\n\nexport async function saveChat({\n  id,\n  userId,\n  projectId,\n  title,\n  updatedAt,\n  isInitialized,\n  type\n}: {\n  id: string;\n  userId: string;\n  projectId?: string;\n  title: string;\n  updatedAt: Date,\n  isInitialized?: boolean,\n  type?: 'app' | 'design' | 'discuss'\n}) {\n  try {\n    const chats = await db.insert(chat).values({\n      id,\n      createdAt: new Date(),\n      updatedAt,\n      userId,\n      projectId,\n      title,\n      isInitialized,\n      type: type || 'app' // Default to 'app' if not specified\n    }).returning();\n    return chats[0];\n  } catch (error) {\n    console.error('Failed to save chat in database', error);\n    throw error;\n  }\n}\n\nexport async function markChatAsInitialized({ id }: { id: string }) {\n  try {\n    await db\n        .update(chat)\n        .set({\n          isInitialized: true,\n        })\n        .where(eq(chat.id, id));\n  } catch (error) {\n    console.error('Failed to update chat in database', error);\n  }\n}\n", "startLine": 103, "endLine": 172, "type": "util", "symbols": ["checkMessageLimit"], "score": 1, "context": "This function checks message limits based on subscription and daily limits, showing usage gating logic in database queries.", "includesImports": false}], "additionalFiles": []}}