{"timestamp": "2025-06-14T17:36:35.691Z", "query": "Find the authentication system and how user sessions are managed across the application", "executionTime": 25083, "snippetsCount": 15, "additionalFilesCount": 0, "totalLines": 976, "snippets": [{"filePath": "src/hooks/use-auth.ts", "type": "hook", "context": "This hook uses next-auth's useSession to manage user session state and provides authentication status and redirect handling, directly related to user session management.", "score": 1, "lines": 25, "startLine": 3, "endLine": 27, "symbols": ["useAuth"], "preview": "import { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\n\nexport function useAuth() {\n    const { data: session, status } = useSession();\n..."}, {"filePath": "src/app/(auth)/actions.ts", "type": "util", "context": "This function handles user login by validating credentials and calling signIn from auth.ts, directly related to authentication and session initiation.", "score": 1, "lines": 25, "startLine": 25, "endLine": 49, "symbols": ["login"], "preview": "export const login = async (\n  _: LoginActionState,\n  formData: FormData,\n): Promise<LoginActionState> => {\n  try {\n..."}, {"filePath": "src/app/(auth)/actions.ts", "type": "util", "context": "This function handles user registration, creating a user and signing them in, directly related to authentication and session creation.", "score": 1, "lines": 57, "startLine": 61, "endLine": 117, "symbols": ["register"], "preview": "export const register = async (\n  _: RegisterActionState,\n  formData: FormData,\n): Promise<RegisterActionState> => {\n  try {\n..."}, {"filePath": "src/app/(auth)/auth.ts", "type": "unknown", "context": "This file configures NextAuth with Google and Credentials providers, manages user authorization, JWT token callbacks, and session callbacks, directly implementing authentication and session management.", "score": 1, "lines": 127, "startLine": 1, "endLine": 127, "symbols": ["NextAuth configuration"], "preview": "import NextAuth, { type User, type Session } from 'next-auth';\nimport { JWT } from '@auth/core/jwt';\nimport Google from 'next-auth/providers/google';\nimport Credentials from 'next-auth/providers/credentials';\nimport { compare } from 'bcrypt-ts';\n..."}, {"filePath": "src/app/(auth)/auth.config.ts", "type": "unknown", "context": "Defines NextAuth configuration including authorization callback that manages route protection based on user session, relevant to session management and authentication flow.", "score": 0.9, "lines": 57, "startLine": 3, "endLine": 59, "symbols": ["authConfig"], "preview": "export const authConfig: NextAuthConfig = {\n    pages: {\n        signIn: '/login',\n        newUser: '/',\n    },\n..."}, {"filePath": "src/app/(auth)/login/page.tsx", "type": "component", "context": "React component for login page that uses login action and manages session state and redirects, relevant to authentication and session management UI.", "score": 0.9, "lines": 91, "startLine": 13, "endLine": 103, "symbols": ["LoginForm"], "preview": "function LoginForm() {\n  const router = useRouter();\n  const [email, setEmail] = useState('');\n  const [isSuccessful, setIsSuccessful] = useState(false);\n  const searchParams = useSearchParams();\n..."}, {"filePath": "src/app/(auth)/register/page.tsx", "type": "component", "context": "React component for registration page that uses register action and manages session state and redirects, relevant to authentication and session management UI.", "score": 0.9, "lines": 80, "startLine": 11, "endLine": 90, "symbols": ["RegisterForm"], "preview": "function RegisterForm() {\n  const router = useRouter();\n\n  const [email, setEmail] = useState('');\n  const [name, setName] = useState('');\n..."}, {"filePath": "src/app/(auth)/actions.ts", "type": "util", "context": "<PERSON>les password reset request, part of authentication flow and session recovery.", "score": 0.8, "lines": 52, "startLine": 136, "endLine": 187, "symbols": ["requestPasswordReset"], "preview": "export const requestPasswordReset = async (\n  _: ResetPasswordRequestState,\n  formData: FormData,\n): Promise<ResetPasswordRequestState> => {\n  try {\n..."}, {"filePath": "src/app/(auth)/actions.ts", "type": "util", "context": "<PERSON>les password reset with token, part of authentication flow and session recovery.", "score": 0.8, "lines": 37, "startLine": 189, "endLine": 225, "symbols": ["resetPassword"], "preview": "export const resetPassword = async (\n  _: ResetPasswordState,\n  formData: FormData,\n): Promise<ResetPasswordState> => {\n  try {\n..."}, {"filePath": "src/components/login-dialog.tsx", "type": "component", "context": "Component managing login dialog with session and signIn calls, handling user session and login flow.", "score": 0.8, "lines": 137, "startLine": 15, "endLine": 151, "symbols": ["LoginDialog"], "preview": "export const LoginDialog = observer(() => {\n  const [isLoading, setIsLoading] = useState(false);\n  const { generatorStore } = useStores();\n  const { loginDialogOpen, toggleLoginDialog } = generatorStore;\n  const { anonymousId } = useAnonymousSession();\n..."}, {"filePath": "src/lib/auth/auth.utils.ts", "type": "util", "context": "Utility function that checks if a user session exists and returns 401 Unauthorized if not, relevant for session validation in authentication.", "score": 0.7, "lines": 9, "startLine": 1, "endLine": 9, "symbols": ["checkAccess"], "preview": "import {auth} from \"@/app/(auth)/auth\";\n\nexport const checkAccess = async () => {\n    const session = await auth();\n\n..."}, {"filePath": "src/app/(auth)/reset-password/request/page.tsx", "type": "component", "context": "Component for requesting password reset, part of authentication and session recovery flow.", "score": 0.7, "lines": 69, "startLine": 11, "endLine": 79, "symbols": ["RequestPasswordResetPage"], "preview": "export default function RequestPasswordResetPage() {\n  const router = useRouter();\n  const [state, formAction] = useActionState<ResetPasswordRequestState, FormData>(requestPasswordReset, {\n    status: 'idle',\n  });\n..."}, {"filePath": "src/app/(auth)/reset-password/[token]/PasswordTokenComponent.tsx", "type": "component", "context": "Component for resetting password with token, part of authentication and session recovery flow.", "score": 0.7, "lines": 70, "startLine": 8, "endLine": 77, "symbols": ["PasswordTokenComponent"], "preview": "export default function PasswordTokenComponent({token}: { token: string }) {\n    const router = useRouter();\n    const [state, formAction] = useActionState<ResetPasswordState, FormData>(resetPassword, {\n        status: 'idle',\n    });\n..."}, {"filePath": "src/components/auth/google-button.tsx", "type": "component", "context": "Component for Google OAuth sign-in button, relevant to authentication flow.", "score": 0.7, "lines": 60, "startLine": 3, "endLine": 62, "symbols": ["GoogleButton"], "preview": "import { Button } from '@/components/ui/button';\nimport { signIn } from 'next-auth/react';\nimport { Loader2 } from 'lucide-react';\nimport { useState } from 'react';\n\n..."}, {"filePath": "src/components/auth-form.tsx", "type": "component", "context": "Reusable form component for login and registration forms, relevant for authentication UI but not session management logic.", "score": 0.6, "lines": 80, "startLine": 6, "endLine": 85, "symbols": ["AuthForm"], "preview": "export function AuthForm({\n  action,\n  children,\n  defaultEmail = '',\n  isRegistration\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/hooks/use-auth.ts", "content": "import { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\n\nexport function useAuth() {\n    const { data: session, status } = useSession();\n    const router = useRouter();\n\n    const isAuthenticated = status === 'authenticated';\n    const isLoading = status === 'loading';\n\n    const handleRedirect = () => {\n        if (isAuthenticated) {\n            router.push('/applications');\n        } else {\n            router.push('/login');\n        }\n    };\n\n    return {\n        isAuthenticated,\n        isLoading,\n        handleRedirect,\n        session,\n    };\n}", "startLine": 3, "endLine": 27, "type": "hook", "symbols": ["useAuth"], "score": 1, "context": "This hook uses next-auth's useSession to manage user session state and provides authentication status and redirect handling, directly related to user session management.", "includesImports": false}, {"filePath": "src/app/(auth)/actions.ts", "content": "export const login = async (\n  _: LoginActionState,\n  formData: FormData,\n): Promise<LoginActionState> => {\n  try {\n    const validatedData = authFormSchema.parse({\n      email: formData.get('email'),\n      password: formData.get('password'),\n    });\n\n    await signIn('credentials', {\n      email: validatedData.email,\n      password: validatedData.password,\n      redirect: false,\n    });\n\n    return { status: 'success' };\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { status: 'invalid_data' };\n    }\n\n    return { status: 'failed' };\n  }\n};", "startLine": 25, "endLine": 49, "type": "util", "symbols": ["login"], "score": 1, "context": "This function handles user login by validating credentials and calling signIn from auth.ts, directly related to authentication and session initiation.", "includesImports": false}, {"filePath": "src/app/(auth)/actions.ts", "content": "export const register = async (\n  _: RegisterActionState,\n  formData: FormData,\n): Promise<RegisterActionState> => {\n  try {\n    const validatedData = signupFormSchema.parse({\n      email: formData.get('email'),\n      password: formData.get('password'),\n      name: formData.get(\"name\")\n    });\n\n    const [existingUser] = await getUser(validatedData.email);\n\n    if (existingUser) {\n      return { status: 'user_exists' };\n    }\n\n    // Create the user first\n    const newUser = await createUser(\n      validatedData.email,\n      validatedData.password,\n      validatedData.name,\n        'credentials'\n    );\n\n    // Then sign them in\n    const signInResult = await signIn('credentials', {\n      email: validatedData.email,\n      password: validatedData.password,\n      redirect: false,\n    });\n\n    if (signInResult?.error) {\n      console.error('Failed to sign in after registration:', signInResult.error);\n      return { status: 'failed' };\n    }\n\n    // Send welcome email\n    try {\n      await sendWelcomeEmail({\n        email: validatedData.email,\n        name: validatedData.name,\n      });\n    } catch (emailError) {\n      // Log the error but don't fail the registration process\n      console.error('Failed to send welcome email:', emailError);\n    }\n\n    return { status: 'success' };\n  } catch (error) {\n    console.error('Registration error:', error);\n    if (error instanceof z.ZodError) {\n      return { status: 'invalid_data' };\n    }\n    return { status: 'failed' };\n  }\n};", "startLine": 61, "endLine": 117, "type": "util", "symbols": ["register"], "score": 1, "context": "This function handles user registration, creating a user and signing them in, directly related to authentication and session creation.", "includesImports": false}, {"filePath": "src/app/(auth)/auth.ts", "content": "import NextAuth, { type User, type Session } from 'next-auth';\nimport { JWT } from '@auth/core/jwt';\nimport Google from 'next-auth/providers/google';\nimport Credentials from 'next-auth/providers/credentials';\nimport { compare } from 'bcrypt-ts';\n\nimport {createUser, getUser} from '@/lib/db/queries';\nimport { authConfig } from './auth.config';\nimport { sendWelcomeEmail } from '@/lib/email';\n\nexport const {\n  handlers: { GET, POST },\n  auth,\n  signIn,\n  signOut,\n} = NextAuth({\n  ...authConfig,\n  providers: [\n    Google({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n      authorization: {\n        params: {\n          prompt: \"select_account\"\n        }\n      }\n    }),\n    Credentials({\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize({ email, password }: any) {\n        const users = await getUser(email);\n        if (users.length === 0) return null;\n\n        const passwordsMatch = await compare(password, users[0].password!);\n        if (!passwordsMatch) return null;\n\n        // Return user without password\n        const { password: _, ...userWithoutPassword } = users[0];\n        return userWithoutPassword as User;\n      },\n    })\n  ],\n  callbacks: {\n    async jwt({ token, user, account }: { token: any, user: User | null, account: any }) {\n      if (user) {\n        if (account?.provider === 'google') {\n          try {\n            // Check if user exists\n            const existingUsers = await getUser(user.email!);\n            let userId: string;\n            let isNewUser = false;\n\n            if (existingUsers.length === 0) {\n              // Create new user for Google OAuth\n              const userName = user.name || user.email!.split('@')[0]; // Use name or email prefix as name\n              const createdUser = await createUser(\n                user.email!,\n                '', // No password for Google OAuth\n                userName,\n                'google' // Set provider as google\n              );\n              userId = createdUser.id;\n              isNewUser = true;\n\n              // Send welcome email for new Google OAuth users\n              try {\n                await sendWelcomeEmail({\n                  email: user.email!,\n                  name: userName,\n                });\n              } catch (emailError) {\n                // Log the error but don't fail the authentication process\n                console.error('Failed to send welcome email for Google OAuth user:', emailError);\n              }\n            } else {\n              // User exists, treat as login\n              userId = existingUsers[0].id;\n            }\n\n            // Set token properties for Google user\n            token.id = userId;\n            token.isNewUser = isNewUser;\n            token.email = user.email!;\n            token.name = user.name;\n            token.picture = user.image;\n            token.provider = account.provider;\n          } catch (error) {\n            console.error('Error in Google OAuth flow:', error);\n            throw error;\n          }\n        } else {\n          // Credentials provider sign in\n          token.id = user.id as string;\n          token.email = user.email!;\n          token.name = user.name;\n          token.provider = account?.provider || 'credentials';\n          token.isNewUser = false;\n        }\n      }\n      return token;\n    },\n    // @ts-ignore - Bypassing type check for build\n    async session({\n      session,\n      token,\n    }) {\n      if (session.user) {\n        // @ts-ignore\n        session.user.id = token.id;\n        // @ts-ignore\n        session.user.email = token.email;\n        // @ts-ignore\n        session.user.name = token.name;\n        // @ts-ignore\n        session.user.image = token.picture;\n        // @ts-ignore\n        session.user.provider = token.provider;\n        // @ts-ignore\n        session.user.isNewUser = token.isNewUser;\n      }\n      return session;\n    },\n  },\n});", "startLine": 1, "endLine": 127, "type": "unknown", "symbols": ["NextAuth configuration"], "score": 1, "context": "This file configures NextAuth with Google and Credentials providers, manages user authorization, JWT token callbacks, and session callbacks, directly implementing authentication and session management.", "includesImports": false}, {"filePath": "src/app/(auth)/auth.config.ts", "content": "export const authConfig: NextAuthConfig = {\n    pages: {\n        signIn: '/login',\n        newUser: '/',\n    },\n    providers: [\n        // added later in auth.ts since it requires bcrypt which is only compatible with Node.js\n        // while this file is also used in non-Node.js environments\n    ],\n    callbacks: {\n        authorized({auth, request: {nextUrl}}) {\n            const isLoggedIn = !!auth?.user;\n            \n            // Check if this is a media API route - these should be public\n            if (nextUrl.pathname.startsWith('/api/media')) {\n                return true; // Always allow access to media API routes\n            }\n\n            // Protected Routes that require auth\n            const protectedPaths = [\n                '/dashboard'\n                // '/generator' removed to allow anonymous access\n            ];\n\n            // Auth Routes (login/register)\n            const authPaths = [\n                '/login',\n                '/register'\n            ];\n\n            const isProtectedRoute = protectedPaths.some(path =>\n                nextUrl.pathname.startsWith(path)\n            );\n            const isAuthRoute = authPaths.some(path =>\n                nextUrl.pathname.startsWith(path)\n            );\n\n            // Redirect logged-in users away from auth pages\n            if (isLoggedIn && isAuthRoute) {\n                return Response.redirect(new URL('/', nextUrl));\n            }\n\n            // Allow access to auth pages if not logged in\n            if (isAuthRoute) {\n                return true;\n            }\n\n            // Protect routes that require auth\n            if (isProtectedRoute) {\n                return isLoggedIn;\n            }\n\n            // Allow public access to all other routes (including landing page)\n            return true;\n        },\n    },\n} satisfies NextAuthConfig;", "startLine": 3, "endLine": 59, "type": "unknown", "symbols": ["authConfig"], "score": 0.9, "context": "Defines NextAuth configuration including authorization callback that manages route protection based on user session, relevant to session management and authentication flow.", "includesImports": false}, {"filePath": "src/app/(auth)/login/page.tsx", "content": "function LoginForm() {\n  const router = useRouter();\n  const [email, setEmail] = useState('');\n  const [isSuccessful, setIsSuccessful] = useState(false);\n  const searchParams = useSearchParams();\n\n  const [state, formAction] = useActionState<LoginActionState, FormData>(\n    login,\n    {\n      status: 'idle',\n    },\n  );\n\n  const callbackUrl = searchParams?.get('callbackUrl') || '/';\n\n  useEffect(() => {\n    if (state.status === 'failed') {\n      toast.error('Invalid credentials!');\n      // Track failed login attempt\n      trackUserEvent('SIGNED_IN', {\n        auth_method: 'email',\n        error_type: 'invalid_credentials'\n      });\n    } else if (state.status === 'invalid_data') {\n      toast.error('Failed validating your submission!');\n      // Track validation failure\n      trackUserEvent('SIGNED_IN', {\n        auth_method: 'email',\n        error_type: 'invalid_data'\n      });\n    } else if (state.status === 'success') {\n      // Track successful login\n      trackUserEvent('SIGNED_IN', {\n        auth_method: 'email',\n        time_to_complete: 0 // We don't have timing info here, but could be added\n      });\n      setIsSuccessful(true);\n      router.refresh();\n    }\n  }, [state.status, router]);\n\n  const handleSubmit = (formData: FormData) => {\n    setEmail(formData.get('email') as string);\n    formAction(formData);\n  };\n\n  return (\n    <div className=\"flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background\">\n      <div className=\"w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12\">\n        <div className=\"flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16\">\n          <h3 className=\"text-xl font-semibold dark:text-zinc-50\">Sign In</h3>\n          <p className=\"text-sm text-gray-500 dark:text-zinc-400\">\n            Continue with your preferred method\n          </p>\n        </div>\n        <div className=\"px-4 sm:px-16\">\n          <GoogleButton callbackUrl={callbackUrl} />\n          <div className=\"relative my-4\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"w-full border-t border-muted\" />\n            </div>\n            <div className=\"relative flex justify-center text-xs uppercase\">\n              <span className=\"bg-background px-2 text-muted-foreground\">Or continue with email</span>\n            </div>\n          </div>\n        </div>\n        <AuthForm action={handleSubmit} defaultEmail={email}>\n          <div className=\"space-y-4\">\n            <SubmitButton isSuccessful={isSuccessful}>Sign in</SubmitButton>\n\n            {/*<div className=\"text-center text-sm\">*/}\n            {/*  <Link href=\"/reset-password/request\" className=\"text-blue-600 hover:underline\">*/}\n            {/*    Forgot your password?*/}\n            {/*  </Link>*/}\n            {/*</div>*/}\n          </div>\n          <p className=\"text-center text-sm text-gray-600 mt-4 dark:text-zinc-400\">\n            {\"Don't have an account? \"}\n            <Link\n              href={`/register?callbackUrl=${encodeURIComponent(callbackUrl)}}`}\n              className=\"font-semibold text-gray-800 hover:underline dark:text-zinc-200\"\n            >\n              Sign up\n            </Link>\n            {' for free.'}\n          </p>\n        </AuthForm>\n      </div>\n    </div>\n  );\n}", "startLine": 13, "endLine": 103, "type": "component", "symbols": ["LoginForm"], "score": 0.9, "context": "React component for login page that uses login action and manages session state and redirects, relevant to authentication and session management UI.", "includesImports": false}, {"filePath": "src/app/(auth)/register/page.tsx", "content": "function RegisterForm() {\n  const router = useRouter();\n\n  const [email, setEmail] = useState('');\n  const [name, setName] = useState('');\n  const [isSuccessful, setIsSuccessful] = useState(false);\n  const searchParams = useSearchParams();\n\n  const callbackUrl = searchParams?.get('callbackUrl') || '/';\n\n  const [state, formAction] = useActionState<RegisterActionState, FormData>(\n    register,\n    {\n      status: 'idle',\n    },\n  );\n\n  useEffect(() => {\n    if (state.status === 'user_exists') {\n      toast.error('Account already exists');\n    } else if (state.status === 'failed') {\n      toast.error('Failed to create account');\n    } else if (state.status === 'invalid_data') {\n      toast.error('Failed validating your submission!');\n    } else if (state.status === 'success') {\n      toast.success('Account created successfully');\n      setIsSuccessful(true);\n      router.refresh();\n    }\n  }, [state, router]);\n\n  const handleSubmit = (formData: FormData) => {\n    setEmail(formData.get('email') as string);\n    setName(formData.get('name') as string)\n    formAction(formData);\n  };\n\n  return (\n    <div className=\"flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background\">\n      <div className=\"w-full max-w-md overflow-hidden rounded-2xl gap-12 flex flex-col\">\n        <div className=\"flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16\">\n          <h3 className=\"text-xl font-semibold dark:text-zinc-50\">Sign Up</h3>\n          <p className=\"text-sm text-muted-foreground\">\n            Create your account to get started\n          </p>\n        </div>\n        <div className=\"px-4 sm:px-16\">\n          <GoogleButton callbackUrl={callbackUrl} />\n          <div className=\"relative my-4\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"w-full border-t border-muted\" />\n            </div>\n            <div className=\"relative flex justify-center text-xs uppercase\">\n              <span className=\"bg-background px-2 text-muted-foreground\">Or sign up with email</span>\n            </div>\n          </div>\n        </div>\n        <AuthForm \n          action={handleSubmit} \n          defaultEmail={email}\n          isRegistration={true}\n        >\n          <SubmitButton\n            className=\"w-full\"\n            loading={state.status === 'in_progress'}\n            isSuccessful={isSuccessful}\n          >\n            Sign Up\n          </SubmitButton>\n          <p className=\"text-center text-sm text-muted-foreground\">\n            Already have an account?{' '}\n            <Link href={`/login?callbackUrl=${encodeURIComponent(callbackUrl)}`} className=\"underline\">\n              Sign in\n            </Link>\n          </p>\n        </AuthForm>\n      </div>\n    </div>\n  );\n}", "startLine": 11, "endLine": 90, "type": "component", "symbols": ["RegisterForm"], "score": 0.9, "context": "React component for registration page that uses register action and manages session state and redirects, relevant to authentication and session management UI.", "includesImports": false}, {"filePath": "src/app/(auth)/actions.ts", "content": "export const requestPasswordReset = async (\n  _: ResetPasswordRequestState,\n  formData: FormData,\n): Promise<ResetPasswordRequestState> => {\n  try {\n    const validatedData = resetPasswordRequestSchema.parse({\n      email: formData.get('email'),\n    });\n\n    // Check rate limit before proceeding\n    const rateLimitAllowed = await checkPasswordResetRateLimit(validatedData.email);\n    if (!rateLimitAllowed) {\n      return { status: 'rate_limited' };\n    }\n\n    const users = await getUser(validatedData.email);\n    if (users.length === 0) {\n      return { status: 'user_not_found' };\n    }\n\n    const user = users[0];\n    const token = crypto.randomUUID();\n    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours\n\n    await createPasswordResetToken({\n      userId: user.id,\n      token,\n      expiresAt,\n    });\n\n    const resetLink = `${process.env.NEXT_PUBLIC_APP_URL}/reset-password/${token}`;\n\n    try {\n      await sendPasswordResetEmail({\n        email: user.email,\n        resetLink,\n      });\n    } catch (error) {\n      console.error('Failed to send reset email:', error);\n      // Delete the token if email fails\n      await deletePasswordResetToken(token);\n      return { status: 'failed' };\n    }\n\n    return { status: 'success' };\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { status: 'invalid_data' };\n    }\n    return { status: 'failed' };\n  }\n};", "startLine": 136, "endLine": 187, "type": "util", "symbols": ["requestPasswordReset"], "score": 0.8, "context": "<PERSON>les password reset request, part of authentication flow and session recovery.", "includesImports": false}, {"filePath": "src/app/(auth)/actions.ts", "content": "export const resetPassword = async (\n  _: ResetPasswordState,\n  formData: FormData,\n): Promise<ResetPasswordState> => {\n  try {\n    const validatedData = resetPasswordSchema.parse({\n      token: formData.get('token'),\n      password: formData.get('password'),\n    });\n\n    const tokens = await getPasswordResetToken(validatedData.token);\n    if (tokens.length === 0) {\n      return { status: 'invalid_token' };\n    }\n\n    const token = tokens[0];\n    if (token.expiresAt < new Date()) {\n      return { status: 'token_expired' };\n    }\n\n    const salt = genSaltSync(10);\n    const hashedPassword = hashSync(validatedData.password, salt);\n    await updateUserPassword({\n      userId: token.userId,\n      hashedPassword,\n    });\n\n    await deletePasswordResetToken(token.token);\n\n    return { status: 'success' };\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { status: 'invalid_data' };\n    }\n    return { status: 'failed' };\n  }\n};", "startLine": 189, "endLine": 225, "type": "util", "symbols": ["resetPassword"], "score": 0.8, "context": "<PERSON>les password reset with token, part of authentication flow and session recovery.", "includesImports": false}, {"filePath": "src/components/login-dialog.tsx", "content": "export const LoginDialog = observer(() => {\n  const [isLoading, setIsLoading] = useState(false);\n  const { generatorStore } = useStores();\n  const { loginDialogOpen, toggleLoginDialog } = generatorStore;\n  const { anonymousId } = useAnonymousSession();\n  const { data: session } = useSession();\n  const { status: transferStatus, transfer, setStatus } = useTransferOwnership();\n  \n  // Track when login dialog is opened\n  useEffect(() => {\n    if (loginDialogOpen) {\n      trackUserEvent('SIGNED_IN', {\n        auth_method: 'dialog_shown',\n        referral_source: window.location.pathname\n      });\n      \n      // Also track as a subscription event since this is often related to usage limits\n      trackSubscriptionEvent('UPGRADE_DIALOG_VIEWED', {\n        trigger_reason: 'login_prompt',\n        current_plan: 'anonymous'\n      });\n    }\n  }, [loginDialogOpen]);\n\n  const handleLogin = async (provider: string) => {\n    try {\n      // Track login attempt with specific provider\n      trackUserEvent('SIGNED_IN', {\n        auth_method: provider,\n        referral_source: window.location.pathname\n      });\n      \n      setIsLoading(true);\n      // Store anonymous ID for transfer\n      if (anonymousId) {\n        localStorage.setItem('pendingTransfer', anonymousId);\n      }\n      await signIn(provider, { callbackUrl: window.location.href });\n    } catch (error) {\n      console.error('Login error:', error);\n      \n      // Track login error\n      trackUserEvent('SIGNED_IN', {\n        auth_method: provider,\n        error_type: 'login_error',\n        referral_source: window.location.pathname\n      });\n      \n      setIsLoading(false);\n    }\n  };\n\n  // Check and handle pending transfer\n  useEffect(() => {\n    const pendingTransfer = localStorage.getItem('pendingTransfer');\n    if (pendingTransfer && session?.user) {\n      transfer(pendingTransfer);\n    }\n  }, [session, transfer]);\n\n  useEffect(() => {\n    if(transferStatus === \"success\") {\n      toggleLoginDialog(false);\n    }\n  }, [transferStatus]);\n\n  return (\n    <>\n      <Dialog \n        open={loginDialogOpen} \n        onOpenChange={(open) => generatorStore.toggleLoginDialog(open)}\n      >\n      <DialogContent className=\"sm:max-w-[360px] bg-black text-white p-4\">\n        <DialogHeader>\n          <DialogTitle className=\"text-xl font-bold text-white\">Continue Building Your App</DialogTitle>\n          <DialogDescription className=\"text-gray-400\">\n            Sign in to continue building your app and access more features.\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"flex flex-col gap-4 mt-4\">\n          <div className=\"space-y-4\">\n            <h4 className=\"text-sm font-medium text-white\">What you&apos;ll get:</h4>\n            <ul className=\"space-y-2 text-sm text-gray-400\">\n              <li>✓ More app generations</li>\n              <li>✓ Save & resume projects</li>\n              <li>✓ Access to all templates</li>\n              <li>✓ Custom configurations</li>\n            </ul>\n          </div>\n\n          <Button\n            className=\"w-full bg-white text-black hover:bg-gray-200\"\n            onClick={() => handleLogin('google')}\n            disabled={isLoading}\n          >\n            {isLoading ? (\n              <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n            ) : (\n              <>\n                <svg className=\"mr-2 h-4 w-4\" viewBox=\"0 0 24 24\">\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                  />\n                </svg>\n                Continue with Google\n              </>\n            )}\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n    <TransferStatus \n      open={transferStatus === 'loading' || transferStatus === 'success'} \n      status={transferStatus === 'loading' ? 'loading' : 'success'}\n      onOpenChange={(open) => {\n        if (!open && transferStatus === 'success') {\n          setStatus('idle');\n        }\n      }}\n    />\n    </>\n  );\n});", "startLine": 15, "endLine": 151, "type": "component", "symbols": ["LoginDialog"], "score": 0.8, "context": "Component managing login dialog with session and signIn calls, handling user session and login flow.", "includesImports": false}, {"filePath": "src/lib/auth/auth.utils.ts", "content": "import {auth} from \"@/app/(auth)/auth\";\n\nexport const checkAccess = async () => {\n    const session = await auth();\n\n    if (!session || !session.user || !session.user.id) {\n        return new Response('Unauthorized', {status: 401});\n    }\n}", "startLine": 1, "endLine": 9, "type": "util", "symbols": ["checkAccess"], "score": 0.7, "context": "Utility function that checks if a user session exists and returns 401 Unauthorized if not, relevant for session validation in authentication.", "includesImports": false}, {"filePath": "src/app/(auth)/reset-password/request/page.tsx", "content": "export default function RequestPasswordResetPage() {\n  const router = useRouter();\n  const [state, formAction] = useActionState<ResetPasswordRequestState, FormData>(requestPasswordReset, {\n    status: 'idle',\n  });\n\n  useEffect(() => {\n    if (state.status === 'success') {\n      router.push('/login?reset=requested');\n    }\n  }, [state.status, router]);\n\n  return (\n    <div className=\"flex min-h-screen items-center justify-center\">\n      <div className=\"w-full max-w-md space-y-8 px-4 py-12\">\n        <div className=\"space-y-2 text-center\">\n          <h1 className=\"text-3xl font-bold\">Reset Password</h1>\n          <p className=\"text-gray-500\">\n            Enter your email address and we&apos;ll send you a link to reset your\n            password.\n          </p>\n        </div>\n\n        <form action={formAction} className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Input\n              id=\"email\"\n              name=\"email\"\n              type=\"email\"\n              placeholder=\"Email\"\n              required\n              className=\"w-full\"\n            />\n          </div>\n\n          {state.status === 'user_not_found' && (\n            <p className=\"text-sm text-red-500\">\n              No account found with this email address.\n            </p>\n          )}\n\n          {state.status === 'rate_limited' && (\n            <p className=\"text-sm text-red-500\">\n              Too many reset attempts. Please try again in 24 hours.\n            </p>\n          )}\n\n          {state.status === 'invalid_data' && (\n            <p className=\"text-sm text-red-500\">Please enter a valid email address.</p>\n          )}\n\n          {state.status === 'failed' && (\n            <p className=\"text-sm text-red-500\">\n              Something went wrong. Please try again.\n            </p>\n          )}\n\n          <Button\n            type=\"submit\"\n            className=\"w-full\"\n            disabled={state.status === 'in_progress'}\n          >\n            {state.status === 'in_progress' ? 'Sending...' : 'Send Reset Link'}\n          </Button>\n        </form>\n      </div>\n    </div>\n  );\n}", "startLine": 11, "endLine": 79, "type": "component", "symbols": ["RequestPasswordResetPage"], "score": 0.7, "context": "Component for requesting password reset, part of authentication and session recovery flow.", "includesImports": false}, {"filePath": "src/app/(auth)/reset-password/[token]/PasswordTokenComponent.tsx", "content": "export default function PasswordTokenComponent({token}: { token: string }) {\n    const router = useRouter();\n    const [state, formAction] = useActionState<ResetPasswordState, FormData>(resetPassword, {\n        status: 'idle',\n    });\n\n    useEffect(() => {\n        if (state.status === 'success') {\n            router.push('/login?reset=success');\n        }\n    }, [state.status, router]);\n\n    return (\n        <div className=\"flex min-h-screen items-center justify-center\">\n            <div className=\"w-full max-w-md space-y-8 px-4 py-12\">\n                <div className=\"space-y-2 text-center\">\n                    <h1 className=\"text-3xl font-bold\">Reset Password</h1>\n                    <p className=\"text-gray-500\">Enter your new password below.</p>\n                </div>\n\n                <form action={formAction} className=\"space-y-4\">\n                    <input type=\"hidden\" name=\"token\" value={token}/>\n\n                    <div className=\"space-y-2\">\n                        <Input\n                            id=\"password\"\n                            name=\"password\"\n                            type=\"password\"\n                            placeholder=\"New Password\"\n                            required\n                            minLength={6}\n                            className=\"w-full\"\n                        />\n                    </div>\n\n                    {state.status === 'invalid_token' && (\n                        <p className=\"text-sm text-red-500\">\n                            This reset link is invalid or has already been used.\n                        </p>\n                    )}\n\n                    {state.status === 'token_expired' && (\n                        <p className=\"text-sm text-red-500\">\n                            This reset link has expired. Please request a new one.\n                        </p>\n                    )}\n\n                    {state.status === 'invalid_data' && (\n                        <p className=\"text-sm text-red-500\">\n                            Password must be at least 6 characters long.\n                        </p>\n                    )}\n\n                    {state.status === 'failed' && (\n                        <p className=\"text-sm text-red-500\">\n                            Something went wrong. Please try again.\n                        </p>\n                    )}\n\n                    <Button\n                        type=\"submit\"\n                        className=\"w-full\"\n                        disabled={state.status === 'in_progress'}\n                    >\n                        {state.status === 'in_progress' ? 'Resetting...' : 'Reset Password'}\n                    </Button>\n                </form>\n            </div>\n        </div>\n    );", "startLine": 8, "endLine": 77, "type": "component", "symbols": ["PasswordTokenComponent"], "score": 0.7, "context": "Component for resetting password with token, part of authentication and session recovery flow.", "includesImports": false}, {"filePath": "src/components/auth/google-button.tsx", "content": "import { Button } from '@/components/ui/button';\nimport { signIn } from 'next-auth/react';\nimport { Loader2 } from 'lucide-react';\nimport { useState } from 'react';\n\nimport { trackUserEvent } from '@/lib/analytics/track';\n\nexport function GoogleButton({ callbackUrl = '/generator' }) {\n  const [isLoading, setIsLoading] = useState(false);\n\n  const handleGoogleSignIn = async () => {\n    try {\n      setIsLoading(true);\n      await signIn('google', { redirectTo: callbackUrl });\n    } catch (error) {\n      console.error('Google sign in error:', error);\n      \n      // Track Google sign-in error\n      trackUserEvent('SIGNED_IN', {\n        auth_method: 'google',\n        error_type: 'google_auth_error',\n        referral_source: window.location.pathname\n      });\n    }\n  };\n\n  return (\n    <Button\n      variant=\"outline\"\n      type=\"button\"\n      disabled={isLoading}\n      className=\"w-full\"\n      onClick={handleGoogleSignIn}\n    >\n      {isLoading ? (\n        <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n      ) : (\n        <svg className=\"mr-2 h-4 w-4\" viewBox=\"0 0 24 24\">\n          <path\n            d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n            fill=\"#4285F4\"\n          />\n          <path\n            d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n            fill=\"#34A853\"\n          />\n          <path\n            d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n            fill=\"#FBBC05\"\n          />\n          <path\n            d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n            fill=\"#EA4335\"\n          />\n        </svg>\n      )}\n      Continue with Google\n    </Button>\n  );\n}", "startLine": 3, "endLine": 62, "type": "component", "symbols": ["GoogleButton"], "score": 0.7, "context": "Component for Google OAuth sign-in button, relevant to authentication flow.", "includesImports": false}, {"filePath": "src/components/auth-form.tsx", "content": "export function AuthForm({\n  action,\n  children,\n  defaultEmail = '',\n  isRegistration\n}: {\n  action: NonNullable<\n    string | ((formData: FormData) => void | Promise<void>) | undefined\n  >;\n  children: React.ReactNode;\n  defaultEmail?: string;\n  isRegistration?: boolean;\n}) {\n  return (\n      <Form action={action} className=\"flex flex-col gap-4 px-4 sm:px-16\">\n        {\n          isRegistration ?\n              <div className=\"flex flex-col gap-2\">\n                <Label\n                    htmlFor=\"name\"\n                    className=\"text-zinc-600 font-normal dark:text-zinc-400\"\n                >\n                  What should we call you?\n                </Label>\n\n                <Input\n                    id=\"name\"\n                    name=\"name\"\n                    className=\"bg-muted text-md md:text-sm\"\n                    type=\"text\"\n                    placeholder=\"Your first name (Alex)\"\n                    autoComplete=\"first-name\"\n                    required\n                    autoFocus\n                />\n              </div>:\n              null\n        }\n\n        <div className=\"flex flex-col gap-2\">\n          <Label\n              htmlFor=\"email\"\n              className=\"text-zinc-600 font-normal dark:text-zinc-400\"\n          >\n            Email Address\n          </Label>\n\n          <Input\n              id=\"email\"\n              name=\"email\"\n              className=\"bg-muted text-md md:text-sm\"\n              type=\"email\"\n              placeholder=\"<EMAIL>\"\n              autoComplete=\"email\"\n              required\n              defaultValue={defaultEmail}\n          />\n        </div>\n\n        <div className=\"flex flex-col gap-2\">\n          <Label\n              htmlFor=\"password\"\n              className=\"text-zinc-600 font-normal dark:text-zinc-400\"\n          >\n            Password\n          </Label>\n\n          <Input\n              id=\"password\"\n              name=\"password\"\n              className=\"bg-muted text-md md:text-sm\"\n              type=\"password\"\n              required\n          />\n        </div>\n\n        {children}\n      </Form>\n  );\n}", "startLine": 6, "endLine": 85, "type": "component", "symbols": ["AuthForm"], "score": 0.6, "context": "Reusable form component for login and registration forms, relevant for authentication UI but not session management logic.", "includesImports": false}], "additionalFiles": []}}