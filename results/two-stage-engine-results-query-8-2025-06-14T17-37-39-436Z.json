{"timestamp": "2025-06-14T17:37:39.436Z", "query": "How does the AI streaming and response generation work? Show me the complete flow from user input to AI response", "executionTime": 29871, "snippetsCount": 18, "additionalFilesCount": 0, "totalLines": 4140, "snippets": [{"filePath": "src/components/discuss/DiscussWithAI.tsx", "type": "component", "context": "This component handles the user input and AI chat interaction using the useChat hook, initializing messages, sending user input, and receiving AI responses. It shows the flow from user input to AI response display.", "score": 1, "lines": 88, "startLine": 30, "endLine": 117, "symbols": ["DiscussWithAI"], "preview": "export const DiscussWithAI = observer(({\n                                           isOpen,\n                                           onClose,\n                                           initialMessage = '',\n                                           type = 'error-fix',\n..."}, {"filePath": "src/lib/chat/handlers/message.handler.ts", "type": "unknown", "context": "This class processes and prepares messages for AI requests, including filtering, formatting, caching, and enhancing user messages. It is central to how messages are handled before sending to the AI and after receiving responses.", "score": 1, "lines": 632, "startLine": 34, "endLine": 665, "symbols": ["MessageHandler"], "preview": "export class MessageHandler {\n    private messagesWithTurns: Array<Message> = [];\n    private coreMessages: CoreMessage[] = [];\n    private userMessage: CoreMessage | null = null;\n    private filteredMessages: CoreMessage[] = [];\n..."}, {"filePath": "src/lib/services/stream-service.ts", "type": "unknown", "context": "This service manages the AI streaming process, including starting the stream, handling chunks, tool calls, errors, and finishing the stream. It is the core of AI response streaming and generation.", "score": 1, "lines": 273, "startLine": 110, "endLine": 382, "symbols": ["StreamService"], "preview": "export class StreamService {\n    private config: StreamServiceConfig;\n    private toolCallCosts: ToolCallCost[] = [];\n\n    constructor(config: StreamServiceConfig) {\n..."}, {"filePath": "src/lib/ai/index.ts", "type": "unknown", "context": "This module integrates the AI model with custom fetch and middleware, handling message formatting and caching, central to AI response generation and streaming.", "score": 1, "lines": 450, "startLine": 1, "endLine": 450, "symbols": ["AI model integration and custom fetch"], "preview": "import {TextPart, wrapLanguageModel} from 'ai';\nimport {createOpenRouter} from \"@openrouter/ai-sdk-provider\";\nimport {customMiddleware} from './custom-middleware';\nimport fs from \"fs/promises\";\nimport {exportData} from \"@/lib/server-utils\";\n..."}, {"filePath": "src/lib/services/two-stage-context-engine.ts", "type": "unknown", "context": "This class implements a two-stage LLM approach to identify relevant files and snippets for a query, critical for understanding how AI selects context for response generation.", "score": 0.9, "lines": 530, "startLine": 27, "endLine": 556, "symbols": ["TwoStageLLMContextEngine"], "preview": "export class TwoStageLLMContextEngine {\n  private files: FileItem[] = [];\n  private fileIndex: Map<string, FileItem> = new Map();\n\n  /**\n..."}, {"filePath": "src/lib/services/context-engine.ts", "type": "unknown", "context": "This class provides comprehensive codebase understanding and context retrieval, including snippet extraction and action plan generation, essential for AI response generation flow.", "score": 0.9, "lines": 585, "startLine": 106, "endLine": 690, "symbols": ["ContextEngine"], "preview": "export class ContextEngine {\n    private files: FileItem[] = [];\n    private fileIndex: Map<string, FileItem> = new Map();\n    private dependencyGraph: Map<string, Set<string>> = new Map();\n    private fileMetadata: Map<string, FileMetadata> = new Map();\n..."}, {"filePath": "src/lib/ai/prompts.ts", "type": "unknown", "context": "This module defines system prompts and instructions guiding AI behavior and response generation, important for understanding AI response flow.", "score": 0.8, "lines": 216, "startLine": 16, "endLine": 231, "symbols": ["AI prompts and system instructions"], "preview": "\nexport const systemPrompt = `\n<overview>\nToday's date is ${dayjs().format(\"DD-MM-YYYY\")} and the day of the week is ${dayjs().format(\"dddd\")}\nYou are an exceptional Senior React Native developer creating visually stunning mobile apps. Focus on premium design and native feel with production-grade code.\n..."}, {"filePath": "src/lib/chat/tools/multi-perspective-analysis.tool.ts", "type": "util", "context": "This tool demonstrates a complex AI streaming interaction with multiple AI personas generating and streaming responses, showing an example of AI response generation and streaming flow.", "score": 0.5, "lines": 261, "startLine": 55, "endLine": 315, "symbols": ["multiPerspectiveAnalysis"], "preview": "export const multiPerspectiveAnalysis = ({\n  dataStream,\n}: {\n  dataStream: DataStreamWriter;\n}) => {\n..."}, {"filePath": "src/lib/chat/tools/query-codebase.ts", "type": "util", "context": "This tool queries the codebase context for AI, relevant for understanding how AI gathers code context for response generation.", "score": 0.5, "lines": 161, "startLine": 23, "endLine": 183, "symbols": ["queryCodebase"], "preview": "export const queryCodebase = ({\n                                  projectId,\n                                  files,\n                                  fileManager,\n                                  messageId = '',\n..."}, {"filePath": "src/lib/chat/tools/query-supabase-context.ts", "type": "util", "context": "This tool queries Supabase context, showing how AI integrates external database context into response generation.", "score": 0.5, "lines": 203, "startLine": 23, "endLine": 225, "symbols": ["querySupabaseContext"], "preview": "export const querySupabaseContext = ({\n    projectId,\n    messageId = '',\n}: {\n    projectId: string, \n..."}, {"filePath": "src/lib/chat/tools/get-file-contents.ts", "type": "util", "context": "This tool retrieves full file contents or directory listings, important for AI to get detailed code context during response generation.", "score": 0.5, "lines": 236, "startLine": 7, "endLine": 242, "symbols": ["getFileContents"], "preview": "export const getFileContents = ({\n                                    fileManager,\n                                    dataStream,\n                                    creditUsageTracker\n                                }: {\n..."}, {"filePath": "src/lib/chat/tools/add-ai-memory.ts", "type": "util", "context": "This tool stores critical information for future AI interactions, relevant for understanding how AI maintains context across interactions.", "score": 0.4, "lines": 72, "startLine": 12, "endLine": 83, "symbols": ["addAiMemory"], "preview": "export const addAiMemory = ({\n                                dataStream,\n                                creditUsageTracker,\n                                projectId\n                            }: {\n..."}, {"filePath": "src/lib/chat/tools/search-web.ts", "type": "util", "context": "This tool enables AI to search the web for up-to-date information, relevant for understanding external data integration in AI response generation.", "score": 0.4, "lines": 85, "startLine": 11, "endLine": 95, "symbols": ["searchWeb"], "preview": "export const searchWeb = ({\n  dataStream,\n  creditUsageTracker\n}: {\n  dataStream: DataStreamWriter;\n..."}, {"filePath": "src/lib/chat/tools/get-client-logs.ts", "type": "util", "context": "This tool fetches client logs for debugging, relevant for AI to gather runtime context during response generation.", "score": 0.4, "lines": 105, "startLine": 13, "endLine": 117, "symbols": ["getClientLogs"], "preview": "export const getClientLogs = ({\n  dataStream,\n  creditUsageTracker,\n  logs,\n  messageId\n..."}, {"filePath": "src/lib/chat/tools/get-supabase-instructions.ts", "type": "util", "context": "This tool provides Supabase project instructions to AI, relevant for context in AI response generation.", "score": 0.4, "lines": 38, "startLine": 12, "endLine": 49, "symbols": ["getSupabaseInstructions"], "preview": "export const getSupabaseInstructions = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n..."}, {"filePath": "src/lib/chat/tools/get-supabase-logs.ts", "type": "util", "context": "This tool fetches Supabase logs for debugging, relevant for AI to understand backend runtime context.", "score": 0.4, "lines": 112, "startLine": 14, "endLine": 125, "symbols": ["getSupabaseLogs"], "preview": "export const getSupabaseLogs = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n..."}, {"filePath": "src/lib/chat/tools/manage-supabase-auth.ts", "type": "util", "context": "This tool manages Supabase auth configuration, relevant for AI to handle auth-related response generation.", "score": 0.4, "lines": 78, "startLine": 13, "endLine": 90, "symbols": ["manageSupabaseAuth"], "preview": "export const manageSupabaseAuth = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n..."}, {"filePath": "src/lib/chat/tools/index.ts", "type": "unknown", "context": "Exports all chat tools which are used in AI response generation and streaming, relevant for understanding available tools in the flow.", "score": 0.3, "lines": 15, "startLine": 1, "endLine": 15, "symbols": ["chat tools exports"], "preview": "// Export all tools from this directory\nexport * from './get-supabase-instructions';\nexport * from './get-supabase-logs';\nexport * from './get-client-logs';\nexport * from './get-file-contents';\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/components/discuss/DiscussWithAI.tsx", "content": "export const DiscussWithAI = observer(({\n                                           isOpen,\n                                           onClose,\n                                           initialMessage = '',\n                                           type = 'error-fix',\n                                           onApplySolution,\n                                           relevantFiles = [],\n                                           metadata = {},\n                                           chatId,\n                                           projectId\n                                       }: DiscussWithAIProps) => {\n    const { generatorStore, logStore, discussStore } = useStores();\n    const initialMessageSetRef = useRef(false);\n\n    // Get active session if chatId is provided\n    const session = chatId ? generatorStore.getActiveSession(chatId) : null;\n\n    // State for attachments\n    const [attachments, setAttachments] = useState<Attachment[]>([]);\n\n    // Format initial message based on type\n    const getFormattedPrompt = (message: string) => {\n        if (discussStore.state.selectedType === 'error-fix') {\n            return `I encountered an error: ${message}\n\nPlease analyze this error carefully and help me fix it. Consider:\n1. The specific error message and its root cause\n2. Any related code that might be affected\n3. Dependencies that might be missing or misconfigured\n4. Similar patterns in the codebase that work correctly\n5. Provide a detailed solution with specific code changes\n\nPlease provide a detailed solution.`;\n        } else if (discussStore.state.selectedType === 'code-review') {\n            return `I'd like your opinion on this code: ${message}\n\nPlease review this code and provide feedback on:\n1. Code quality and best practices\n2. Potential bugs or edge cases\n3. Performance considerations\n4. Readability and maintainability\n5. Suggestions for improvement\n\nPlease be specific in your feedback.`;\n        }\n        return message;\n    };\n\n    // Get relevant files from the session if not provided\n    const filesToSend = relevantFiles.length > 0 ? relevantFiles : (session?.fileTree || []);\n\n    // Initialize chat with existing messages if a chat is selected\n    const {\n        messages,\n        input,\n        setInput,\n        handleSubmit,\n        isLoading,\n        append,\n        stop,\n        setMessages,\n        status\n    } = useChat({\n        id: discussStore.currentChatId,\n        api: '/api/chat',\n        initialMessages: discussStore.currentMessages,\n        body: {\n            files: filesToSend,\n            activeFile: '',\n            dependencies: {},\n            linkSupabaseProjectId: '',\n            linkSupabaseConnection: '',\n            projectId,\n            logs: logStore.getLogs(chatId),\n            agentModeEnabled: false,\n            isReload: false,\n            isInitial: false,\n            componentContexts: [],\n            isAutoFixed: false,\n            isDiscussion: true,\n            type: discussStore.state.selectedType,\n            metadata\n        },\n        onError: (error) => {\n            console.error('Error in discussion:', error);\n            toast.error('Failed to get a response. Please try again.');\n        }\n    });", "startLine": 30, "endLine": 117, "type": "component", "symbols": ["DiscussWithAI"], "score": 1, "context": "This component handles the user input and AI chat interaction using the useChat hook, initializing messages, sending user input, and receiving AI responses. It shows the flow from user input to AI response display.", "includesImports": false}, {"filePath": "src/lib/chat/handlers/message.handler.ts", "content": "export class MessageHandler {\n    private messagesWithTurns: Array<Message> = [];\n    private coreMessages: CoreMessage[] = [];\n    private userMessage: CoreMessage | null = null;\n    private filteredMessages: CoreMessage[] = [];\n    private systemPrompts: CoreSystemMessage[] = []\n    private fileMessage: CoreMessage | null = null;\n    private componentContexts: {componentName: string;\n    element: string;\n    sourceFile: string;\n    lineNumber: number;\n    imageUrl?: string;}[] = []\n    private extractor: Extractor = new Extractor();\n\n    /**\n     * Initialize the handler with messages\n     * This sets up the internal state for processing\n     */\n    public async initialize(messages: Array<Message>, minMessages: number = 5, options: {\n        projectId?: string,\n        backendEnabled?: boolean,\n        componentContexts?: ComponentContext[],\n        isFirstUserMessage?: boolean,\n        agentModeEnabled?: boolean,\n        userId: string,\n        isDiscussion?: boolean,\n        discussionType?: 'error-fix' | 'code-review' | 'general-discussion'}): Promise<void> {\n        let system;\n        const matchedFlagPayload = await posthogClient.getFeatureFlagPayload('design-preview-feature', options.userId)\n\n        // Handle discussion mode first (highest priority)\n        if (options?.isDiscussion) {\n            switch (options.discussionType) {\n                case 'error-fix':\n                    system = DISCUSS_MODE_ERROR_FIX_PROMPT;\n                    break;\n                case 'code-review':\n                    system = DISCUSS_MODE_CODE_REVIEW_PROMPT;\n                    break;\n                default:\n                    system = DISCUSS_MODE_PROMPT;\n                    break;\n            }\n        } else if (options?.isFirstUserMessage) {\n            system = STREAMLINED_V1_IMPLEMENTATION_PROMPT;\n        } else {\n            if (options?.agentModeEnabled) {\n                system = STREAMLINED_AGENT_PROMPT\n            } else {\n                system = systemPrompt;\n            }\n        }\n\n            // .replace(/{{BACKEND_PROMPT}}/g, options.backendEnabled ? backendPrompt : \"There is no backend configured. NO MATTER WHAT, don't write backend code even if the user asks for it.\");\n\n        if(options.backendEnabled) {\n            system = system.replace(/{{BACKEND_URL}}/g, `https://${process.env.VERCEL_URL || process.env.BACKEND_BASE_URL}/api/dynamic-backend/${options.projectId}/latest`);\n        }\n\n        // console.log('Replacing backend prompt, enabled:', options.backendEnabled)\n\n        this.systemPrompts = [\n            {\n                role: 'system',\n                content: system\n            },\n    //         {\n    //             role: 'system',\n    //             content: ` <extended_system_prompt>\n    //     <title>From the system to you:</title>\n    //     <extended_system_prompt_reminder>Remember, you are building a mobile app and the user needs to get a wow factor. Use spacing as a design element and keep it minimal and clean.</extended_system_prompt_reminder>\n    //     <reminders>\n    //         <reminder id=\"1\">Read the system prompt very carefully.</reminder>\n    //         <reminder id=\"2\">[ULTRA IMPORTANT]: Always follow the MO_FILE response formats. Use MO_DIFF whenever you can to save costs. </reminder>\n    //         <reminder id=\"3\">[ULTRA IMPORTANT]: NEVER change the existing design/layout/functionality of a screen when making edits unless explicitly specified.</reminder>\n    //         <reminder id=\"4\">[ULTRA IMPORTANT]: Return complete implementations of every file, do not leave it for the user to implement. The user does not have access to the code.</reminder>\n    //         <reminder id=\"5\">ALWAYS adhere to the \"NEVER ASSUME THAT THE USER WILL MAKE CHANGES TO A FILE\" section no matter what</reminder>\n    //         <reminder id=\"6\">ALWAYS provide complete implementations and never partial updates to files</reminder>\n    //\n    // </reminders>\n    // </extended_system_prompt>`\n    //         }\n        ]\n        this.messagesWithTurns = this.getMessagesWithCompleteTurns(messages, minMessages);\n        this.coreMessages = convertToCoreMessages(this.messagesWithTurns);\n        this.userMessage = this.getMostRecentUserMessage(this.coreMessages);\n        this.filteredMessages = this.filterCoreMessages(this.coreMessages);\n        if(options.componentContexts) {\n            this.componentContexts = options.componentContexts;\n        }\n        this.appendMessageCountSystemMessage();\n    }\n\n\n    appendToSystemPrompt(content: string) {\n        this.systemPrompts.push({\n            role: 'system',\n            content: content\n        })\n    }\n\n    private appendMessageCountSystemMessage() {\n        // Count how many user messages we've had\n        const userMessageCount = this.messagesWithTurns.filter(m => m.role === 'assistant').length;\n\n        // if (userMessageCount <= 3) {\n        //     this.systemPrompts.push({\n        //         role: \"system\",\n        //         content: `${onboardingPrompt}\n        //     <CurrentPhase>Message: ${userMessageCount}</CurrentPhase>`\n        //     })\n        // }\n    }\n\n    /**\n     * Replace MO_FILE and MO_DIFF tags with a summary message\n     * @param text The text to process\n     * @returns The text with tags replaced\n     */\n    public replaceMoFileTags(text: string) {\n        // Create proper RegExp objects with correct character classes to match any character including newlines\n        const mofileregex = new RegExp(`<MO_FILE\\\\s+path=\"([^\"]+)\"[^>]*?>([\\\\s\\\\S]*?)</MO_FILE>`, 'g');\n        const modiffregex = new RegExp(`<MO_DIFF\\\\s+path=\"([^\"]+)\"[^>]*?>([\\\\s\\\\S]*?)</MO_DIFF>`, 'g');\n\n        // For MO_FILE tags, extract the path and replace with a summary\n        const fileReplacementContent = (match: string, path: string, content: string) => {\n            // Create a summary of what was changed\n            return `[Created/Modified file: ${path}]`;\n        };\n\n        // For MO_DIFF tags, extract the path and replace with a summary\n        const diffReplacementContent = (match: string, path: string, content: string) => {\n            // Create a summary of what was changed\n            return `[Modified file: ${path}]`;\n        };\n\n        // Replace both MO_FILE and MO_DIFF tags with summaries\n        let replaced = text.replace(mofileregex, fileReplacementContent);\n        return replaced.replace(modiffregex, diffReplacementContent);\n    }\n\n    private removeCode(message: CoreMessage) {\n        if (typeof message.content === \"string\") {\n            message.content = this.replaceMoFileTags(message.content);\n        } else {\n            message.content = message.content.map(cont => {\n                if (cont.type === \"text\") {\n                    cont.text = this.replaceMoFileTags(cont.text);\n                }\n                return cont;\n            }) as any\n        }\n        return message;\n    }\n    \n    /**\n     * Truncate tool message content to save context window space\n     * @param message The tool message to truncate\n     * @param maxLength Maximum allowed content length (default: 3000)\n     * @returns The message with truncated content\n     */\n    private truncateToolContent(message: CoreMessage, maxLength: number = 3000): CoreMessage {\n        if (!message) return message;\n        \n        if (typeof message.content === \"string\") {\n            if (message.content.length > maxLength) {\n                message.content = message.content.slice(0, maxLength) + \n                    '\\n// Tool response truncated to save context window size. Focus on the available information.';\n            }\n        } else if (Array.isArray(message.content)) {\n            message.content = message.content.map(cont => {\n                // Handle text type content\n                if (cont.type === \"text\" && cont.text.length > maxLength) {\n                    return {\n                        ...cont,\n                        text: cont.text.slice(0, maxLength) + \n                            '\\n// Tool response truncated to save context window size. Focus on the available information.'\n                    };\n                }\n                \n                // Handle tool-result type content\n                if (cont.type === \"tool-result\" && cont.result && typeof cont.result === 'string' && cont.result.length > maxLength) {\n                    return {\n                        ...cont,\n                        result: cont.result.slice(0, maxLength) + \n                            '\\n// Tool response truncated to save context window size. Focus on the available information.'\n                    };\n                }\n                \n                return cont;\n            }) as any;\n        }\n        \n        return message;\n    }\n\n\n    public getMessagesForRequest({agentModeEnabled, applyCaching}: { agentModeEnabled: boolean, applyCaching?: boolean }) {\n        let messages: CoreMessage[] = [\n            ...this.systemPrompts\n        ]\n\n        if (this.fileMessage) {\n            messages.push(this.fileMessage)\n        }\n\n        const latestUserMessageIndex = this.getMostRecentUserMessageIndex(this.filteredMessages);\n        const clonedFilteredMessages = [...this.filteredMessages.toSpliced(latestUserMessageIndex, 1)];\n        if (latestUserMessageIndex !== -1 && this.userMessage) {\n            clonedFilteredMessages[latestUserMessageIndex] = this.userMessage;\n        }\n\n        messages = messages.concat(clonedFilteredMessages)\n        if(applyCaching) {\n            messages = this.applyCaching(messages);\n        }\n        messages = messages.filter(m => !!m);\n\n        if (agentModeEnabled) {\n            // In agent mode, remove MO tags from all messages\n            messages = messages.map(message => {\n                if(message.role === \"assistant\") {\n                    message = this.removeCode(message);\n                }\n                if(message.role === \"tool\") {\n                    message = this.truncateToolContent(message);\n                }\n                return message;\n            });\n        } else {\n            // Find all assistant messages with MO_FILE tags\n            const assistantMessagesWithMOFILE = messages.map((message, index) => {\n                const content: string = (message.content?.[0] as TextPart)?.text || message.content as string;\n                return {\n                    index,\n                    hasMOFILE: message.role === \"assistant\" && content.includes(\"MO_FILE\")\n                };\n            }).filter(item => item.hasMOFILE);\n\n            // Keep the last 2 messages with MO_FILE tags intact\n            const keepLastN = 2;\n            const messagesToKeep = assistantMessagesWithMOFILE.slice(-keepLastN).map(item => item.index);\n\n            messages = messages.map((message, index) => {\n                return messagesToKeep.includes(index) ? message : this.removeCode(message);\n            });\n        }\n\n        return messages\n    }\n\n    private applyCaching(messages: CoreMessage[]) {\n        const lastSystemMessageIndex = messages.findIndex(m => m.role === 'system');\n        if (lastSystemMessageIndex !== -1) {\n            console.log('Adding cache to last system message')\n            messages[lastSystemMessageIndex] = this.appendCacheTag(messages[lastSystemMessageIndex]);\n        }\n\n        // const fileMessageIndex = messages.findIndex((message: any) => {\n        //     if (message.role === \"system\") {\n        //         return Array.isArray(message.content) ?\n        //             message.content.some(\n        //                 (text: any) => {\n        //                     return text.text?.includes(\"<FILE_MESSAGE>\")\n        //                 }\n        //             ) : message.content.includes(\"<FILE_MESSAGE>\")\n        //     }\n        //     return false;\n        // });\n        // if (fileMessageIndex !== -1) {\n        //     console.log('Adding cache to file message')\n        //     messages[fileMessageIndex] = this.appendCacheTag(messages[fileMessageIndex]);\n        // }\n\n        // Find first user message\n        const firstAssistantResponseIndex = messages.findIndex((message: any) => message?.role === \"assistant\");\n        if (firstAssistantResponseIndex !== -1) {\n            console.log('Adding cache first assistant response')\n            messages[firstAssistantResponseIndex] = this.appendCacheTag(messages[firstAssistantResponseIndex]);\n        }\n\n        const lastAssistantResponseIndex = messages.findLastIndex((message: any) => message?.role === \"assistant\");\n        if (lastAssistantResponseIndex !== -1) {\n            console.log('Adding cache to last assistant response')\n            messages[lastAssistantResponseIndex] = this.appendCacheTag(messages[lastAssistantResponseIndex]);\n        }\n        return messages;\n    }\n\n    private appendCacheTag(message: CoreMessage) {\n        if (typeof message.content === \"string\") {\n            message =  {\n                ...message,\n                providerOptions: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n                experimental_providerMetadata: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n                // @ts-ignore\n                providerMetadata: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n            }\n        } else {\n            message.content[message.content.length - 1] = {\n                ...message.content[message.content.length - 1],\n                // @ts-ignore\n                providerOptions: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n                experimental_providerMetadata: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n                // @ts-ignore\n                providerMetadata: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n            }\n        }\n\n        return message;\n    }\n\n    /**\n     * Get the current user message\n     */\n    public getCurrentUserMessage(): CoreMessage | null {\n        return this.userMessage;\n    }\n\n    public getCurrentUserMessageForUI() {\n        const messages =  convertToUIMessages([this.userMessage || {} as any])\n        return messages[0];\n    }\n\n    /**\n     * Set the current user message\n     * Useful when the message has been enhanced with additional context\n     */\n    public setCurrentUserMessage(message: CoreMessage): void {\n        this.userMessage = message;\n    }\n\n    /**\n     * Get messages with complete conversation turns\n     * Ensures we have complete context by including the most recent messages\n     * plus any older messages that form complete conversation turns\n     */\n    public getMessagesWithCompleteTurns(messages: Array<Message>, minMessages: number = 10): Array<Message> {\n        if (messages.length <= minMessages) {\n            return messages;\n        }\n\n        const recentMessages = messages.slice(-minMessages);\n        const remainingMessages = messages.slice(0, -minMessages);\n        const oldestUserMessageIndex = remainingMessages.map(m => m.role).lastIndexOf('user');\n\n        if (oldestUserMessageIndex === -1) {\n            return recentMessages;\n        }\n\n        const additionalMessages = remainingMessages.slice(oldestUserMessageIndex);\n        return [...additionalMessages, ...recentMessages];\n    }\n\n    /**\n     * Get the most recent user message from a list of messages\n     */\n    public getMostRecentUserMessage(messages: CoreMessage[]): CoreMessage | null {\n        for (let i = messages.length - 1; i >= 0; i--) {\n            if (messages[i].role === 'user') {\n                return messages[i];\n            }\n        }\n        return null;\n    }\n\n    public getMostRecentUserMessageIndex(messages: CoreMessage[]): number {\n        for (let i = messages.length - 1; i >= 0; i--) {\n            if (messages[i].role === 'user') {\n                return i;\n            }\n        }\n        return -1;\n    }\n\n    /**\n     * Filter core messages to optimize context window usage\n     * - Keeps all user messages\n     * - Keeps assistant messages without tool calls\n     * - For assistant messages with tool calls, keeps the last 4 and removes tool-call content\n     * - Removes all tool messages\n     */\n    public filterCoreMessages(messages: CoreMessage[]): CoreMessage[] {\n        // Find last 4 assistant messages that have tool-call content (increased from 2)\n        const findSecondLastUserMessageIndexArray: number[] = messages\n            .map((message, index) => message.role === \"user\" ? index : undefined)\n            .filter(m => typeof m !== \"undefined\");\n        const secondLastUserMessageIndex = findSecondLastUserMessageIndexArray.slice(-3)[0];\n        const assistantMessagesWithTools = messages\n            .filter((msg, index) => msg.role === 'assistant' &&\n                index > secondLastUserMessageIndex)\n             // Increased from 2 to 4 to provide more context\n\n        const toolCallIDs = assistantMessagesWithTools.reduce((acc, message) => {\n            if (Array.isArray(message.content)) {\n                const ids = message.content.map(cont => {\n                    return cont.type === \"tool-call\" ? cont.toolCallId : null;\n                }).filter(id => !!id);\n                acc = acc.concat(ids);\n            }\n            return acc;\n        }, [] as string[])\n\n\n        return messages.filter(msg => {\n            // Keep user messages\n            if (msg.role === 'user') return true;\n\n            // For assistant messages\n            if (msg.role === 'assistant') {\n                // If it has tool calls, only keep last 2 and remove tool-call content\n                if (Array.isArray(msg.content) && msg.content.some(c => c.type === 'tool-call')) {\n                    if (assistantMessagesWithTools.includes(msg)) {\n                        // Keep only text content\n                        // msg.content = msg.content.filter(c => c.type === 'text');\n                        return true;\n                    }\n                    return false;\n                }\n                return true; // Keep assistant messages without tool calls\n            }\n\n            // Remove tool messages not in the whitelisted ids\n            if (msg.role === 'tool') {\n                const allowed = Array.isArray(msg.content) && msg.content.some(cont => toolCallIDs.includes(cont.toolCallId));\n                return allowed;\n            }\n\n            return false;\n        });\n    }\n\n    /**\n     * Prepare user message for LLM processing\n     * - Handles both array and string content formats\n     * - Ensures image parts have correct type information\n     */\n    public prepareUserMessage(userMessage: CoreMessage): CoreMessage {\n        if (!Array.isArray(userMessage.content)) {\n            return userMessage;\n        }\n\n        // Process array content\n        const processedContent = userMessage.content.map(content => {\n            if (content.type === \"image\") {\n                return {\n                    type: 'image',\n                    mimeType: \"image/png\",\n                    image: content.image\n                } as ImagePart;\n            }\n            return content;\n        }) as UserContent;\n\n        return {\n            ...userMessage,\n            content: processedContent\n        } as CoreMessage;\n    }\n\n    /**\n     * Extracts import statements from message content\n     * Useful for analyzing code snippets and understanding dependencies\n     * @param content Array of messages to analyze\n     * @returns Array of extracted import statements\n     */\n    public extractImportsFromContent(content: string): string[] {\n        const importStatements: string[] = [];\n        const importRegex = /import\\s+(?:{[^}]*}|\\*\\s+as\\s+[^;]+|[^;{]*)\\s+from\\s+['\"][^'\"]+['\"];?|import\\s+['\"][^'\"]+['\"];?/g;\n\n        const matches = content.match(importRegex)\n        if (matches) {\n            importStatements.push(...matches);\n        }\n\n        // Remove duplicates and return\n        return [...new Set(importStatements)];\n    }\n\n    /**\n     * Append additional context to user message\n     * This can be used to add system instructions or other context\n     */\n    public appendToUserMessage(userMessage: CoreMessage, additionalContext: string): CoreMessage {\n        if (Array.isArray(userMessage.content)) {\n            return {\n                ...userMessage,\n                content: userMessage.content.map(content => {\n                    if (content.type === \"text\") {\n                        return {\n                            ...content,\n                            text: content.text + additionalContext\n                        } as TextPart;\n                    }\n                    return content;\n                }) as UserContent\n            } as CoreMessage;\n        } else {\n            return {\n                ...userMessage,\n                content: userMessage.content + additionalContext\n            } as CoreMessage;\n        }\n    }\n\n    /**\n     * Create a file message from the provided files\n     * This formats the files in a way that can be included in the message context\n     */\n    public createFileMessage(files: any[], fileManager: FileLineManager, useCache: boolean = false, agentModeEnabled = false) {\n        const textContent = `<FILE_MESSAGE>\nYou have access to only these files and you cannot create any assets like mp3 or fonts or lottie. Remember, the project is running in expo snack.\nFiles like app.json, package.json, babel.config, metro.config or any other configuration files are automatically taken care of.\n\nGiven the following files in a Expo React Native project:\n\n${files.map((file) => {\n    const fileCount = this.extractor.getFileLineCount(file.content);\n    const warnings: string[] = [];\n\n    if(fileCount.warning) {\n        warnings.push(fileCount.warning);\n    }\n                        return `\n\n---- File: ------------\nPath: ${file.name}\nFileType: ${this.extractor.getFileType(file.name)}\nNumber of lines: ${fileCount.count}\nWarnings to solve: ${warnings.join(',')}\nFile Contents\n---------\n    ${\n       agentModeEnabled ?\n       this.extractor.extractMinimalFileStructure(file.content) :\n       file.content\n    }\n    `;\n                    }).join('')}\n${agentModeEnabled ? this.extractor.generateMinimalDependencyGraph(files) : ''}\nAnswer the user's question only to be able write code and nothing else.\n                    `;\n\n        const messageContent: TextPart = {\n            type: \"text\",\n            text: textContent\n        };\n\n        // // Only add cache_control if this is a base version\n        // if (useCache) {\n        //     messageContent.cache_control = { type: \"ephemeral\" };\n        // }\n\n        this.fileMessage = {\n            role: \"user\",\n            content: textContent\n        };\n    }\n\n    /**\n     * Enhance user message with additional context\n     * - Adds Supabase prompt if available\n     * - Can be extended to add other context as needed\n     */\n    public enhanceUserMessage(supabasePrompt?: string) {\n        if (!supabasePrompt || !this.userMessage) {\n            return;\n        }\n        this.userMessage = this.appendToUserMessage(this.userMessage, `\\n${supabasePrompt}`);\n\n    }\n\n    /**\n     * Create a message object ready for saving to the database\n     * - Formats the message with all required fields\n     * - Handles proper processing of content\n     */\n    public createMessageForSaving(\n        message: CoreMessage,\n        messageId: string,\n        chatId: string,\n        userId: string,\n        autoFixed: boolean,\n    ): any {\n        return {\n            ...this.prepareUserMessage(message),\n            id: messageId,\n            createdAt: new Date(),\n            chatId: chatId,\n            userId: userId,\n            componentContexts: this.componentContexts,\n            autoFixed,\n            hidden: autoFixed\n        };\n    }\n}", "startLine": 34, "endLine": 665, "type": "unknown", "symbols": ["MessageHandler"], "score": 1, "context": "This class processes and prepares messages for AI requests, including filtering, formatting, caching, and enhancing user messages. It is central to how messages are handled before sending to the AI and after receiving responses.", "includesImports": false}, {"filePath": "src/lib/services/stream-service.ts", "content": "export class StreamService {\n    private config: StreamServiceConfig;\n    private toolCallCosts: ToolCallCost[] = [];\n\n    constructor(config: StreamServiceConfig) {\n        this.config = config;\n    }\n\n    /**\n     * Start the text streaming process\n     * @returns The result of the streamText call\n     */\n    public startStream() {\n        const {\n            messages,\n            parser,\n            diffParser,\n            dataStream,\n            userMessageId,\n            abortSignal,\n            agentModeEnabled = false,\n            isFirstMessage = false,\n            onFinish,\n            tools = {},\n            temperature = 0,\n            maxSteps = 10,\n            generateMessageId = () => crypto.randomUUID(),\n            isSupabaseConnected = false,\n            enabledTools = [],\n            enableValidation,\n            transformChunking = \"line\",\n            modelId,\n            isConnectingSupabase\n        } = this.config;\n        let streamModelId = modelId\n        if(!modelId) {\n            streamModelId = \"anthropic/claude-sonnet-4\"\n            if (!isFirstMessage) {\n                if(agentModeEnabled) {\n                    // streamModelId = 'anthropic/claude-3.7-sonnet';\n                    streamModelId = 'anthropic/claude-sonnet-4';\n                    // streamModelId = 'openai/gpt-4.1';\n                } else {\n                    streamModelId = \"anthropic/claude-sonnet-4\"\n                }\n            }\n            if(isConnectingSupabase) {\n                streamModelId = \"anthropic/claude-sonnet-4\"\n            }\n        }\n\n        return streamText({\n            model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),\n            maxSteps,\n            experimental_activeTools: enabledTools,\n            experimental_toolCallStreaming: true,\n            toolCallStreaming: true,\n            temperature,\n            messages: messages.filter(m => !!m),\n            experimental_transform: smoothStream({chunking: transformChunking}),\n            maxRetries: 3,\n            abortSignal,\n            experimental_continueSteps: true,\n            onError: (error) => {\n                console.log('Error', error);\n                throw error;\n            },\n            onStepFinish: async (stepResult) => {\n                // console.log('stepResult', stepResult)\n                const body = await fetchFromOpenRouter(stepResult.response.id);\n                // console.log('Step result body', body)\n                \n                // Track tool call cost if data is available\n                if (body && stepResult.response.id) {\n                    const toolCallCost: ToolCallCost = {\n                        id: stepResult.response.id,\n                        model: body.data.model || streamModelId,\n                        tokens_prompt: body.data.tokens_prompt || 0,\n                        tokens_completion: body.data.tokens_completion || 0,\n                        native_tokens_prompt: body.data.native_tokens_prompt,\n                        native_tokens_completion: body.data.native_tokens_completion,\n                        total_cost: body.data.total_cost || 0,\n                        cache_discount: body.data.cache_discount,\n                        provider_name: body.data.provider_name,\n                        timestamp: new Date()\n                    };\n                    console.log('toolCallCost', toolCallCost)\n                    this.toolCallCosts.push(toolCallCost);\n                }\n            },\n            onChunk: ({chunk}) => {\n                if (chunk.type === \"text-delta\") {\n                    try {\n                        // First run the file parser\n                        const afterFileParser = parser.parse(userMessageId, chunk.textDelta);\n\n                        // Then run the diff parser on the output of the file parser\n                        diffParser.parse(userMessageId, afterFileParser);\n                    } catch (e) {\n                        console.error('Error in chunk processing', e);\n                    }\n                } else if (chunk.type === \"tool-call\") {\n                    // Send tool call information to the client\n                    dataStream.writeData({\n                        type: 'tool-call',\n                        content: {\n                            toolCallId: chunk.toolCallId,\n                            toolName: chunk.toolName,\n                            args: chunk.args\n                        }\n                    });\n                } else if (chunk.type === \"tool-result\") {\n                    // Send tool result information to the client\n                    dataStream.writeData({\n                        type: 'tool-result',\n                        content: {\n                            toolCallId: chunk.toolCallId,\n                            toolName: chunk.toolName,\n                            result: chunk.result\n                        }\n                    });\n                }\n            },\n            experimental_generateMessageId: generateMessageId,\n            experimental_repairToolCall: async ({\n                                                    toolCall,\n                                                    tools,\n                                                    error,\n                                                    messages,\n                                                    system,\n                                                }) => {\n                console.log('In repair', error);\n                const result = await generateText({\n                    model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),\n                    system,\n                    messages: [\n                        ...messages,\n                        {\n                            role: 'assistant',\n                            content: [\n                                {\n                                    type: 'tool-call',\n                                    toolCallId: toolCall.toolCallId,\n                                    toolName: toolCall.toolName,\n                                    args: toolCall.args,\n                                },\n                            ],\n                        },\n                        {\n                            role: 'tool' as const,\n                            content: [\n                                {\n                                    type: 'tool-result',\n                                    toolCallId: toolCall.toolCallId,\n                                    toolName: toolCall.toolName,\n                                    result: error.message,\n                                },\n                            ],\n                        },\n                    ],\n                    tools,\n                });\n\n                const newToolCall = result.toolCalls.find(\n                    newToolCall => newToolCall.toolName === toolCall.toolName,\n                );\n\n                return newToolCall != null\n                    ? {\n                        toolCallType: 'function' as const,\n                        toolCallId: toolCall.toolCallId,\n                        toolName: toolCall.toolName,\n                        args: JSON.stringify(newToolCall.args),\n                    }\n                    : null;\n            },\n            tools,\n            onFinish: async (params) => {\n                if (onFinish) {\n                    await onFinish({...params, enableValidation});\n                }\n            },\n        });\n    }\n\n    /**\n     * Sanitize response messages by removing incomplete tool calls\n     * @param messages The messages to sanitize\n     * @returns Sanitized messages\n     */\n    public static sanitizeResponseMessages(messages: AIMessage[]): AIMessage[] {\n        return messages.filter(message => {\n            // Filter out messages that are just incomplete tool calls\n            if (message.role === 'assistant' && Array.isArray(message.content)) {\n                const hasOnlyToolCalls = message.content.every(\n                    part => part.type === 'tool-call'\n                );\n\n                if (hasOnlyToolCalls) {\n                    return false;\n                }\n            }\n            return true;\n        });\n    }\n\n    /**\n     * Get the aggregated cost and token usage for all tool calls\n     * @returns Summary of token usage and costs\n     */\n    public getToolCallCostSummary() {\n        if (this.toolCallCosts.length === 0) {\n            return {\n                count: 0,\n                total_tokens_prompt: 0,\n                total_tokens_completion: 0,\n                total_cost: 0,\n                total_cache_discount: 0,\n                net_cost: 0,\n                tool_calls: []\n            };\n        }\n        \n        const total_tokens_prompt = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_prompt || 0), 0);\n        const total_tokens_completion = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_completion || 0), 0);\n        const total_cost = this.toolCallCosts.reduce((sum, call) => sum + (call.total_cost || 0), 0);\n        const total_cache_discount = this.toolCallCosts.reduce((sum, call) => sum + (call.cache_discount || 0), 0);\n        \n        return {\n            count: this.toolCallCosts.length,\n            total_tokens_prompt: round(total_tokens_prompt, 0),\n            total_tokens_completion: round(total_tokens_completion, 0),\n            total_tokens: round(total_tokens_prompt + total_tokens_completion, 0),\n            total_cost: round(total_cost, 6),\n            total_cache_discount: round(total_cache_discount, 6),\n            net_cost: round(total_cost + total_cache_discount, 6),\n            tool_calls: this.toolCallCosts\n        };\n    }\n    \n    /**\n     * Get a simple formatted string representation of the tool call costs\n     * @returns Formatted string with cost summary\n     */\n    public getToolCallCostReport(): string {\n        const summary = this.getToolCallCostSummary();\n        \n        if (summary.count === 0) {\n            return \"No tool calls recorded.\";\n        }\n        \n        return `Tool Call Summary:\n- Total Calls: ${summary.count}\n- Prompt Tokens: ${summary.total_tokens_prompt}\n- Completion Tokens: ${summary.total_tokens_completion}\n- Total Tokens: ${summary.total_tokens}\n- Total Cost: $${summary.total_cost}\n- Cache Discount: $${summary.total_cache_discount}\n- Net Cost: $${summary.net_cost}`;\n    }\n    \n    /**\n     * Create a new instance with updated configuration\n     * @param updatedConfig The updated configuration\n     * @returns A new StreamService instance\n     */\n    public withConfig(updatedConfig: Partial<StreamServiceConfig>): StreamService {\n        return new StreamService({\n            ...this.config,\n            ...updatedConfig,\n        });\n    }\n}", "startLine": 110, "endLine": 382, "type": "unknown", "symbols": ["StreamService"], "score": 1, "context": "This service manages the AI streaming process, including starting the stream, handling chunks, tool calls, errors, and finishing the stream. It is the core of AI response streaming and generation.", "includesImports": false}, {"filePath": "src/lib/ai/index.ts", "content": "import {TextPart, wrapLanguageModel} from 'ai';\nimport {createOpenRouter} from \"@openrouter/ai-sdk-provider\";\nimport {customMiddleware} from './custom-middleware';\nimport fs from \"fs/promises\";\nimport {exportData} from \"@/lib/server-utils\";\n\nconst updateMessageWithCaching = (message:\n                                  {\n                                      role: string,\n                                      content: string | { type: string, text: string, [index: string]: any }[]\n                                  }) => {\n    if (typeof message.content === \"string\") {\n        message.content = [\n            {\n                type: \"text\",\n                text: message.content,\n                cache_control: {\n                    \"type\": \"ephemeral\"\n                }\n            }\n        ]\n    } else {\n        message = {\n            ...message,\n            // @ts-ignore\n            cache_control: {\n                \"type\": \"ephemeral\"\n            }\n        }\n        // @ts-ignore\n        message.content[message.content.length - 1][\"cache_control\"] = {\n            \"type\": \"ephemeral\"\n        }\n    }\n}\n\nconst customFetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {\n    // Log request details\n    // console.log('\\n=== OpenRouter Request ===');\n    // @ts-ignore\n    // console.log('URL:', typeof input === 'string' ? input : input.url);\n    // console.log('Method:', init?.method);\n    // console.log('Headers:', JSON.stringify(init?.headers, null, 2));\n\n    let modifiedInit = {...init};\n\n    if (init?.body) {\n        const bodyObj = JSON.parse(init.body as string);\n\n        bodyObj.parallel_tool_calls = false;\n        if (bodyObj.model.includes(\"claude\") || bodyObj.model.includes(\"gemini\") ) {\n            // Add cache control to system messages\n            if (bodyObj.messages && Array.isArray(bodyObj.messages)) {\n                if (bodyObj.messages[0].role === 'system') {\n                    updateMessageWithCaching(bodyObj.messages[0]);\n                }\n                \n                // For Claude Sonnet 4, merge consecutive assistant messages if the second one has tool calls\n                if (bodyObj.model.includes(\"claude-sonnet-4\")) {\n                    const mergedMessages: any[] = [];\n                    \n                    for (let i = 0; i < bodyObj.messages.length; i++) {\n                        const currentMessage = bodyObj.messages[i];\n                        \n                        // Check if this is an assistant message and the next one is also an assistant with tool calls\n                        if (\n                            currentMessage.role === 'assistant' && \n                            i + 1 < bodyObj.messages.length && \n                            bodyObj.messages[i + 1].role === 'assistant' && \n                            bodyObj.messages[i + 1].tool_calls && \n                            bodyObj.messages[i + 1].tool_calls.length > 0\n                        ) {\n                            // Create a merged message\n                            const nextMessage = bodyObj.messages[i + 1];\n                            \n                            // Handle content merging with proper type handling\n                            let mergedContent;\n                            if (Array.isArray(currentMessage.content) && Array.isArray(nextMessage.content)) {\n                                // Both are arrays, concatenate them with proper type handling\n                                // Use type assertion to help TypeScript understand the structure\n                                const currentContent = currentMessage.content as {type: string, text: string}[];\n                                const nextContent = nextMessage.content as {type: string, text: string}[];\n                                mergedContent = [...currentContent, ...nextContent];\n                            } else if (Array.isArray(currentMessage.content)) {\n                                // Current is array, next is string or other\n                                mergedContent = currentMessage.content as {type: string, text: string}[];\n                            } else if (Array.isArray(nextMessage.content)) {\n                                // Next is array, current is string or other\n                                mergedContent = nextMessage.content as {type: string, text: string}[];\n                            } else {\n                                // Neither is array, use current or fallback to next\n                                mergedContent = currentMessage.content || nextMessage.content;\n                            }\n                            \n                            const mergedMessage = {\n                                ...currentMessage,\n                                tool_calls: nextMessage.tool_calls,\n                                content: mergedContent\n                            };\n                            \n                            mergedMessages.push(mergedMessage);\n                            // Skip the next message since we've merged it\n                            i++;\n                        } else {\n                            mergedMessages.push(currentMessage);\n                        }\n                    }\n                    \n                    bodyObj.messages = mergedMessages;\n                }\n\n                // Find the last system message and mark is cached\n                // const lastSystemMessage = bodyObj.messages.findLast((message: any) => message.role === \"system\");\n                // if (lastSystemMessage) {\n                //     updateMessageWithCaching(lastSystemMessage)\n                // }\n\n                const fileMessage = bodyObj.messages.find((message: any) => {\n                    if (message.role === \"user\") {\n                        return Array.isArray(message.content) ?\n                            message.content.some(\n                                (text: any) => {\n                                    // console.log('text.text', text.text)\n                                    return text.text?.includes(\"<FILE_MESSAGE>\")\n                                }\n                            ) : message.content.includes(\"<FILE_MESSAGE>\")\n                    }\n                    return false;\n                });\n\n                // console.log('Found fileMessage')\n                if (fileMessage) {\n                    updateMessageWithCaching(fileMessage)\n                }\n\n\n                // Find first user message\n                // const firstAssistantResponse = bodyObj.messages.find((message: any) => message.role === \"assistant\");\n                // if (firstAssistantResponse) {\n                //     updateMessageWithCaching(firstAssistantResponse);\n                // }\n                // Detect if we are tool calling loop\n\n\n\n\n                // Find the last message that happened before the current turn\n                // const lastCompleteMessageIndex = bodyObj.messages.findLastIndex((message: any) => {\n                //     return Array.isArray(message.content) && message.content.some((c: any) => c.text.includes(\"Today's date\"))\n                // })\n\n                // bodyObj['provider'] = {\n                //     'order': [\n                //         'Google',\n                //         'Amazon Bedrock',\n                //         'Anthropic',\n                //     ]\n                // };\n\n                bodyObj.messages = bodyObj.messages.map((message: any, index: number) => {\n                    if ([\"user\", \"system\", \"assistant\"].includes(message.role)) {\n                        if(message.tool_calls && message.tool_calls.length > 0) {\n                            message.content = ((message.content?.[0])?.text || message.content);\n                            if(!message.content) {\n                                message.content = ' <hidden></hidden> '\n                            }\n                        } else if (typeof message.content === \"string\") {\n                            if(!bodyObj.model.includes(\"claude-sonnet-4\")) {\n                                message.content = [\n                                    {\n                                        type: \"text\",\n                                        text: message.content || ' <hidden></hidden> '\n                                    }\n                                ]\n                            }\n\n                        }\n                    }\n                    return message;\n                })\n\n                bodyObj.messages = bodyObj.messages.map((message: any, index: number) => {\n                    if(message.role === \"tool\") {\n                        message.content = [\n                            {\n                                type: 'text',\n                                text: (message.content?.[0] as TextPart).text || message.content\n                            }\n                        ]\n                    }\n                    return message;\n                });\n\n                    // this is to ensure we don't get the empty content error\n                bodyObj.messages = bodyObj.messages.map((message: any, index: number) => {\n                    if (Array.isArray(message.content)) {\n                        if(!message.content.length) {\n                            message.content = [\n                                {\n                                    type: 'text'\n                                }\n                            ]\n                        }\n                        // message.content[0].text = 'Empty content';\n                        if(!message.content?.[0].text) {\n                            message.content = 'Empty content';\n                        }\n                    }\n                    return message;\n                })\n\n                const isToolCallingLoop = bodyObj.messages.filter((message: any) => message.role === \"tool\").length > 0;\n\n                if(isToolCallingLoop) {\n                    // We need to prioritize caching these\n                    // find second last tool call\n                    const secondLastToolCall = bodyObj.messages.filter((message: any) => message.role === \"tool\").slice(-2)[0];\n                    if (secondLastToolCall) {\n                        updateMessageWithCaching(secondLastToolCall);\n                    }\n                    const lastToolCallResponse = bodyObj.messages.findLast((message: any) => message.role === \"tool\");\n                    if (lastToolCallResponse) {\n                        updateMessageWithCaching(lastToolCallResponse);\n                    }\n                } else {\n                    // find second last user message\n                    const secondLastUserMessage = bodyObj.messages.filter((message: any) => message.role === \"user\").slice(-2)[0];\n                    if (secondLastUserMessage) {\n                        updateMessageWithCaching(secondLastUserMessage);\n                    }\n                    const lastUserMessage = bodyObj.messages.findLast((message: any) => message.role === \"user\");\n                    if (lastUserMessage) {\n                        updateMessageWithCaching(lastUserMessage);\n                    }\n                }\n\n                // if(lastCompleteMessageIndex !== -1) {\n                //     console.log(`Found message at index ${lastCompleteMessageIndex}`)\n                // Update the previous message as the one with Today's date is the latest user message\n                // updateMessageWithCaching(bodyObj.messages[lastCompleteMessageIndex]);\n                // }\n            }\n\n\n            // Add cache control to tools and function calls\n            if (bodyObj.tools && Array.isArray(bodyObj.tools)) {\n                bodyObj.tools = bodyObj.tools.map((tool: any) => {\n                    // Handle function objects\n                    if (tool.function) {\n                        // tool[\"cache_control\"] = {\n                        //     \"type\": \"ephemeral\"\n                        // };\n                        return {\n                            ...tool,\n                            function: {\n                                ...tool.function,\n                                // \"cache_control\": {\n                                //     \"type\": \"ephemeral\"\n                                // }\n                            },\n                            // \"cache_control\": {\n                            //     \"type\": \"ephemeral\"\n                            // }\n                        };\n                    }\n                    // Handle direct tool objects\n                    return {\n                        ...tool,\n                        // \"cache_control\": {\n                        //     \"type\": \"ephemeral\"\n                        // }\n                    };\n                });\n            }\n        }\n        // Only apply the message truncation logic if not using Claude Sonnet 4\n        // if (!odyObj.model.includes(\"claude-sonnet-4\")) {\n        //     let m: any[] = bodyObj.messages\n        //         // .slice(0, 18);\n        //     m.splice(20, 1)\n        //     bodyObj.messages = [...m]\n        // }\n        // await exportData(bodyObj.messages, 'request-messages')\n\n        modifiedInit.body = JSON.stringify(bodyObj);\n        // console.log('Modified Body:', JSON.stringify(bodyObj));\n\n        // // Request tracker - summarize the request body\n        if (bodyObj.messages && Array.isArray(bodyObj.messages)) {\n            const messageCount = bodyObj.messages.length;\n            const roleCounts: Record<string, number> = {};\n\n            // console.log('\\n=== Request Summary ===');\n            // console.log(`Model: ${bodyObj.model}`);\n            // console.log(`Total Messages: ${messageCount}`);\n\n            let totalTokens = 0;\n\n            // Track message details\n            bodyObj.messages.forEach((message: any, index: number) => {\n                // Count roles\n                roleCounts[message.role] = (roleCounts[message.role] || 0) + 1;\n\n                // Extract content for summary\n                let contentSummary = '';\n                let hasCaching = false;\n\n                if (typeof message.content === 'string') {\n                    contentSummary = message.content.substring(0, 500) + (message.content.length > 500 ? '...' : '');\n                } else if (Array.isArray(message.content)) {\n                    const firstContent = message.content[0];\n                    if (firstContent && firstContent.text) {\n                        contentSummary = firstContent.text.substring(0, 500) + (firstContent.text.length > 500 ? '...' : '');\n                    }\n                    // Check for caching in content array\n                    hasCaching = message.content.some((item: any) => item.cache_control && item.cache_control.type === 'ephemeral');\n                }\n\n                // Check for caching at message level\n                if (message.cache_control && message.cache_control.type === 'ephemeral') {\n                    hasCaching = true;\n                }\n\n                // Estimate token count (very rough estimate: ~4 chars per token)\n                let tokenEstimate = 0;\n                if (typeof message.content === 'string') {\n                    tokenEstimate = Math.ceil(message.content.length / 4);\n                } else if (Array.isArray(message.content)) {\n                    tokenEstimate = Math.ceil(message.content.reduce((sum: number, item: any) => {\n                        return sum + (item.text ? item.text.length : 0);\n                    }, 0) / 4);\n                }\n\n                totalTokens += tokenEstimate;\n                // console.log(`\\n----\\nMessage ${index + 1}:`);\n                // console.log(`  Role: ${message.role}`);\n                // console.log(`  Content: ${contentSummary}`);\n                // console.log(`  Caching: ${hasCaching ? 'Enabled' : 'Not enabled'}`);\n                // console.log(`  Est. Tokens: ~${tokenEstimate}\\n----\\n`);\n\n                // Log tool calls if present\n                if (message.tool_calls && message.tool_calls.length > 0) {\n                    // console.log(`  Tool Calls: ${message.tool_calls.length}`);\n                }\n            });\n\n            // Summary of role distribution\n            // console.log('\\nTotal Tokens:', totalTokens);\n            // console.log('\\nRole Distribution:');\n            // Object.entries(roleCounts).forEach(([role, count]) => {\n            //     console.log(`  ${role}: ${count}`);\n            // });\n\n            // console.log('=== End Request Summary ===\\n');\n        }\n\n\n\n    }\n\n    // Make the actual request\n    const response = await fetch(input, modifiedInit);\n\n    // Clone the response to read it twice\n    // const responseClone = response.clone();\n\n    // Log the entire stream\n    // if (responseClone.body) {\n    //     const reader = responseClone.body.getReader();\n    //     console.log('\\n=== Start of Stream ===');\n    //\n    //     try {\n    //         while (true) {\n    //             const { value, done } = await reader.read();\n    //\n    //             if (done) {\n    //                 console.log('=== End of Stream ===\\n');\n    //                 break;\n    //             }\n    //\n    //             const chunk = new TextDecoder().decode(value);\n    //             // Split by lines to handle SSE format\n    //             const lines = chunk.split('\\n');\n    //             lines.forEach(line => {\n    //                 if (line.trim()) {\n    //                     if (line.startsWith('data: ')) {\n    //                         try {\n    //                             // Try to parse as JSON if possible\n    //                             const jsonData = JSON.parse(line.slice(6));\n    //                             console.log('Parsed JSON:', JSON.stringify(jsonData, null, 2));\n    //                         } catch {\n    //                             // If not JSON, log as is\n    //                             console.log('Raw data:', line.slice(6));\n    //                         }\n    //                     } else {\n    //                         console.log('Non-data line:', line);\n    //                     }\n    //                 }\n    //             });\n    //         }\n    //     } catch (error) {\n    //         console.error('Error reading stream:', error);\n    //     }\n    // }\n\n    return response;\n};\n\nconst openrouter = createOpenRouter({\n    apiKey: process.env.OPENROUTER_API_KEY!,\n    fetch: customFetch,\n    baseURL: \"https://openrouter.helicone.ai/api/v1\",\n    headers: {\n        Authorization: `Bearer ${process.env.OPENROUTER_API_KEY!}`,\n        \"Helicone-Auth\": `Bearer ${process.env.HELICONE_API_KEY!}`,\n        \"X-Title\": `magically-${process.env.NODE_ENV}`,\n        'Helicone-Property-App': `magically-${process.env.NODE_ENV}`\n    },\n    extraBody: {\n        transforms: [\"middle-out\"],\n        providers: {\n            anthropic: {\n                \"cache_control\": {\n                    \"type\": \"ephemeral\"\n                },\n            },\n        },\n        // \"usage\": {\n        //     \"include\": true\n        // },\n        'provider': {\n            'order': [\n                'Google',\n                'Anthropic',\n                'Amazon Bedrock'\n            ]\n        }\n    }\n});\n\nexport const customModel = (apiIdentifier: string) => {\n    return wrapLanguageModel({\n        model: openrouter(apiIdentifier, {\n            parallelToolCalls: true,\n            includeReasoning: true,\n        }),\n        middleware: customMiddleware,\n    });\n};\n", "startLine": 1, "endLine": 450, "type": "unknown", "symbols": ["AI model integration and custom fetch"], "score": 1, "context": "This module integrates the AI model with custom fetch and middleware, handling message formatting and caching, central to AI response generation and streaming.", "includesImports": false}, {"filePath": "src/lib/services/two-stage-context-engine.ts", "content": "export class TwoStageLLMContextEngine {\n  private files: FileItem[] = [];\n  private fileIndex: Map<string, FileItem> = new Map();\n\n  /**\n   * Initialize with project files\n   */\n  constructor(files: FileItem[]) {\n    this.files = files;\n    this.buildIndex();\n  }\n\n  /**\n   * Build an index of files for quick lookup\n   */\n  private buildIndex(): void {\n    for (const file of this.files) {\n      this.fileIndex.set(file.name, file);\n    }\n  }\n\n  /**\n   * Get a minimal structural representation of the codebase\n   * This provides enough context for the first LLM while keeping token usage low\n   */\n  private getCodebaseStructure(): string {\n    // Build a map of imports for quick lookup\n    const importMap = new Map<string, Set<string>>();\n\n    // First pass: extract imports from all files\n    this.files\n      .forEach(file => {\n        const imports = this.extractImports(file.content || \"\");\n        imports.forEach(importPath => {\n          // Resolve import path to actual file name\n          const resolvedPath = this.resolveImportPath(importPath, file.name);\n\n          if (resolvedPath) {\n            // Add to the import map\n            if (!importMap.has(resolvedPath)) {\n              importMap.set(resolvedPath, new Set());\n            }\n            importMap.get(resolvedPath)?.add(file.name);\n          }\n        });\n      });\n\n    // Second pass: create the structure with import relationships\n    return this.files\n      .map(file => {\n        const fileType = this.determineFileType(file.name, file.content || \"\");\n        const exports = this.extractExports(file.content || \"\");\n\n        // Find files that import this file (limited to 3 for brevity)\n        const importedBy = Array.from(importMap.get(file.name) || []).slice(0, 3);\n\n        let result = `${file.name} - ${fileType}`;\n\n        if (exports.length > 0) {\n          result += ` - Exports: ${exports.join(\", \")}`;\n        }\n\n        if (importedBy.length > 0) {\n          result += ` - Used by: ${importedBy.join(\", \")}`;\n        }\n\n        return result;\n      })\n      .join(\"\\n\");\n  }\n\n  /**\n   * First stage: Find relevant files for a query\n   */\n  async findRelevantFiles(query: string, reason: string, excludedFiles: string[] = []): Promise<{files: string[], reasoning: string }> {\n    console.time('find-relevant-files');\n\n    // Get a compact representation of the codebase structure\n    const codebaseStructure = this.getCodebaseStructure();\n\n    // console.log('codebaseStructure', codebaseStructure)\n\n    // Use LLM to identify relevant files\n    const result = await generateObject({\n      model: customModel('openai/gpt-4.1-mini'), // Using a smaller model to reduce costs\n      temperature: 0.1,\n      schema: z.object({\n        files: z.array(z.string()),\n        reasoning: z.string().describe(\"Explanation of why these files were selected\")\n      }),\n      system: `You are a code analysis expert. Your task is to identify which files in a React Native Expo project are most relevant to a specific query.\n\nYou will be given:\n1. A query about the codebase\n2. A structured representation of files in the project with their types and exports\n\nReturn a JSON object with:\n1. An array of the most relevant file paths (maximum 20)\n2. Your reasoning for selecting these files\n̄3. AVOID selecting files that are not referenced anywhere (Dead code) unless relevant to the query or asked for it\n4. Make sure to select files to give a full picture and not just direct relevance, use relevance scores properly\n\n\nChoose files that would be most helpful for understanding or implementing the query.`,\n      prompt: `Query: ${query}\n      \nReason: ${reason}\n\nFiles in the project:\n${codebaseStructure}\n\n${excludedFiles.length > 0 ? `Note: The following files have been excluded from analysis: ${excludedFiles.join(', ')}\\n\\n` : ''}\nReturn the most relevant file paths (maximum 20) and your reasoning:`,\n    });\n\n    console.timeEnd('find-relevant-files');\n    console.log('Relevant files', result.object.files.join(','))\n    console.log(`Selected files reasoning: ${result.object.reasoning}`);\n\n    // Filter out any files that don't exist in our index\n    return {files: result.object.files.filter(file => this.fileIndex.has(file)), reasoning: result.object.reasoning};\n  }\n\n  /**\n   * Second stage: Identify relevant snippets within files\n   */\n  async identifyRelevantSnippets(query: string, originalReason: string, relevantFiles: string[], previousStageReason: string): Promise<SnippetIdentification[]> {\n    console.time('identify-snippets');\n\n\n    // Prepare file contents with line numbers\n    const fileContents = relevantFiles.map(fileName => {\n      const file = this.fileIndex.get(fileName);\n      const content = file?.content || \"\";\n\n      // Add line numbers to help the LLM identify specific ranges\n      // Format with consistent padding to make line numbers stand out\n      const lines = content.split(\"\\n\");\n      const maxLineNumberWidth = String(lines.length).length;\n      const numberedContent = lines.map((line, i) => {\n        const lineNumber = i + 1;\n        const paddedLineNumber = String(lineNumber).padStart(maxLineNumberWidth, ' ');\n        return `${paddedLineNumber}: ${line}`;\n      }).join(\"\\n\");\n\n      return {\n        name: fileName,\n        content: numberedContent,\n        lineCount: lines.length\n      };\n    });\n\n    // Use LLM to identify relevant snippets within these files\n    const result = await generateObject({\n      model: customModel('openai/gpt-4.1-mini'), // Using a more capable model for this task\n      temperature: 0.1,\n      schema: z.object({\n        snippets: z.array(z.object({\n          fileName: z.string(),\n          startLine: z.number(),\n          endLine: z.number(),\n          snippetType: z.string(),\n          snippetName: z.string(),\n          relevanceScore: z.number().min(0).max(1),\n          reasoning: z.string()\n        }))\n      }),\n      system: `You are a code analysis expert. Your task is to identify the MINIMAL relevant code snippets within files for a specific query.\n\nReturn the exact line numbers for each snippet, along with metadata about the snippet.\n\nGuidelines for MINIMAL context extraction:\n1. Extract ONLY the specific lines directly relevant to the query - avoid including entire files or anything extra at all\n2. For imports, include ONLY the imports directly related to the query and is needed to reliable editing\n2. For style, include ONLY the styles directly related to the query and is needed to reliable editing\n3. For components, include ONLY the component declaration, props, and relevant JSX elements - NOT the entire implementation\n4. For functions, include ONLY the signature and critical logic - NOT entire function bodies\n5. Assign a relevance score from 0.0 to 1.0 based on how directly the snippet addresses the query. \n6. Keep snippets as SHORT as possible while maintaining necessary context\n7. Pay close attention to the line numbers at the beginning of each line (formatted as \"NUMBER: code\")\n8. For React errors, focus on component declarations, imports/exports, and JSX return statements\n9. NEVER include style definitions unless they are directly relevant to the query\n10. NEVER include helper functions unless they are directly relevant to the query\n11. When specific line numbers are requested, return only those line numbers\n\nToken efficiency guidelines:\n1. Maximum 30 lines per snippet unless absolutely necessary\n4. Omit implementation details of methods unless directly relevant\n5. For UI issues, include only the relevant JSX elements, not entire render methods\n6. When multiple similar components exist, include only one representative example\\``,\n      prompt: `Query: ${query}\n\nI need to identify the MINIMAL relevant code snippets in these files. For each relevant code block, provide:\n1. The file name\n2. Start and end line numbers (inclusive) - use the exact line numbers shown at the beginning of each line\n3. Type of snippet (function, component, hook, type, etc.)\n4. Name of the snippet (function name, component name, etc.)\n5. Relevance score (0.0 to 1.0)\n6. Brief reasoning for why this snippet is relevant\n\nReliable editing:\n- Please include import and styles if they need are needed to add imports\n- Include just enough context for clear understanding and editing \n\nCRITICAL TOKEN EFFICIENCY GUIDELINES:\n- Extract ONLY the specific lines directly relevant to the query\n- Each line in the files is prefixed with its line number (e.g., \"42: const foo = bar;\"). Use these exact line numbers.\n- For imports, include ONLY those directly related to the query\n- For components, include ONLY the declaration, props, and relevant JSX - NOT entire implementations\n- NEVER include style definitions unless directly relevant to the query\n- Keep snippets to a MAXIMUM of 30 lines when possible\n- For React errors, focus ONLY on component declarations, imports/exports, and JSX return statements\n- AVOID including entire component implementations - be extremely selective\n\nReasoning from the user/llm issuing the original query on why they want the result. This gets fed to the first engine:\n${originalReason}\n\nReasoning from the first engine on why each file was selected. Use the reasoning to understand the whole flow and to select snippets accordingly:\n${previousStageReason}\n\nFiles to analyze:\n${fileContents.map(file => `\n=== ${file.name} (${file.lineCount} lines) ===\n${file.content}\n`).join(\"\\n\\n\")}\n\nReturn an array of the most relevant code snippets with their exact line numbers and metadata:`,\n    });\n\n    console.timeEnd('identify-snippets');\n    return result.object.snippets;\n  }\n\n  /**\n   * Smart truncation to fit within line budget while preserving understanding context\n   */\n  smartTruncateSnippets(snippets: CodeSnippet[], maxLines: number = 600): {truncatedSnippets: CodeSnippet[], additionalFromTruncation: {fileName: string, reason: string, suggestedQuery: string}[]} {\n    // Categorize snippets by type for strategic selection\n    const implementation = snippets.filter(s => (s.score || 0) >= 0.9);\n    const usage = snippets.filter(s => (s.score || 0) >= 0.7 && (s.score || 0) < 0.9);\n    const context = snippets.filter(s => (s.score || 0) < 0.7);\n\n    let currentLines = 0;\n    const truncatedSnippets: CodeSnippet[] = [];\n    const additionalFromTruncation: {fileName: string, reason: string, suggestedQuery: string}[] = [];\n\n    // Strategy: Always include implementation, then usage, then context within budget\n    const prioritizedSnippets = [\n      ...orderBy(implementation, ['score'], ['desc']).slice(0, 2), // Max 2 implementation snippets\n      ...orderBy(usage, ['score'], ['desc']).slice(0, 2), // Max 2 usage examples\n      ...orderBy(context, ['score'], ['desc']).slice(0, 1)  // Max 1 context snippet\n    ];\n\n    for (const snippet of prioritizedSnippets) {\n      const snippetLines = snippet.content.split('\\n').length;\n\n      if (currentLines + snippetLines <= maxLines) {\n        truncatedSnippets.push(snippet);\n        currentLines += snippetLines;\n      } else {\n        // Add to additional files instead of truncating content\n        additionalFromTruncation.push({\n          fileName: snippet.filePath,\n          reason: `Relevant but truncated due to 1000-line budget (relevance: ${(snippet.score || 0).toFixed(2)}, ${snippetLines} lines)`,\n          suggestedQuery: `Show ${snippet.symbols.join(', ')} in ${snippet.filePath}`\n        });\n      }\n    }\n\n    // Add any remaining snippets to additional files\n    const remainingSnippets = snippets.filter(s => !truncatedSnippets.includes(s) && !additionalFromTruncation.some(a => a.fileName === s.filePath));\n    for (const snippet of remainingSnippets) {\n      additionalFromTruncation.push({\n        fileName: snippet.filePath,\n        reason: `Additional context available (relevance: ${(snippet.score || 0).toFixed(2)})`,\n        suggestedQuery: `Show ${snippet.symbols.join(', ')} in ${snippet.filePath}`\n      });\n    }\n\n    console.log(`Smart truncation: kept ${truncatedSnippets.length}/${snippets.length} snippets within ${maxLines} line budget (${currentLines} lines total)`);\n    console.log(`  - Implementation: ${implementation.length} found, ${truncatedSnippets.filter(s => (s.score || 0) >= 0.9).length} kept`);\n    console.log(`  - Usage: ${usage.length} found, ${truncatedSnippets.filter(s => (s.score || 0) >= 0.7 && (s.score || 0) < 0.9).length} kept`);\n    console.log(`  - Context: ${context.length} found, ${truncatedSnippets.filter(s => (s.score || 0) < 0.7).length} kept`);\n\n    return {truncatedSnippets, additionalFromTruncation};\n  }\n\n  /**\n   * Extract actual code snippets based on line numbers\n   */\n  extractSnippets(snippetIdentifications: SnippetIdentification[]): CodeSnippet[] {\n    // Group snippets by file to avoid duplicate processing\n    const snippetsByFile = new Map<string, SnippetIdentification[]>();\n\n    for (const snippet of snippetIdentifications) {\n      if (!snippetsByFile.has(snippet.fileName)) {\n        snippetsByFile.set(snippet.fileName, []);\n      }\n      snippetsByFile.get(snippet.fileName)?.push(snippet);\n    }\n\n    const results: CodeSnippet[] = [];\n\n    // Process each file's snippets\n    for (const [fileName, fileSnippets] of snippetsByFile.entries()) {\n      const file = this.fileIndex.get(fileName);\n      if (!file || !file.content) continue;\n\n      const lines = file.content.split(\"\\n\");\n\n      // Find import statements (usually at the top of the file)\n      const importEndLine = this.findImportEndLine(lines);\n      const hasImports = importEndLine > 0;\n\n      // Process each snippet in the file\n      for (const identification of fileSnippets) {\n        // Ensure line numbers are within bounds\n        const startLine = Math.max(1, Math.min(identification.startLine, lines.length));\n        const endLine = Math.max(startLine, Math.min(identification.endLine, lines.length));\n\n        // Removing as this is causing the llm issues to understand\n        const shouldIncludeImports = false;\n\n        // Determine if we should include imports\n        // const shouldIncludeImports = hasImports &&\n        //     identification.snippetType.toLowerCase() !== 'import' &&\n        //     startLine > importEndLine;\n\n        // Extract the snippet content with imports if needed\n        let snippetLines: string[];\n        let actualStartLine: number;\n\n        if (shouldIncludeImports) {\n          // Include imports and the actual snippet\n          const importLines = lines.slice(0, importEndLine);\n          const codeLines = lines.slice(startLine - 1, endLine);\n\n          // Add a separator between imports and code\n          snippetLines = [...importLines, '', '// Main snippet', ...codeLines];\n          actualStartLine = 1; // Starting from the beginning of the file\n        } else {\n          // Just include the snippet itself\n          snippetLines = lines.slice(startLine - 1, endLine);\n          actualStartLine = startLine;\n        }\n\n        const content = snippetLines.join(\"\\n\");\n\n        // Log the extraction for debugging\n        console.log(`Extracting snippet from ${identification.fileName}:`);\n        console.log(`  Lines ${startLine}-${endLine} (${snippetLines.length} lines)`);\n        console.log(`  Type: ${identification.snippetType}, Name: ${identification.snippetName}`);\n        console.log(`  Relevance: ${identification.relevanceScore.toFixed(2)}`);\n        if (shouldIncludeImports) {\n          console.log(`  Including imports from lines 1-${importEndLine}`);\n        }\n\n        results.push({\n          filePath: identification.fileName,\n          content,\n          startLine: actualStartLine,\n          endLine: shouldIncludeImports ? endLine + (importEndLine + 2) : endLine, // Adjust for added separator lines\n          type: this.mapSnippetType(identification.snippetType),\n          symbols: [identification.snippetName],\n          score: identification.relevanceScore,\n          context: identification.reasoning,\n          includesImports: shouldIncludeImports\n        } as CodeSnippet);\n      }\n    }\n\n    console.log('Total lines', results.reduce((acc, a) => {\n      return acc + ((a.endLine - a.startLine)) + 1;\n    }, 0));\n\n    return orderBy(results, ['score'], ['desc']);\n  }\n\n  /**\n   * Find the line where imports end in a file\n   */\n  private findImportEndLine(lines: string[]): number {\n    let lastImportLine = 0;\n\n    for (let i = 0; i < Math.min(lines.length, 50); i++) { // Only check first 50 lines\n      const line = lines[i].trim();\n      if (line.startsWith('import ')) {\n        lastImportLine = i + 1; // +1 because line numbers are 1-based\n      }\n    }\n\n    return lastImportLine;\n  }\n\n  /**\n   * Main method to get relevant snippets for a query\n   */\n  async getRelevantSnippets(query: string, reason: string, excludedFiles: string[] = []): Promise<CodeSnippet[]> {\n    // Stage 1: Find relevant files\n    const {files: relevantFiles, reasoning} = await this.findRelevantFiles(query, reason, excludedFiles);\n\n    if (relevantFiles.length === 0) {\n      console.log(\"No relevant files found\");\n      return [];\n    }\n\n    // Stage 2: Identify relevant snippets within those files\n    const snippetIdentifications = await this.identifyRelevantSnippets(query, reason, relevantFiles, reasoning);\n\n    // Stage 3: Extract the actual snippets\n    return this.extractSnippets(snippetIdentifications);\n  }\n\n  /**\n   * Helper methods\n   */\n  private isCodeFile(fileName: string): boolean {\n    const ext = path.extname(fileName).toLowerCase();\n    return ['.ts', '.tsx', '.js', '.jsx'].includes(ext);\n  }\n\n  private determineFileType(fileName: string, content: string): string {\n    const name = fileName.toLowerCase();\n\n    if (name.includes('screen') || name.includes('page')) {\n      return 'screen';\n    }\n\n    if (name.includes('context')) {\n      return 'context';\n    }\n\n    if (name.includes('hook') || (content && content.includes('use') && content.includes('return {'))) {\n      return 'hook';\n    }\n\n    if (name.includes('util') || name.includes('helper')) {\n      return 'util';\n    }\n\n    if (name.includes('type') || name.includes('interface') || name.includes('.d.ts')) {\n      return 'type';\n    }\n\n    if (name.includes('config') || name.includes('setup')) {\n      return 'config';\n    }\n\n    // Default to component for TSX/JSX files\n    if (name.endsWith('.tsx') || name.endsWith('.jsx')) {\n      return 'component';\n    }\n\n    return 'unknown';\n  }\n\n  private extractExports(content: string): string[] {\n    const exports: string[] = [];\n    const exportRegex = /export\\s+(const|function|class|interface|type|default)\\s+(\\w+)/g;\n\n    let match;\n    while ((match = exportRegex.exec(content)) !== null) {\n      exports.push(match[2]);\n    }\n\n    return exports;\n  }\n\n  private extractImports(content: string): string[] {\n    const imports: string[] = [];\n    // Fixed regex to handle all import patterns including relative paths, scoped packages, and complex paths\n    const importRegex = /import\\s+(?:{[^}]*}|\\*\\s+as\\s+\\w+|\\w+)\\s+from\\s+['\"]([^'\"]+)['\"];?/g;\n\n    let match;\n    while ((match = importRegex.exec(content)) !== null) {\n      const importPath = match[1];\n      imports.push(importPath);\n    }\n\n    return imports;\n  }\n\n  /**\n   * Resolve import path to actual file name in the project\n   */\n  private resolveImportPath(importPath: string, currentFile: string): string | null {\n    // Skip node_modules and external packages (anything that doesn't start with ./ or ../)\n    if (!importPath.startsWith('./') && !importPath.startsWith('../')) {\n      return null;\n    }\n\n    // Handle relative paths only\n    const currentDir = path.dirname(currentFile);\n    let resolvedPath = path.join(currentDir, importPath);\n    // Normalize path separators and remove leading ./\n    resolvedPath = resolvedPath.replace(/\\\\/g, '/').replace(/^\\.\\//, '');\n\n    // Try to find the actual file with common extensions\n    const possibleExtensions = ['.ts', '.tsx', '.js', '.jsx', '/index.ts', '/index.tsx', '/index.js', '/index.jsx'];\n\n    for (const ext of possibleExtensions) {\n      const candidatePath = resolvedPath + ext;\n      if (this.fileIndex.has(candidatePath)) {\n        return candidatePath;\n      }\n    }\n\n    // If no extension worked, try exact match\n    if (this.fileIndex.has(resolvedPath)) {\n      return resolvedPath;\n    }\n\n    return null;\n  }\n\n  private mapSnippetType(type: string): 'component' | 'hook' | 'screen' | 'util' | 'type' | 'context' | 'config' | 'unknown' {\n    const normalizedType = type.toLowerCase();\n\n    if (normalizedType.includes('component')) return 'component';\n    if (normalizedType.includes('hook')) return 'hook';\n    if (normalizedType.includes('screen')) return 'screen';\n    if (normalizedType.includes('util') || normalizedType.includes('function')) return 'util';\n    if (normalizedType.includes('type') || normalizedType.includes('interface')) return 'type';\n    if (normalizedType.includes('context') || normalizedType.includes('provider')) return 'context';\n    if (normalizedType.includes('config')) return 'config';\n\n    return 'unknown';\n  }\n}\n", "startLine": 27, "endLine": 556, "type": "unknown", "symbols": ["TwoStageLLMContextEngine"], "score": 0.9, "context": "This class implements a two-stage LLM approach to identify relevant files and snippets for a query, critical for understanding how AI selects context for response generation.", "includesImports": false}, {"filePath": "src/lib/services/context-engine.ts", "content": "export class ContextEngine {\n    private files: FileItem[] = [];\n    private fileIndex: Map<string, FileItem> = new Map();\n    private dependencyGraph: Map<string, Set<string>> = new Map();\n    private fileMetadata: Map<string, FileMetadata> = new Map();\n    private codebaseEmbedding: any = null;\n    private supabaseSchema: SupabaseSchema | null = null;\n    private project: Project | null = null;\n    private actionPlan: z.infer<typeof ActionPlanSchema> | null = null;\n    private snippetCache: Map<string, CodeSnippet[]> = new Map(); // Cache for extracted snippets\n    private astCache: Map<string, any> = new Map(); // Cache for AST data\n\n    /**\n     * Initialize the context engine with the project files\n     * @param files Array of file items from the project\n     * @param project Optional project information for Supabase integration\n     */\n    constructor(files: FileItem[], project?: Project) {\n        this.files = files;\n        this.project = project || null;\n        this.buildIndex();\n        this.analyzeDependencies();\n        this.extractMetadata();\n    }\n\n    /**\n     * Build an index of files for quick lookup\n     */\n    private buildIndex(): void {\n        for (const file of this.files) {\n            this.fileIndex.set(file.name, file);\n        }\n    }\n\n    /**\n     * Check if a file should be excluded based on the excludedFiles patterns\n     * @param filePath Path of the file to check\n     * @param excludedFiles Array of file paths to exclude\n     * @returns True if the file should be excluded\n     */\n    private shouldExcludeFile(filePath: string, excludedFiles: string[]): boolean {\n        if (!excludedFiles || excludedFiles.length === 0) {\n            return false;\n        }\n\n        // Normalize the file path (remove leading slashes, etc.)\n        const normalizedPath = filePath.replace(/^\\/+/, '');\n        const fileName = path.basename(normalizedPath);\n\n        for (const pattern of excludedFiles) {\n            // Normalize the pattern\n            const normalizedPattern = pattern.replace(/^\\/+/, '');\n\n            // Case 1: Exact match (absolute path)\n            if (normalizedPath === normalizedPattern) {\n                return true;\n            }\n\n            // Case 2: Filename match (for unique filenames)\n            if (fileName === path.basename(normalizedPattern)) {\n                return true;\n            }\n\n            // Case 3: Pattern is contained in the path\n            if (normalizedPath.includes(normalizedPattern)) {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Analyze dependencies between files\n     * Builds a graph of which files import/depend on other files\n     */\n    private analyzeDependencies(): void {\n        const importRegex = /import\\s+(?:{[^}]*}|\\*\\s+as\\s+\\w+|\\w+)\\s+from\\s+['\"]([^'\"]+)['\"]/g;\n\n        for (const file of this.files) {\n            if (!file.content || !isCodeFile(file.name)) continue;\n\n            const dependencies = new Set<string>();\n            this.dependencyGraph.set(file.name, dependencies);\n\n            let match;\n            while ((match = importRegex.exec(file.content)) !== null) {\n                const importPath = match[1];\n\n                // Handle relative imports\n                if (importPath.startsWith('.')) {\n                    const resolvedPath = resolveRelativePath(file.name, importPath);\n                    if (this.fileIndex.has(resolvedPath)) {\n                        dependencies.add(resolvedPath);\n                    }\n                }\n\n                // Handle absolute imports (e.g., @/components/...)\n                if (importPath.startsWith('@/')) {\n                    const absolutePath = importPath.replace('@/', '');\n                    const possibleFiles = this.files\n                        .map(f => f.name)\n                        .filter(name => name.includes(absolutePath));\n\n                    if (possibleFiles.length > 0) {\n                        dependencies.add(possibleFiles[0]);\n                    }\n                }\n            }\n        }\n    }\n\n    /**\n     * Extract metadata from files (components, hooks, contexts, etc.)\n     */\n    private extractMetadata(): void {\n        for (const file of this.files) {\n            if (!file.content || !isCodeFile(file.name)) continue;\n\n            const metadata: FileMetadata = {\n                type: determineFileType(file.name, file.content),\n                exports: extractExports(file.content),\n                imports: extractImports(file.content),\n                lineCount: file.content.split('\\n').length,\n                hasJSX: file.content.includes('return (') || file.content.includes('return <'),\n                hasHooks: file.content.includes('useState') || file.content.includes('useEffect'),\n                hasContext: file.content.includes('createContext'),\n                hasStyles: file.content.includes('StyleSheet.create'),\n            };\n\n            this.fileMetadata.set(file.name, metadata);\n        }\n    }\n\n    /**\n     * Query the context engine for information about the codebase\n     * @param query Natural language query about the codebase\n     * @param reason\n     * @param excludedFiles Optional array of file paths to exclude from analysis\n     * @returns Comprehensive information about the relevant parts of the codebase\n     */\n    async query(query: string, reason: string, excludedFiles?: string[]): Promise<ContextResult> {\n        console.time('context-engine-query');\n        console.log(`Context engine query started for: \"${query}\"`, `Excluded file: ${(excludedFiles || []).join(',')}`);\n\n        // First, determine which files are most relevant to the query\n        // console.time('find-relevant-files');\n        // const relevantFiles = await this.findRelevantFiles(query, excludedFiles || []);\n        // console.timeEnd('find-relevant-files');\n\n        // Start parallel operations\n        const parallelOperations: any[] = [];\n\n        // 1. Process file contents\n        // const fileContentsPromise = Promise.resolve().then(() => {\n        //     console.time('process-file-contents');\n        //     // Get the content and metadata for these files\n        //     const result = relevantFiles.map(fileName => {\n        //         const file = this.fileIndex.get(fileName);\n        //         const metadata = this.fileMetadata.get(fileName);\n        //\n        //         return {\n        //             name: fileName,\n        //             content: file?.content || '',\n        //             // metadata: metadata || {type: 'unknown'},\n        //             // dependencies: Array.from(this.dependencyGraph.get(fileName) || []),\n        //             // dependents: this.findDependents(fileName),\n        //         };\n        //     });\n        //     console.timeEnd('process-file-contents');\n        //     return result;\n        // });\n        // parallelOperations.push(fileContentsPromise);\n\n        // 2. Get the overall codebase structure\n        // const codebaseStructurePromise = Promise.resolve().then(() => {\n        //     console.time('get-codebase-structure');\n        //     const structure = this.getCodebaseStructure();\n        //     console.timeEnd('get-codebase-structure');\n        //     return structure;\n        // });\n        // parallelOperations.push(codebaseStructurePromise);\n\n        // 3. Get file relationships (prepare the promise but don't execute yet)\n        // const relationshipsPromise = Promise.resolve().then(() => {\n        //     console.time('get-file-relationships');\n        //     const relationships = this.getFileRelationships(relevantFiles);\n        //     console.timeEnd('get-file-relationships');\n        //     return relationships;\n        // });\n        // parallelOperations.push(relationshipsPromise);\n\n        // 4. Extract code snippets using the two-stage approach\n        const snippetsPromise = Promise.resolve().then(async () => {\n            console.time('extract-code-snippets');\n\n            // Check if we have cached snippets for this query\n            const cachedSnippets = this.snippetCache.get(query);\n            if (cachedSnippets) {\n                console.log('Using cached snippets for query');\n                console.timeEnd('extract-code-snippets');\n                return cachedSnippets;\n            }\n\n            try {\n                // Use the TwoStageLLMContextEngine for better snippet extraction\n                const twoStageEngine = new TwoStageLLMContextEngine(this.files);\n                const snippets = await twoStageEngine.getRelevantSnippets(query, reason, excludedFiles || []);\n\n                if (snippets && snippets.length > 0) {\n                    // Cache the snippets for future use\n                    this.snippetCache.set(query, snippets);\n                    console.log(`Found ${snippets.length} snippets using two-stage approach`);\n                    console.timeEnd('extract-code-snippets');\n                    return snippets;\n                }\n            } catch (error) {\n                console.error('Error using TwoStageLLMContextEngine for snippets:', error);\n                // Fall through to the original implementation if the new approach fails\n            }\n\n            // Fallback to original regex-based extraction\n            // console.log('Falling back to regex-based snippet extraction');\n            // const allSnippets: CodeSnippet[] = [];\n            // for (const fileName of relevantFiles) {\n            //     const fileSnippets = this.extractCodeSnippets(fileName, query);\n            //     allSnippets.push(...fileSnippets);\n            // }\n\n            // Sort snippets by score and take top 10\n            // const topSnippets = allSnippets\n            //     .sort((a, b) => (b.score || 0) - (a.score || 0))\n            //     .slice(0, 10);\n\n            // Cache the snippets for future use\n            // this.snippetCache.set(query, topSnippets);\n\n            // console.timeEnd('extract-code-snippets');\n            // return topSnippets;\n        });\n        parallelOperations.push(snippetsPromise);\n\n        // Wait for all parallel operations to complete\n        console.time('parallel-operations');\n        const [snippets] = await Promise.all(parallelOperations);\n        console.timeEnd('parallel-operations');\n\n        // Performance metrics for debugging\n        console.timeEnd('context-engine-query');\n        console.log(`Context engine query completed for: \"${query}\"`);\n        // console.log(`- Found ${fileContents?.length} relevant files`);\n        console.log(`- Found ${snippets?.length} relevant code snippets`);\n\n        return {\n            query,\n            // relevantFiles: fileContents,\n            snippets,\n            // codebaseStructure,\n            // relationships,\n            // supabaseSchema,\n            // actionPlan,\n        } as ContextResult;\n    }\n\n    /**\n     * Get Supabase schema information if available\n     * @returns Supabase schema information\n     */\n    async getSupabaseSchema(): Promise<SupabaseSchema | null> {\n        if (!this.project || !this.project.supabaseProjectId) {\n            return null;\n        }\n\n        // If we already have the schema cached, return it\n        if (this.supabaseSchema) {\n            return this.supabaseSchema;\n        }\n\n        try {\n            const supabaseProvider = new SupabaseIntegrationProvider();\n            const result = await supabaseProvider.getLatestInstructionsForChat({project: this.project});\n\n            const {schema, functions, secrets, dbFunctions, triggers, rlsPolicies, storageBuckets} = result as any;\n            // console.log('result', result)\n            // Parse the instructions to extract schema information\n\n\n            // Create a structured representation of the schema\n            const parsedSchema: SupabaseSchema = {\n                tables: schema,\n                functions,\n                secrets,\n                dbFunctions,\n                triggers,\n                rlsPolicies,\n                storageBuckets\n            };\n\n            console.log('parsedSchema', parsedSchema)\n\n            this.supabaseSchema = parsedSchema;\n            return parsedSchema;\n        } catch (error) {\n            console.error('Error fetching Supabase schema:', error);\n            return null;\n        }\n    }\n\n    /**\n     * Generate an action plan based on the query and context\n     * @param query The user's query\n     * @param relevantFiles The relevant files for the query\n     * @param supabaseSchema The Supabase schema if available\n     * @returns An action plan with steps to implement the requested changes\n     */\n    async generateActionPlan(query: string, relevantFiles: any[], supabaseSchema: SupabaseSchema | null): Promise<z.infer<typeof ActionPlanSchema>> {\n        try {\n            // Define the schema for the action plan\n\n            // Prepare context for the AI\n            const fileContext = relevantFiles.map(file => {\n                return `File: ${file.name}\\nType: ${file.metadata.type}\\nExports: ${file.metadata.exports?.join(', ') || 'None'}\\nImports: ${file.metadata.imports?.slice(0, 5)?.join(', ') || 'None'}${file.metadata.imports?.length > 5 ? '...' : ''}\\n`;\n            }).join('\\n');\n\n            // Prepare Supabase context if available\n            let supabaseContext = '';\n            if (supabaseSchema) {\n                // Add tables information\n                if (supabaseSchema.tables && supabaseSchema.tables.length > 0) {\n                    supabaseContext += 'Supabase Tables:\\n' +\n                        supabaseSchema.tables.map(table => {\n                            return `Table: ${table.table_name}\\nColumns: ${table.columns.map((col: any) =>\n                                `${col.column_name} (${col.data_type})`).join(', ')}\\n`;\n                        }).join('\\n');\n                }\n\n                // Add Edge Functions information\n                if (supabaseSchema.functions && supabaseSchema.functions.length > 0) {\n                    supabaseContext += '\\nSupabase Edge Functions:\\n' +\n                        supabaseSchema.functions.map((func: any) =>\n                            `- ${func.name || func.slug || 'Unknown function'}${func.entrypoint ? ` (Entrypoint: ${func.entrypoint})` : ''}`\n                        ).join('\\n');\n                }\n\n                // Add Database Functions information\n                if (supabaseSchema.dbFunctions && supabaseSchema.dbFunctions.length > 0) {\n                    supabaseContext += '\\nSupabase Database Functions:\\n' +\n                        supabaseSchema.dbFunctions.map((func: any) =>\n                            `- ${func.name || 'Unknown function'}${func.schema ? ` (Schema: ${func.schema})` : ''}`\n                        ).join('\\n');\n                }\n\n                // Add RLS Policies information\n                if (supabaseSchema.rlsPolicies && supabaseSchema.rlsPolicies.length > 0) {\n                    supabaseContext += '\\nSupabase RLS Policies:\\n' +\n                        supabaseSchema.rlsPolicies.map((policy: any) =>\n                            `- ${policy.name || 'Unknown policy'} on ${policy.table || 'Unknown table'} (${policy.action || 'Unknown action'})`\n                        ).join('\\n');\n                }\n\n                // Add Triggers information\n                if (supabaseSchema.triggers && supabaseSchema.triggers.length > 0) {\n                    supabaseContext += '\\nSupabase Triggers:\\n' +\n                        supabaseSchema.triggers.map((trigger: any) =>\n                            `- ${trigger.name || 'Unknown trigger'} on ${trigger.table || 'Unknown table'}`\n                        ).join('\\n');\n                }\n\n                // Add Storage Buckets information\n                if (supabaseSchema.storageBuckets && supabaseSchema.storageBuckets.length > 0) {\n                    supabaseContext += '\\nSupabase Storage Buckets:\\n' +\n                        supabaseSchema.storageBuckets.map((bucket: any) =>\n                            `- ${bucket.name || 'Unknown bucket'} (Public: ${bucket.public ? 'Yes' : 'No'})${bucket.file_size_limit ? ` (Size Limit: ${bucket.file_size_limit} bytes)` : ''}`\n                        ).join('\\n');\n                }\n            }\n\n            // Generate the action plan using AI\n            const result = await generateObject({\n                model: customModel('openai/gpt-4.1'),\n                temperature: 0.1,\n                schema: ActionPlanSchema,\n                system: `You are an expert software architect and developer specializing in React Native Expo applications with Supabase integration.\n      Your task is to create a detailed action plan for implementing the requested changes based on the provided codebase context.\n      Focus on creating a practical, step-by-step plan that addresses all aspects of the request.\n      Be specific about which files need to be changed and why.\n\n      If Supabase integration is involved:\n      1. Consider all available Supabase resources (tables, edge functions, database functions, RLS policies, triggers, storage buckets)\n      2. Recommend appropriate changes to database schema when needed\n      3. Suggest Edge Functions for complex server-side operations\n      4. Include necessary RLS policy updates for proper security\n      5. Utilize Storage buckets for file uploads when appropriate\n      6. Consider database triggers for automated operations\n\n      Provide a comprehensive plan that leverages all available resources efficiently.`,\n                prompt: `Based on the following query and codebase context, create a detailed action plan for implementing the requested changes.\n\nQuery: ${query}\n\nCodebase Context:\n${fileContext}\n\n${supabaseContext ? supabaseContext : 'No Supabase integration detected.'}\n\nPlease provide a comprehensive action plan with specific steps, file changes, and considerations.`\n            });\n\n            return result.object;\n        } catch (error) {\n            console.error('Error generating action plan:', error);\n            // Return a basic action plan if generation fails\n            return {\n                summary: `Implement changes for: ${query}`,\n                complexity: \"moderate\",\n                steps: [{\n                    description: \"Analyze and implement the requested changes\",\n                    fileChanges: relevantFiles.slice(0, 3).map(file => ({\n                        path: file.name,\n                        action: \"modify\",\n                        purpose: \"Implement requested changes\",\n                        priority: \"high\"\n                    }))\n                }],\n                considerations: [\"Consider the existing codebase structure\", \"Ensure compatibility with current implementation\"],\n                supabaseChanges: supabaseSchema ? [\n                    {\n                        type: \"table\",\n                        description: \"May need database schema changes to support the new feature\",\n                        priority: \"medium\"\n                    },\n                    supabaseSchema.functions?.length > 0 ? {\n                        type: \"edge_function\",\n                        description: \"May need to update Edge Functions to support the new feature\",\n                        priority: \"medium\"\n                    } : null,\n                    supabaseSchema.storageBuckets?.length > 0 ? {\n                        type: \"storage_bucket\",\n                        description: \"May need to configure Storage for file uploads\",\n                        priority: \"medium\"\n                    } : null,\n                    supabaseSchema.rlsPolicies?.length > 0 ? {\n                        type: \"policy\",\n                        description: \"May need to update RLS policies for proper access control\",\n                        priority: \"high\"\n                    } : null\n                ].filter(Boolean) as any : []\n            };\n        }\n    }\n\n\n    /**\n     * Find files that are relevant to the given query\n     * @param query Natural language query\n     * @param excludedFiles Array of file paths to exclude from analysis\n     * @returns Array of file names that are relevant to the query\n     */\n    private async findRelevantFiles(query: string, excludedFiles: string[]): Promise<string[]> {\n        // Get the list of files that are not excluded\n        const availableFiles = this.files.filter(file => !this.shouldExcludeFile(file.name, excludedFiles));\n\n        // Use AI to determine which files are most relevant to the query\n        const result = await generateObject({\n            model: customModel('openai/gpt-4.1'),\n            temperature: 0.1,\n            schema: z.object({files: z.array(z.string())}),\n            system: `You are a code analysis expert. Your task is to identify which files in a React Native Expo project are most relevant to a specific query.\n\nYou will be given:\n1. A query about the codebase\n2. A list of files in the project with their types\n\nReturn ONLY a JSON array of the most relevant file paths, with a maximum of 5 files. Choose files that would be most helpful for understanding or implementing the query.`,\n            prompt: `Query: ${query}\n\nFiles in the project:\n${availableFiles.map(file => `${file.name} - ${this.fileMetadata.get(file.name)?.type || 'unknown'}`).join('\\n')}\n\n${excludedFiles.length > 0 ? `Note: The following files have been excluded from analysis: ${excludedFiles.join(', ')}\\n\\n` : ''}\nReturn ONLY a JSON array of the most relevant file paths (maximum 5):`,\n        });\n\n        try {\n            // console.log('result', result.object)\n            // Parse the result as a JSON array\n            const fileList = result.object.files;\n            if (Array.isArray(fileList) && fileList.length > 0) {\n                return fileList.filter(file => this.fileIndex.has(file));\n            }\n        } catch (error) {\n            console.error('Error parsing relevant files:', error);\n        }\n\n        // Fallback: return the most common file types if AI parsing fails\n        return this.files\n            .filter(file => isCodeFile(file.name) && !this.shouldExcludeFile(file.name, excludedFiles))\n            .map(file => file.name)\n            .slice(0, 5);\n    }\n\n    /**\n     * Find all files that depend on the given file\n     * @param fileName Name of the file\n     * @returns Array of file names that depend on the given file\n     */\n    private findDependents(fileName: string): string[] {\n        const dependents: string[] = [];\n\n        for (const [file, dependencies] of this.dependencyGraph.entries()) {\n            if (dependencies.has(fileName)) {\n                dependents.push(file);\n            }\n        }\n\n        return dependents;\n    }\n\n    /**\n     * Get the overall structure of the codebase\n     * @returns Object representing the codebase structure\n     */\n    private getCodebaseStructure(): CodebaseStructure {\n        const structure: CodebaseStructure = {\n            components: [],\n            screens: [],\n            contexts: [],\n            hooks: [],\n            utils: [],\n            types: [],\n            configs: [],\n        };\n\n        for (const [fileName, metadata] of this.fileMetadata.entries()) {\n            switch (metadata.type) {\n                case 'component':\n                    structure.components.push(fileName);\n                    break;\n                case 'screen':\n                    structure.screens.push(fileName);\n                    break;\n                case 'context':\n                    structure.contexts.push(fileName);\n                    break;\n                case 'hook':\n                    structure.hooks.push(fileName);\n                    break;\n                case 'util':\n                    structure.utils.push(fileName);\n                    break;\n                case 'type':\n                    structure.types.push(fileName);\n                    break;\n                case 'config':\n                    structure.configs.push(fileName);\n                    break;\n            }\n        }\n\n        return structure;\n    }\n\n    /**\n     * Get relationships between files\n     * @param fileNames Array of file names\n     * @returns Object representing relationships between files\n     */\n    private getFileRelationships(fileNames: string[]): FileRelationships {\n        const relationships: FileRelationships = {\n            imports: {},\n            exports: {},\n            dependencies: {},\n            dependents: {},\n        };\n\n        for (const fileName of fileNames) {\n            relationships.imports[fileName] = this.fileMetadata.get(fileName)?.imports || [];\n            relationships.exports[fileName] = this.fileMetadata.get(fileName)?.exports || [];\n            relationships.dependencies[fileName] = Array.from(this.dependencyGraph.get(fileName) || []);\n            relationships.dependents[fileName] = this.findDependents(fileName);\n        }\n\n        return relationships;\n    }\n}", "startLine": 106, "endLine": 690, "type": "unknown", "symbols": ["ContextEngine"], "score": 0.9, "context": "This class provides comprehensive codebase understanding and context retrieval, including snippet extraction and action plan generation, essential for AI response generation flow.", "includesImports": false}, {"filePath": "src/lib/ai/prompts.ts", "content": "\nexport const systemPrompt = `\n<overview>\nToday's date is ${dayjs().format(\"DD-MM-YYYY\")} and the day of the week is ${dayjs().format(\"dddd\")}\nYou are an exceptional Senior React Native developer creating visually stunning mobile apps. Focus on premium design and native feel with production-grade code.\n\n⚠️ Handling Non-Technical Users:\n• ALWAYS interpret vague requests charitably and clarify when needed\n• INFER technical details that users may not know to specify\n• TRANSLATE user's business language into technical requirements\n• SUGGEST best practices even when not explicitly requested\n• EDUCATE gently without overwhelming with technical details\n• CONFIRM understanding before implementing complex changes\n  \n⚠️ CRITICAL RULES:\n• Return COMPLETE implementations only\n• Use @expo/vector-icons for icons\n• Write modular, reusable code\n• Be concise and focus on business value\n• ONLY write Expo/React Native TypeScript code\n• Use Supabase for backend, NO server-side code\n• NEVER use JavaScript (.js) files\n</overview>\n\n<thinking_approach>\n  ⚠️ HOLISTIC THINKING FRAMEWORK:\n  \n  <decision_framework>\n    • Assess complexity: Simple (1-2 files) → Moderate (2-3 files) → Complex (max 5 files)\n    • Request files strategically: Start with most essential, NEVER request >3 files without implementing\n    • STOP and implement if: analyzed twice, repeating concepts, or looked at 3+ files\n    • Working solution > perfect solution\n  </decision_framework>\n\n  <thinking>\n  • HOLISTIC UNDERSTANDING: Make broad, comprehensive queries for entire features\n  • COMPLETE IMPLEMENTATION: Provide full solutions across ALL relevant files\n  • NON-TECHNICAL USERS: Interpret vague requests charitably, infer technical details\n  \n  • File Structure Checks:\n    - Break files >300 lines into smaller modules\n    - Ensure proper imports, navigation, and connections\n    - Follow folder structure guidelines\n  \n  • Secrets Handling:\n    - NEVER embed secrets (API keys, tokens) directly in code\n    - Use Supabase Edge Functions for sensitive operations\n    - If secrets needed + Supabase connected: Create Edge Function\n    - If secrets needed + NO Supabase: Ask user to connect Supabase\n  \n  • Navigation & Connections:\n    - NEVER leave buttons without navigation\n    - Connect all screens to navigation structure\n    - Implement proper error handling\n  \n  • Implementation Plan:\n    - List essential files to change (max 3)\n    - For each: filename - action - purpose\n  </thinking>\n  \n  Always show your thinking process before implementing. Use active voice and focus on holistic understanding.\n</thinking_approach>\n\n<code_structure>\n  ⚠️ CRITICAL FOLDER STRUCTURE:\n  • components/: UI components (ui/ for basic, <screen>/ for screen-specific)\n  • screens/: Main application screens\n  • navigation/: Navigation configuration\n  • hooks/, utils/, constants/, contexts/, stores/, types/\n  • services/ (API client), supabase/ (edge functions), libs/ (client config)\n  \n  ⚠️ FILE ORGANIZATION RULES:\n  • NEVER create files >250-300 lines, break into smaller modules\n  • Keep files focused and single-purpose\n  • Use consistent naming conventions\n  • One module/component per file\n  • Add @magic_description comments for all components/functions\n  • ALWAYS implement navigation for buttons/components\n  • ALWAYS add logging for API calls, navigation, debugging\n  • Escape quotes within strings (use \\' or \\\")\n  • Follow single responsibility principle\n  • NEVER leave a button without navigation if its screen exists\n  \n  ⚠️ ARCHITECTURE PRINCIPLES:\n  • Separate visual styling from business logic\n  • Centralize design tokens in theme file\n  • Create base components for design principles\n  • Structure code for easy extension\n</code_structure>\n\n<response_format>\n  ⚠️ COMMUNICATION RULES FOR NON-TECHNICAL USERS:\n  • Skip preambles, be direct and concise\n  • Use active voice and simple language\n  • Focus on business value, not technical details\n  • NEVER use technical jargon\n  • For unclear requests: interpret charitably, suggest what they likely need\n  • MAXIMUM 3 actions at the end only\n  \n  ⚠️ IMPLEMENTATION SUMMARY FORMAT:\n  What's New:\n  ✅ [First change in simple terms]\n  ✅ [Second change in simple terms]\n  ✅ [Third change in simple terms]\n  \n  Summary: [1-2 sentences about what the changes do for the user]\n  \n  What You Can Do Now:\n  - [Action 1 user can take]\n  - [Action 2 user can take]\n  \n  ⚠️ ACTIONS FORMAT:\n  <actions>\n    <action link=\"URL\" type=\"anchor\">Text</action>\n    <action tool=\"TOOL_NAME\" type=\"tool\">Description</action>\n    <action type=\"code\">Description</action>\n    <action type=\"feature\">Description</action>\n    <action tool=\"secrets_form\" type=\"tool\" secretName=\"NAME\">Enter secret</action>\n    <action tool=\"supabase_integration\" type=\"tool\">Integrate Supabase</action>\n  </actions>\n  \n  ⚠️ SECRETS HANDLING:\n  If app needs sensitive secrets (API keys, tokens, passwords):\n  • If Supabase connected + secrets present: Create Edge Function\n  • If Supabase connected + secrets missing: <action tool=\"secrets_form\" type=\"tool\" secretName=\"SECRET_NAME\">Enter secret</action>\n  • If Supabase not connected: <action tool=\"supabase_integration\" type=\"tool\">Integrate Supabase</action>\n  \n  ⚠️ ERROR RECOVERY:\n  If you break a feature:\n  • Acknowledge the issue simply\n  • Fix immediately before adding new features\n  • Make targeted changes to restore functionality\n  • Test by asking user to try the feature\n  \n  ⚠️ MEMORY MANAGEMENT:\n  Use addAiMemory only for significant information about:\n  • Product vision and preferences\n  • Important user requirements\n  • Technical decisions and constraints\n  • Only send latest learnings, not existing understanding\n</response_format>\n\n<environment_constraints>\n  ⚠️ PRODUCTION CODE REQUIREMENTS:\n  • Write PRODUCTION-grade code, not demos\n  • ONLY use Expo/React Native with TypeScript\n  • For backend, use ONLY Supabase\n  • NO new asset files (images, fonts, audio)\n  • App.tsx must be present and Expo-compatible\n  • ALWAYS implement full features, not placeholders\n  \n  ⚠️ ALLOWED CODE:\n  • Expo and React Native TypeScript\n  • Supabase client integration\n  • Database migrations (MO_DATABASE_QUERY) when Supabase connected\n  • Dummy JSON files\n  • Available dependencies only\n  \n  ⚠️ PROHIBITED CODE:\n  • Sample assets (images, fonts, audio)\n  • Backend code of any format\n  • JavaScript (.js) files\n  • Configuration files (babel.config, metro.config, package.json, app.json)\n  • Non-Expo/React Native code\n  • SQL files\n  • Embedded sensitive keys\n  \n  ⚠️ LOGGING REQUIREMENTS:\n  • Create API client with request/response logging\n  • Add logging for navigation route changes\n  • Add debug logs for troubleshooting\n</environment_constraints>\n\n<implementation_rules>\n  ⚠️ STRICT MODE - CRITICAL:\n  • COMPLETE code only - partial code is system failure\n  • VERIFY before every response:\n    - Every line of implementation included\n    - ALL styles, data, imports present\n    - NO ellipses (...) or \"rest of code remains\" comments\n  • If checks fail: HALT and return complete implementation\n  \n  ⚠️ DEPENDENCIES:\n  • ONLY use dependencies from this list:\n  ${JSON.stringify(DEFAULT_DEPENDENCIES, null, 2)}\n  • NEVER add new dependencies\n  \n  ⚠️ MEDIA HANDLING:\n  • Use Image and Video API exactly as instructed\n  • NEVER duplicate image/video descriptions on same page\n  • Text on images MUST be light, not dark\n  ${LLMMediaService.getLLMInstructions()}\n  \n  ⚠️ STRING ESCAPING - CRITICAL:\n  • ALWAYS escape quotes within strings\n  • BROKEN: 'What's this?' → CORRECT: 'What\\\\'s this?'\n  • BROKEN: \"He said \"hello\"\" → CORRECT: \"He said \\\\\"hello\\\\\"\" \n  • VALIDATE all strings before submitting\n  \n  ⚠️ CRITICAL MISTAKES TO AVOID:\n  • NEVER use variable references as types (typeof libraryItems[0])\n  • NEVER create more features than requested\n  • ALWAYS connect existing features/screens\n  • ALWAYS include ALL imports in EVERY file\n  • NEVER use ellipses or abbreviate imports\n  • NEVER create screens without navigation connections\n  • NEVER leave buttons without navigation.navigate()\n  • ALWAYS implement navigation BEFORE screens\n  • Group imports: React first, then libraries, then internal\n  • For Supabase: NO 'react-native-url-polyfill/auto' import\n \n</implementation_rules>\n\n${KNOWN_ERRORS}\n${STREAMLINED_ARTIFACT_EXAMPLE}\n`;", "startLine": 16, "endLine": 231, "type": "unknown", "symbols": ["AI prompts and system instructions"], "score": 0.8, "context": "This module defines system prompts and instructions guiding AI behavior and response generation, important for understanding AI response flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/multi-perspective-analysis.tool.ts", "content": "export const multiPerspectiveAnalysis = ({\n  dataStream,\n}: {\n  dataStream: DataStreamWriter;\n}) => {\n  return tool({\n    description: 'Analyze a topic from multiple perspectives using different AI personas to reach a more comprehensive and balanced understanding',\n    parameters: multiPerspectiveAnalysisSchema,\n    execute: async ({ topic, context = '', focusAreas = [], requiredPersonas = [], analysisDepth = 'standard', discussionRounds = 3 }) => {\n      try {\n        // Stream the initial setup information\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `# Multi-Perspective Analysis: ${topic}\\n\\nInitiating analysis with multiple perspectives...\\n\\n`\n        });\n\n        // Select which personas to use\n        const selectedPersonaNames = requiredPersonas.length > 0 ? requiredPersonas : personaList;\n        \n        if (selectedPersonaNames.length === 0) {\n          throw new Error('No valid personas selected for analysis');\n        }\n\n        // Determine token allocation based on analysis depth\n        let maxTokensPerPersona;\n        switch (analysisDepth) {\n          case 'brief':\n            maxTokensPerPersona = 500;\n            break;\n          case 'comprehensive':\n            maxTokensPerPersona = 1500;\n            break;\n          case 'standard':\n          default:\n            maxTokensPerPersona = 1000;\n        }\n\n        // Track all contributions to the discussion\n        type Contribution = {\n          persona: PersonaName;\n          content: string;\n          round: number;\n          replyTo?: PersonaName[];\n        };\n\n        const contributions: Contribution[] = [];\n        \n        // Initialize the discussion with the topic introduction\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `## Multi-Perspective Discussion\\n\\n`\n        });\n\n        // PHASE 1: Initial perspectives from each persona\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `### Round 1: Initial Perspectives\\n\\n`\n        });\n\n        // Generate initial perspectives from each persona\n        for (const personaName of selectedPersonaNames) {\n          dataStream.writeData({\n            type: 'multi-perspective-update',\n            content: `#### ${personaName}:\\n\\n`\n          });\n\n          // Create the prompt for this persona\n          const personaPrompt = `\nYou are the \"${personaName}\" persona with the following characteristics:\n${personaDescriptions[personaName]}\n\nAnalyze the following topic from your unique perspective:\nTopic: ${topic}\n${context ? `Context: ${context}` : ''}\n${focusAreas.length > 0 ? `Focus Areas: ${focusAreas.join(', ')}` : ''}\n\nProvide your analysis in a clear, concise manner that reflects your perspective. Be insightful but concise.\n`;\n\n          // Generate the persona's perspective\n          const result = streamText({\n            model: customModel(\"anthropic/claude-sonnet-4\"),\n            temperature: 0.7,\n            messages: [\n              {\n                role: 'system',\n                content: personaPrompt\n              }\n            ]\n          });\n          \n          let perspective = '';\n          for await (const chunk of result.textStream) {\n            perspective += chunk;\n          }\n\n          // Add this perspective to the contributions\n          contributions.push({\n            persona: personaName,\n            content: perspective,\n            round: 1\n          });\n\n          dataStream.writeData({\n            type: 'multi-perspective-update',\n            content: `${perspective}\\n\\n`\n          });\n        }\n\n        // PHASE 2: Interactive discussion rounds\n        // Each round, personas respond to previous contributions\n        for (let round = 2; round <= discussionRounds; round++) {\n          dataStream.writeData({\n            type: 'multi-perspective-update',\n            content: `### Round ${round}: Developing the Discussion\\n\\n`\n          });\n\n          // Get all contributions from the previous round\n          const previousRoundContributions = contributions.filter(c => c.round === round - 1);\n          \n          // Each persona responds to the previous round\n          for (const personaName of selectedPersonaNames) {\n            // Get all previous contributions except this persona's own contribution\n            const relevantContributions = previousRoundContributions\n              .filter(c => c.persona !== personaName);\n              \n            if (relevantContributions.length === 0) continue;\n            \n            dataStream.writeData({\n              type: 'multi-perspective-update',\n              content: `#### ${personaName}:\\n\\n`\n            });\n\n            // Format the previous contributions for the prompt\n            const previousContributionsText = relevantContributions\n              .map(c => `${c.persona}: ${c.content}`)\n              .join('\\n\\n');\n\n            // Create a prompt that encourages building on previous ideas\n            const dialoguePrompt = `\nYou are the \"${personaName}\" persona with the following characteristics:\n${personaDescriptions[personaName]}\n\nYou're participating in round ${round} of a discussion on \"${topic}\".\n\nHere are the most recent contributions from other participants:\n\n${previousContributionsText}\n\nBased on your unique perspective and the discussion so far:\n1. Respond to at least 2 specific points made by others\n2. Build upon or challenge ideas in a constructive way\n3. Introduce a new insight or question that advances the discussion\n4. Maintain your distinct perspective while seeking common ground\n\nBe concise but insightful. Your goal is to deepen the collective understanding.\n`;\n\n            // Generate the persona's response\n            const result = streamText({\n              model: customModel(\"anthropic/claude-sonnet-4\"),\n              temperature: 0.7,\n              messages: [\n                {\n                  role: 'system',\n                  content: dialoguePrompt\n                }\n              ]\n            });\n            \n            let response = '';\n            for await (const chunk of result.textStream) {\n              response += chunk;\n            }\n\n            // Add this response to the contributions\n            contributions.push({\n              persona: personaName,\n              content: response,\n              round: round,\n              replyTo: relevantContributions.map(c => c.persona)\n            });\n\n            dataStream.writeData({\n              type: 'multi-perspective-update',\n              content: `${response}\\n\\n`\n            });\n          }\n        }\n\n        // PHASE 3: Generate synthesis and consensus\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `## Synthesis and Consensus\\n\\n`\n        });\n\n        // Compile all contributions for synthesis\n        const allContent = contributions\n          .map(c => `Round ${c.round} - ${c.persona}: ${c.content}`)\n          .join('\\n\\n');\n\n        // Create the synthesis prompt\n        const synthesisPrompt = `\nYou are a neutral synthesizer tasked with finding common ground and key insights from multiple perspectives on the topic: \"${topic}\"\n\nHere are all the perspectives and dialogue exchanges:\n\n${allContent}\n\nPlease provide:\n1. A summary of key points of agreement across perspectives\n2. Important points of disagreement or tension\n3. Unique insights contributed by each perspective\n4. A balanced synthesis that incorporates the strongest elements from each viewpoint\n5. Remaining questions or areas for further exploration\n\nYour synthesis should be fair to all perspectives while highlighting the most valuable insights from each.\n`;\n\n        // Generate the synthesis\n        const synthesisResult = streamText({\n          model: customModel(\"anthropic/claude-sonnet-4\"),\n          temperature: 0.7,\n          messages: [\n            {\n              role: 'system',\n              content: synthesisPrompt\n            }\n          ]\n        });\n        \n        let synthesis = '';\n        for await (const chunk of synthesisResult.textStream) {\n          synthesis += chunk;\n        }\n\n        // Synthesis is built in the streaming loop above\n\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: synthesis\n        });\n\n        // Return the complete analysis results\n        return {\n          topic,\n          contributions,\n          synthesis,\n          result: 'success'\n        };\n      } catch (error) {\n        console.error('Error in multi-perspective analysis:', error);\n        dataStream.writeData({\n          type: 'error',\n          error: error instanceof Error ? error.message : 'Failed to complete multi-perspective analysis'\n        });\n        throw error;\n      }\n    }\n  });\n};", "startLine": 55, "endLine": 315, "type": "util", "symbols": ["multiPerspectiveAnalysis"], "score": 0.5, "context": "This tool demonstrates a complex AI streaming interaction with multiple AI personas generating and streaming responses, showing an example of AI response generation and streaming flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/query-codebase.ts", "content": "export const queryCodebase = ({\n                                  projectId,\n                                  files,\n                                  fileManager,\n                                  messageId = '',\n                                  userMessage = ''\n                              }: { projectId: string, files: FileItem[], fileManager: FileLineManager, messageId?: string, userMessage?: string }) => {\n    return tool({\n        description: 'Query the codebase to understand its structure, relationships, and relevant files for a specific task. ' +\n            'Use this instead of requesting individual files. ' +\n            'You can exclude specific files from the analysis by providing an array of file paths in the excludedFiles parameter. ' +\n            'Use filePath parameter to retrieve a SINGLE file in full ONLY when absolutely necessary. You are LIMITED to ONE file per request.',\n        parameters: QueryCodebaseSchema,\n        execute: async ({query, excludedFiles, reason}: z.infer<typeof QueryCodebaseSchema>) => {\n            console.log('Query:', query)\n            console.log('Reason:', reason)\n            console.log('Excluded files:', excludedFiles?.join())\n\n            if(!query) {\n                return {\n                    result: null,\n                    message: \"One of query or filePath is required. To look at the holistic view, use query and to look at a specific file, use filePath.\"\n                }\n            }\n            // Check if this is a \"continue\" message\n            const isContinueMessage = userMessage.toLowerCase().trim().startsWith('continue');\n\n            // Get the tool tracker instance\n            const toolTracker = ToolCountTracker.getInstance();\n\n            console.log('Tool call allowed (queryCodebase):', toolTracker.shouldAllowToolCall(messageId, 'queryCodebase'))\n\n            // Check if we should allow this tool call\n            if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'queryCodebase')) {\n                return {\n                    result: null,\n                    message: \"⚠️ Tool call limit reached. You've already made multiple codebase queries. Please implement or summarize with the information you already have.\",\n                };\n            }\n\n            // Increment the tool count if we have a chat ID\n            if (messageId) {\n                toolTracker.incrementToolCount(messageId, 'queryCodebase');\n            }\n            try {\n                if (!files || files.length === 0) {\n                    throw new Error('No files available for context engine');\n                }\n\n                // Get project information if available\n                let project: Project | null = null;\n                if (projectId) {\n                    try {\n                        project = await getProjectById({id: projectId});\n                    } catch (error) {\n                        console.warn('Could not fetch project information:', error);\n                        // Continue without project info\n                    }\n                }\n\n                if (!project) {\n                    throw new Error('No project information available');\n                }\n\n                // Check if we're requesting a specific file in full\n                // Note: filePath takes precedence over semantic query when both are provided\n                // if (filePath) {\n                //     console.log('Specific file requested:', filePath);\n                //\n                //     // Find the requested file\n                //     const fileItem = fileManager.getFileItems().find(file => file.name === filePath.trim());\n                //\n                //     if (!fileItem) {\n                //         throw new Error(`The requested file was not found: ${filePath}`);\n                //     }\n                //\n                //     const fileItems = [fileItem];\n                //\n                //     // Create snippets for each file\n                //     const snippets = fileItems.map(fileItem => ({\n                //         filePath: fileItem.name,\n                //         content: fileItem.content || '',\n                //         startLine: 1,\n                //         endLine: (fileItem.content || '').split('\\n').length,\n                //         type: 'unknown',\n                //         symbols: [],\n                //         score: 1.0\n                //     }));\n                //\n                //     // Create a simplified result with just the full file contents\n                //     const result = {\n                //         query,\n                //         snippets,\n                //         codebaseStructure: { components: [], screens: [], contexts: [], hooks: [], utils: [], types: [], configs: [] },\n                //         relationships: { imports: {}, exports: {}, dependencies: {}, dependents: {} }\n                //     };\n                //\n                //     let message = `Showing full content of the requested file: ${fileItems[0].name}`;\n                //\n                //     return {\n                //         result,\n                //         message,\n                //         toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')\n                //     };\n                // }\n                \n                // Initialize the context engine with the current files and project\n                const contextEngine = new ContextEngine(fileManager.getFileItems(), project);\n\n                // Default excluded files for Expo Snack projects if not provided\n                const defaultExcludedFiles = [\n                    // Large asset files\n                    'assets/',\n                    '.mp3',\n                    '.mp4',\n                    '.jpg',\n                    '.png',\n                    '.gif',\n                    // Generated type files\n                    'types/generated',\n                    // These don't exist in Expo Snack but kept as fallbacks\n                    'node_modules',\n                    'dist'\n                ];\n\n                // Combine user-provided excludedFiles with defaults\n                const combinedExcludedFiles = excludedFiles ?\n                    [...excludedFiles, ...defaultExcludedFiles] :\n                    defaultExcludedFiles;\n\n                // Query the context engine\n                const result = await contextEngine.query(query, reason, combinedExcludedFiles);\n\n                // Prepare a more informative response\n                let message = `Found ${result.snippets?.length} snippets for your query.`;\n\n                // Add information about excluded files if any\n                if (excludedFiles && excludedFiles.length > 0) {\n                    message += `\\n${excludedFiles.length} files were excluded from the analysis.`;\n                }\n\n                if (result.actionPlan) {\n                    message += `\\n\\nAction Plan: ${result.actionPlan.summary}\\nComplexity: ${result.actionPlan.complexity}\\nSteps: ${result.actionPlan.steps.length}`;\n                }\n\n                // if (result.supabaseSchema) {\n                //     message += `\\n\\nSupabase integration detected with ${result.supabaseSchema.tables.length} tables.`;\n                // }\n\n                return {\n                    result,\n                    message,\n                    toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')\n                };\n            } catch (error) {\n                console.error('Context engine tool error:', error);\n                throw new Error(`Failed to query context engine: ${error instanceof Error ? error.message : 'Unknown error'}`);\n            }\n        },\n    });\n}", "startLine": 23, "endLine": 183, "type": "util", "symbols": ["queryCodebase"], "score": 0.5, "context": "This tool queries the codebase context for AI, relevant for understanding how AI gathers code context for response generation.", "includesImports": false}, {"filePath": "src/lib/chat/tools/query-supabase-context.ts", "content": "export const querySupabaseContext = ({\n    projectId,\n    messageId = '',\n}: {\n    projectId: string, \n    messageId?: string, \n}) => {\n    return tool({\n        description: 'Query Supabase resources or execute SQL (either one at a time) to understand the project tables, dbFunctions, triggers, functions, RLS policies, storage buckets, and secrets. ' +\n            'Use this instead of requesting full Supabase instructions. Issue queries that are as specific as possible. ' +\n            'You can exclude specific resources using the excludedResources parameter in the format \"resourceType.resourceName\" or use wildcards like \"table.*\". ' +\n            'This tool uses a two-stage approach to efficiently retrieve only the relevant Supabase resources. ' +\n            'If the initial query returns incomplete information, the tool may indicate mustRetrieveAdditionalResources=true with guidance on what additional resources to request in a follow-up query. ' +\n            'You can also execute SELECT SQL queries directly against the database using the executeSQL parameter. Only SELECT queries are allowed for security reasons. When executing a SQL query, code exploration will not happen.',\n        parameters: QuerySupabaseContextSchema,\n        execute: async ({query, excludedResources, executeSQL, reason}: z.infer<typeof QuerySupabaseContextSchema>) => {\n            try {\n                console.log('Supabase Query:', query);\n                console.log('Excluded Resources:', excludedResources || []);\n                console.log('SQL Query:', executeSQL || 'None');\n                console.log('Reason:', reason);\n\n                // Validate inputs\n                if (!query && !executeSQL) {\n                    return {\n                        result: null,\n                        message: \"Either a natural language query or SQL query is required.\"\n                    };\n                }\n\n                // Get the tool tracker instance\n                const toolTracker = ToolCountTracker.getInstance();\n\n                console.log('Tool call allowed (querySupabaseContext):', toolTracker.shouldAllowToolCall(messageId, 'querySupabaseContext'));\n\n                // Check if we should allow this tool call\n                if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'querySupabaseContext')) {\n                    return {\n                        result: null,\n                        message: \"⚠️ Tool call limit reached. You've already made multiple Supabase context queries. Please implement or summarize with the information you already have.\",\n                    };\n                }\n\n                // Increment the tool count if we have a chat ID\n                if (messageId) {\n                    toolTracker.incrementToolCount(messageId, 'querySupabaseContext');\n                }\n\n                // Get project information\n                let project: Project | null = null;\n                try {\n                    if (!projectId) {\n                        return {\n                            result: null,\n                            message: \"Project ID is required\"\n                        };\n                    }\n                    \n                    project = await getProjectById({id: projectId});\n                    if (!project) {\n                        return {\n                            result: null,\n                            message: `Project not found: ${projectId}`\n                        };\n                    }\n                    \n                    // Check if project is connected to Supabase\n                    if (!project.connectionId || !project.supabaseProjectId) {\n                        return {\n                            result: null,\n                            message: 'This project is not connected to Supabase'\n                        };\n                    }\n                } catch (error) {\n                    console.error('Error fetching project:', error);\n                    return {\n                        result: null,\n                        message: `Failed to fetch project information: ${error instanceof Error ? error.message : 'Unknown error'}`\n                    };\n                }\n                \n                // Handle SQL execution if provided\n                if (executeSQL) {\n                    console.log('Executing SQL query:', executeSQL);\n                    \n                    // Validate that it's a SELECT query only\n                    const sqlLower = executeSQL.trim().toLowerCase();\n                    if (!sqlLower.startsWith('select ')) {\n                        return {\n                            result: null,\n                            message: '⚠️ Only SELECT queries are allowed for security reasons. Your query was rejected.'\n                        };\n                    }\n                    \n                    // Check for potentially harmful statements\n                    const disallowedKeywords = ['insert', 'update', 'delete', 'drop', 'alter', 'create', 'truncate'];\n                    if (disallowedKeywords.some(keyword => sqlLower.includes(keyword))) {\n                        return {\n                            result: null,\n                            message: '⚠️ SQL query contains disallowed keywords. Only pure SELECT queries are permitted. Your query was rejected.'\n                        };\n                    }\n                    \n                    try {\n                        // Execute the SQL query\n                        const supabaseProvider = new SupabaseIntegrationProvider();\n                        const result = await supabaseProvider.executeSQL({\n                            query: executeSQL,\n                            projectId\n                        });\n                        \n                        // Truncate the result if it's too large\n                        const resultStr = JSON.stringify(result || {}, null, 2);\n                        const MAX_LENGTH = 3000;\n                        \n                        let sqlResult;\n                        let message = '📊 SQL query executed successfully.';\n                        \n                        if (resultStr.length > MAX_LENGTH) {\n                            sqlResult = {\n                                data: result?.data,\n                                truncated: true,\n                                originalLength: resultStr.length,\n                                message: `SQL result was truncated from ${resultStr.length} to ${MAX_LENGTH} characters`\n                            };\n                            message += ` Results were truncated (${sqlResult.message}).`;\n                        } else {\n                            sqlResult = result;\n                        }\n                        \n                        return {\n                            result: { sqlResult },\n                            message,\n                            toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'querySupabaseContext')\n                        };\n                    } catch (error) {\n                        console.error('SQL execution error:', error);\n                        return {\n                            result: null,\n                            message: `⚠️ Failed to execute SQL query: ${error instanceof Error ? error.message : 'Unknown error'}`\n                        };\n                    }\n                } else {\n                    // Only query the context engine if no SQL was provided\n                    try {\n                        // Initialize the Supabase context engine\n                        const contextEngine = new SupabaseContextEngine(project);\n\n                        // Query the Supabase context engine\n                        const result = await contextEngine.getRelevantSupabaseResources(query, excludedResources);\n\n                        // Prepare a more informative response\n                        const resourceCount = result.resources?.length || 0;\n                        let message = `Found ${resourceCount} relevant Supabase resources for your query.`;\n\n                        // Add information about resource types\n                        const resourceTypes = result.resources?.map(r => r.resourceType) || [];\n                        const uniqueTypes = [...new Set(resourceTypes)];\n                        \n                        if (uniqueTypes.length > 0) {\n                            message += ` Types: ${uniqueTypes.join(', ')}.`;\n                        }\n                        \n                        // Add information about excluded resources if any\n                        if (excludedResources && excludedResources.length > 0) {\n                            message += `\\n${excludedResources.length} resources were excluded from the analysis.`;\n                        }\n                        \n                        // Add information about additional resources if needed\n                        if (result.hasAdditional) {\n                            message += `\\nThere are additional resources that may be relevant but weren't included due to the result limit.`;\n                            \n                            if (result.mustRetrieveAdditionalResources) {\n                                message += `\\n\\n⚠️ IMPORTANT: You MUST make another query to retrieve critical additional resources. ${result.additionalResourcesSummary}`;\n                            } else if (result.additionalResourcesSummary) {\n                                message += `\\n\\nConsider querying for: ${result.additionalResourcesSummary}`;\n                            }\n                        }\n                        \n                        return {\n                            result,\n                            message,\n                            toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'querySupabaseContext')\n                        };\n                    } catch (error) {\n                        console.error('Supabase context engine error:', error);\n                        return {\n                            result: null,\n                            message: `⚠️ Failed to query Supabase context engine: ${error instanceof Error ? error.message : 'Unknown error'}`\n                        };\n                    }\n                }\n            } catch (error) {\n                // Global error handler - ensures we never throw errors\n                console.error('Unexpected error in querySupabaseContext:', error);\n                return {\n                    result: null,\n                    message: `⚠️ An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`\n                };\n            }\n        },\n    });\n};", "startLine": 23, "endLine": 225, "type": "util", "symbols": ["querySupabaseContext"], "score": 0.5, "context": "This tool queries Supabase context, showing how AI integrates external database context into response generation.", "includesImports": false}, {"filePath": "src/lib/chat/tools/get-file-contents.ts", "content": "export const getFileContents = ({\n                                    fileManager,\n                                    dataStream,\n                                    creditUsageTracker\n                                }: {\n    fileManager: FileLineManager,\n    dataStream: DataStreamWriter,\n    creditUsageTracker: CreditUsageTracker\n}) => {\n    return tool({\n        description: 'Get complete file content or list directory contents when queryCodebase snippets are insufficient. Use sparingly - only when you need to see the full file structure, all imports/exports, or complete implementation details that snippets miss.',\n        parameters: z.object({\n            path: z.string().describe(\"File or directory path to retrieve. For files: must match exactly a file from the project. For directories: use folder path like 'components' or 'screens' to list contents. Use '*' to see all top-level directories and files.\"),\n            lineRange: z.array(z.number()).length(2).optional().describe(\"Optional [start, end] line range (1-based, inclusive). Use [start, -1] for start to end of file. Only applicable for files.\"),\n            searchPattern: z.string().optional().describe(\"Optional regex pattern to search within the file. Shows matching lines with context. Only applicable for files.\"),\n            reason: z.string().describe(\"Why you need the full file content or directory listing instead of using queryCodebase snippets.\")\n        }),\n        execute: async ({path, lineRange, searchPattern, reason}: {\n            path: string,\n            lineRange?: [number, number],\n            searchPattern?: string,\n            reason: string\n        }) => {\n            try {\n                const files = fileManager.getFileItems();\n                // Normalize path - handle both /libs and libs formats\n                const normalizedPath = path.startsWith('/') ? path.slice(1) : path;\n\n                // Check if this is a directory listing request\n                const isDirectoryRequest = !normalizedPath.includes('.') || normalizedPath.endsWith('/') || normalizedPath === '*';\n\n                if (isDirectoryRequest) {\n                    // Directory listing\n                    const dirPath = normalizedPath === '*' ? '' : normalizedPath.replace(/\\/$/, ''); // Remove trailing slash\n\n                    // Find all files that start with this directory path\n                    const matchingFiles = files.filter(f => {\n                        const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                        if (dirPath === '') {\n                            // For root or * pattern, include all files\n                            return true;\n                        }\n                        return fileName.startsWith(dirPath + '/');\n                    });\n\n                    if (matchingFiles.length === 0) {\n                        // Show available top-level directories\n                        const topLevelDirs = new Set<string>();\n                        files.forEach(f => {\n                            const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                            const firstSlash = fileName.indexOf('/');\n                            if (firstSlash > 0) {\n                                topLevelDirs.add(fileName.substring(0, firstSlash));\n                            }\n                        });\n\n                        return `Directory ${path} not found. Available directories: ${Array.from(topLevelDirs).join(', ')}`;\n                    }\n\n                    // Group files by subdirectory with depth 2 support\n                    const structure = new Map<string, { files: string[], subdirs: Map<string, string[]> }>();\n\n                    matchingFiles.forEach(f => {\n                        const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                        const relativePath = fileName.substring(dirPath.length + (dirPath ? 1 : 0));\n                        const pathParts = relativePath.split('/');\n\n                        if (pathParts.length === 1) {\n                            // Direct file in this directory\n                            if (!structure.has('.')) structure.set('.', { files: [], subdirs: new Map() });\n                            structure.get('.')!.files.push(pathParts[0]);\n                        } else if (pathParts.length === 2) {\n                            // File in immediate subdirectory (depth 1)\n                            const subDir = pathParts[0];\n                            if (!structure.has(subDir)) structure.set(subDir, { files: [], subdirs: new Map() });\n                            structure.get(subDir)!.files.push(pathParts[1]);\n                        } else if (pathParts.length >= 3) {\n                            // File in nested subdirectory (depth 2+)\n                            const subDir = pathParts[0];\n                            const nestedSubDir = pathParts[1];\n                            if (!structure.has(subDir)) structure.set(subDir, { files: [], subdirs: new Map() });\n\n                            const subDirData = structure.get(subDir)!;\n                            if (!subDirData.subdirs.has(nestedSubDir)) {\n                                subDirData.subdirs.set(nestedSubDir, []);\n                            }\n                            subDirData.subdirs.get(nestedSubDir)!.push(pathParts.slice(2).join('/'));\n                        }\n                    });\n\n                    // Format directory listing\n                    const displayPath = path === '*' ? 'Project Root' : path;\n                    let listing = `Directory: ${displayPath} (${matchingFiles.length} items)\\n\\n`;\n\n                    // Show subdirectories first\n                    const sortedKeys = Array.from(structure.keys()).sort((a, b) => {\n                        if (a === '.') return 1; // Files last\n                        if (b === '.') return -1;\n                        return a.localeCompare(b);\n                    });\n\n                    sortedKeys.forEach(key => {\n                        if (key === '.') {\n                            // Direct files\n                            const directFiles = structure.get(key)!.files.sort();\n                            directFiles.forEach(file => {\n                                const fullPath = dirPath ? `${dirPath}/${file}` : file;\n                                listing += `📄 ${file} (${fullPath})\\n`;\n                            });\n                        } else {\n                            // Subdirectory\n                            const subDirData = structure.get(key)!;\n                            const subDirPath = dirPath ? `${dirPath}/${key}` : key;\n                            const totalFiles = subDirData.files.length +\n                                Array.from(subDirData.subdirs.values()).reduce((sum, files) => sum + files.length, 0);\n\n                            listing += `📁 ${key}/ (${totalFiles} files) - ${subDirPath}/\\n`;\n\n                            // Show files in this subdirectory\n                            subDirData.files.sort().forEach(file => {\n                                listing += `  📄 ${file} (${subDirPath}/${file})\\n`;\n                            });\n\n                            // Show nested subdirectories (depth 2)\n                            const sortedSubDirs = Array.from(subDirData.subdirs.keys()).sort();\n                            sortedSubDirs.forEach(nestedDir => {\n                                const nestedFiles = subDirData.subdirs.get(nestedDir)!;\n                                const nestedPath = `${subDirPath}/${nestedDir}`;\n                                listing += `  📁 ${nestedDir}/ (${nestedFiles.length} files) - ${nestedPath}/\\n`;\n\n                                // Show first few files in nested directory\n                                const filesToShow = nestedFiles.slice(0, 3);\n                                filesToShow.forEach(file => {\n                                    listing += `    📄 ${file} (${nestedPath}/${file})\\n`;\n                                });\n\n                                if (nestedFiles.length > 3) {\n                                    listing += `    ... and ${nestedFiles.length - 3} more files\\n`;\n                                }\n                            });\n                        }\n                    });\n\n                    return listing;\n                }\n\n                // File content request\n                const fileItem = files.find(f => f.name === normalizedPath || f.name === `/${normalizedPath}`);\n\n                if (!fileItem) {\n                    return `File ${path} not found. Available files: ${files.slice(0, 10).map(f => f.name).join(', ')}${files.length > 10 ? '...' : ''}`;\n                }\n\n                let content = fileItem.content;\n                const lines = content.split('\\n');\n                const totalLines = lines.length;\n\n                // Apply search pattern if specified\n                if (searchPattern) {\n                    try {\n                        const regex = new RegExp(searchPattern, 'gi');\n                        const matchingLines: string[] = [];\n                        const contextBefore = 3;\n                        const contextAfter = 3;\n\n                        let lastIncludedLine = -1;\n\n                        lines.forEach((line, index) => {\n                            if (regex.test(line)) {\n                                const startContext = Math.max(0, index - contextBefore);\n                                const endContext = Math.min(lines.length - 1, index + contextAfter);\n\n                                // Add gap indicator if there's a gap\n                                if (startContext > lastIncludedLine + 1) {\n                                    matchingLines.push('...');\n                                }\n\n                                // Add context and matching line\n                                for (let i = startContext; i <= endContext; i++) {\n                                    if (i > lastIncludedLine) {\n                                        const lineNumber = i + 1;\n                                        const prefix = i === index ? '>' : ' ';\n                                        matchingLines.push(`${prefix}${lineNumber.toString().padStart(4)}: ${lines[i]}`);\n                                        lastIncludedLine = i;\n                                    }\n                                }\n                            }\n                        });\n\n                        content = matchingLines.join('\\n');\n                    } catch (regexError) {\n                        return `Invalid regex pattern: ${searchPattern}`;\n                    }\n                } else if (lineRange) {\n                    // Apply line range if specified\n                    const [start, end] = lineRange;\n                    const startLine = Math.max(1, start) - 1; // Convert to 0-based\n                    const endLine = end === -1 ? lines.length : Math.min(end, lines.length);\n\n                    const selectedLines = lines.slice(startLine, endLine);\n                    content = selectedLines.map((line, index) => {\n                        const lineNumber = startLine + index + 1;\n                        return `${lineNumber.toString().padStart(4)}: ${line}`;\n                    }).join('\\n');\n                } else {\n                    // Return full file with line numbers\n                    content = lines.map((line, index) => {\n                        const lineNumber = index + 1;\n                        return `${lineNumber.toString().padStart(4)}: ${line}`;\n                    }).join('\\n');\n                }\n\n                creditUsageTracker.trackOperation('tool_call');\n\n                const resultLines = content.split('\\n').length;\n                const truncated = resultLines > 500;\n\n                if (truncated) {\n                    const truncatedLines = content.split('\\n').slice(0, 500);\n                    content = truncatedLines.join('\\n') + '\\n\\n<response clipped - file too long>';\n                }\n\n                return `File: ${path} (${totalLines} lines total, showing ${truncated ? '500' : resultLines} lines)\nReason: ${reason}\n${lineRange ? `Line range: ${lineRange[0]}-${lineRange[1] === -1 ? 'end' : lineRange[1]}` : ''}\n${searchPattern ? `Search pattern: ${searchPattern}` : ''}\n\n${content}`;\n\n            } catch (error: any) {\n                console.error('Error in getFileContents:', error);\n                return `Error retrieving file content: ${error.message}`;\n            }\n        }\n    });\n};", "startLine": 7, "endLine": 242, "type": "util", "symbols": ["getFileContents"], "score": 0.5, "context": "This tool retrieves full file contents or directory listings, important for AI to get detailed code context during response generation.", "includesImports": false}, {"filePath": "src/lib/chat/tools/add-ai-memory.ts", "content": "export const addAiMemory = ({\n                                dataStream,\n                                creditUsageTracker,\n                                projectId\n                            }: {\n    dataStream: DataStreamWriter,\n    creditUsageTracker: CreditUsageTracker,\n    projectId: string\n}) => {\n    return tool({\n        description: `Use the addAiMemory tool to store critical information for future interactions.\n        WHEN TO USE:\n        - After completing part of a multi-step task to track remaining steps\n        - To record user preferences, project requirements, or design decisions\n        - To capture technical constraints or architectural decisions\n        - To maintain context across multiple interactions\n\n        HOW TO USE:\n        - Use clear prefixes for different types of memories:\n          * USER PREFERENCE: For user style/feature preferences\n          * REQUIREMENT: For project requirements\n          * COMPLETED/NEXT: For task progress tracking\n          * DECISION: For design/technical decisions\n          * CONSTRAINT: For technical limitations\n        - Be specific and concise (1-2 sentences per memory)\n        - Always use at the END of your response\n        `,\n        parameters: z.object({\n            knowledge: z.string().describe(`Concise, specific information to remember for future interactions. Use prefixes like USER PREFERENCE:, REQUIREMENT:, COMPLETED:/NEXT:, DECISION:, or CONSTRAINT: to categorize the memory. Be specific and actionable - this information will be available in future interactions.`)\n        }),\n        execute: async ({knowledge}: { knowledge: string }) => {\n            try {\n                const project = await getProjectById({id: projectId})\n                if (!project) {\n                    return \"Project not found\"\n                }\n\n                // Constants for memory management\n                const MAX_MEMORY_CHARS = 8000; // About 2000 tokens\n                const MAX_CATEGORY_ENTRIES = 10; // Maximum entries per category before summarization\n\n                // Process the new knowledge entry\n                const timestamp = dayjs().toISOString();\n                const newEntry = {\n                    timestamp,\n                    content: knowledge.trim(),\n                    category: extractCategory(knowledge)\n                };\n\n                // Parse existing memory into structured format\n                const existingMemories = parseMemories(project.aiGeneratedMemory || '');\n\n                // Add new entry\n                existingMemories.push(newEntry);\n\n                // Manage memory size through categorization and summarization\n                const managedMemories = manageMemorySize(existingMemories, MAX_MEMORY_CHARS, MAX_CATEGORY_ENTRIES);\n\n                // Convert back to string format\n                const updatedMemory = memoriesToString(managedMemories);\n\n                // Update the project with the new memory\n                creditUsageTracker.trackOperation(\"add_ai_memory\", 1);\n                await updateProject({id: projectId, aiGeneratedMemory: updatedMemory});\n                return \"Memory saved and optimized\"\n            } catch (e: any) {\n                console.log('Error while saving memory', e);\n                return `Failed to save memory: ${e.message}`\n            }\n        }\n    });\n};", "startLine": 12, "endLine": 83, "type": "util", "symbols": ["addAiMemory"], "score": 0.4, "context": "This tool stores critical information for future AI interactions, relevant for understanding how AI maintains context across interactions.", "includesImports": false}, {"filePath": "src/lib/chat/tools/search-web.ts", "content": "export const searchWeb = ({\n  dataStream,\n  creditUsageTracker\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n}) => {\n  return tool({\n    description: 'Performs a web search to get a list of relevant web documentation for the given query and optional domain filter. Use this tool ONLY when you need up-to-date information that is not available in the codebase or when the user explicitly asks for external information. IT WILL ALWAYS be used to search for documentation. Do not use this tool for general coding questions or tasks that can be solved with your existing knowledge.',\n    parameters: z.object({\n      query: z.string().describe(\"The search query to find relevant information. Make sure to filter for documentation and be as specific as possible\"),\n      domain: z.string().optional().describe(\"Optional domain to recommend the search prioritize. Try to include it as the number of results is limited to 2 and truncated heavily to save tokens.\"),\n      reason: z.string().describe(\"Explain why this information cannot be found in the codebase and why it's essential for completing the user's task\")\n    }),\n    execute: async ({ query, domain, reason }: { \n      query: string;\n      domain?: string;\n      reason: string;\n    }) => {\n      try {\n        console.log(`Searching web for: \"${query}\". Reason: ${reason}, Domain: ${domain}`);\n\n        // Validate the search reason to ensure the tool is not being misused\n        // const validReasons = [\n        //   \"current events\",\n        //   \"up-to-date information\",\n        //   \"external documentation\",\n        //   \"third-party API details\",\n        //   \"user explicitly requested\",\n        //   \"package documentation\"\n        // ];\n        //\n        // const isValidReason = validReasons.some(validReason =>\n        //   reason.toLowerCase().includes(validReason.toLowerCase())\n        // );\n        \n        // if (!isValidReason) {\n        //   return JSON.stringify({\n        //     error: \"Invalid search reason. Web search should only be used for obtaining up-to-date information that cannot be found in the codebase or when explicitly requested by the user.\",\n        //     suggestedAction: \"Use your existing knowledge or codebase search tools instead.\"\n        //   });\n        // }\n        \n        // Create a new instance of the TavilyService\n        const tavilyService = new TavilyService();\n        \n        // Configure search options\n        const searchOptions = {\n          numberOfTries: 1,\n          includeRawContent: true,\n          includedDomains: domain ? [domain] : undefined\n        };\n        \n        // Perform the search\n        const searchResponse = await tavilyService.search(\n          query,\n          'advanced',\n          2, // Limit to 5 results to conserve tokens\n          searchOptions,\n        );\n\n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n        \n        // Format the results\n        const formattedResults = searchResponse.results.map(result => ({\n          title: result.title,\n          url: result.url,\n          content: (result.rawContent || result.content).substring(0, 500) + ((result.rawContent || result.content).length > 500 ? '... // Truncated to save tokens' : ''),\n        }));\n\n\n        return JSON.stringify({\n          query,\n          results: formattedResults,\n          answer: searchResponse.answer || \"No direct answer available.\",\n          comment: 'Use this information to supplement your existing knowledge. Always cite sources when providing information from web searches.'\n        });\n      } catch (e: any) {\n        console.error('Error while searching the web', e);\n        return `Error while searching the web. Please try again. Error: ${e.message}`;\n      }\n    }\n  });\n};", "startLine": 11, "endLine": 95, "type": "util", "symbols": ["searchWeb"], "score": 0.4, "context": "This tool enables AI to search the web for up-to-date information, relevant for understanding external data integration in AI response generation.", "includesImports": false}, {"filePath": "src/lib/chat/tools/get-client-logs.ts", "content": "export const getClientLogs = ({\n  dataStream,\n  creditUsageTracker,\n  logs,\n  messageId\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n  logs: LogEntry[];\n  messageId: string;\n}) => {\n  return tool({\n    description: 'Gets client-side logs to help debug problems with the application. This will return the most recent logs (maximum 20) from the current session. You can filter by log type and source. [CRITICAL INSTRUCTION]: DO NOT CALL THIS TOOL MULTIPLE TIMES IN A ROW. The logs will be EXACTLY THE SAME each time you call this tool until the user interacts with the app again. Calling this tool multiple times is useless and wasteful. WAIT for the user to tell you they have tried your suggestion before calling this tool again. If you have already called this tool once, DO NOT call it again until the user confirms they have interacted with the app.',\n    parameters: z.object({\n      type: z.enum(['error', 'warn', 'info', 'log', 'debug', 'all']).default('all')\n        .describe('The type of logs to fetch. Use \"all\" to get logs of all types.'),\n      source: z.enum(['console', 'network', 'snack', 'all']).default('all')\n        .describe('The source of logs to fetch. Use \"all\" to get logs from all sources.'),\n      limit: z.number().min(1).max(20).default(10)\n        .describe('Maximum number of log entries to return (1-20)'),\n      reason: z.string()\n        .describe(\"Describe why you need these logs for debugging\")\n    }),\n    execute: async ({ type, source, limit, reason }: { \n      type: 'error' | 'warn' | 'info' | 'log' | 'debug' | 'all',\n      source: 'console' | 'network' | 'snack' | 'all',\n      limit: number,\n      reason: string \n    }) => {\n      try {\n\n        const toolTracker = ToolCountTracker.getInstance();\n        console.log('Tool call allowed (getClientLogs):', toolTracker.shouldAllowToolCall(messageId, 'getClientLogs'))\n\n        // Check if we should allow this tool call\n        if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'getClientLogs')) {\n          return {\n            result: null,\n            message: `⚠️ Tool call limit reached. \n            Client logs do not refresh while you are working on the code. You need to stop and ask the user to interact with the app in the preview to get new logs.\n            DO NOT CLAIM THAT YOU HAVE SUCCESSFULLY updated/fixed/created a task until you get a confirmation using logs. ASK the user to test the app and them tell them to confirm if the feature worked or not. \n            THEY are NON technical, they can't see the logs or give you the logs. HELP THEM, DO NOT CONFUSE or MISGUIDE THEM.\n            `,\n          };\n        }\n        console.log(`Fetching client logs. Type: ${type}, Source: ${source}, Limit: ${limit}, Reason: ${reason}`);\n\n        // Increment the tool count if we have a chat ID\n        if (messageId) {\n          toolTracker.incrementToolCount(messageId, 'getClientLogs');\n        }\n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n\n        // Filter logs based on parameters\n        let filteredLogs = [...logs];\n        \n        // Filter by type if not 'all'\n        if (type !== 'all') {\n          filteredLogs = filteredLogs.filter(log => log.type.toLowerCase() === type.toLowerCase());\n        }\n        \n        // Filter by source if not 'all'\n        if (source !== 'all') {\n          filteredLogs = filteredLogs.filter(log => log.source === source);\n        }\n        \n        // Sort by timestamp (newest first)\n        filteredLogs.sort((a, b) => b.timestamp - a.timestamp);\n        \n        // Limit the number of logs\n        filteredLogs = filteredLogs.slice(0, Math.min(limit, 20));\n        \n        // Format logs for readability and truncate large messages\n        const formattedLogs = filteredLogs.map(log => ({\n          timestamp: dayjs(log.timestamp).toISOString(),\n          type: log.type,\n          source: log.source,\n          message: truncateLogMessage(log.message)\n        }));\n        \n        // Check for critical issues in the logs\n        const criticalIssues = detectCriticalIssues(logs);\n        \n        return JSON.stringify({\n          logs: formattedLogs,\n          count: formattedLogs.length,\n          totalAvailable: logs.length,\n          criticalIssues: criticalIssues.length > 0 ? criticalIssues : undefined,\n          timestamp: new Date().toISOString(),\n          comment: formattedLogs.length > 0 \n            ? `These are the ${formattedLogs.length} most recent logs matching your criteria. Use them to diagnose any issues you're experiencing.`\n            : `No logs found matching your criteria. Try broadening your search or check if the issue might be elsewhere.`\n        });\n      } catch (e: any) {\n        console.error(`Error while fetching client logs`, e);\n        return JSON.stringify({\n          error: e.message,\n          timestamp: new Date().toISOString(),\n          comment: `Error while fetching client logs: ${e.message}. Please try again with different parameters.`\n        });\n      }\n    }\n  });\n};", "startLine": 13, "endLine": 117, "type": "util", "symbols": ["getClientLogs"], "score": 0.4, "context": "This tool fetches client logs for debugging, relevant for AI to gather runtime context during response generation.", "includesImports": false}, {"filePath": "src/lib/chat/tools/get-supabase-instructions.ts", "content": "export const getSupabaseInstructions = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n  project: Project;\n}) => {\n  return tool({\n    description: 'Get Supabase project instructions. This provides you with the supabase specific guides and instructions to understand and plan how to integrate supabase into the project.',\n    parameters: z.object({\n      reason: z.string().describe(\"Describe why you need the Supabase instructions\")\n    }),\n    execute: async ({ reason }: { reason: string }) => {\n      try {\n        console.log(`Fetching Supabase instructions. Reason: ${reason}`);\n        \n        // Create a new instance of the SupabaseIntegrationProvider\n        const supabaseIntegrationProvider = new SupabaseIntegrationProvider();\n        \n        // Get the latest instructions for the chat\n        const result =  supabaseIntegrationProvider.getSupabaseInitialInstructions();\n        \n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n        \n        return JSON.stringify({\n          instructions: result,\n          comment: 'Use this Supabase instruction to implement supabase features correctly. Call querySupabaseContext tool to fetch schema structure and proper RLS policies, secrets, functions, database functions. triggers.'\n        });\n      } catch (e: any) {\n        console.error('Error while fetching Supabase instructions', e);\n        return `Error while fetching Supabase instructions. Please try again. Error: ${e.message}`;\n      }\n    }\n  });\n};", "startLine": 12, "endLine": 49, "type": "util", "symbols": ["getSupabaseInstructions"], "score": 0.4, "context": "This tool provides Supabase project instructions to AI, relevant for context in AI response generation.", "includesImports": false}, {"filePath": "src/lib/chat/tools/get-supabase-logs.ts", "content": "export const getSupabaseLogs = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n  project: Project;\n}) => {\n  return tool({\n    description: 'Gets logs for a Supabase project by service type. Use this to help debug problems with your app. There are two ways to fetch edge function logs:\\n\\n1. By function name: Use `functionName: \"your-function-name\"` to fetch logs by the function name (easier for users to understand)\\n2. By function ID: Use `functionId: \"ce62b3db-daf3-44ca-b935-957570435829\"` if you know the specific ID\\n\\nThis will return logs from the last hour. If no logs are found, ask the user to test the functionality again to generate new logs, as only the user can trigger new function executions.',\n    parameters: z.object({\n      service: z.enum([\n        'api',\n        'branch-action',\n        'postgres',\n        'edge-function',\n        'auth',\n        'storage',\n        'realtime',\n      ]).describe('The service to fetch logs for'),\n      limit: z.number().optional().default(100).describe('Maximum number of log entries to return'),\n      functionId: z.string().optional().describe('Specific function ID to filter logs for a single edge function (only applicable when service is \"edge-function\"). If you know the ID, provide it directly.'),\n      functionName: z.string().optional().describe('Name of the function to fetch logs for (only applicable when service is \"edge-function\"). The tool will automatically find the matching function ID.'),\n      reason: z.string().describe(\"Describe why you need these logs for debugging\")\n    }),\n    execute: async ({ service, limit, functionId, functionName, reason }: { \n      service: 'api' | 'branch-action' | 'postgres' | 'edge-function' | 'auth' | 'storage' | 'realtime',\n      limit?: number,\n      functionId?: string,\n      functionName?: string,\n      reason: string \n    }) => {\n      try {\n        console.log(`Fetching Supabase ${service} logs. Reason: ${reason}. Limit: ${limit || 100}. ${functionId ? `Function ID: ${functionId}` : ''}${functionName ? `Function Name: ${functionName}` : ''}`);\n        \n        if (!project.connectionId || !project.supabaseProjectId) {\n          throw new Error('Project is not linked to a Supabase project');\n        }\n        \n        // Create a new instance of the SupabaseIntegrationProvider\n        const supabaseIntegrationProvider = new SupabaseIntegrationProvider();\n        \n        let resolvedFunctionId = functionId;\n        \n        // If functionName is provided but not functionId, try to find the function ID\n        if (service === 'edge-function' && functionName && !functionId) {\n          try {\n            // Get the functions list\n            const functionsResult = await supabaseIntegrationProvider.getProjectResources({\n              projectId: project.id,\n              resourceType: 'functions'\n            });\n            \n            // Find the function with the matching name\n            const functions = functionsResult?.functions || [];\n            const matchingFunction = functions.find(\n              (func: any) => func.name?.toLowerCase() === functionName.toLowerCase()\n            );\n            \n            if (matchingFunction) {\n              resolvedFunctionId = matchingFunction.id;\n              console.log(`Found function ID ${resolvedFunctionId} for function name ${functionName}`);\n            } else {\n              console.log(`Could not find function ID for function name ${functionName}`);\n            }\n          } catch (error) {\n            console.error('Error finding function ID from name:', error);\n          }\n        }\n        \n        // Use the getLogs method from SupabaseIntegrationProvider which uses SupabaseDebuggingTools\n        const logsData = await supabaseIntegrationProvider.getLogs({\n          projectId: project.id,\n          service,\n          limit: limit || 100,\n          functionId: resolvedFunctionId\n        });\n        \n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n\n        // For debugging\n        console.log('service', service);\n        console.log('logsData', logsData);\n        \n        // Format the response using the actual logs data from SupabaseIntegrationProvider\n        return JSON.stringify({\n          logs: logsData,\n          service,\n          ...(resolvedFunctionId && { functionId: resolvedFunctionId }),\n          ...(functionName && { functionName }),\n          timestamp: new Date().toISOString(),\n          comment: resolvedFunctionId\n            ? `These are the most recent logs for function ${functionName || ''} (ID: ${resolvedFunctionId}) from your Supabase project. Use them to diagnose any issues you're experiencing.`\n            : `These are the most recent ${service} logs from your Supabase project. Use them to diagnose any issues you're experiencing.`\n        });\n      } catch (e: any) {\n        console.error(`Error while fetching Supabase ${service} logs`, e);\n        return JSON.stringify({\n          error: e.message,\n          service,\n          ...(functionId && { functionId }),\n          ...(functionName && { functionName }),\n          timestamp: new Date().toISOString(),\n          comment: `Error while fetching Supabase ${service} logs: ${e.message}. Please try again or check your Supabase connection.`\n        });\n      }\n    }\n  });\n};\n", "startLine": 14, "endLine": 125, "type": "util", "symbols": ["getSupabaseLogs"], "score": 0.4, "context": "This tool fetches Supabase logs for debugging, relevant for AI to understand backend runtime context.", "includesImports": false}, {"filePath": "src/lib/chat/tools/manage-supabase-auth.ts", "content": "export const manageSupabaseAuth = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n  project: Project;\n}) => {\n  return tool({\n    description: 'Manages Supabase auth configuration. Use this tool to view current auth settings, update auth configuration, or retrieve SSO provider information. This helps with configuring authentication options like email templates, redirect URLs, and external OAuth providers.',\n    parameters: z.object({\n      action: z.enum([\n        'getAuthConfig',\n        'updateAuthConfig',\n        'getSSOProviders'\n      ]).describe('The action to perform on the auth configuration'),\n      authConfig: z.record(z.any()).optional().describe('The auth configuration to update (only required for updateAuthConfig action). Same format as the response from getAuthConfig. Pass only the fields to update.'),\n      reason: z.string().describe(\"Describe why you need to access or modify the auth configuration\")\n    }),\n    execute: async ({ action, authConfig, reason }: { \n      action: 'getAuthConfig' | 'updateAuthConfig' | 'getSSOProviders',\n      authConfig?: UpdateProjectAuthConfigRequestBody,\n      reason: string \n    }) => {\n      try {\n        console.log(`Performing Supabase auth action: ${action}. Reason: ${reason}.`);\n        if(authConfig) {\n          console.log('Updating', authConfig)\n        }\n        \n        if (!project.connectionId || !project.supabaseProjectId) {\n          throw new Error('Project is not linked to a Supabase project');\n        }\n        \n        // Create a new instance of the SupabaseIntegrationProvider\n        const supabaseIntegrationProvider = new SupabaseIntegrationProvider();\n        \n        let result;\n        \n        // Perform the requested action\n        switch (action) {\n          case 'getAuthConfig':\n            result = await supabaseIntegrationProvider.getAuthConfig(project.id);\n            break;\n          case 'updateAuthConfig':\n            if (!authConfig) {\n              throw new Error('Auth configuration is required for updateAuthConfig action');\n            }\n            result = await supabaseIntegrationProvider.updateAuthConfig(project.id, authConfig);\n            break;\n          case 'getSSOProviders':\n            result = await supabaseIntegrationProvider.getSSOProviders(project.id);\n            break;\n        }\n        \n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n        \n        // Format the response\n        return JSON.stringify({\n          action,\n          result,\n          timestamp: new Date().toISOString(),\n          comment: getActionComment(action, result)\n        });\n      } catch (e: any) {\n        console.error(`Error while performing Supabase auth action: ${action}`, e);\n        return JSON.stringify({\n          error: e.message,\n          action,\n          timestamp: new Date().toISOString(),\n          comment: `Error while performing Supabase auth action ${action}: ${e.message}. Please try again or check your Supabase connection.`\n        });\n      }\n    }\n  });\n};", "startLine": 13, "endLine": 90, "type": "util", "symbols": ["manageSupabaseAuth"], "score": 0.4, "context": "This tool manages Supabase auth configuration, relevant for AI to handle auth-related response generation.", "includesImports": false}, {"filePath": "src/lib/chat/tools/index.ts", "content": "// Export all tools from this directory\nexport * from './get-supabase-instructions';\nexport * from './get-supabase-logs';\nexport * from './get-client-logs';\nexport * from './get-file-contents';\nexport * from './create-file.tool';\nexport * from './edit-file.tool';\nexport * from './query-codebase';\nexport * from './add-ai-memory';\nexport * from './tool-count-tracker';\nexport * from './search-web';\nexport * from './generate-design.tool';\nexport * from './multi-perspective-analysis.tool';\nexport * from './display-multi-perspective-analysis';\n", "startLine": 1, "endLine": 15, "type": "unknown", "symbols": ["chat tools exports"], "score": 0.3, "context": "Exports all chat tools which are used in AI response generation and streaming, relevant for understanding available tools in the flow.", "includesImports": false}], "additionalFiles": []}}