{"timestamp": "2025-06-15T06:33:24.803Z", "query": "How does the payment work in the app? Where can we change to fix subscription renewal bug related to credit not getting reset?", "executionTime": 16599, "snippetsCount": 4, "additionalFilesCount": 0, "totalLines": 221, "snippets": [{"filePath": "src/lib/subscription.ts", "type": "util", "context": "This function checks the subscription status of a user, including credits and credits used, which is directly related to how payment and subscription renewal works and where credits are managed and reset.", "score": 1, "lines": 83, "startLine": 19, "endLine": 101, "symbols": ["checkSubscriptionStatus"], "preview": "export async function checkSubscriptionStatus(userId: string, isAnonymous?: boolean): Promise<SubscriptionStatus> {\n  // Get appropriate plan based on user type\n  const freePlan = PLANS.find(plan => plan.tier === \"free\");\n  const anonymousPlan = PLANS.find(plan => plan.tier === \"anonymous\");\n  \n..."}, {"filePath": "src/lib/subscription.ts", "type": "util", "context": "This function checks the message limit based on subscription status and credits used, relevant to understanding how subscription renewal and credit usage is enforced.", "score": 0.9, "lines": 70, "startLine": 103, "endLine": 172, "symbols": ["checkMessageLimit"], "preview": "export async function checkMessageLimit(userId: string, isAnonymous: boolean) {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n\n  // Get subscription status first (which already has creditsUsed for paid plans)\n..."}, {"filePath": "src/lib/db/schema.ts", "type": "unknown", "context": "The subscriptions table schema defines the fields related to subscription status, credits, creditsUsed, resetDate, and other billing related fields, which are critical to understanding and fixing subscription renewal and credit reset bugs.", "score": 0.9, "lines": 39, "startLine": 378, "endLine": 416, "symbols": ["subscriptions"], "preview": "export const subscriptions = pgTable('Subscription', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  userId: varchar('userId', { length: 255 }).notNull(),\n  status: varchar('status', { length: 50 }).notNull().default('inactive'),\n  planId: varchar('planId', { length: 50 }).notNull().default('free'),\n..."}, {"filePath": "src/lib/db/admin-queries.ts", "type": "util", "context": "This function updates subscription status on cancellation and includes logic to update resetDate, which is related to subscription renewal and possibly credit reset.", "score": 0.8, "lines": 29, "startLine": 1539, "endLine": 1567, "symbols": ["updateSubscriptionOnCancel"], "preview": "        projectId: msg.projectId || '',\n        userId: msg.userId || '',\n        createdAt: msg.createdAt,\n        category: errorCategory,\n        content: msg.content,\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/subscription.ts", "content": "export async function checkSubscriptionStatus(userId: string, isAnonymous?: boolean): Promise<SubscriptionStatus> {\n  // Get appropriate plan based on user type\n  const freePlan = PLANS.find(plan => plan.tier === \"free\");\n  const anonymousPlan = PLANS.find(plan => plan.tier === \"anonymous\");\n  \n  // Skip subscription check for anonymous users\n  if (isAnonymous) {\n    // For anonymous users, count messages from token consumption\n    const result = await db\n      .select({\n        messageCount: sql<number>`COUNT(*)`\n      })\n      .from(tokenConsumption)\n      .where(\n        eq(tokenConsumption.userId, userId)\n      );\n\n    // Ensure we have a number by explicitly converting\n    const messagesUsed = Number(result[0]?.messageCount || 0);\n    const anonMessageLimit = anonymousPlan?.operations || 100;\n    \n    return {\n      isActive: false,\n      status: 'active',\n      plan: 'anonymous',\n      expiresAt: null,\n      credits: anonMessageLimit,  // Now represents message limit\n      creditsUsed: messagesUsed,  // Now represents messages used\n      creditsRemaining: Math.max(0, anonMessageLimit - messagesUsed)\n    };\n  }\n  \n  // For authenticated users, check if they have an active subscription\n  const userSubscription = await db\n    .select()\n    .from(subscriptions)\n    .where(and(\n        eq(subscriptions.userId, userId),\n        eq(subscriptions.isActive, true)\n    ))\n    .orderBy(desc(subscriptions.updatedAt))\n    .limit(1)\n    .then(results => results[0]);\n\n  if (!userSubscription || !userSubscription.isActive) {\n    // For free tier users, count messages from token consumption\n    const result = await db\n      .select({\n        messageCount: sql<number>`COUNT(*)`\n      })\n      .from(tokenConsumption)\n      .where(\n        eq(tokenConsumption.userId, userId)\n      );\n\n    // Ensure we have a number by explicitly converting\n    const messagesUsed = Number(result[0]?.messageCount || 0);\n    const freeMessageLimit = freePlan?.operations || 50;\n\n    return {\n      isActive: false,\n      plan: 'free',\n      status: 'active',\n      expiresAt: null,\n      credits: freeMessageLimit, // Free tier message limit\n      creditsUsed: messagesUsed, // Messages used\n      creditsRemaining: Math.max(0, freeMessageLimit - messagesUsed)\n    };\n  }\n\n  // User has an active subscription - use subscription.creditsUsed as source of truth for message count\n  return {\n    expiryDate: userSubscription.resetDate ? userSubscription.resetDate.toISOString() : undefined,\n    isActive: !!userSubscription.isActive,\n    subscriptionId: userSubscription.id,\n    status: userSubscription.status as any,\n    plan: userSubscription.planId as PlanTier,\n    expiresAt: userSubscription.resetDate, // Use resetDate as the expiration date\n    credits: userSubscription.credits,      // Now represents message limit\n    creditsUsed: userSubscription.creditsUsed || 0, // Now represents messages used\n    creditsRemaining: Math.max(0, userSubscription.credits - (userSubscription.creditsUsed || 0))\n  };\n}", "startLine": 19, "endLine": 101, "type": "util", "symbols": ["checkSubscriptionStatus"], "score": 1, "context": "This function checks the subscription status of a user, including credits and credits used, which is directly related to how payment and subscription renewal works and where credits are managed and reset.", "includesImports": false}, {"filePath": "src/lib/subscription.ts", "content": "export async function checkMessageLimit(userId: string, isAnonymous: boolean) {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n\n  // Get subscription status first (which already has creditsUsed for paid plans)\n  const subscription = await checkSubscriptionStatus(userId, isAnonymous);\n  \n  // For consistency, use the creditsUsed from subscription status (now represents messages)\n  const messagesUsed = subscription.creditsUsed;\n\n  // Get plan details\n  const plan = getPlanByTier(subscription.plan);\n  \n  // Get daily limit from plan\n  const dailyLimit = plan.dailyLimit;\n  \n  // Check if plan has a daily limit (not -1)\n  if (dailyLimit > 0) {\n    // Count messages used today\n    const todayResult = await db\n      .select({\n        todayMessageCount: sql<number>`COUNT(*)`\n      })\n      .from(tokenConsumption)\n      .where(\n        and(\n          eq(tokenConsumption.userId, userId),\n          gte(tokenConsumption.createdAt, today)\n        )\n      );\n    \n    // Ensure we have a number by explicitly converting\n    const todayMessagesUsed = Number(todayResult[0]?.todayMessageCount || 0);\n    const dailyRemaining = Math.max(0, dailyLimit - todayMessagesUsed);\n    \n    // Check both daily and monthly message limits\n    return {\n      canSendMessage: todayMessagesUsed < dailyLimit && messagesUsed < subscription.credits,\n      remaining: Math.min(\n        dailyRemaining,\n        Math.max(0, subscription.credits - messagesUsed)\n      ),\n      limit: subscription.credits,\n      dailyLimit: dailyLimit,\n      dailyRemaining: dailyRemaining,\n      isPro: subscription.plan !== 'free' && subscription.plan !== 'anonymous',\n      isAnonymous: subscription.plan === 'anonymous',\n      planTier: subscription.plan,\n      totalCredits: subscription.credits,      // Now represents total message limit\n      creditsUsed: messagesUsed,               // Now represents messages used\n      creditsRemaining: Math.max(0, subscription.credits - messagesUsed) // Messages remaining\n    };\n  }\n\n  // Plans without daily limit (dailyLimit = -1)\n  return {\n    canSendMessage: subscription.creditsRemaining > 0,\n    remaining: subscription.creditsRemaining,\n    limit: subscription.credits,\n    isPro: subscription.plan !== 'free' && subscription.plan !== 'anonymous',\n    isAnonymous: subscription.plan === 'anonymous',\n    planTier: subscription.plan,\n    totalCredits: subscription.credits,      // Now represents total message limit\n    creditsUsed: subscription.creditsUsed,   // Now represents messages used\n    creditsRemaining: subscription.creditsRemaining, // Messages remaining\n    // Include dailyLimit as -1 to indicate unlimited\n    dailyLimit: -1,\n    dailyRemaining: -1\n  };\n}", "startLine": 103, "endLine": 172, "type": "util", "symbols": ["checkMessageLimit"], "score": 0.9, "context": "This function checks the message limit based on subscription status and credits used, relevant to understanding how subscription renewal and credit usage is enforced.", "includesImports": false}, {"filePath": "src/lib/db/schema.ts", "content": "export const subscriptions = pgTable('Subscription', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  userId: varchar('userId', { length: 255 }).notNull(),\n  status: varchar('status', { length: 50 }).notNull().default('inactive'),\n  planId: varchar('planId', { length: 50 }).notNull().default('free'),\n  subscriptionId: varchar('subscriptionId', { length: 255 }).unique(),\n  credits: integer('credits').notNull().default(50), // Default to free tier credits\n  creditsUsed: integer('creditsUsed').notNull().default(0),\n  isActive: boolean('isActive').default(false),\n  // LemonSqueezy fields\n  lemonSqueezyCustomerId: varchar('lemonSqueezyCustomerId'),\n  lemonSqueezySubscriptionId: varchar('lemonSqueezySubscriptionId'),\n  lemonSqueezyOrderId: varchar('lemonSqueezyOrderId'),\n  lemonSqueezyVariantId: varchar('lemonSqueezyVariantId'),\n  provider: varchar('provider'),\n  // Stripe fields (for compatibility)\n  stripeCustomerId: varchar('stripeCustomerId'),\n  stripeSubscriptionId: varchar('stripeSubscriptionId'),\n  stripePriceId: varchar('stripePriceId'),\n  stripeCurrentPeriodEnd: timestamp('stripeCurrentPeriodEnd'),\n  stripeVariantId: timestamp('stripeVariantId'),\n  // Billing cycle\n  resetDate: timestamp('resetDate'),\n  // Flags\n  isDowngraded: boolean('isDowngraded').default(false),\n  // Additional metadata (cancellation feedback, etc.)\n  metadata: json('metadata').$type<Record<string, any>>(),\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n}, (table) => {\n  return {\n    userIdIdx: index('subscription_userId_idx').on(table.userId),\n    statusIdx: index('subscription_status_idx').on(table.status),\n    userIdUpdatedAtIdx: index('subscription_userId_updatedAt_idx').on(table.userId, table.updatedAt),\n    isActiveIdx: index('subscription_isActive_idx').on(table.isActive)\n  };\n});\n\nexport type Subscription = InferSelectModel<typeof subscriptions>;", "startLine": 378, "endLine": 416, "type": "unknown", "symbols": ["subscriptions"], "score": 0.9, "context": "The subscriptions table schema defines the fields related to subscription status, credits, creditsUsed, resetDate, and other billing related fields, which are critical to understanding and fixing subscription renewal and credit reset bugs.", "includesImports": false}, {"filePath": "src/lib/db/admin-queries.ts", "content": "        projectId: msg.projectId || '',\n        userId: msg.userId || '',\n        createdAt: msg.createdAt,\n        category: errorCategory,\n        content: msg.content,\n        // Extract a snippet of the error message for display\n        snippet: displayText.substring(0, 300) + (displayText.length > 300 ? '...' : '')\n      };\n    }).filter(Boolean) as any[];\n    \n    // Get error counts by category\n    const categoryCounts: Record<string, number> = {};\n    processedErrors.forEach(error => {\n      if (!error) return;\n      categoryCounts[error.category] = (categoryCounts[error.category] || 0) + 1;\n    });\n    \n    // Get error counts by day for the time range\n    const dateRange: string[] = [];\n    for (let i = 0; i < timeRange; i++) {\n      const date = new Date();\n      date.setDate(date.getDate() - i);\n      date.setHours(0, 0, 0, 0);\n      dateRange.unshift(date.toISOString().split('T')[0]);\n    }\n    \n    const errorsByDate: Record<string, number> = {};\n    dateRange.forEach(date => {\n      errorsByDate[date] = 0;", "startLine": 1539, "endLine": 1567, "type": "util", "symbols": ["updateSubscriptionOnCancel"], "score": 0.8, "context": "This function updates subscription status on cancellation and includes logic to update resetDate, which is related to subscription renewal and possibly credit reset.", "includesImports": false}], "additionalFiles": []}}