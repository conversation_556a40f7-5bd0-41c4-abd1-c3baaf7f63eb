{"timestamp": "2025-06-15T06:28:01.450Z", "query": "How does the payment work in the app? Where can we change to fix subscription renewal bug?", "executionTime": 6358, "snippetsCount": 6, "additionalFilesCount": 0, "totalLines": 1985, "snippets": [{"filePath": "src/lib/services/supabase-context-engine.ts", "type": "unknown", "context": "This class handles resource discovery and retrieval from Supabase, crucial for understanding subscription and payment management.", "score": 1, "lines": 469, "startLine": 22, "endLine": 490, "symbols": ["SupabaseContextEngine"], "preview": "export class SupabaseContextEngine {\n    private supabaseProvider: SupabaseIntegrationProvider;\n    private project: Project;\n    private resourceIndex: Record<string, string[]> = {};\n    private resourceDetails: Record<string, any> = {};\n..."}, {"filePath": "src/lib/db/schema.ts", "type": "unknown", "context": "Contains schema definitions for subscriptions, users, and related entities, essential for understanding how subscription data is stored and managed.", "score": 0.9, "lines": 564, "startLine": 17, "endLine": 580, "symbols": ["Database schema"], "preview": "export const user = pgTable('User', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  email: varchar('email', { length: 64 }).notNull(),\n  password: varchar('password', { length: 64 }),\n  name: varchar(),\n..."}, {"filePath": "src/lib/services/two-stage-context-engine.ts", "type": "unknown", "context": "This class manages code snippet identification within files, relevant for fixing subscription renewal bugs if related code snippets are involved.", "score": 0.8, "lines": 677, "startLine": 27, "endLine": 703, "symbols": ["TwoStageLLMContextEngine"], "preview": "export class TwoStageLLMContextEngine {\n  private files: FileItem[] = [];\n  private fileIndex: Map<string, FileItem> = new Map();\n\n  /**\n..."}, {"filePath": "src/lib/db/project-queries.ts", "type": "util", "context": "Includes functions for retrieving and updating project info, relevant for understanding project-specific subscription and payment details.", "score": 0.8, "lines": 1, "startLine": 102, "endLine": 102, "symbols": ["Project queries"], "preview": ""}, {"filePath": "src/lib/services/stream-service.ts", "type": "unknown", "context": "This class manages AI streaming, which may include payment handling for API calls, relevant for understanding payment flow.", "score": 0.7, "lines": 273, "startLine": 110, "endLine": 382, "symbols": ["StreamService"], "preview": "export class StreamService {\n    private config: StreamServiceConfig;\n    private toolCallCosts: ToolCallCost[] = [];\n\n    constructor(config: StreamServiceConfig) {\n..."}, {"filePath": "src/lib/db/queries.ts", "type": "unknown", "context": "Contains migration scripts that define schema, indirectly relevant for understanding data structure but not core logic.", "score": 0.2, "lines": 1, "startLine": 1671, "endLine": 1671, "symbols": ["Migration script"], "preview": "}"}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/services/supabase-context-engine.ts", "content": "export class SupabaseContextEngine {\n    private supabaseProvider: SupabaseIntegrationProvider;\n    private project: Project;\n    private resourceIndex: Record<string, string[]> = {};\n    private resourceDetails: Record<string, any> = {};\n    private fullResources: {\n        schema: any[];\n        functions: any[];\n        secrets: any[];\n        rlsPolicies: any[];\n        dbFunctions: any[];\n        triggers: any[];\n        storageBuckets: any[];\n    } | null = null;\n\n    /**\n     * Initialize with project\n     */\n    constructor(project: Project) {\n        this.project = project;\n        this.supabaseProvider = new SupabaseIntegrationProvider();\n    }\n\n    /**\n     * Build a lightweight index of all Supabase resources\n     * This is the first stage of the two-stage approach\n     */\n    async buildResourceIndex(): Promise<Record<string, string[]>> {\n        console.time('build-resource-index');\n\n        if (!this.project.connectionId || !this.project.supabaseProjectId) {\n            throw new Error('Project is not connected to Supabase');\n        }\n\n        console.log(`[SupabaseContextEngine] Building resource index for project: ${this.project.supabaseProjectId}`);\n\n        try {\n            // Use the existing getLatestInstructionsForChat method to fetch all resources at once\n            const {\n                schema,\n                functions,\n                secrets,\n                rlsPolicies,\n                dbFunctions,\n                triggers,\n                storageBuckets\n            } = await this.supabaseProvider.getLatestInstructionsForChat({\n                project: this.project\n            });\n\n            // Initialize resource index with all resource types\n            this.resourceIndex = {\n                tables: [],\n                functions: [],\n                policies: [],\n                buckets: [],\n                dbFunctions: [],\n                triggers: [],\n                secrets: []\n            };\n\n            // Extract resource names for the index\n            if (Array.isArray(schema)) {\n                this.resourceIndex.tables = schema\n                    .filter(table => table && typeof table === 'object' && 'table_name' in table)\n                    .map(table => (table as any).table_name);\n            }\n\n            if (Array.isArray(functions)) {\n                this.resourceIndex.functions = functions\n                    .filter(func => func && typeof func === 'object' && 'name' in func)\n                    .map(func => func.name);\n            }\n\n            if (Array.isArray(rlsPolicies)) {\n                this.resourceIndex.policies = rlsPolicies\n                    .filter(policy => policy && typeof policy === 'object' && 'table' in policy && 'policy_name' in policy)\n                    .map(policy => `${(policy as any).table}:${(policy as any).policy_name}`);\n            }\n\n            if (Array.isArray(storageBuckets)) {\n                this.resourceIndex.buckets = storageBuckets\n                    .filter(bucket => bucket && typeof bucket === 'object' && 'name' in bucket)\n                    .map(bucket => bucket.name);\n            }\n\n            if (Array.isArray(dbFunctions)) {\n                this.resourceIndex.dbFunctions = dbFunctions\n                    .filter(func => func && typeof func === 'object' && 'function_name' in func)\n                    .map(func => (func as any).function_name);\n            }\n\n            if (Array.isArray(triggers)) {\n                this.resourceIndex.triggers = triggers\n                    .filter(trigger => trigger && typeof trigger === 'object' && 'trigger_name' in trigger)\n                    .map(trigger => (trigger as any).trigger_name);\n            }\n\n            if (Array.isArray(secrets)) {\n                this.resourceIndex.secrets = secrets\n                    .filter(secret => secret && typeof secret === 'object' && 'name' in secret)\n                    .map(secret => secret.name);\n            }\n\n            // Store the full resources for later detailed retrieval\n            this.fullResources = {\n                schema: Array.isArray(schema) ? schema : [],\n                functions: Array.isArray(functions) ? functions : [],\n                secrets: Array.isArray(secrets) ? secrets : [],\n                rlsPolicies: Array.isArray(rlsPolicies) ? rlsPolicies : [],\n                dbFunctions: Array.isArray(dbFunctions) ? dbFunctions : [],\n                triggers: Array.isArray(triggers) ? triggers : [],\n                storageBuckets: Array.isArray(storageBuckets) ? storageBuckets : []\n            };\n\n            console.log('[SupabaseContextEngine] Resource index built:', {\n                tables: this.resourceIndex.tables.length,\n                functions: this.resourceIndex.functions.length,\n                policies: this.resourceIndex.policies.length,\n                buckets: this.resourceIndex.buckets.length,\n                dbFunctions: this.resourceIndex.dbFunctions.length,\n                triggers: this.resourceIndex.triggers.length,\n                secrets: this.resourceIndex.secrets.length\n            });\n\n            return this.resourceIndex;\n        } catch (error) {\n            console.error('[SupabaseContextEngine] Error building resource index:', error);\n            throw error;\n        } finally {\n            console.timeEnd('build-resource-index');\n        }\n    }\n\n\n    /**\n     * First stage: Find relevant resources for a query\n     */\n    async findRelevantResources(query: string): Promise<{\n        resources: ResourceIdentification[],\n        reasoning: string,\n        hasAdditional: boolean,\n        additionalResourcesSummary?: string,\n        mustRetrieveAdditionalResources?: boolean\n    }> {\n        console.time('find-relevant-resources');\n\n        // Make sure we have the resource index\n        if (Object.keys(this.resourceIndex).length === 0) {\n            await this.buildResourceIndex();\n        }\n\n        const prompt = `Query: \"${query}\"\n\nAvailable Supabase Resources:\n${Object.entries(this.resourceIndex)\n            .map(([type, names]) => `${type.toUpperCase()}: ${names.join('\\n ')}`)\n            .join('\\n\\n')}\n\nSelect the most relevant resources for this query. Limit your selection to at most 10 resources total.`\n\n        // Use LLM to identify relevant resources\n        const result = await generateObject({\n            model: customModel('openai/gpt-4.1'), // Using a smaller model to reduce costs\n            temperature: 0.1,\n            schema: z.object({\n                resources: z.array(z.object({\n                    resourceType: z.enum(['table', 'function', 'policy', 'bucket', 'dbFunction', 'trigger', 'secret']).describe(\"Type of resource. Must match the exact terms.\"),\n                    resourceName: z.string().describe(\"Name of the resource\"),\n                    relevanceScore: z.number().min(1).max(10).describe(\"Relevance score from 1-10\"),\n                    reasoning: z.string().describe(\"Why this resource is relevant to the query\")\n                })),\n                reasoning: z.string().describe(\"Explanation of why these resources were selected\"),\n                hasAdditional: z.boolean().describe('Apart from the 10 returned, are there more possible resources that may relate to the query?'),\n                additionalResourcesSummary: z.string().optional().describe(`List the SPECIFIC additional resources from the provided resource list that are relevant but weren't included in your selection due to the 10-resource limit.\n        Be definitive about which resources exist and are relevant - do NOT use hypothetical language like \"if X exists\" when referring to resources you can see in the provided list.\n        Use precise resource names from the available resources list.\n        Explain exactly why each additional resource is relevant and how it relates to the resources you've already selected.\n        If certain resources are critical for understanding the system, use direct language like \"You MUST query for X because it is essential for understanding Y.\"\n        `),\n                mustRetrieveAdditionalResources: z.boolean().optional().describe('If additional resources must be retrieved, set this to true. This will force LLM to request additional resources on the basis of the additionalResourcesSummary.'),\n            }),\n            system: `You are a Supabase database expert. Your task is to identify which resources in a Supabase project are most relevant to a specific query.\n\nYou will be given:\n1. A query about the Supabase project\n2. A list of available resources categorized by type\n\nYour job is to select the most relevant resources that would help answer the query, including resources that reveal important relationships between entities. Consider the following guidelines:\n\n1. IDENTIFY CORE RESOURCES: First identify the primary resources directly mentioned in the query.\n\n2. IDENTIFY RELATED RESOURCES: Then identify resources that have relationships with the primary resources:\n   - Tables that reference each other through foreign keys\n   - Functions that operate on identified tables\n   - Policies that control access to identified tables\n   - Triggers that affect identified tables\n   - Secrets that might be used by identified functions\n\n3. CONSIDER DATA FLOW: Think about how data flows through the application. If a query mentions user authentication, consider not just the users table, but also related tables like profiles, sessions, or audit logs.\n\n4. SET mustRetrieveAdditionalResources=true when:\n   - Critical relationship information is missing\n   - The query cannot be properly answered without additional context\n   - There are clear dependencies on resources not included in the initial selection\n\n5. PROVIDE CLEAR REASONING: For each resource, explain why it's relevant and how it relates to other resources.\n\nIMPORTANT: Balance between being comprehensive and focused. Don't include everything, but don't miss critical relationships either.`,\n            prompt\n        });\n\n        console.timeEnd('find-relevant-resources');\n        return result.object;\n    }\n\n    /**\n     * Second stage: Get detailed information about specific resources\n     */\n    async getResourceDetails(resources: ResourceIdentification[]): Promise<Record<string, any>> {\n        console.time('get-resource-details');\n\n        if (!this.fullResources) {\n            // If we don't have the full resources cached, rebuild the index\n            await this.buildResourceIndex();\n\n            if (!this.fullResources) {\n                throw new Error('Failed to build resource index');\n            }\n        }\n\n        const details: Record<string, any> = {\n            tables: [],\n            functions: [],\n            policies: [],\n            buckets: [],\n            dbFunctions: [],\n            triggers: [],\n            secrets: []\n        };\n\n        // Group resources by type\n        const resourcesByType: Record<string, string[]> = {\n            table: [],\n            function: [],\n            policy: [],\n            bucket: [],\n            dbFunction: [],\n            trigger: [],\n            secret: []\n        };\n\n        // Populate resourcesByType\n        for (const resource of resources) {\n            if (resourcesByType[resource.resourceType]) {\n                resourcesByType[resource.resourceType].push(resource.resourceName);\n            }\n        }\n\n        // Get details for each resource type from the cached full resources\n        if (resourcesByType.table.length > 0 && Array.isArray(this.fullResources.schema)) {\n            details.tables = this.fullResources.schema.filter(table =>\n                table && typeof table === 'object' && 'table_name' in table &&\n                resourcesByType.table.includes(table.table_name)\n            );\n        }\n\n        if (resourcesByType.function.length > 0 && Array.isArray(this.fullResources.functions)) {\n            details.functions = this.fullResources.functions.filter(func =>\n                func && typeof func === 'object' && 'name' in func &&\n                resourcesByType.function.includes(func.name)\n            );\n        }\n\n        if (resourcesByType.policy.length > 0 && Array.isArray(this.fullResources.rlsPolicies)) {\n            // Policies are in format \"table:policy\"\n            const policyMap = resourcesByType.policy.map(p => {\n                const [tableName, policyName] = p.split(':');\n                return {tableName, policyName};\n            });\n\n            details.policies = this.fullResources.rlsPolicies.filter(policy =>\n                policy && typeof policy === 'object' && 'table' in policy && 'policy_name' in policy &&\n                policyMap.some(p => p.tableName === policy.table && p.policyName === policy.policy_name)\n            );\n        }\n\n        if (resourcesByType.bucket.length > 0 && Array.isArray(this.fullResources.storageBuckets)) {\n            details.buckets = this.fullResources.storageBuckets.filter(bucket =>\n                bucket && typeof bucket === 'object' && 'name' in bucket &&\n                resourcesByType.bucket.includes(bucket.name)\n            );\n        }\n\n        if (resourcesByType.dbFunction.length > 0 && Array.isArray(this.fullResources.dbFunctions)) {\n            details.dbFunctions = this.fullResources.dbFunctions.filter(func =>\n                func && typeof func === 'object' && 'function_name' in func &&\n                resourcesByType.dbFunction.includes(func.function_name)\n            );\n        }\n\n        if (resourcesByType.trigger.length > 0 && Array.isArray(this.fullResources.triggers)) {\n            details.triggers = this.fullResources.triggers.filter(trigger =>\n                trigger && typeof trigger === 'object' && 'trigger_name' in trigger &&\n                resourcesByType.trigger.includes(trigger.trigger_name)\n            );\n        }\n\n        if (resourcesByType.secret.length > 0 && Array.isArray(this.fullResources.secrets)) {\n            details.secrets = this.fullResources.secrets.filter(secret =>\n                secret && typeof secret === 'object' && 'name' in secret &&\n                resourcesByType.secret.includes(secret.name)\n            );\n        }\n\n        console.timeEnd('get-resource-details');\n        this.resourceDetails = details;\n        return details;\n    }\n\n    // The fetch methods are no longer needed as we're using the cached resources from SupabaseIntegrationProvider\n\n    /**\n     * Helper function to normalize resource type for matching\n     * Handles case insensitivity and partial matches like 'function', 'functions', 'fun', etc.\n     */\n    private normalizeResourceType(resourceType: string): string | null {\n        resourceType = resourceType.toLowerCase();\n\n        // Map of partial/alternative names to canonical resource types\n        const resourceTypeMap: Record<string, string> = {\n            'table': 'table',\n            'tables': 'table',\n            'tab': 'table',\n            'tbl': 'table',\n\n            'function': 'function',\n            'functions': 'function',\n            'func': 'function',\n            'fun': 'function',\n            'fn': 'function',\n\n            'policy': 'policy',\n            'policies': 'policy',\n            'pol': 'policy',\n\n            'bucket': 'bucket',\n            'buckets': 'bucket',\n            'bkt': 'bucket',\n            'storage': 'bucket',\n\n            'dbfunction': 'dbFunction',\n            'dbfunctions': 'dbFunction',\n            'dbfunc': 'dbFunction',\n            'dbfn': 'dbFunction',\n            'db_function': 'dbFunction',\n\n            'trigger': 'trigger',\n            'triggers': 'trigger',\n            'trig': 'trigger',\n\n            'secret': 'secret',\n            'secrets': 'secret',\n            'sec': 'secret'\n        };\n\n        // Try exact match first\n        if (resourceTypeMap[resourceType]) {\n            return resourceTypeMap[resourceType];\n        }\n\n        // Try partial match\n        for (const [key, value] of Object.entries(resourceTypeMap)) {\n            if (key.startsWith(resourceType) || resourceType.startsWith(key)) {\n                return value;\n            }\n        }\n\n        return null; // No match found\n    }\n\n    /**\n     * Check if a resource should be excluded based on the excludedResources list\n     */\n    private shouldExcludeResource(resource: ResourceIdentification, excludedResources: string[]): boolean {\n        if (!excludedResources || excludedResources.length === 0) {\n            return false;\n        }\n\n        for (const excludedResource of excludedResources) {\n            const parts = excludedResource.split('.');\n            if (parts.length !== 2) continue;\n\n            const [excludedType, excludedName] = parts;\n            const normalizedExcludedType = this.normalizeResourceType(excludedType);\n\n            if (!normalizedExcludedType) continue;\n\n            // Check if the resource type matches and the name matches or is a wildcard\n            if (normalizedExcludedType === resource.resourceType &&\n                (excludedName === '*' || excludedName.toLowerCase() === resource.resourceName.toLowerCase())) {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Main method to get relevant Supabase resources for a query\n     * @param query The natural language query to find relevant resources for\n     * @param excludedResources Optional array of resources to exclude in the format \"resourceType.resourceName\"\n     */\n    async getRelevantSupabaseResources(query: string, excludedResources?: string[]): Promise<any> {\n        // Stage 1: Find relevant resources\n        const {\n            resources: allResources,\n            reasoning,\n            additionalResourcesSummary,\n            hasAdditional,\n            mustRetrieveAdditionalResources\n        } = await this.findRelevantResources(query);\n\n        // Filter out excluded resources if any\n        let excludedCount = 0;\n        const resources = excludedResources && excludedResources.length > 0\n            ? allResources.filter(resource => {\n                const shouldExclude = this.shouldExcludeResource(resource, excludedResources);\n                if (shouldExclude) excludedCount++;\n                return !shouldExclude;\n            })\n            : allResources;\n\n        if (excludedCount > 0) {\n            console.log(`[SupabaseContextEngine] Excluded ${excludedCount} resources based on user preferences`);\n        }\n\n        if (resources.length === 0) {\n            console.log(\"[SupabaseContextEngine] No relevant resources found\");\n            return {\n                query,\n                resources: [],\n                reasoning: reasoning || \"No relevant resources found\",\n                details: {}\n            };\n        }\n\n        // Stage 2: Get details for those resources\n        const details = await this.getResourceDetails(resources);\n\n        // Calculate token usage metrics\n        const result = {\n            query,\n            resources,\n            reasoning,\n            details,\n            additionalResourcesSummary,\n            hasAdditional,\n            mustRetrieveAdditionalResources,\n            excludedResources: excludedResources || []\n        };\n\n        // Log metrics\n        const resultString = JSON.stringify(result);\n        console.log(`[SupabaseContextEngine] Result size: ${resultString.length} chars / ~${Math.ceil(resultString.length / 4)} tokens`);\n\n        return result;\n    }\n}", "startLine": 22, "endLine": 490, "type": "unknown", "symbols": ["SupabaseContextEngine"], "score": 1, "context": "This class handles resource discovery and retrieval from Supabase, crucial for understanding subscription and payment management.", "includesImports": false}, {"filePath": "src/lib/db/schema.ts", "content": "export const user = pgTable('User', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  email: varchar('email', { length: 64 }).notNull(),\n  password: varchar('password', { length: 64 }),\n  name: varchar(),\n  firstName: varchar(),\n  lastName: varchar(),\n  linkedin: varchar('linkedin', { length: 255 }),\n  provider: varchar('provider', { enum: ['credentials', 'google'] }).default('credentials'),\n  isAnonymous: boolean('isAnonymous').default(false),\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n});\n\nexport type User = InferSelectModel<typeof user>;\n\n// Forward declare projects table to avoid circular references\nexport const projects = pgTable('Project', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  appName: text('appName'), // New field for project/app name\n  slug: text('slug'), // URL-friendly identifier\n  scheme: text('scheme'), // URL scheme for deep linking\n\n  // App identifiers\n  bundleIdentifier: text('bundleIdentifier'), // iOS bundle ID (com.company.app)\n  packageName: text('packageName'), // Android package name\n\n  isMigratedv1: boolean('isMigratedv1').default(false),\n  // App assets\n  icon: text('icon'), // URL to app icon\n  splashImage: text('splashImage'), // URL to splash screen image\n  primaryColor: text('primaryColor').default('#000000'), // Primary brand color\n\n  // App configuration\n  description: text('description'), // App store description\n  privacyPolicyUrl: text('privacyPolicyUrl'),\n  termsOfServiceUrl: text('termsOfServiceUrl'),\n\n  // Ownership\n  userId: uuid('userId')\n    .notNull()\n    .references(() => user.id, { onDelete: 'cascade' }),\n  connectionId: uuid('connectionId')\n    .references(() => connection.id, { onDelete: 'cascade' }),\n  visibility: varchar('visibility', { enum: ['public', 'private', 'hidden'] })\n    .notNull()\n    .default('private'),\n\n  // Timestamps\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n\n  prompt: text('prompt'),\n  initialUXGuidelines: text('initialUXGuidelines'),\n\n  knowledgeBase: text('knowledgeBase'),\n  aiGeneratedMemory: text('aiGeneratedMemory'),\n\n  // Design preview fields\n  approvedDesignHtml: text('approvedDesignHtml'),\n  designChatId: uuid('designChatId').references(() => chat.id),\n\n  // Supabase fields at project level\n  supabaseProjectId: text('supabaseProjectId'),\n  supabaseAnonKey: text('supabaseAnonKey'),\n  supabaseServiceKey: text('supabaseServiceKey'),\n});\n\nexport type Project = InferSelectModel<typeof projects>;\n\nexport const chat = pgTable('Chat', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  createdAt: timestamp('createdAt').notNull(),\n  updatedAt: timestamp('updatedAt').notNull(),\n  title: text('title').notNull(),\n  userId: uuid('userId')\n    .notNull()\n    .references(() => user.id),\n  projectId: uuid('projectId')\n    .references(() => projects.id), // Reference to parent project\n  visibility: varchar('visibility', { enum: ['public', 'private', 'hidden'] })\n    .notNull()\n    .default('private'),\n  connectionId: uuid('connectionId')\n    .references(() => connection.id),\n  // Chat type to distinguish between app development and design preview\n  type: varchar('type', { enum: ['app', 'design', 'admin', 'discuss'] }).notNull().default('app'),\n  // Keep Supabase fields for backward compatibility but make them nullable\n  supabaseProjectId: text('supabaseProjectId'),\n  supabaseAnonKey: text('supabaseAnonKey'),\n  supabaseServiceKey: text('supabaseServiceKey'),\n  isInitialized: boolean('isInitialized').default(true),\n  // Flag to indicate if the chat needs continuation due to model context limits\n  needsContinuation: boolean('needsContinuation').default(false),\n  // Design preview fields\n  designHtml: text('designHtml'),\n  designStatus: varchar('designStatus', { enum: ['initializing', 'generating', 'complete', 'error'] }),\n  isDesignApproved: boolean('isDesignApproved').default(false),\n}, (table) => {\n  return {\n    userIdIdx: index('chat_userId_idx').on(table.userId),\n    projectIdIdx: index('chat_projectId_idx').on(table.projectId),\n    userProjectIdx: index('chat_userId_projectId_idx').on(table.userId, table.projectId),\n    updatedAtIdx: index('chat_updatedAt_idx').on(table.updatedAt)\n  };\n});\n\nexport type Chat = InferSelectModel<typeof chat>;\n\nexport const message = pgTable('Message', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  remoteProvider: varchar(\"remoteProvider\"),\n  remoteProviderId: varchar(\"remoteProviderId\"),\n  chatId: uuid('chatId')\n    .notNull()\n    .references(() => chat.id),\n  projectId: uuid('projectId')\n    .references(() => projects.id), // Reference to project\n  role: varchar('role').notNull(),\n  content: json('content').notNull(),\n  componentContexts: json('componentContexts'),\n  createdAt: timestamp('createdAt').notNull(),\n  autoFixed: boolean('autoFixed').default(false),\n  parts: json('parts'),\n  version: integer('version').default(1),\n  attachments: json('attachments'),\n  hidden: boolean('hidden').default(false),\n  userId: uuid('userId')\n    .references(() => user.id),\n  finishReason: varchar('finishReason'),\n  parentUserMessageId: uuid('parentUserMessageId'),\n  parentAssistantMessageId: uuid('parentAssistantMessageId'),\n  isAssistantGroupHead: boolean('isAssistantGroupHead').default(false)\n}, (table) => {\n  return {\n    chatIdIdx: index('message_chatId_idx').on(table.chatId),\n    projectIdIdx: index('message_projectId_idx').on(table.projectId),\n    userIdIdx: index('message_userId_idx').on(table.userId),\n    chatCreatedAtIdx: index('message_chatId_createdAt_idx').on(table.chatId, table.createdAt),\n    roleIdx: index('message_role_idx').on(table.role),\n    parentAssistantMessageIdIdx: index('message_parentAssistantMessageId_idx').on(table.parentAssistantMessageId)\n  };\n});\n\nexport type Message = InferSelectModel<typeof message>;\n\nexport const vote = pgTable(\n  'Vote',\n  {\n    chatId: uuid('chatId')\n      .notNull()\n      .references(() => chat.id),\n    projectId: uuid('projectId')\n      .references(() => projects.id), // Reference to project\n    messageId: uuid('messageId')\n      .notNull()\n      .references(() => message.id),\n    isUpvoted: boolean('isUpvoted').notNull(),\n  },\n  (table) => {\n    return {\n      pk: primaryKey({ columns: [table.chatId, table.messageId] }),\n    };\n  },\n);\n\nexport type Vote = InferSelectModel<typeof vote>;\n\nexport const document = pgTable(\n  'Document',\n  {\n    id: uuid('id').notNull().defaultRandom(),\n    createdAt: timestamp('createdAt').notNull(),\n    title: text('title').notNull(),\n    content: text('content'),\n    kind: varchar('text', { enum: ['text', 'code'] })\n      .notNull()\n      .default('text'),\n    userId: uuid('userId')\n      .notNull()\n      .references(() => user.id),\n  },\n  (table) => {\n    return {\n      pk: primaryKey({ columns: [table.id, table.createdAt] }),\n    };\n  },\n);\n\nexport type Document = InferSelectModel<typeof document>;\n\nexport const suggestion = pgTable(\n  'Suggestion',\n  {\n    id: uuid('id').notNull().defaultRandom(),\n    documentId: uuid('documentId').notNull(),\n    documentCreatedAt: timestamp('documentCreatedAt').notNull(),\n    originalText: text('originalText').notNull(),\n    suggestedText: text('suggestedText').notNull(),\n    description: text('description'),\n    isResolved: boolean('isResolved').notNull().default(false),\n    userId: uuid('userId')\n      .notNull()\n      .references(() => user.id),\n    createdAt: timestamp('createdAt').notNull(),\n  },\n  (table) => ({\n    pk: primaryKey({ columns: [table.id] }),\n    documentRef: foreignKey({\n      columns: [table.documentId, table.documentCreatedAt],\n      foreignColumns: [document.id, document.createdAt],\n    }),\n  }),\n);\n\nexport type Suggestion = InferSelectModel<typeof suggestion>;\n\nexport const passwordResetTokens = pgTable('password_reset_tokens', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  userId: uuid('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),\n  token: text('token').notNull().unique(),\n  expiresAt: timestamp('expires_at').notNull(),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n});\n\nexport type PasswordResetToken = InferSelectModel<typeof passwordResetTokens>;\n\n// Connection status enum\nexport const ConnectionStatus = {\n  PENDING: 'pending',\n  CONNECTED: 'connected',\n  DISCONNECTED: 'disconnected',\n  FAILED: 'failed'\n} as const;\n\nexport type ConnectionStatus = typeof ConnectionStatus[keyof typeof ConnectionStatus];\n\n// Base connection table\nexport const fileState = pgTable('FileState', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  chatId: uuid('chatId').notNull().references(() => chat.id),\n  projectId: uuid('projectId').references(() => projects.id), // Reference to project\n  sqlQuery: text('sqlQuery'),\n  sqlStatus: varchar('sqlStatus', { enum: ['pending', 'executing', 'done', 'error'] }),\n  sqlError: text('sqlError'),\n  messageId: uuid('messageId').references(() => message.id),\n  files: json('files').notNull(),\n  dependencies: json('dependencies').notNull(),\n  createdAt: timestamp('createdAt').notNull(),\n  version: integer('version').default(1),\n  isBaseCacheVersion: boolean('is_base_cache_version').default(false),\n}, (table) => {\n  return {\n    chatIdIdx: index('fileState_chatId_idx').on(table.chatId),\n    projectIdIdx: index('fileState_projectId_idx').on(table.projectId),\n    messageIdIdx: index('fileState_messageId_idx').on(table.messageId),\n    baseCacheVersionIdx: index('fileState_isBaseCacheVersion_idx').on(table.isBaseCacheVersion),\n    chatBaseCacheIdx: index('fileState_chatId_isBaseCacheVersion_idx').on(table.chatId, table.isBaseCacheVersion),\n    versionIdx: index('fileState_version_idx').on(table.version),\n    chatMessageIdx: index('fileState_chatId_messageId_idx').on(table.chatId, table.messageId),\n    createdAtIdx: index('fileState_createdAt_idx').on(table.createdAt)\n  };\n});\n\nexport type FileState = InferSelectModel<typeof fileState>;\n\n// Screenshot state table for storing screenshot metadata\nexport const screenshotState = pgTable('ScreenshotState', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  projectId: uuid('projectId').references(() => projects.id),\n  chatId: uuid('chatId'),\n  messageId: uuid('messageId'),\n  screenshots: json('screenshots').notNull(), // Array of screenshot objects with metadata\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n}, (table) => {\n  return {\n    projectIdIdx: index('screenshotState_projectId_idx').on(table.projectId),\n    chatIdIdx: index('screenshotState_chatId_idx').on(table.chatId),\n    messageIdIdx: index('screenshotState_messageId_idx').on(table.messageId),\n    createdAtIdx: index('screenshotState_createdAt_idx').on(table.createdAt),\n  };\n});\n\nexport type ScreenshotState = InferSelectModel<typeof screenshotState>;\n\nexport const connection = pgTable('Connection', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  userId: uuid('userId')\n    .notNull()\n    .references(() => user.id, { onDelete: 'cascade' }),\n  provider: varchar('provider', { length: 50 }).notNull(),\n  status: varchar('status', { length: 50 })\n    .notNull()\n    .default(ConnectionStatus.PENDING),\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n  lastSyncedAt: timestamp('lastSyncedAt'),\n  metadata: json('metadata').$type<Record<string, any>>(),\n});\n\nexport type Connection = InferSelectModel<typeof connection>;\n\nexport const tokenConsumption = pgTable('TokenConsumption', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  model: varchar('model', { length: 50 }).notNull(),\n  totalTimeToken: real('totalTimeToken').notNull(),\n  promptTokens: integer('promptTokens').notNull(),\n  completionTokens: integer('completionTokens').notNull(),\n  totalTokens: integer('totalTokens').notNull(),\n  chatId: uuid('chatId')\n    .notNull(),  // No foreign key as specified\n  projectId: uuid('projectId')\n    .notNull(),  // No foreign key as specified\n  messageId: uuid('messageId')\n    .notNull(),  // No foreign key as specified\n  userId: uuid('userId')\n    .notNull()\n    .references(() => user.id, { onDelete: 'cascade' }),  // Add foreign key to User\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n  inputCost: real('inputCost').notNull(),  // Store as float\n  outputCost: real('outputCost').notNull(),  // Store as float\n  cachingDiscount: real('cachingDiscount'),\n  cacheDiscountPercent: real('cacheDiscountPercent'),\n  subtotal: real('subtotal'),\n  totalCost: real('totalCost').notNull(),  // Store as float\n  isAnonymous: boolean('isAnonymous').default(false),\n  remoteProvider: varchar(\"remoteProvider\"),\n  remoteProviderId: varchar(\"remoteProviderId\"),\n  creditsConsumed: integer('creditsConsumed'),\n  discountedCredits: integer('discountedCredits'),\n  discountReason: varchar('discountReason', { length: 50 }),\n  errorId: varchar('errorId'),\n  discounted: boolean('discounted').default(false),\n  isAutoFixed: boolean('isAutoFixed').default(false)\n}, (table) => {\n  return {\n    userIdIdx: index('tokenConsumption_userId_idx').on(table.userId),\n    chatIdIdx: index('tokenConsumption_chatId_idx').on(table.chatId),\n    projectIdIdx: index('tokenConsumption_projectId_idx').on(table.projectId),\n    userCreatedAtIdx: index('tokenConsumption_userId_createdAt_idx').on(table.userId, table.createdAt),\n    isAnonymousIdx: index('tokenConsumption_isAnonymous_idx').on(table.isAnonymous)\n  };\n});\n\nexport type TokenConsumption = InferSelectModel<typeof tokenConsumption>;\n\n// Update projects table to reference chat (already defined above)\nexport const projectChats = pgTable('ProjectChat', {\n  projectId: uuid('projectId')\n    .notNull()\n    .references(() => projects.id),\n  chatId: uuid('chatId')\n    .notNull()\n    .references(() => chat.id),\n  isPrimary: boolean('isPrimary').default(true),\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n});\n\nexport type ProjectChat = InferSelectModel<typeof projectChats>;\n\nexport const subscriptions = pgTable('Subscription', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  userId: varchar('userId', { length: 255 }).notNull(),\n  status: varchar('status', { length: 50 }).notNull().default('inactive'),\n  planId: varchar('planId', { length: 50 }).notNull().default('free'),\n  subscriptionId: varchar('subscriptionId', { length: 255 }).unique(),\n  credits: integer('credits').notNull().default(50), // Default to free tier credits\n  creditsUsed: integer('creditsUsed').notNull().default(0),\n  isActive: boolean('isActive').default(false),\n  // LemonSqueezy fields\n  lemonSqueezyCustomerId: varchar('lemonSqueezyCustomerId'),\n  lemonSqueezySubscriptionId: varchar('lemonSqueezySubscriptionId'),\n  lemonSqueezyOrderId: varchar('lemonSqueezyOrderId'),\n  lemonSqueezyVariantId: varchar('lemonSqueezyVariantId'),\n  provider: varchar('provider'),\n  // Stripe fields (for compatibility)\n  stripeCustomerId: varchar('stripeCustomerId'),\n  stripeSubscriptionId: varchar('stripeSubscriptionId'),\n  stripePriceId: varchar('stripePriceId'),\n  stripeCurrentPeriodEnd: timestamp('stripeCurrentPeriodEnd'),\n  stripeVariantId: timestamp('stripeVariantId'),\n  // Billing cycle\n  resetDate: timestamp('resetDate'),\n  // Flags\n  isDowngraded: boolean('isDowngraded').default(false),\n  // Additional metadata (cancellation feedback, etc.)\n  metadata: json('metadata').$type<Record<string, any>>(),\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n}, (table) => {\n  return {\n    userIdIdx: index('subscription_userId_idx').on(table.userId),\n    statusIdx: index('subscription_status_idx').on(table.status),\n    userIdUpdatedAtIdx: index('subscription_userId_updatedAt_idx').on(table.userId, table.updatedAt),\n    isActiveIdx: index('subscription_isActive_idx').on(table.isActive)\n  };\n});\n\nexport type Subscription = InferSelectModel<typeof subscriptions>;\n\nexport const projectConnections = pgTable('ProjectConnection', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  projectId: uuid('projectId')\n    .notNull()\n    .references(() => projects.id, { onDelete: 'cascade' }),\n  provider: varchar('provider', { length: 50 }).notNull(),\n  providerProjectId: text('providerProjectId').notNull(),\n  providerProjectData: json('providerProjectData').$type<Record<string, any>>(),\n  types: json('types').$type<{\n    database: string;\n    client: string;\n    helper: string;\n    projectRef: string;\n    host: string;\n    anonKey: string;\n  }>(),\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n});\n\nexport type ProjectConnection = InferSelectModel<typeof projectConnections>;\n\n// APK Builds table for tracking Android build jobs\nexport const apkBuilds = pgTable('apk_builds', {\n  id: uuid('id').primaryKey().notNull(),\n  createdAt: timestamp('created_at').notNull().defaultNow(),\n  updatedAt: timestamp('updated_at').notNull().defaultNow(),\n  status: text('status').notNull(),\n  apkUrl: text('apk_url'),\n  error: text('error'),\n  metadata: json('metadata').$type<Record<string, any>>(),\n});\n\nexport type ApkBuild = InferSelectModel<typeof apkBuilds>;\n\n// Deployments table for tracking all platform deployments\nexport const deployments = pgTable('deployments', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  projectId: uuid('projectId').references(() => projects.id), // Reference to project\n  userId: uuid('userId').notNull().references(() => user.id),\n  slug: text('slug').notNull(),\n  platform: varchar('platform', { enum: ['web', 'android', 'ios'] }).notNull(),\n  version: varchar('version', { length: 20 }).notNull(), // Semantic versioning (e.g., 1.0.0)\n  versionCode: integer('versionCode'), // Auto-incrementing version number for platforms that require it\n  status: varchar('status', {\n    enum: ['queued', 'processing', 'completed', 'failed']\n  }).notNull().default('queued'),\n  url: text('url'), // URL where the app is deployed (web) or download link (APK)\n  buildId: uuid('buildId').references(() => apkBuilds.id), // For Android/iOS builds\n  fileStateId: uuid('fileStateId').references(() => fileState.id), // Reference to file structure\n  metadata: json('metadata').$type<Record<string, any>>(),\n  createdAt: timestamp('created_at').notNull().defaultNow(),\n  updatedAt: timestamp('updated_at').notNull().defaultNow(),\n  error: text('error'),\n}, (table) => {\n  return {\n    // Add a unique constraint on slug\n    slugUnique: unique('slug_unique').on(table.slug),\n\n    // Add a dedicated index on slug for faster lookups\n    slugIdx: index('slug_idx').on(table.slug),\n\n    // Add a composite index on slug and platform\n    slugPlatformIdx: index('slug_platform_idx').on(table.slug, table.platform)\n  };\n});\n\nexport type Deployment = InferSelectModel<typeof deployments>;\n\nexport const stream = pgTable(\n    'Stream',\n    {\n      id: uuid('id').notNull().defaultRandom(),\n      chatId: uuid('chatId').notNull(),\n      createdAt: timestamp('createdAt').notNull(),\n    },\n    (table) => ({\n      pk: primaryKey({ columns: [table.id] })\n    }),\n);\nexport type Stream = InferSelectModel<typeof stream>;\n\n// Design screens table for storing individual screen designs\nexport const designScreens = pgTable(\n  'DesignScreen',\n  {\n    id: uuid('id').primaryKey().notNull().defaultRandom(),\n    chatId: uuid('chatId')\n      .notNull()\n      .references(() => chat.id, { onDelete: 'cascade' }),\n    projectId: uuid('projectId')\n      .notNull()\n      .references(() => projects.id, { onDelete: 'cascade' }),\n    name: text('name').notNull(),\n    html: text('html').notNull(),\n    order: real('order').notNull().default(0),\n    status: varchar('status', { enum: ['starting', 'generating', 'complete', 'error'] })\n      .notNull()\n      .default('complete'),\n    createdAt: timestamp('createdAt').notNull().defaultNow(),\n    updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n  },\n  (table) => ({\n    chatIdIdx: index('design_screen_chatId_idx').on(table.chatId),\n    projectIdIdx: index('design_screen_projectId_idx').on(table.projectId),\n    orderIdx: index('design_screen_order_idx').on(table.order),\n  }),\n);\nexport type DesignScreen = InferSelectModel<typeof designScreens>;\n\n// Temperature optimization metadata table\nexport const temperatureOptimization = pgTable('TemperatureOptimization', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  messageId: uuid('messageId')\n    .notNull()\n    .references(() => message.id, { onDelete: 'cascade' }),\n  chatId: uuid('chatId')\n    .notNull()\n    .references(() => chat.id, { onDelete: 'cascade' }),\n  projectId: uuid('projectId')\n    .references(() => projects.id, { onDelete: 'cascade' }),\n  userId: uuid('userId')\n    .references(() => user.id, { onDelete: 'cascade' }),\n\n  // Optimization results\n  optimizedTemperature: real('optimizedTemperature').notNull(),\n  selectedModel: varchar('selectedModel', { length: 100 }).notNull(),\n  reasoning: text('reasoning').notNull(),\n  contextFactors: json('contextFactors').$type<string[]>().notNull(),\n\n  // User progression tracking (key metric for development platforms)\n  userProgression: json('userProgression').$type<{\n    isProgressing: boolean;\n    isStuck: boolean;\n    stuckSeverity?: \"mild\" | \"moderate\" | \"severe\";\n  }>(),\n\n  // Project complexity metrics\n  fileCount: integer('fileCount').notNull().default(0),\n\n  // Performance metrics\n  optimizationDuration: integer('optimizationDuration'), // milliseconds\n  wasSuccessful: boolean('wasSuccessful').notNull().default(true),\n  errorMessage: text('errorMessage'),\n\n  // Fallback info\n  usedFallback: boolean('usedFallback').notNull().default(false),\n  fallbackReason: varchar('fallbackReason', { length: 100 }),\n\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n}, (table) => ({\n  messageIdIdx: index('temperature_optimization_messageId_idx').on(table.messageId),\n  chatIdIdx: index('temperature_optimization_chatId_idx').on(table.chatId),\n  projectIdIdx: index('temperature_optimization_projectId_idx').on(table.projectId),\n  userIdIdx: index('temperature_optimization_userId_idx').on(table.userId),\n  createdAtIdx: index('temperature_optimization_createdAt_idx').on(table.createdAt),\n  temperatureIdx: index('temperature_optimization_temperature_idx').on(table.optimizedTemperature),\n  successfulIdx: index('temperature_optimization_successful_idx').on(table.wasSuccessful),\n}));\n\nexport type TemperatureOptimization = InferSelectModel<typeof temperatureOptimization>;\n\n", "startLine": 17, "endLine": 580, "type": "unknown", "symbols": ["Database schema"], "score": 0.9, "context": "Contains schema definitions for subscriptions, users, and related entities, essential for understanding how subscription data is stored and managed.", "includesImports": false}, {"filePath": "src/lib/services/two-stage-context-engine.ts", "content": "export class TwoStageLLMContextEngine {\n  private files: FileItem[] = [];\n  private fileIndex: Map<string, FileItem> = new Map();\n\n  /**\n   * Initialize with project files\n   */\n  constructor(files: FileItem[]) {\n    this.files = files;\n    this.buildIndex();\n  }\n\n  /**\n   * Build an index of files for quick lookup\n   */\n  private buildIndex(): void {\n    for (const file of this.files) {\n      this.fileIndex.set(file.name, file);\n    }\n  }\n\n  /**\n   * Get a structured representation of the codebase optimized for cheap LLM understanding\n   * Groups files logically and provides clear context for React Native + Supabase projects\n   */\n  private getCodebaseStructure(): string {\n    // Build import relationships\n    const importMap = new Map<string, Set<string>>();\n\n    // Extract imports and build relationships\n    this.files.forEach(file => {\n      const imports = this.extractImports(file.content || \"\");\n      imports.forEach(importPath => {\n        const resolvedPath = this.resolveImportPath(importPath, file.name);\n        if (resolvedPath) {\n          if (!importMap.has(resolvedPath)) {\n            importMap.set(resolvedPath, new Set());\n          }\n          importMap.get(resolvedPath)?.add(file.name);\n        }\n      });\n    });\n\n    // Group files by logical categories for better LLM understanding\n    const fileGroups = this.groupFilesByCategory();\n\n    // Build structured output\n    const sections: string[] = [];\n\n    // Add header\n    sections.push(\"PROJECT STRUCTURE:\");\n    sections.push(\"================\");\n\n    // Process each category\n    Object.entries(fileGroups).forEach(([category, files]) => {\n      if (files.length > 0) {\n        sections.push(`\\n${category}:`);\n\n        files.forEach(file => {\n          const fileType = this.determineFileType(file.name, file.content || \"\");\n          const exports = this.extractExports(file.content || \"\");\n          const usageCount = importMap.get(file.name)?.size || 0;\n          const usedByFiles = Array.from(importMap.get(file.name) || []).slice(0, 2); // Limit to 2 for space\n\n          // Create clean, structured line\n          let line = `  ${file.name}`;\n\n          // Add type indicator\n          if (fileType !== 'unknown') {\n            line += ` [${fileType}]`;\n          }\n\n          // Add key exports (limit to 3 most important)\n          const keyExports = this.getKeyExports(exports, fileType);\n          if (keyExports.length > 0) {\n            line += ` → ${keyExports.slice(0, 3).join(', ')}`;\n            if (keyExports.length > 3) {\n              line += ` +${keyExports.length - 3}more`;\n            }\n          }\n\n          // Add usage information (count + specific files)\n          if (usageCount > 0) {\n            line += ` (${usageCount} refs`;\n            if (usedByFiles.length > 0) {\n              // Show just the file names without full paths for brevity\n              const shortNames = usedByFiles.map(f => f.split('/').pop()).slice(0, 2);\n              line += `: ${shortNames.join(', ')}`;\n              if (usageCount > 2) {\n                line += ` +${usageCount - 2}more`;\n              }\n            }\n            line += ')';\n          }\n\n          sections.push(line);\n        });\n      }\n    });\n\n    return sections.join('\\n');\n  }\n\n  /**\n   * Group files into logical categories for better LLM understanding\n   */\n  private groupFilesByCategory(): Record<string, typeof this.files> {\n    const groups: Record<string, typeof this.files> = {\n      'App Entry': [],\n      'Screens': [],\n      'Components': [],\n      'Navigation': [],\n      'State/Stores': [],\n      'Services': [],\n      'Database/Supabase': [],\n      'Utils/Helpers': [],\n      'Types': [],\n      'Config': [],\n      'Other': []\n    };\n\n    this.files.forEach(file => {\n      const path = file.name.toLowerCase();\n      const name = file.name.toLowerCase();\n\n      // App entry points\n      if (name.includes('app.') || name.includes('index.') || name === 'app.tsx' || name === 'app.ts') {\n        groups['App Entry'].push(file);\n      }\n      // Screens\n      else if (path.includes('screen') || path.includes('page') || name.endsWith('screen.tsx')) {\n        groups['Screens'].push(file);\n      }\n      // Navigation\n      else if (path.includes('navigation') || path.includes('navigator') || name.includes('navigation')) {\n        groups['Navigation'].push(file);\n      }\n      // Components\n      else if (path.includes('component') || name.endsWith('.tsx') && !path.includes('screen')) {\n        groups['Components'].push(file);\n      }\n      // State management\n      else if (path.includes('store') || path.includes('context') || name.includes('store') || name.includes('context')) {\n        groups['State/Stores'].push(file);\n      }\n      // Services\n      else if (path.includes('service') || path.includes('api') || name.includes('service')) {\n        groups['Services'].push(file);\n      }\n      // Database/Supabase\n      else if (path.includes('supabase') || path.includes('database') || path.includes('db') || name.includes('supabase')) {\n        groups['Database/Supabase'].push(file);\n      }\n      // Types\n      else if (name.includes('type') || name.includes('.d.ts') || path.includes('types')) {\n        groups['Types'].push(file);\n      }\n      // Utils\n      else if (path.includes('util') || path.includes('helper') || name.includes('util') || name.includes('helper')) {\n        groups['Utils/Helpers'].push(file);\n      }\n      // Config\n      else if (name.includes('config') || name.includes('.config.') || path.includes('config')) {\n        groups['Config'].push(file);\n      }\n      // Everything else\n      else {\n        groups['Other'].push(file);\n      }\n    });\n\n    // Sort files within each group by importance (usage and name)\n    Object.keys(groups).forEach(category => {\n      groups[category].sort((a, b) => {\n        // Prioritize index files and main entry points\n        if (a.name.includes('index.') && !b.name.includes('index.')) return -1;\n        if (!a.name.includes('index.') && b.name.includes('index.')) return 1;\n\n        // Then by name\n        return a.name.localeCompare(b.name);\n      });\n    });\n\n    return groups;\n  }\n\n  /**\n   * Extract key exports, filtering noise for better LLM focus\n   */\n  private getKeyExports(exports: string[], fileType: string): string[] {\n    // Filter out common noise\n    const filtered = exports.filter(exp =>\n      exp !== 'default' &&\n      exp.length > 2 && // Skip very short names\n      !exp.toLowerCase().includes('props') &&\n      !exp.toLowerCase().includes('style')\n    );\n\n    // For components, prioritize component names\n    if (fileType === 'component' || fileType === 'screen') {\n      return filtered.filter(exp =>\n        !exp.toLowerCase().startsWith('use') || exp.length > 6 // Keep longer hooks\n      );\n    }\n\n    return filtered.slice(0, 5); // Limit to 5 key exports\n  }\n\n  /**\n   * First stage: Find relevant files for a query\n   */\n  async findRelevantFiles(query: string, reason: string, excludedFiles: string[] = []): Promise<{files: string[], reasoning: string }> {\n    console.time('find-relevant-files');\n\n    // Get a compact representation of the codebase structure\n    const codebaseStructure = this.getCodebaseStructure();\n\n    console.log('codebaseStructure', codebaseStructure)\n\n    // Use LLM to identify relevant files\n    const result = await generateObject({\n      model: customModel('openai/gpt-4.1-nano'), // Using a smaller model to reduce costs\n      temperature: 0.1,\n      schema: z.object({\n        files: z.array(z.string()),\n        reasoning: z.string().describe(\"Explanation of why these files were selected\")\n      }),\n      system: `You are a code analysis expert. Your task is to identify which files in a React Native Expo project are most relevant to a specific query.\n\nYou will be given:\n1. A query about the codebase\n2. A structured representation of files organized by category\n\nHOW TO READ THE FILE FORMAT:\n- Files are grouped by category (App Entry, Screens, Components, Services, etc.)\n- Each file line shows: filename [type] → exports (usage_info)\n- [type] indicates file purpose: [component], [hook], [service], [context], [config], etc.\n- → exports shows key functions/classes exported by the file\n- (N refs: file1, file2 +Nmore) shows which files import/use this file\n- Higher ref counts indicate more important/central files\n- Files with many references are often core infrastructure\n\nSELECTION STRATEGY:\n1. For architectural queries: Focus on files with high ref counts and core types\n2. For specific features: Look for files in relevant categories with matching exports\n3. For debugging: Include both the problematic area AND its dependencies (ref relationships)\n4. Always include some high-ref-count files for context, even if not directly related\n\nReturn a JSON object with:\n1. An array of the most relevant file paths (maximum 20)\n2. Your reasoning for selecting these files\n\nChoose files that would be most helpful for understanding or implementing the query.`,\n      prompt: `Query: ${query}\n      \nReason: ${reason}\n\nFiles in the project:\n${codebaseStructure}\n\n${excludedFiles.length > 0 ? `Note: The following files have been excluded from analysis: ${excludedFiles.join(', ')}\\n\\n` : ''}\nReturn the most relevant file paths (maximum 20) and your reasoning:`,\n    });\n\n    console.timeEnd('find-relevant-files');\n    console.log(`Selected files reasoning: ${result.object.reasoning}`);\n\n    // Filter out any files that don't exist in our index\n    return {files: result.object.files.filter(file => this.fileIndex.has(file)), reasoning: result.object.reasoning};\n  }\n\n  /**\n   * Second stage: Identify relevant snippets within files\n   */\n  async identifyRelevantSnippets(query: string, originalReason: string, relevantFiles: string[], previousStageReason: string): Promise<SnippetIdentification[]> {\n    console.time('identify-snippets');\n\n\n    // Prepare file contents with line numbers\n    const fileContents = relevantFiles.map(fileName => {\n      const file = this.fileIndex.get(fileName);\n      const content = file?.content || \"\";\n\n      // Add line numbers to help the LLM identify specific ranges\n      // Format with consistent padding to make line numbers stand out\n      const lines = content.split(\"\\n\");\n      const maxLineNumberWidth = String(lines.length).length;\n      const numberedContent = lines.map((line, i) => {\n        const lineNumber = i + 1;\n        const paddedLineNumber = String(lineNumber).padStart(maxLineNumberWidth, ' ');\n        return `${paddedLineNumber}: ${line}`;\n      }).join(\"\\n\");\n\n      return {\n        name: fileName,\n        content: numberedContent,\n        lineCount: lines.length\n      };\n    });\n\n    // Use LLM to identify relevant snippets within these files\n    const result = await generateObject({\n      model: customModel('openai/gpt-4.1-nano'), // Using a more capable model for this task\n      temperature: 0.1,\n      schema: z.object({\n        snippets: z.array(z.object({\n          fileName: z.string(),\n          startLine: z.number(),\n          endLine: z.number(),\n          snippetType: z.string(),\n          snippetName: z.string(),\n          relevanceScore: z.number().min(0).max(1),\n          reasoning: z.string()\n        }))\n      }),\n      system: `You are a code analysis expert. Your task is to identify the MINIMAL relevant code snippets within files for a specific query.\n\nReturn the exact line numbers for each snippet, along with metadata about the snippet.\n\nGuidelines for MINIMAL context extraction:\n1. Extract ONLY the specific lines directly relevant to the query - avoid including entire files or anything extra at all\n2. For imports, include ONLY the imports directly related to the query and is needed to reliable editing\n2. For style, include ONLY the styles directly related to the query and is needed to reliable editing\n3. For components, include ONLY the component declaration, props, and relevant JSX elements - NOT the entire implementation\n4. For functions, include ONLY the signature and critical logic - NOT entire function bodies\n5. Assign a relevance score from 0.0 to 1.0 based on how directly the snippet addresses the query. \n6. Keep snippets as SHORT as possible while maintaining necessary context\n7. Pay close attention to the line numbers at the beginning of each line (formatted as \"NUMBER: code\")\n8. For React errors, focus on component declarations, imports/exports, and JSX return statements\n9. NEVER include style definitions unless they are directly relevant to the query\n10. NEVER include helper functions unless they are directly relevant to the query\n11. When specific line numbers are requested, return only those line numbers\n\nToken efficiency guidelines:\n1. Maximum 30 lines per snippet unless absolutely necessary\n4. Omit implementation details of methods unless directly relevant\n5. For UI issues, include only the relevant JSX elements, not entire render methods\n6. When multiple similar components exist, include only one representative example\\``,\n      prompt: `Query: ${query}\n\nI need to identify the MINIMAL relevant code snippets in these files. For each relevant code block, provide:\n1. The file name\n2. Start and end line numbers (inclusive) - use the exact line numbers shown at the beginning of each line\n3. Type of snippet (function, component, hook, type, etc.)\n4. Name of the snippet (function name, component name, etc.)\n5. Relevance score (0.0 to 1.0)\n6. Brief reasoning for why this snippet is relevant\n\nReliable editing:\n- Please include import and styles if they need are needed to add imports\n- Include just enough context for clear understanding and editing \n\nCRITICAL TOKEN EFFICIENCY GUIDELINES:\n- Extract ONLY the specific lines directly relevant to the query\n- Each line in the files is prefixed with its line number (e.g., \"42: const foo = bar;\"). Use these exact line numbers.\n- For imports, include ONLY those directly related to the query\n- For components, include ONLY the declaration, props, and relevant JSX - NOT entire implementations\n- NEVER include style definitions unless directly relevant to the query\n- Keep snippets to a MAXIMUM of 30 lines when possible\n- For React errors, focus ONLY on component declarations, imports/exports, and JSX return statements\n- AVOID including entire component implementations - be extremely selective\n\nReasoning from the user/llm issuing the original query on why they want the result. This gets fed to the first engine:\n${originalReason}\n\nReasoning from the first engine on why each file was selected. Use the reasoning to understand the whole flow and to select snippets accordingly:\n${previousStageReason}\n\nFiles to analyze:\n${fileContents.map(file => `\n=== ${file.name} (${file.lineCount} lines) ===\n${file.content}\n`).join(\"\\n\\n\")}\n\nReturn an array of the most relevant code snippets with their exact line numbers and metadata:`,\n    });\n\n    console.timeEnd('identify-snippets');\n    return result.object.snippets;\n  }\n\n  /**\n   * Smart truncation to fit within line budget while preserving understanding context\n   */\n  smartTruncateSnippets(snippets: CodeSnippet[], maxLines: number = 600): {truncatedSnippets: CodeSnippet[], additionalFromTruncation: {fileName: string, reason: string, suggestedQuery: string}[]} {\n    // Categorize snippets by type for strategic selection\n    const implementation = snippets.filter(s => (s.score || 0) >= 0.9);\n    const usage = snippets.filter(s => (s.score || 0) >= 0.7 && (s.score || 0) < 0.9);\n    const context = snippets.filter(s => (s.score || 0) < 0.7);\n\n    let currentLines = 0;\n    const truncatedSnippets: CodeSnippet[] = [];\n    const additionalFromTruncation: {fileName: string, reason: string, suggestedQuery: string}[] = [];\n\n    // Strategy: Always include implementation, then usage, then context within budget\n    const prioritizedSnippets = [\n      ...orderBy(implementation, ['score'], ['desc']).slice(0, 2), // Max 2 implementation snippets\n      ...orderBy(usage, ['score'], ['desc']).slice(0, 2), // Max 2 usage examples\n      ...orderBy(context, ['score'], ['desc']).slice(0, 1)  // Max 1 context snippet\n    ];\n\n    for (const snippet of prioritizedSnippets) {\n      const snippetLines = snippet.content.split('\\n').length;\n\n      if (currentLines + snippetLines <= maxLines) {\n        truncatedSnippets.push(snippet);\n        currentLines += snippetLines;\n      } else {\n        // Add to additional files instead of truncating content\n        additionalFromTruncation.push({\n          fileName: snippet.filePath,\n          reason: `Relevant but truncated due to 1000-line budget (relevance: ${(snippet.score || 0).toFixed(2)}, ${snippetLines} lines)`,\n          suggestedQuery: `Show ${snippet.symbols.join(', ')} in ${snippet.filePath}`\n        });\n      }\n    }\n\n    // Add any remaining snippets to additional files\n    const remainingSnippets = snippets.filter(s => !truncatedSnippets.includes(s) && !additionalFromTruncation.some(a => a.fileName === s.filePath));\n    for (const snippet of remainingSnippets) {\n      additionalFromTruncation.push({\n        fileName: snippet.filePath,\n        reason: `Additional context available (relevance: ${(snippet.score || 0).toFixed(2)})`,\n        suggestedQuery: `Show ${snippet.symbols.join(', ')} in ${snippet.filePath}`\n      });\n    }\n\n    console.log(`Smart truncation: kept ${truncatedSnippets.length}/${snippets.length} snippets within ${maxLines} line budget (${currentLines} lines total)`);\n    console.log(`  - Implementation: ${implementation.length} found, ${truncatedSnippets.filter(s => (s.score || 0) >= 0.9).length} kept`);\n    console.log(`  - Usage: ${usage.length} found, ${truncatedSnippets.filter(s => (s.score || 0) >= 0.7 && (s.score || 0) < 0.9).length} kept`);\n    console.log(`  - Context: ${context.length} found, ${truncatedSnippets.filter(s => (s.score || 0) < 0.7).length} kept`);\n\n    return {truncatedSnippets, additionalFromTruncation};\n  }\n\n  /**\n   * Extract actual code snippets based on line numbers\n   */\n  extractSnippets(snippetIdentifications: SnippetIdentification[]): CodeSnippet[] {\n    // Group snippets by file to avoid duplicate processing\n    const snippetsByFile = new Map<string, SnippetIdentification[]>();\n\n    for (const snippet of snippetIdentifications) {\n      if (!snippetsByFile.has(snippet.fileName)) {\n        snippetsByFile.set(snippet.fileName, []);\n      }\n      snippetsByFile.get(snippet.fileName)?.push(snippet);\n    }\n\n    const results: CodeSnippet[] = [];\n\n    // Process each file's snippets\n    for (const [fileName, fileSnippets] of snippetsByFile.entries()) {\n      const file = this.fileIndex.get(fileName);\n      if (!file || !file.content) continue;\n\n      const lines = file.content.split(\"\\n\");\n\n      // Find import statements (usually at the top of the file)\n      const importEndLine = this.findImportEndLine(lines);\n      const hasImports = importEndLine > 0;\n\n      // Process each snippet in the file\n      for (const identification of fileSnippets) {\n        // Ensure line numbers are within bounds\n        const startLine = Math.max(1, Math.min(identification.startLine, lines.length));\n        const endLine = Math.max(startLine, Math.min(identification.endLine, lines.length));\n\n        // Removing as this is causing the llm issues to understand\n        const shouldIncludeImports = false;\n\n        // Determine if we should include imports\n        // const shouldIncludeImports = hasImports &&\n        //     identification.snippetType.toLowerCase() !== 'import' &&\n        //     startLine > importEndLine;\n\n        // Extract the snippet content with imports if needed\n        let snippetLines: string[];\n        let actualStartLine: number;\n\n        if (shouldIncludeImports) {\n          // Include imports and the actual snippet\n          const importLines = lines.slice(0, importEndLine);\n          const codeLines = lines.slice(startLine - 1, endLine);\n\n          // Add a separator between imports and code\n          snippetLines = [...importLines, '', '// Main snippet', ...codeLines];\n          actualStartLine = 1; // Starting from the beginning of the file\n        } else {\n          // Just include the snippet itself\n          snippetLines = lines.slice(startLine - 1, endLine);\n          actualStartLine = startLine;\n        }\n\n        const content = snippetLines.join(\"\\n\");\n\n        // Log the extraction for debugging\n        console.log(`Extracting snippet from ${identification.fileName}:`);\n        console.log(`  Lines ${startLine}-${endLine} (${snippetLines.length} lines)`);\n        console.log(`  Type: ${identification.snippetType}, Name: ${identification.snippetName}`);\n        console.log(`  Relevance: ${identification.relevanceScore.toFixed(2)}`);\n        if (shouldIncludeImports) {\n          console.log(`  Including imports from lines 1-${importEndLine}`);\n        }\n\n        results.push({\n          filePath: identification.fileName,\n          content,\n          startLine: actualStartLine,\n          endLine: shouldIncludeImports ? endLine + (importEndLine + 2) : endLine, // Adjust for added separator lines\n          type: this.mapSnippetType(identification.snippetType),\n          symbols: [identification.snippetName],\n          score: identification.relevanceScore,\n          context: identification.reasoning,\n          includesImports: shouldIncludeImports\n        } as CodeSnippet);\n      }\n    }\n\n    console.log('Total lines', results.reduce((acc, a) => {\n      return acc + ((a.endLine - a.startLine)) + 1;\n    }, 0));\n\n    return orderBy(results, ['score'], ['desc']);\n  }\n\n  /**\n   * Find the line where imports end in a file\n   */\n  private findImportEndLine(lines: string[]): number {\n    let lastImportLine = 0;\n\n    for (let i = 0; i < Math.min(lines.length, 50); i++) { // Only check first 50 lines\n      const line = lines[i].trim();\n      if (line.startsWith('import ')) {\n        lastImportLine = i + 1; // +1 because line numbers are 1-based\n      }\n    }\n\n    return lastImportLine;\n  }\n\n  /**\n   * Main method to get relevant snippets for a query\n   */\n  async getRelevantSnippets(query: string, reason: string, excludedFiles: string[] = []): Promise<CodeSnippet[]> {\n    // Stage 1: Find relevant files\n    const {files: relevantFiles, reasoning} = await this.findRelevantFiles(query, reason, excludedFiles);\n\n    if (relevantFiles.length === 0) {\n      console.log(\"No relevant files found\");\n      return [];\n    }\n\n    // Stage 2: Identify relevant snippets within those files\n    const snippetIdentifications = await this.identifyRelevantSnippets(query, reason, relevantFiles, reasoning);\n\n    // Stage 3: Extract the actual snippets\n    return this.extractSnippets(snippetIdentifications);\n  }\n\n  /**\n   * Helper methods\n   */\n  private isCodeFile(fileName: string): boolean {\n    const ext = path.extname(fileName).toLowerCase();\n    return ['.ts', '.tsx', '.js', '.jsx'].includes(ext);\n  }\n\n  private determineFileType(fileName: string, content: string): string {\n    const name = fileName.toLowerCase();\n\n    if (name.includes('screen') || name.includes('page')) {\n      return 'screen';\n    }\n\n    if (name.includes('context')) {\n      return 'context';\n    }\n\n    if (name.includes('hook') || (content && content.includes('use') && content.includes('return {'))) {\n      return 'hook';\n    }\n\n    if (name.includes('util') || name.includes('helper')) {\n      return 'util';\n    }\n\n    if (name.includes('type') || name.includes('interface') || name.includes('.d.ts')) {\n      return 'type';\n    }\n\n    if (name.includes('config') || name.includes('setup')) {\n      return 'config';\n    }\n\n    // Default to component for TSX/JSX files\n    if (name.endsWith('.tsx') || name.endsWith('.jsx')) {\n      return 'component';\n    }\n\n    return 'unknown';\n  }\n\n  private extractExports(content: string): string[] {\n    const exports: string[] = [];\n    const exportRegex = /export\\s+(const|function|class|interface|type|default)\\s+(\\w+)/g;\n\n    let match;\n    while ((match = exportRegex.exec(content)) !== null) {\n      exports.push(match[2]);\n    }\n\n    return exports;\n  }\n\n  private extractImports(content: string): string[] {\n    const imports: string[] = [];\n    // Fixed regex to handle all import patterns including relative paths, scoped packages, and complex paths\n    const importRegex = /import\\s+(?:{[^}]*}|\\*\\s+as\\s+\\w+|\\w+)\\s+from\\s+['\"]([^'\"]+)['\"];?/g;\n\n    let match;\n    while ((match = importRegex.exec(content)) !== null) {\n      const importPath = match[1];\n      imports.push(importPath);\n    }\n\n    return imports;\n  }\n\n  /**\n   * Resolve import path to actual file name in the project\n   */\n  private resolveImportPath(importPath: string, currentFile: string): string | null {\n    // Skip node_modules and external packages (anything that doesn't start with ./ or ../)\n    if (!importPath.startsWith('./') && !importPath.startsWith('../')) {\n      return null;\n    }\n\n    // Handle relative paths only\n    const currentDir = path.dirname(currentFile);\n    let resolvedPath = path.join(currentDir, importPath);\n    // Normalize path separators and remove leading ./\n    resolvedPath = resolvedPath.replace(/\\\\/g, '/').replace(/^\\.\\//, '');\n\n    // Try to find the actual file with common extensions\n    const possibleExtensions = ['.ts', '.tsx', '.js', '.jsx', '/index.ts', '/index.tsx', '/index.js', '/index.jsx'];\n\n    for (const ext of possibleExtensions) {\n      const candidatePath = resolvedPath + ext;\n      if (this.fileIndex.has(candidatePath)) {\n        return candidatePath;\n      }\n    }\n\n    // If no extension worked, try exact match\n    if (this.fileIndex.has(resolvedPath)) {\n      return resolvedPath;\n    }\n\n    return null;\n  }\n\n  private mapSnippetType(type: string): 'component' | 'hook' | 'screen' | 'util' | 'type' | 'context' | 'config' | 'unknown' {\n    const normalizedType = type.toLowerCase();\n\n    if (normalizedType.includes('component')) return 'component';\n    if (normalizedType.includes('hook')) return 'hook';\n    if (normalizedType.includes('screen')) return 'screen';\n    if (normalizedType.includes('util') || normalizedType.includes('function')) return 'util';\n    if (normalizedType.includes('type') || normalizedType.includes('interface')) return 'type';\n    if (normalizedType.includes('context') || normalizedType.includes('provider')) return 'context';\n    if (normalizedType.includes('config')) return 'config';\n\n    return 'unknown';\n  }\n}", "startLine": 27, "endLine": 703, "type": "unknown", "symbols": ["TwoStageLLMContextEngine"], "score": 0.8, "context": "This class manages code snippet identification within files, relevant for fixing subscription renewal bugs if related code snippets are involved.", "includesImports": false}, {"filePath": "src/lib/db/project-queries.ts", "content": "", "startLine": 102, "endLine": 102, "type": "util", "symbols": ["Project queries"], "score": 0.8, "context": "Includes functions for retrieving and updating project info, relevant for understanding project-specific subscription and payment details.", "includesImports": false}, {"filePath": "src/lib/services/stream-service.ts", "content": "export class StreamService {\n    private config: StreamServiceConfig;\n    private toolCallCosts: ToolCallCost[] = [];\n\n    constructor(config: StreamServiceConfig) {\n        this.config = config;\n    }\n\n    /**\n     * Start the text streaming process\n     * @returns The result of the streamText call\n     */\n    public startStream() {\n        const {\n            messages,\n            parser,\n            diffParser,\n            dataStream,\n            userMessageId,\n            abortSignal,\n            agentModeEnabled = false,\n            isFirstMessage = false,\n            onFinish,\n            tools = {},\n            temperature = 0,\n            maxSteps = 10,\n            generateMessageId = () => crypto.randomUUID(),\n            isSupabaseConnected = false,\n            enabledTools = [],\n            enableValidation,\n            transformChunking = \"line\",\n            modelId,\n            isConnectingSupabase\n        } = this.config;\n        let streamModelId = modelId\n        if(!modelId) {\n            streamModelId = \"anthropic/claude-sonnet-4\"\n            if (!isFirstMessage) {\n                if(agentModeEnabled) {\n                    // streamModelId = 'anthropic/claude-3.7-sonnet';\n                    streamModelId = 'anthropic/claude-sonnet-4';\n                    // streamModelId = 'openai/gpt-4.1';\n                } else {\n                    streamModelId = \"anthropic/claude-sonnet-4\"\n                }\n            }\n            if(isConnectingSupabase) {\n                streamModelId = \"anthropic/claude-sonnet-4\"\n            }\n        }\n\n        return streamText({\n            model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),\n            maxSteps,\n            experimental_activeTools: enabledTools,\n            experimental_toolCallStreaming: true,\n            toolCallStreaming: true,\n            temperature,\n            messages: messages.filter(m => !!m),\n            experimental_transform: smoothStream({chunking: transformChunking}),\n            maxRetries: 3,\n            abortSignal,\n            experimental_continueSteps: true,\n            onError: (error) => {\n                console.log('Error', error);\n                throw error;\n            },\n            onStepFinish: async (stepResult) => {\n                // console.log('stepResult', stepResult)\n                const body = await fetchFromOpenRouter(stepResult.response.id);\n                // console.log('Step result body', body)\n                \n                // Track tool call cost if data is available\n                if (body && stepResult.response.id) {\n                    const toolCallCost: ToolCallCost = {\n                        id: stepResult.response.id,\n                        model: body.data.model || streamModelId,\n                        tokens_prompt: body.data.tokens_prompt || 0,\n                        tokens_completion: body.data.tokens_completion || 0,\n                        native_tokens_prompt: body.data.native_tokens_prompt,\n                        native_tokens_completion: body.data.native_tokens_completion,\n                        total_cost: body.data.total_cost || 0,\n                        cache_discount: body.data.cache_discount,\n                        provider_name: body.data.provider_name,\n                        timestamp: new Date()\n                    };\n                    console.log('toolCallCost', toolCallCost)\n                    this.toolCallCosts.push(toolCallCost);\n                }\n            },\n            onChunk: ({chunk}) => {\n                if (chunk.type === \"text-delta\") {\n                    try {\n                        // First run the file parser\n                        const afterFileParser = parser.parse(userMessageId, chunk.textDelta);\n\n                        // Then run the diff parser on the output of the file parser\n                        diffParser.parse(userMessageId, afterFileParser);\n                    } catch (e) {\n                        console.error('Error in chunk processing', e);\n                    }\n                } else if (chunk.type === \"tool-call\") {\n                    // Send tool call information to the client\n                    dataStream.writeData({\n                        type: 'tool-call',\n                        content: {\n                            toolCallId: chunk.toolCallId,\n                            toolName: chunk.toolName,\n                            args: chunk.args\n                        }\n                    });\n                } else if (chunk.type === \"tool-result\") {\n                    // Send tool result information to the client\n                    dataStream.writeData({\n                        type: 'tool-result',\n                        content: {\n                            toolCallId: chunk.toolCallId,\n                            toolName: chunk.toolName,\n                            result: chunk.result\n                        }\n                    });\n                }\n            },\n            experimental_generateMessageId: generateMessageId,\n            experimental_repairToolCall: async ({\n                                                    toolCall,\n                                                    tools,\n                                                    error,\n                                                    messages,\n                                                    system,\n                                                }) => {\n                console.log('In repair', error);\n                const result = await generateText({\n                    model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),\n                    system,\n                    messages: [\n                        ...messages,\n                        {\n                            role: 'assistant',\n                            content: [\n                                {\n                                    type: 'tool-call',\n                                    toolCallId: toolCall.toolCallId,\n                                    toolName: toolCall.toolName,\n                                    args: toolCall.args,\n                                },\n                            ],\n                        },\n                        {\n                            role: 'tool' as const,\n                            content: [\n                                {\n                                    type: 'tool-result',\n                                    toolCallId: toolCall.toolCallId,\n                                    toolName: toolCall.toolName,\n                                    result: error.message,\n                                },\n                            ],\n                        },\n                    ],\n                    tools,\n                });\n\n                const newToolCall = result.toolCalls.find(\n                    newToolCall => newToolCall.toolName === toolCall.toolName,\n                );\n\n                return newToolCall != null\n                    ? {\n                        toolCallType: 'function' as const,\n                        toolCallId: toolCall.toolCallId,\n                        toolName: toolCall.toolName,\n                        args: JSON.stringify(newToolCall.args),\n                    }\n                    : null;\n            },\n            tools,\n            onFinish: async (params) => {\n                if (onFinish) {\n                    await onFinish({...params, enableValidation});\n                }\n            },\n        });\n    }\n\n    /**\n     * Sanitize response messages by removing incomplete tool calls\n     * @param messages The messages to sanitize\n     * @returns Sanitized messages\n     */\n    public static sanitizeResponseMessages(messages: AIMessage[]): AIMessage[] {\n        return messages.filter(message => {\n            // Filter out messages that are just incomplete tool calls\n            if (message.role === 'assistant' && Array.isArray(message.content)) {\n                const hasOnlyToolCalls = message.content.every(\n                    part => part.type === 'tool-call'\n                );\n\n                if (hasOnlyToolCalls) {\n                    return false;\n                }\n            }\n            return true;\n        });\n    }\n\n    /**\n     * Get the aggregated cost and token usage for all tool calls\n     * @returns Summary of token usage and costs\n     */\n    public getToolCallCostSummary() {\n        if (this.toolCallCosts.length === 0) {\n            return {\n                count: 0,\n                total_tokens_prompt: 0,\n                total_tokens_completion: 0,\n                total_cost: 0,\n                total_cache_discount: 0,\n                net_cost: 0,\n                tool_calls: []\n            };\n        }\n        \n        const total_tokens_prompt = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_prompt || 0), 0);\n        const total_tokens_completion = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_completion || 0), 0);\n        const total_cost = this.toolCallCosts.reduce((sum, call) => sum + (call.total_cost || 0), 0);\n        const total_cache_discount = this.toolCallCosts.reduce((sum, call) => sum + (call.cache_discount || 0), 0);\n        \n        return {\n            count: this.toolCallCosts.length,\n            total_tokens_prompt: round(total_tokens_prompt, 0),\n            total_tokens_completion: round(total_tokens_completion, 0),\n            total_tokens: round(total_tokens_prompt + total_tokens_completion, 0),\n            total_cost: round(total_cost, 6),\n            total_cache_discount: round(total_cache_discount, 6),\n            net_cost: round(total_cost + total_cache_discount, 6),\n            tool_calls: this.toolCallCosts\n        };\n    }\n    \n    /**\n     * Get a simple formatted string representation of the tool call costs\n     * @returns Formatted string with cost summary\n     */\n    public getToolCallCostReport(): string {\n        const summary = this.getToolCallCostSummary();\n        \n        if (summary.count === 0) {\n            return \"No tool calls recorded.\";\n        }\n        \n        return `Tool Call Summary:\n- Total Calls: ${summary.count}\n- Prompt Tokens: ${summary.total_tokens_prompt}\n- Completion Tokens: ${summary.total_tokens_completion}\n- Total Tokens: ${summary.total_tokens}\n- Total Cost: $${summary.total_cost}\n- Cache Discount: $${summary.total_cache_discount}\n- Net Cost: $${summary.net_cost}`;\n    }\n    \n    /**\n     * Create a new instance with updated configuration\n     * @param updatedConfig The updated configuration\n     * @returns A new StreamService instance\n     */\n    public withConfig(updatedConfig: Partial<StreamServiceConfig>): StreamService {\n        return new StreamService({\n            ...this.config,\n            ...updatedConfig,\n        });\n    }\n}", "startLine": 110, "endLine": 382, "type": "unknown", "symbols": ["StreamService"], "score": 0.7, "context": "This class manages AI streaming, which may include payment handling for API calls, relevant for understanding payment flow.", "includesImports": false}, {"filePath": "src/lib/db/queries.ts", "content": "}", "startLine": 1671, "endLine": 1671, "type": "unknown", "symbols": ["Migration script"], "score": 0.2, "context": "Contains migration scripts that define schema, indirectly relevant for understanding data structure but not core logic.", "includesImports": false}], "additionalFiles": []}}