{"timestamp": "2025-06-15T13:50:10.187Z", "query": "How does the AI streaming and response generation work? Show me the complete flow from user input to AI response", "executionTime": 14619, "snippetsCount": 5, "additionalFilesCount": 0, "totalLines": 1179, "snippets": [{"filePath": "src/lib/services/stream-service.ts", "type": "unknown", "context": "This class handles the AI text streaming process, including configuration, streaming from the model, chunk processing, tool call handling, error handling, and finishing the stream. It represents the core flow from user input messages to AI response streaming.", "score": 1, "lines": 273, "startLine": 110, "endLine": 382, "symbols": ["StreamService"], "preview": "export class StreamService {\n    private config: StreamServiceConfig;\n    private toolCallCosts: ToolCallCost[] = [];\n\n    constructor(config: StreamServiceConfig) {\n..."}, {"filePath": "src/lib/ai/custom-middleware.ts", "type": "unknown", "context": "This middleware wraps the streaming process to handle text chunks, finish chunks, error chunks, and retries on interruptions or rate limits. It is critical for managing the streaming response generation flow and recovery.", "score": 0.9, "lines": 138, "startLine": 3, "endLine": 140, "symbols": ["customMiddleware"], "preview": "export const customMiddleware: LanguageModelV1Middleware = {\n    // In custom-middleware.ts\n    wrapStream: async ({doStream, params, model}) => {\n        const {stream, rawResponse, ...rest} = await doStream();\n        let generatedText = '';\n..."}, {"filePath": "src/lib/ai/index.ts", "type": "util", "context": "This function customizes the fetch request to the AI model API, modifies messages for caching and merging, and logs request details. It is part of the request preparation and response handling in the AI streaming flow.", "score": 0.8, "lines": 371, "startLine": 37, "endLine": 407, "symbols": ["customFetch"], "preview": "const customFetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {\n    // Log request details\n    // console.log('\\n=== OpenRouter Request ===');\n    // @ts-ignore\n    // console.log('URL:', typeof input === 'string' ? input : input.url);\n..."}, {"filePath": "src/lib/chat/tools/query-codebase.ts", "type": "util", "context": "This tool function queries the codebase using a context engine to provide relevant code snippets for AI processing. It is part of the input preparation and context gathering before AI response generation.", "score": 0.6, "lines": 161, "startLine": 23, "endLine": 183, "symbols": ["queryCodebase"], "preview": "export const queryCodebase = ({\n                                  projectId,\n                                  files,\n                                  fileManager,\n                                  messageId = '',\n..."}, {"filePath": "src/lib/chat/tools/get-file-contents.ts", "type": "util", "context": "This tool fetches full file contents or directory listings when snippets are insufficient. It supports the AI's ability to retrieve detailed code context for response generation.", "score": 0.5, "lines": 236, "startLine": 7, "endLine": 242, "symbols": ["getFileContents"], "preview": "export const getFileContents = ({\n                                    fileManager,\n                                    dataStream,\n                                    creditUsageTracker\n                                }: {\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/services/stream-service.ts", "content": "export class StreamService {\n    private config: StreamServiceConfig;\n    private toolCallCosts: ToolCallCost[] = [];\n\n    constructor(config: StreamServiceConfig) {\n        this.config = config;\n    }\n\n    /**\n     * Start the text streaming process\n     * @returns The result of the streamText call\n     */\n    public startStream() {\n        const {\n            messages,\n            parser,\n            diffParser,\n            dataStream,\n            userMessageId,\n            abortSignal,\n            agentModeEnabled = false,\n            isFirstMessage = false,\n            onFinish,\n            tools = {},\n            temperature = 0,\n            maxSteps = 10,\n            generateMessageId = () => crypto.randomUUID(),\n            isSupabaseConnected = false,\n            enabledTools = [],\n            enableValidation,\n            transformChunking = \"line\",\n            modelId,\n            isConnectingSupabase\n        } = this.config;\n        let streamModelId = modelId\n        if(!modelId) {\n            streamModelId = \"anthropic/claude-sonnet-4\"\n            if (!isFirstMessage) {\n                if(agentModeEnabled) {\n                    // streamModelId = 'anthropic/claude-3.7-sonnet';\n                    streamModelId = 'anthropic/claude-sonnet-4';\n                    // streamModelId = 'openai/gpt-4.1';\n                } else {\n                    streamModelId = \"anthropic/claude-sonnet-4\"\n                }\n            }\n            if(isConnectingSupabase) {\n                streamModelId = \"anthropic/claude-sonnet-4\"\n            }\n        }\n\n        return streamText({\n            model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),\n            maxSteps,\n            experimental_activeTools: enabledTools,\n            experimental_toolCallStreaming: true,\n            toolCallStreaming: true,\n            temperature,\n            messages: messages.filter(m => !!m),\n            experimental_transform: smoothStream({chunking: transformChunking}),\n            maxRetries: 3,\n            abortSignal,\n            experimental_continueSteps: true,\n            onError: (error) => {\n                console.log('Error', error);\n                throw error;\n            },\n            onStepFinish: async (stepResult) => {\n                // console.log('stepResult', stepResult)\n                const body = await fetchFromOpenRouter(stepResult.response.id);\n                // console.log('Step result body', body)\n                \n                // Track tool call cost if data is available\n                if (body && stepResult.response.id) {\n                    const toolCallCost: ToolCallCost = {\n                        id: stepResult.response.id,\n                        model: body.data.model || streamModelId,\n                        tokens_prompt: body.data.tokens_prompt || 0,\n                        tokens_completion: body.data.tokens_completion || 0,\n                        native_tokens_prompt: body.data.native_tokens_prompt,\n                        native_tokens_completion: body.data.native_tokens_completion,\n                        total_cost: body.data.total_cost || 0,\n                        cache_discount: body.data.cache_discount,\n                        provider_name: body.data.provider_name,\n                        timestamp: new Date()\n                    };\n                    console.log('toolCallCost', toolCallCost)\n                    this.toolCallCosts.push(toolCallCost);\n                }\n            },\n            onChunk: ({chunk}) => {\n                if (chunk.type === \"text-delta\") {\n                    try {\n                        // First run the file parser\n                        const afterFileParser = parser.parse(userMessageId, chunk.textDelta);\n\n                        // Then run the diff parser on the output of the file parser\n                        diffParser.parse(userMessageId, afterFileParser);\n                    } catch (e) {\n                        console.error('Error in chunk processing', e);\n                    }\n                } else if (chunk.type === \"tool-call\") {\n                    // Send tool call information to the client\n                    dataStream.writeData({\n                        type: 'tool-call',\n                        content: {\n                            toolCallId: chunk.toolCallId,\n                            toolName: chunk.toolName,\n                            args: chunk.args\n                        }\n                    });\n                } else if (chunk.type === \"tool-result\") {\n                    // Send tool result information to the client\n                    dataStream.writeData({\n                        type: 'tool-result',\n                        content: {\n                            toolCallId: chunk.toolCallId,\n                            toolName: chunk.toolName,\n                            result: chunk.result\n                        }\n                    });\n                }\n            },\n            experimental_generateMessageId: generateMessageId,\n            experimental_repairToolCall: async ({\n                                                    toolCall,\n                                                    tools,\n                                                    error,\n                                                    messages,\n                                                    system,\n                                                }) => {\n                console.log('In repair', error);\n                const result = await generateText({\n                    model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),\n                    system,\n                    messages: [\n                        ...messages,\n                        {\n                            role: 'assistant',\n                            content: [\n                                {\n                                    type: 'tool-call',\n                                    toolCallId: toolCall.toolCallId,\n                                    toolName: toolCall.toolName,\n                                    args: toolCall.args,\n                                },\n                            ],\n                        },\n                        {\n                            role: 'tool' as const,\n                            content: [\n                                {\n                                    type: 'tool-result',\n                                    toolCallId: toolCall.toolCallId,\n                                    toolName: toolCall.toolName,\n                                    result: error.message,\n                                },\n                            ],\n                        },\n                    ],\n                    tools,\n                });\n\n                const newToolCall = result.toolCalls.find(\n                    newToolCall => newToolCall.toolName === toolCall.toolName,\n                );\n\n                return newToolCall != null\n                    ? {\n                        toolCallType: 'function' as const,\n                        toolCallId: toolCall.toolCallId,\n                        toolName: toolCall.toolName,\n                        args: JSON.stringify(newToolCall.args),\n                    }\n                    : null;\n            },\n            tools,\n            onFinish: async (params) => {\n                if (onFinish) {\n                    await onFinish({...params, enableValidation});\n                }\n            },\n        });\n    }\n\n    /**\n     * Sanitize response messages by removing incomplete tool calls\n     * @param messages The messages to sanitize\n     * @returns Sanitized messages\n     */\n    public static sanitizeResponseMessages(messages: AIMessage[]): AIMessage[] {\n        return messages.filter(message => {\n            // Filter out messages that are just incomplete tool calls\n            if (message.role === 'assistant' && Array.isArray(message.content)) {\n                const hasOnlyToolCalls = message.content.every(\n                    part => part.type === 'tool-call'\n                );\n\n                if (hasOnlyToolCalls) {\n                    return false;\n                }\n            }\n            return true;\n        });\n    }\n\n    /**\n     * Get the aggregated cost and token usage for all tool calls\n     * @returns Summary of token usage and costs\n     */\n    public getToolCallCostSummary() {\n        if (this.toolCallCosts.length === 0) {\n            return {\n                count: 0,\n                total_tokens_prompt: 0,\n                total_tokens_completion: 0,\n                total_cost: 0,\n                total_cache_discount: 0,\n                net_cost: 0,\n                tool_calls: []\n            };\n        }\n        \n        const total_tokens_prompt = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_prompt || 0), 0);\n        const total_tokens_completion = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_completion || 0), 0);\n        const total_cost = this.toolCallCosts.reduce((sum, call) => sum + (call.total_cost || 0), 0);\n        const total_cache_discount = this.toolCallCosts.reduce((sum, call) => sum + (call.cache_discount || 0), 0);\n        \n        return {\n            count: this.toolCallCosts.length,\n            total_tokens_prompt: round(total_tokens_prompt, 0),\n            total_tokens_completion: round(total_tokens_completion, 0),\n            total_tokens: round(total_tokens_prompt + total_tokens_completion, 0),\n            total_cost: round(total_cost, 6),\n            total_cache_discount: round(total_cache_discount, 6),\n            net_cost: round(total_cost + total_cache_discount, 6),\n            tool_calls: this.toolCallCosts\n        };\n    }\n    \n    /**\n     * Get a simple formatted string representation of the tool call costs\n     * @returns Formatted string with cost summary\n     */\n    public getToolCallCostReport(): string {\n        const summary = this.getToolCallCostSummary();\n        \n        if (summary.count === 0) {\n            return \"No tool calls recorded.\";\n        }\n        \n        return `Tool Call Summary:\n- Total Calls: ${summary.count}\n- Prompt Tokens: ${summary.total_tokens_prompt}\n- Completion Tokens: ${summary.total_tokens_completion}\n- Total Tokens: ${summary.total_tokens}\n- Total Cost: $${summary.total_cost}\n- Cache Discount: $${summary.total_cache_discount}\n- Net Cost: $${summary.net_cost}`;\n    }\n    \n    /**\n     * Create a new instance with updated configuration\n     * @param updatedConfig The updated configuration\n     * @returns A new StreamService instance\n     */\n    public withConfig(updatedConfig: Partial<StreamServiceConfig>): StreamService {\n        return new StreamService({\n            ...this.config,\n            ...updatedConfig,\n        });\n    }\n}", "startLine": 110, "endLine": 382, "type": "unknown", "symbols": ["StreamService"], "score": 1, "context": "This class handles the AI text streaming process, including configuration, streaming from the model, chunk processing, tool call handling, error handling, and finishing the stream. It represents the core flow from user input messages to AI response streaming.", "includesImports": false}, {"filePath": "src/lib/ai/custom-middleware.ts", "content": "export const customMiddleware: LanguageModelV1Middleware = {\n    // In custom-middleware.ts\n    wrapStream: async ({doStream, params, model}) => {\n        const {stream, rawResponse, ...rest} = await doStream();\n        let generatedText = '';\n        const startTime = Date.now();\n\n\n        const transformStream = new TransformStream<\n            LanguageModelV1StreamPart,\n            LanguageModelV1StreamPart\n        >({\n            async transform(chunk, controller) {\n                // Handle text chunks\n                if (chunk.type === 'text-delta') {\n                    generatedText += chunk.textDelta;\n                    controller.enqueue(chunk);\n                }\n                // Handle finish chunks - detect unknown finish reason\n                else if (chunk.type === 'finish') {\n                    console.log(`[Stream] Finished with reason: ${chunk.finishReason} after ${Date.now() - startTime}ms`);\n\n                    if (chunk.finishReason === 'unknown') {\n                        console.warn('[Stream] Detected unknown finish reason - stream was interrupted');\n\n                        // Attempt to restart the stream\n                        const maxRetries = 3;\n                        const baseDelay = 1000; // 1 second\n                        let retryCount = 0;\n\n                        while (retryCount < maxRetries) {\n                            console.log(`[Stream] Attempting to restart stream after interruption (${retryCount + 1}/${maxRetries})...`);\n                            try {\n                                // Exponential backoff: 1s, 2s, 4s\n                                const delay = baseDelay * Math.pow(2, retryCount);\n                                await new Promise(resolve => setTimeout(resolve, delay));\n\n                                const retryStartTime = Date.now();\n                                console.log(`[Stream] Starting retry ${retryCount + 1}`);\n\n                                params.prompt.push({\n                                    role: \"system\",\n                                    content: \"<continuation_of_stream priority='critical'>ULTRA IMPORTANT: We are reattempting to continue the partial stream after interruption. Notice your previous partial response and continue generating from there. Don't re-evaluate. Do not add a preamble again as the last file you were working on is currently open and is being edited. Just </continuation_of_stream>\",\n                                });\n\n                                console.log('params before retrying', JSON.stringify(params))\n                                const retryResult = await doStream();\n                                const reader = retryResult.stream.getReader();\n\n                                console.log(`[Stream] Retry ${retryCount + 1} stream obtained after ${Date.now() - retryStartTime}ms`);\n\n                                // Read from the new stream and forward chunks\n                                while (true) {\n                                    const {done, value} = await reader.read();\n                                    if (done) break;\n                                    controller.enqueue(value);\n                                }\n\n                                console.log(`[Stream] Retry ${retryCount + 1} completed successfully`);\n                                return; // Exit if successful\n                            } catch (retryError) {\n                                const errorMessage = retryError instanceof Error ? retryError.message : String(retryError);\n                                console.error(`[Stream] Retry ${retryCount + 1} failed: ${errorMessage}`);\n                                retryCount++;\n                            }\n                        }\n\n                        // If we get here, all retries failed\n                        console.error(`[Stream] All ${maxRetries} retries failed after stream interruption`);\n                    }\n\n                    // Pass through the finish chunk\n                    controller.enqueue(chunk);\n                }\n                // Handle error chunks - keep your existing 429 handler\n                else if (chunk.type === 'error') {\n                    console.log('[Stream] Error chunk received:', chunk.error);\n\n                    if (typeof chunk.error === \"object\") {\n                        if ((JSON.stringify(chunk.error))?.includes('429')) {\n                            const maxRetries = 3;\n                            const baseDelay = 2000; // 2 seconds\n                            let retryCount = 0;\n\n                            while (retryCount < maxRetries) {\n                                console.log(`[Stream] Got 429, attempt ${retryCount + 1} of ${maxRetries}...`);\n                                try {\n                                    // Exponential backoff: 2s, 4s, 8s\n                                    const delay = baseDelay * Math.pow(2, retryCount);\n                                    await new Promise(resolve => setTimeout(resolve, delay));\n\n                                    const retryStartTime = Date.now();\n                                    const retryResult = await doStream();\n                                    const reader = retryResult.stream.getReader();\n\n                                    console.log(`[Stream] 429 retry ${retryCount + 1} stream obtained after ${Date.now() - retryStartTime}ms`);\n\n                                    while (true) {\n                                        const {done, value} = await reader.read();\n                                        if (done) break;\n                                        controller.enqueue(value);\n                                    }\n\n                                    console.log(`[Stream] 429 retry ${retryCount + 1} completed successfully`);\n                                    return; // Exit if successful\n                                } catch (retryError) {\n                                    const errorMessage = retryError instanceof Error ? retryError.message : String(retryError);\n                                    console.error(`[Stream] 429 retry ${retryCount + 1} failed: ${errorMessage}`);\n                                    retryCount++;\n                                }\n                            }\n\n                            // If we get here, all retries failed\n                            console.error(`[Stream] All ${maxRetries} retries failed after 429 error`);\n                        }\n                    }\n\n                    // Pass through the error chunk\n                    controller.enqueue(chunk);\n                }\n                // Pass through all other chunks\n                else {\n                    controller.enqueue(chunk);\n                }\n            },\n\n            flush() {\n                console.log(`[Stream] Stream completed after ${Date.now() - startTime}ms`);\n                console.log(`[Stream] Total generated text length: ${generatedText.length} characters`);\n            },\n        });\n\n        return {\n            stream: stream.pipeThrough(transformStream),\n            ...rest,\n        };\n    }\n};", "startLine": 3, "endLine": 140, "type": "unknown", "symbols": ["customMiddleware"], "score": 0.9, "context": "This middleware wraps the streaming process to handle text chunks, finish chunks, error chunks, and retries on interruptions or rate limits. It is critical for managing the streaming response generation flow and recovery.", "includesImports": false}, {"filePath": "src/lib/ai/index.ts", "content": "const customFetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {\n    // Log request details\n    // console.log('\\n=== OpenRouter Request ===');\n    // @ts-ignore\n    // console.log('URL:', typeof input === 'string' ? input : input.url);\n    // console.log('Method:', init?.method);\n    // console.log('Headers:', JSON.stringify(init?.headers, null, 2));\n\n    let modifiedInit = {...init};\n\n    if (init?.body) {\n        const bodyObj = JSON.parse(init.body as string);\n\n        bodyObj.parallel_tool_calls = false;\n        if (bodyObj.model.includes(\"claude\") || bodyObj.model.includes(\"gemini\") ) {\n            // Add cache control to system messages\n            if (bodyObj.messages && Array.isArray(bodyObj.messages)) {\n                if (bodyObj.messages[0].role === 'system') {\n                    updateMessageWithCaching(bodyObj.messages[0]);\n                }\n                \n                // For Claude Sonnet 4, merge consecutive assistant messages if the second one has tool calls\n                if (bodyObj.model.includes(\"claude-sonnet-4\")) {\n                    const mergedMessages: any[] = [];\n                    \n                    for (let i = 0; i < bodyObj.messages.length; i++) {\n                        const currentMessage = bodyObj.messages[i];\n                        \n                        // Check if this is an assistant message and the next one is also an assistant with tool calls\n                        if (\n                            currentMessage.role === 'assistant' && \n                            i + 1 < bodyObj.messages.length && \n                            bodyObj.messages[i + 1].role === 'assistant' && \n                            bodyObj.messages[i + 1].tool_calls && \n                            bodyObj.messages[i + 1].tool_calls.length > 0\n                        ) {\n                            // Create a merged message\n                            const nextMessage = bodyObj.messages[i + 1];\n                            \n                            // Handle content merging with proper type handling\n                            let mergedContent;\n                            if (Array.isArray(currentMessage.content) && Array.isArray(nextMessage.content)) {\n                                // Both are arrays, concatenate them with proper type handling\n                                // Use type assertion to help TypeScript understand the structure\n                                const currentContent = currentMessage.content as {type: string, text: string}[];\n                                const nextContent = nextMessage.content as {type: string, text: string}[];\n                                mergedContent = [...currentContent, ...nextContent];\n                            } else if (Array.isArray(currentMessage.content)) {\n                                // Current is array, next is string or other\n                                mergedContent = currentMessage.content as {type: string, text: string}[];\n                            } else if (Array.isArray(nextMessage.content)) {\n                                // Next is array, current is string or other\n                                mergedContent = nextMessage.content as {type: string, text: string}[];\n                            } else {\n                                // Neither is array, use current or fallback to next\n                                mergedContent = currentMessage.content || nextMessage.content;\n                            }\n                            \n                            const mergedMessage = {\n                                ...currentMessage,\n                                tool_calls: nextMessage.tool_calls,\n                                content: mergedContent\n                            };\n                            \n                            mergedMessages.push(mergedMessage);\n                            // Skip the next message since we've merged it\n                            i++;\n                        } else {\n                            mergedMessages.push(currentMessage);\n                        }\n                    }\n                    \n                    bodyObj.messages = mergedMessages;\n                }\n\n                // Find the last system message and mark is cached\n                // const lastSystemMessage = bodyObj.messages.findLast((message: any) => message.role === \"system\");\n                // if (lastSystemMessage) {\n                //     updateMessageWithCaching(lastSystemMessage)\n                // }\n\n                const fileMessage = bodyObj.messages.find((message: any) => {\n                    if (message.role === \"user\") {\n                        return Array.isArray(message.content) ?\n                            message.content.some(\n                                (text: any) => {\n                                    // console.log('text.text', text.text)\n                                    return text.text?.includes(\"<FILE_MESSAGE>\")\n                                }\n                            ) : message.content.includes(\"<FILE_MESSAGE>\")\n                    }\n                    return false;\n                });\n\n                // console.log('Found fileMessage')\n                if (fileMessage) {\n                    updateMessageWithCaching(fileMessage)\n                }\n\n\n                // Find first user message\n                // const firstAssistantResponse = bodyObj.messages.find((message: any) => message.role === \"assistant\");\n                // if (firstAssistantResponse) {\n                //     updateMessageWithCaching(firstAssistantResponse);\n                // }\n                // Detect if we are tool calling loop\n\n\n\n\n                // Find the last message that happened before the current turn\n                // const lastCompleteMessageIndex = bodyObj.messages.findLastIndex((message: any) => {\n                //     return Array.isArray(message.content) && message.content.some((c: any) => c.text.includes(\"Today's date\"))\n                // })\n\n                // bodyObj['provider'] = {\n                //     'order': [\n                //         'Google',\n                //         'Amazon Bedrock',\n                //         'Anthropic',\n                //     ]\n                // };\n\n                bodyObj.messages = bodyObj.messages.map((message: any, index: number) => {\n                    if ([\"user\", \"system\", \"assistant\"].includes(message.role)) {\n                        if(message.tool_calls && message.tool_calls.length > 0) {\n                            message.content = ((message.content?.[0])?.text || message.content);\n                            if(!message.content) {\n                                message.content = ' <hidden></hidden> '\n                            }\n                        } else if (typeof message.content === \"string\") {\n                            if(!bodyObj.model.includes(\"claude-sonnet-4\")) {\n                                message.content = [\n                                    {\n                                        type: \"text\",\n                                        text: message.content || ' <hidden></hidden> '\n                                    }\n                                ]\n                            }\n\n                        }\n                    }\n                    return message;\n                })\n\n                bodyObj.messages = bodyObj.messages.map((message: any, index: number) => {\n                    if(message.role === \"tool\") {\n                        message.content = [\n                            {\n                                type: 'text',\n                                text: (message.content?.[0] as TextPart).text || message.content\n                            }\n                        ]\n                    }\n                    return message;\n                });\n\n                    // this is to ensure we don't get the empty content error\n                bodyObj.messages = bodyObj.messages.map((message: any, index: number) => {\n                    if (Array.isArray(message.content)) {\n                        if(!message.content.length) {\n                            message.content = [\n                                {\n                                    type: 'text'\n                                }\n                            ]\n                        }\n                        // message.content[0].text = 'Empty content';\n                        if(!message.content?.[0].text) {\n                            message.content = 'Empty content';\n                        }\n                    }\n                    return message;\n                })\n\n                const isToolCallingLoop = bodyObj.messages.filter((message: any) => message.role === \"tool\").length > 0;\n\n                if(isToolCallingLoop) {\n                    // We need to prioritize caching these\n                    // find second last tool call\n                    const secondLastToolCall = bodyObj.messages.filter((message: any) => message.role === \"tool\").slice(-2)[0];\n                    if (secondLastToolCall) {\n                        updateMessageWithCaching(secondLastToolCall);\n                    }\n                    const lastToolCallResponse = bodyObj.messages.findLast((message: any) => message.role === \"tool\");\n                    if (lastToolCallResponse) {\n                        updateMessageWithCaching(lastToolCallResponse);\n                    }\n                } else {\n                    // find second last user message\n                    const secondLastUserMessage = bodyObj.messages.filter((message: any) => message.role === \"user\").slice(-2)[0];\n                    if (secondLastUserMessage) {\n                        updateMessageWithCaching(secondLastUserMessage);\n                    }\n                    const lastUserMessage = bodyObj.messages.findLast((message: any) => message.role === \"user\");\n                    if (lastUserMessage) {\n                        updateMessageWithCaching(lastUserMessage);\n                    }\n                }\n\n                // if(lastCompleteMessageIndex !== -1) {\n                //     console.log(`Found message at index ${lastCompleteMessageIndex}`)\n                // Update the previous message as the one with Today's date is the latest user message\n                // updateMessageWithCaching(bodyObj.messages[lastCompleteMessageIndex]);\n                // }\n            }\n\n\n            // Add cache control to tools and function calls\n            if (bodyObj.tools && Array.isArray(bodyObj.tools)) {\n                bodyObj.tools = bodyObj.tools.map((tool: any) => {\n                    // Handle function objects\n                    if (tool.function) {\n                        // tool[\"cache_control\"] = {\n                        //     \"type\": \"ephemeral\"\n                        // };\n                        return {\n                            ...tool,\n                            function: {\n                                ...tool.function,\n                                // \"cache_control\": {\n                                //     \"type\": \"ephemeral\"\n                                // }\n                            },\n                            // \"cache_control\": {\n                            //     \"type\": \"ephemeral\"\n                            // }\n                        };\n                    }\n                    // Handle direct tool objects\n                    return {\n                        ...tool,\n                        // \"cache_control\": {\n                        //     \"type\": \"ephemeral\"\n                        // }\n                    };\n                });\n            }\n        }\n        // Only apply the message truncation logic if not using Claude Sonnet 4\n        // if (!odyObj.model.includes(\"claude-sonnet-4\")) {\n        //     let m: any[] = bodyObj.messages\n        //         // .slice(0, 18);\n        //     m.splice(20, 1)\n        //     bodyObj.messages = [...m]\n        // }\n        // await exportData(bodyObj.messages, 'request-messages')\n\n        modifiedInit.body = JSON.stringify(bodyObj);\n        // console.log('Modified Body:', JSON.stringify(bodyObj));\n\n        // // Request tracker - summarize the request body\n        if (bodyObj.messages && Array.isArray(bodyObj.messages)) {\n            const messageCount = bodyObj.messages.length;\n            const roleCounts: Record<string, number> = {};\n\n            // console.log('\\n=== Request Summary ===');\n            // console.log(`Model: ${bodyObj.model}`);\n            // console.log(`Total Messages: ${messageCount}`);\n\n            let totalTokens = 0;\n\n            // Track message details\n            bodyObj.messages.forEach((message: any, index: number) => {\n                // Count roles\n                roleCounts[message.role] = (roleCounts[message.role] || 0) + 1;\n\n                // Extract content for summary\n                let contentSummary = '';\n                let hasCaching = false;\n\n                if (typeof message.content === 'string') {\n                    contentSummary = message.content.substring(0, 500) + (message.content.length > 500 ? '...' : '');\n                } else if (Array.isArray(message.content)) {\n                    const firstContent = message.content[0];\n                    if (firstContent && firstContent.text) {\n                        contentSummary = firstContent.text.substring(0, 500) + (firstContent.text.length > 500 ? '...' : '');\n                    }\n                    // Check for caching in content array\n                    hasCaching = message.content.some((item: any) => item.cache_control && item.cache_control.type === 'ephemeral');\n                }\n\n                // Check for caching at message level\n                if (message.cache_control && message.cache_control.type === 'ephemeral') {\n                    hasCaching = true;\n                }\n\n                // Estimate token count (very rough estimate: ~4 chars per token)\n                let tokenEstimate = 0;\n                if (typeof message.content === 'string') {\n                    tokenEstimate = Math.ceil(message.content.length / 4);\n                } else if (Array.isArray(message.content)) {\n                    tokenEstimate = Math.ceil(message.content.reduce((sum: number, item: any) => {\n                        return sum + (item.text ? item.text.length : 0);\n                    }, 0) / 4);\n                }\n\n                totalTokens += tokenEstimate;\n                // console.log(`\\n----\\nMessage ${index + 1}:`);\n                // console.log(`  Role: ${message.role}`);\n                // console.log(`  Content: ${contentSummary}`);\n                // console.log(`  Caching: ${hasCaching ? 'Enabled' : 'Not enabled'}`);\n                // console.log(`  Est. Tokens: ~${tokenEstimate}\\n----\\n`);\n\n                // Log tool calls if present\n                if (message.tool_calls && message.tool_calls.length > 0) {\n                    // console.log(`  Tool Calls: ${message.tool_calls.length}`);\n                }\n            });\n\n            // Summary of role distribution\n            // console.log('\\nTotal Tokens:', totalTokens);\n            // console.log('\\nRole Distribution:');\n            // Object.entries(roleCounts).forEach(([role, count]) => {\n            //     console.log(`  ${role}: ${count}`);\n            // });\n\n            // console.log('=== End Request Summary ===\\n');\n        }\n\n\n\n    }\n\n    // Make the actual request\n    const response = await fetch(input, modifiedInit);\n\n    // Clone the response to read it twice\n    // const responseClone = response.clone();\n\n    // Log the entire stream\n    // if (responseClone.body) {\n    //     const reader = responseClone.body.getReader();\n    //     console.log('\\n=== Start of Stream ===');\n    //\n    //     try {\n    //         while (true) {\n    //             const { value, done } = await reader.read();\n    //\n    //             if (done) {\n    //                 console.log('=== End of Stream ===\\n');\n    //                 break;\n    //             }\n    //\n    //             const chunk = new TextDecoder().decode(value);\n    //             // Split by lines to handle SSE format\n    //             const lines = chunk.split('\\n');\n    //             lines.forEach(line => {\n    //                 if (line.trim()) {\n    //                     if (line.startsWith('data: ')) {\n    //                         try {\n    //                             // Try to parse as JSON if possible\n    //                             const jsonData = JSON.parse(line.slice(6));\n    //                             console.log('Parsed JSON:', JSON.stringify(jsonData, null, 2));\n    //                         } catch {\n    //                             // If not JSON, log as is\n    //                             console.log('Raw data:', line.slice(6));\n    //                         }\n    //                     } else {\n    //                         console.log('Non-data line:', line);\n    //                     }\n    //                 }\n    //             });\n    //         }\n    //     } catch (error) {\n    //         console.error('Error reading stream:', error);\n    //     }\n    // }\n\n    return response;\n};", "startLine": 37, "endLine": 407, "type": "util", "symbols": ["customFetch"], "score": 0.8, "context": "This function customizes the fetch request to the AI model API, modifies messages for caching and merging, and logs request details. It is part of the request preparation and response handling in the AI streaming flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/query-codebase.ts", "content": "export const queryCodebase = ({\n                                  projectId,\n                                  files,\n                                  fileManager,\n                                  messageId = '',\n                                  userMessage = ''\n                              }: { projectId: string, files: FileItem[], fileManager: FileLineManager, messageId?: string, userMessage?: string }) => {\n    return tool({\n        description: 'Query the codebase to understand its structure, relationships, and relevant files for a specific task. ' +\n            'Use this instead of requesting individual files. ' +\n            'You can exclude specific files from the analysis by providing an array of file paths in the excludedFiles parameter. ' +\n            'Use filePath parameter to retrieve a SINGLE file in full ONLY when absolutely necessary. You are LIMITED to ONE file per request.',\n        parameters: QueryCodebaseSchema,\n        execute: async ({query, excludedFiles, reason}: z.infer<typeof QueryCodebaseSchema>) => {\n            console.log('Query:', query)\n            console.log('Reason:', reason)\n            console.log('Excluded files:', excludedFiles?.join())\n\n            if(!query) {\n                return {\n                    result: null,\n                    message: \"One of query or filePath is required. To look at the holistic view, use query and to look at a specific file, use filePath.\"\n                }\n            }\n            // Check if this is a \"continue\" message\n            const isContinueMessage = userMessage.toLowerCase().trim().startsWith('continue');\n\n            // Get the tool tracker instance\n            const toolTracker = ToolCountTracker.getInstance();\n\n            console.log('Tool call allowed (queryCodebase):', toolTracker.shouldAllowToolCall(messageId, 'queryCodebase'))\n\n            // Check if we should allow this tool call\n            if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'queryCodebase')) {\n                return {\n                    result: null,\n                    message: \"⚠️ Tool call limit reached. You've already made multiple codebase queries. Please implement or summarize with the information you already have.\",\n                };\n            }\n\n            // Increment the tool count if we have a chat ID\n            if (messageId) {\n                toolTracker.incrementToolCount(messageId, 'queryCodebase');\n            }\n            try {\n                if (!files || files.length === 0) {\n                    throw new Error('No files available for context engine');\n                }\n\n                // Get project information if available\n                let project: Project | null = null;\n                if (projectId) {\n                    try {\n                        project = await getProjectById({id: projectId});\n                    } catch (error) {\n                        console.warn('Could not fetch project information:', error);\n                        // Continue without project info\n                    }\n                }\n\n                if (!project) {\n                    throw new Error('No project information available');\n                }\n\n                // Check if we're requesting a specific file in full\n                // Note: filePath takes precedence over semantic query when both are provided\n                // if (filePath) {\n                //     console.log('Specific file requested:', filePath);\n                //\n                //     // Find the requested file\n                //     const fileItem = fileManager.getFileItems().find(file => file.name === filePath.trim());\n                //\n                //     if (!fileItem) {\n                //         throw new Error(`The requested file was not found: ${filePath}`);\n                //     }\n                //\n                //     const fileItems = [fileItem];\n                //\n                //     // Create snippets for each file\n                //     const snippets = fileItems.map(fileItem => ({\n                //         filePath: fileItem.name,\n                //         content: fileItem.content || '',\n                //         startLine: 1,\n                //         endLine: (fileItem.content || '').split('\\n').length,\n                //         type: 'unknown',\n                //         symbols: [],\n                //         score: 1.0\n                //     }));\n                //\n                //     // Create a simplified result with just the full file contents\n                //     const result = {\n                //         query,\n                //         snippets,\n                //         codebaseStructure: { components: [], screens: [], contexts: [], hooks: [], utils: [], types: [], configs: [] },\n                //         relationships: { imports: {}, exports: {}, dependencies: {}, dependents: {} }\n                //     };\n                //\n                //     let message = `Showing full content of the requested file: ${fileItems[0].name}`;\n                //\n                //     return {\n                //         result,\n                //         message,\n                //         toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')\n                //     };\n                // }\n                \n                // Initialize the context engine with the current files and project\n                const contextEngine = new ContextEngine(fileManager.getFileItems(), project);\n\n                // Default excluded files for Expo Snack projects if not provided\n                const defaultExcludedFiles = [\n                    // Large asset files\n                    'assets/',\n                    '.mp3',\n                    '.mp4',\n                    '.jpg',\n                    '.png',\n                    '.gif',\n                    // Generated type files\n                    'types/generated',\n                    // These don't exist in Expo Snack but kept as fallbacks\n                    'node_modules',\n                    'dist'\n                ];\n\n                // Combine user-provided excludedFiles with defaults\n                const combinedExcludedFiles = excludedFiles ?\n                    [...excludedFiles, ...defaultExcludedFiles] :\n                    defaultExcludedFiles;\n\n                // Query the context engine\n                const result = await contextEngine.query(query, reason, combinedExcludedFiles);\n\n                // Prepare a more informative response\n                let message = `Found ${result.snippets?.length} snippets for your query.`;\n\n                // Add information about excluded files if any\n                if (excludedFiles && excludedFiles.length > 0) {\n                    message += `\\n${excludedFiles.length} files were excluded from the analysis.`;\n                }\n\n                if (result.actionPlan) {\n                    message += `\\n\\nAction Plan: ${result.actionPlan.summary}\\nComplexity: ${result.actionPlan.complexity}\\nSteps: ${result.actionPlan.steps.length}`;\n                }\n\n                // if (result.supabaseSchema) {\n                //     message += `\\n\\nSupabase integration detected with ${result.supabaseSchema.tables.length} tables.`;\n                // }\n\n                return {\n                    result,\n                    message,\n                    toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')\n                };\n            } catch (error) {\n                console.error('Context engine tool error:', error);\n                throw new Error(`Failed to query context engine: ${error instanceof Error ? error.message : 'Unknown error'}`);\n            }\n        },\n    });\n}", "startLine": 23, "endLine": 183, "type": "util", "symbols": ["queryCodebase"], "score": 0.6, "context": "This tool function queries the codebase using a context engine to provide relevant code snippets for AI processing. It is part of the input preparation and context gathering before AI response generation.", "includesImports": false}, {"filePath": "src/lib/chat/tools/get-file-contents.ts", "content": "export const getFileContents = ({\n                                    fileManager,\n                                    dataStream,\n                                    creditUsageTracker\n                                }: {\n    fileManager: FileLineManager,\n    dataStream: DataStreamWriter,\n    creditUsageTracker: CreditUsageTracker\n}) => {\n    return tool({\n        description: 'Get complete file content or list directory contents when queryCodebase snippets are insufficient. Use sparingly - only when you need to see the full file structure, all imports/exports, or complete implementation details that snippets miss.',\n        parameters: z.object({\n            path: z.string().describe(\"File or directory path to retrieve. For files: must match exactly a file from the project. For directories: use folder path like 'components' or 'screens' to list contents. Use '*' to see all top-level directories and files.\"),\n            lineRange: z.array(z.number()).length(2).optional().describe(\"Optional [start, end] line range (1-based, inclusive). Use [start, -1] for start to end of file. Only applicable for files.\"),\n            searchPattern: z.string().optional().describe(\"Optional regex pattern to search within the file. Shows matching lines with context. Only applicable for files.\"),\n            reason: z.string().describe(\"Why you need the full file content or directory listing instead of using queryCodebase snippets.\")\n        }),\n        execute: async ({path, lineRange, searchPattern, reason}: {\n            path: string,\n            lineRange?: [number, number],\n            searchPattern?: string,\n            reason: string\n        }) => {\n            try {\n                const files = fileManager.getFileItems();\n                // Normalize path - handle both /libs and libs formats\n                const normalizedPath = path.startsWith('/') ? path.slice(1) : path;\n\n                // Check if this is a directory listing request\n                const isDirectoryRequest = !normalizedPath.includes('.') || normalizedPath.endsWith('/') || normalizedPath === '*';\n\n                if (isDirectoryRequest) {\n                    // Directory listing\n                    const dirPath = normalizedPath === '*' ? '' : normalizedPath.replace(/\\/$/, ''); // Remove trailing slash\n\n                    // Find all files that start with this directory path\n                    const matchingFiles = files.filter(f => {\n                        const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                        if (dirPath === '') {\n                            // For root or * pattern, include all files\n                            return true;\n                        }\n                        return fileName.startsWith(dirPath + '/');\n                    });\n\n                    if (matchingFiles.length === 0) {\n                        // Show available top-level directories\n                        const topLevelDirs = new Set<string>();\n                        files.forEach(f => {\n                            const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                            const firstSlash = fileName.indexOf('/');\n                            if (firstSlash > 0) {\n                                topLevelDirs.add(fileName.substring(0, firstSlash));\n                            }\n                        });\n\n                        return `Directory ${path} not found. Available directories: ${Array.from(topLevelDirs).join(', ')}`;\n                    }\n\n                    // Group files by subdirectory with depth 2 support\n                    const structure = new Map<string, { files: string[], subdirs: Map<string, string[]> }>();\n\n                    matchingFiles.forEach(f => {\n                        const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                        const relativePath = fileName.substring(dirPath.length + (dirPath ? 1 : 0));\n                        const pathParts = relativePath.split('/');\n\n                        if (pathParts.length === 1) {\n                            // Direct file in this directory\n                            if (!structure.has('.')) structure.set('.', { files: [], subdirs: new Map() });\n                            structure.get('.')!.files.push(pathParts[0]);\n                        } else if (pathParts.length === 2) {\n                            // File in immediate subdirectory (depth 1)\n                            const subDir = pathParts[0];\n                            if (!structure.has(subDir)) structure.set(subDir, { files: [], subdirs: new Map() });\n                            structure.get(subDir)!.files.push(pathParts[1]);\n                        } else if (pathParts.length >= 3) {\n                            // File in nested subdirectory (depth 2+)\n                            const subDir = pathParts[0];\n                            const nestedSubDir = pathParts[1];\n                            if (!structure.has(subDir)) structure.set(subDir, { files: [], subdirs: new Map() });\n\n                            const subDirData = structure.get(subDir)!;\n                            if (!subDirData.subdirs.has(nestedSubDir)) {\n                                subDirData.subdirs.set(nestedSubDir, []);\n                            }\n                            subDirData.subdirs.get(nestedSubDir)!.push(pathParts.slice(2).join('/'));\n                        }\n                    });\n\n                    // Format directory listing\n                    const displayPath = path === '*' ? 'Project Root' : path;\n                    let listing = `Directory: ${displayPath} (${matchingFiles.length} items)\\n\\n`;\n\n                    // Show subdirectories first\n                    const sortedKeys = Array.from(structure.keys()).sort((a, b) => {\n                        if (a === '.') return 1; // Files last\n                        if (b === '.') return -1;\n                        return a.localeCompare(b);\n                    });\n\n                    sortedKeys.forEach(key => {\n                        if (key === '.') {\n                            // Direct files\n                            const directFiles = structure.get(key)!.files.sort();\n                            directFiles.forEach(file => {\n                                const fullPath = dirPath ? `${dirPath}/${file}` : file;\n                                listing += `📄 ${file} (${fullPath})\\n`;\n                            });\n                        } else {\n                            // Subdirectory\n                            const subDirData = structure.get(key)!;\n                            const subDirPath = dirPath ? `${dirPath}/${key}` : key;\n                            const totalFiles = subDirData.files.length +\n                                Array.from(subDirData.subdirs.values()).reduce((sum, files) => sum + files.length, 0);\n\n                            listing += `📁 ${key}/ (${totalFiles} files) - ${subDirPath}/\\n`;\n\n                            // Show files in this subdirectory\n                            subDirData.files.sort().forEach(file => {\n                                listing += `  📄 ${file} (${subDirPath}/${file})\\n`;\n                            });\n\n                            // Show nested subdirectories (depth 2)\n                            const sortedSubDirs = Array.from(subDirData.subdirs.keys()).sort();\n                            sortedSubDirs.forEach(nestedDir => {\n                                const nestedFiles = subDirData.subdirs.get(nestedDir)!;\n                                const nestedPath = `${subDirPath}/${nestedDir}`;\n                                listing += `  📁 ${nestedDir}/ (${nestedFiles.length} files) - ${nestedPath}/\\n`;\n\n                                // Show first few files in nested directory\n                                const filesToShow = nestedFiles.slice(0, 3);\n                                filesToShow.forEach(file => {\n                                    listing += `    📄 ${file} (${nestedPath}/${file})\\n`;\n                                });\n\n                                if (nestedFiles.length > 3) {\n                                    listing += `    ... and ${nestedFiles.length - 3} more files\\n`;\n                                }\n                            });\n                        }\n                    });\n\n                    return listing;\n                }\n\n                // File content request\n                const fileItem = files.find(f => f.name === normalizedPath || f.name === `/${normalizedPath}`);\n\n                if (!fileItem) {\n                    return `File ${path} not found. Available files: ${files.slice(0, 10).map(f => f.name).join(', ')}${files.length > 10 ? '...' : ''}`;\n                }\n\n                let content = fileItem.content;\n                const lines = content.split('\\n');\n                const totalLines = lines.length;\n\n                // Apply search pattern if specified\n                if (searchPattern) {\n                    try {\n                        const regex = new RegExp(searchPattern, 'gi');\n                        const matchingLines: string[] = [];\n                        const contextBefore = 3;\n                        const contextAfter = 3;\n\n                        let lastIncludedLine = -1;\n\n                        lines.forEach((line, index) => {\n                            if (regex.test(line)) {\n                                const startContext = Math.max(0, index - contextBefore);\n                                const endContext = Math.min(lines.length - 1, index + contextAfter);\n\n                                // Add gap indicator if there's a gap\n                                if (startContext > lastIncludedLine + 1) {\n                                    matchingLines.push('...');\n                                }\n\n                                // Add context and matching line\n                                for (let i = startContext; i <= endContext; i++) {\n                                    if (i > lastIncludedLine) {\n                                        const lineNumber = i + 1;\n                                        const prefix = i === index ? '>' : ' ';\n                                        matchingLines.push(`${prefix}${lineNumber.toString().padStart(4)}: ${lines[i]}`);\n                                        lastIncludedLine = i;\n                                    }\n                                }\n                            }\n                        });\n\n                        content = matchingLines.join('\\n');\n                    } catch (regexError) {\n                        return `Invalid regex pattern: ${searchPattern}`;\n                    }\n                } else if (lineRange) {\n                    // Apply line range if specified\n                    const [start, end] = lineRange;\n                    const startLine = Math.max(1, start) - 1; // Convert to 0-based\n                    const endLine = end === -1 ? lines.length : Math.min(end, lines.length);\n\n                    const selectedLines = lines.slice(startLine, endLine);\n                    content = selectedLines.map((line, index) => {\n                        const lineNumber = startLine + index + 1;\n                        return `${lineNumber.toString().padStart(4)}: ${line}`;\n                    }).join('\\n');\n                } else {\n                    // Return full file with line numbers\n                    content = lines.map((line, index) => {\n                        const lineNumber = index + 1;\n                        return `${lineNumber.toString().padStart(4)}: ${line}`;\n                    }).join('\\n');\n                }\n\n                creditUsageTracker.trackOperation('tool_call');\n\n                const resultLines = content.split('\\n').length;\n                const truncated = resultLines > 500;\n\n                if (truncated) {\n                    const truncatedLines = content.split('\\n').slice(0, 500);\n                    content = truncatedLines.join('\\n') + '\\n\\n<response clipped - file too long>';\n                }\n\n                return `File: ${path} (${totalLines} lines total, showing ${truncated ? '500' : resultLines} lines)\nReason: ${reason}\n${lineRange ? `Line range: ${lineRange[0]}-${lineRange[1] === -1 ? 'end' : lineRange[1]}` : ''}\n${searchPattern ? `Search pattern: ${searchPattern}` : ''}\n\n${content}`;\n\n            } catch (error: any) {\n                console.error('Error in getFileContents:', error);\n                return `Error retrieving file content: ${error.message}`;\n            }\n        }\n    });\n};", "startLine": 7, "endLine": 242, "type": "util", "symbols": ["getFileContents"], "score": 0.5, "context": "This tool fetches full file contents or directory listings when snippets are insufficient. It supports the AI's ability to retrieve detailed code context for response generation.", "includesImports": false}], "additionalFiles": []}}