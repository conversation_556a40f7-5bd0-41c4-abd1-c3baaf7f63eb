{"timestamp": "2025-06-14T17:23:51.426Z", "query": "Find the authentication system and how user sessions are managed across the application", "executionTime": 13633, "snippetsCount": 5, "additionalFilesCount": 0, "totalLines": 68, "snippets": [{"filePath": "src/lib/auth/auth.utils.ts", "type": "util", "context": "This function directly calls the auth() method to get the current user session and checks if the user is authenticated, returning 401 if not. It is a core part of the authentication system and session validation.", "score": 1, "lines": 9, "startLine": 1, "endLine": 9, "symbols": ["checkAccess"], "preview": "import {auth} from \"@/app/(auth)/auth\";\n\nexport const checkAccess = async () => {\n    const session = await auth();\n\n..."}, {"filePath": "src/lib/store/session-store.ts", "type": "type", "context": "Defines the structure of session data stored in the in-memory session store, including user state and status, which is essential for understanding how user sessions are managed.", "score": 0.9, "lines": 14, "startLine": 6, "endLine": 19, "symbols": ["SessionData interface"], "preview": "interface SessionData {\n  html: string;\n  prompt: string;\n  lastUpdated: Date;\n  status: 'initializing' | 'generating' | 'complete' | 'error';\n..."}, {"filePath": "src/lib/store/session-store.ts", "type": "util", "context": "Function to create a new session with a session ID and initial data, including setting an expiry timeout. This is key to session management.", "score": 0.9, "lines": 28, "startLine": 21, "endLine": 48, "symbols": ["createSession"], "preview": "// In-memory store for sessions\nconst sessions = new Map<string, SessionData>();\n\n// Default expiry time (30 minutes)\nconst SESSION_EXPIRY_MS = 30 * 60 * 1000;\n..."}, {"filePath": "src/lib/store/session-store.ts", "type": "util", "context": "Function to retrieve session data by session ID from the in-memory store, essential for session retrieval.", "score": 0.9, "lines": 3, "startLine": 53, "endLine": 55, "symbols": ["getSession"], "preview": "export function getSession(sessionId: string): SessionData | undefined {\n  return sessions.get(sessionId);\n}"}, {"filePath": "src/lib/store/session-store.ts", "type": "util", "context": "Function to clean up expired sessions and the setup of periodic cleanup on the server side, important for session lifecycle management.", "score": 0.8, "lines": 14, "startLine": 180, "endLine": 193, "symbols": ["cleanupExpiredSessions and periodic cleanup setup"], "preview": "export function cleanupExpiredSessions(): void {\n  const now = new Date();\n  for (const [sessionId, session] of sessions.entries()) {\n    if (now.getTime() - session.lastUpdated.getTime() > SESSION_EXPIRY_MS) {\n      sessions.delete(sessionId);\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/auth/auth.utils.ts", "content": "import {auth} from \"@/app/(auth)/auth\";\n\nexport const checkAccess = async () => {\n    const session = await auth();\n\n    if (!session || !session.user || !session.user.id) {\n        return new Response('Unauthorized', {status: 401});\n    }\n}", "startLine": 1, "endLine": 9, "type": "util", "symbols": ["checkAccess"], "score": 1, "context": "This function directly calls the auth() method to get the current user session and checks if the user is authenticated, returning 401 if not. It is a core part of the authentication system and session validation.", "includesImports": false}, {"filePath": "src/lib/store/session-store.ts", "content": "interface SessionData {\n  html: string;\n  prompt: string;\n  lastUpdated: Date;\n  status: 'initializing' | 'generating' | 'complete' | 'error';\n  // Track incremental updates\n  contentChunks: string[];\n  // Track user interactions (scroll position, form inputs, etc.)\n  userState?: {\n    scrollPosition?: { x: number; y: number };\n    formValues?: Record<string, string>;\n    activeElement?: string;\n  };\n}", "startLine": 6, "endLine": 19, "type": "type", "symbols": ["SessionData interface"], "score": 0.9, "context": "Defines the structure of session data stored in the in-memory session store, including user state and status, which is essential for understanding how user sessions are managed.", "includesImports": false}, {"filePath": "src/lib/store/session-store.ts", "content": "// In-memory store for sessions\nconst sessions = new Map<string, SessionData>();\n\n// Default expiry time (30 minutes)\nconst SESSION_EXPIRY_MS = 30 * 60 * 1000;\n\n/**\n * Create a new design preview session\n */\nexport function createSession(sessionId: string, prompt: string): void {\n  sessions.set(sessionId, {\n    html: getInitialHTML(),\n    prompt,\n    lastUpdated: new Date(),\n    status: 'initializing',\n    contentChunks: [],\n    userState: {\n      scrollPosition: { x: 0, y: 0 },\n      formValues: {},\n      activeElement: ''\n    }\n  });\n\n  // Set up automatic cleanup after session expires\n  setTimeout(() => {\n    sessions.delete(sessionId);\n  }, SESSION_EXPIRY_MS);\n}", "startLine": 21, "endLine": 48, "type": "util", "symbols": ["createSession"], "score": 0.9, "context": "Function to create a new session with a session ID and initial data, including setting an expiry timeout. This is key to session management.", "includesImports": false}, {"filePath": "src/lib/store/session-store.ts", "content": "export function getSession(sessionId: string): SessionData | undefined {\n  return sessions.get(sessionId);\n}", "startLine": 53, "endLine": 55, "type": "util", "symbols": ["getSession"], "score": 0.9, "context": "Function to retrieve session data by session ID from the in-memory store, essential for session retrieval.", "includesImports": false}, {"filePath": "src/lib/store/session-store.ts", "content": "export function cleanupExpiredSessions(): void {\n  const now = new Date();\n  for (const [sessionId, session] of sessions.entries()) {\n    if (now.getTime() - session.lastUpdated.getTime() > SESSION_EXPIRY_MS) {\n      sessions.delete(sessionId);\n    }\n  }\n}\n\n// Set up periodic cleanup if running on server\nif (typeof window === 'undefined') {\n  const CLEANUP_INTERVAL = 15 * 60 * 1000; // 15 minutes\n  setInterval(cleanupExpiredSessions, CLEANUP_INTERVAL);\n}", "startLine": 180, "endLine": 193, "type": "util", "symbols": ["cleanupExpiredSessions and periodic cleanup setup"], "score": 0.8, "context": "Function to clean up expired sessions and the setup of periodic cleanup on the server side, important for session lifecycle management.", "includesImports": false}], "additionalFiles": []}}