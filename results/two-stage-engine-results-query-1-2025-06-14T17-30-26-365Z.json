{"timestamp": "2025-06-14T17:30:26.367Z", "query": "Show me how the two-stage context engine works and how it selects relevant files for queries", "executionTime": 30273, "snippetsCount": 3, "additionalFilesCount": 0, "totalLines": 701, "snippets": [{"filePath": "src/lib/services/two-stage-context-engine.ts", "type": "unknown", "context": "This class implements the two-stage context engine, showing how it selects relevant files in the first stage and relevant snippets in the second stage. It includes methods for building file index, getting codebase structure, finding relevant files, identifying snippets, and extracting snippets with minimal context, directly addressing the query.", "score": 1, "lines": 527, "startLine": 27, "endLine": 553, "symbols": ["TwoStageLLMContextEngine"], "preview": "export class TwoStageLLMContextEngine {\n  private files: FileItem[] = [];\n  private fileIndex: Map<string, FileItem> = new Map();\n\n  /**\n..."}, {"filePath": "src/lib/services/context-engine.ts", "type": "util", "context": "This private async method in ContextEngine uses AI to determine which files are most relevant to a query, filtering out excluded files. It is critical to understanding how relevant files are selected for queries, complementing the two-stage engine's file selection.", "score": 0.9, "lines": 42, "startLine": 564, "endLine": 605, "symbols": ["findRelevantFiles"], "preview": "    private async findRelevantFiles(query: string, excludedFiles: string[]): Promise<string[]> {\n        // Get the list of files that are not excluded\n        const availableFiles = this.files.filter(file => !this.shouldExcludeFile(file.name, excludedFiles));\n\n        // Use AI to determine which files are most relevant to the query\n..."}, {"filePath": "scripts/test-two-stage-context-engine.ts", "type": "util", "context": "This script demonstrates usage of the TwoStageLLMContextEngine with real queries, showing how it is initialized, how queries are run, and how results are processed and saved. It provides practical insight into the two-stage engine's operation and file selection for queries.", "score": 0.8, "lines": 132, "startLine": 298, "endLine": 429, "symbols": ["testTwoStageContextEngine"], "preview": "async function testTwoStageContextEngine() {\n  console.log('🚀 Testing Two-Stage Context Engine with Current Codebase\\n');\n\n  // Parse command line arguments\n  const { useDefault, queries } = parseArguments();\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/services/two-stage-context-engine.ts", "content": "export class TwoStageLLMContextEngine {\n  private files: FileItem[] = [];\n  private fileIndex: Map<string, FileItem> = new Map();\n\n  /**\n   * Initialize with project files\n   */\n  constructor(files: FileItem[]) {\n    this.files = files;\n    this.buildIndex();\n  }\n\n  /**\n   * Build an index of files for quick lookup\n   */\n  private buildIndex(): void {\n    for (const file of this.files) {\n      this.fileIndex.set(file.name, file);\n    }\n  }\n\n  /**\n   * Get a minimal structural representation of the codebase\n   * This provides enough context for the first LLM while keeping token usage low\n   */\n  private getCodebaseStructure(): string {\n    // Build a map of imports for quick lookup\n    const importMap = new Map<string, Set<string>>();\n\n    // First pass: extract imports from all files\n    this.files\n      .forEach(file => {\n        const imports = this.extractImports(file.content || \"\");\n        imports.forEach(importPath => {\n          // Resolve import path to actual file name\n          const resolvedPath = this.resolveImportPath(importPath, file.name);\n\n          if (resolvedPath) {\n            // Add to the import map\n            if (!importMap.has(resolvedPath)) {\n              importMap.set(resolvedPath, new Set());\n            }\n            importMap.get(resolvedPath)?.add(file.name);\n          }\n        });\n      });\n\n    // Second pass: create the structure with import relationships\n    return this.files\n      .map(file => {\n        const fileType = this.determineFileType(file.name, file.content || \"\");\n        const exports = this.extractExports(file.content || \"\");\n\n        // Find files that import this file (limited to 3 for brevity)\n        const importedBy = Array.from(importMap.get(file.name) || []).slice(0, 3);\n\n        let result = `${file.name} - ${fileType}`;\n\n        if (exports.length > 0) {\n          result += ` - Exports: ${exports.join(\", \")}`;\n        }\n\n        if (importedBy.length > 0) {\n          result += ` - Used by: ${importedBy.join(\", \")}`;\n        }\n\n        return result;\n      })\n      .join(\"\\n\");\n  }\n\n  /**\n   * First stage: Find relevant files for a query\n   */\n  async findRelevantFiles(query: string, reason: string, excludedFiles: string[] = []): Promise<{files: string[], reasoning: string }> {\n    console.time('find-relevant-files');\n\n    // Get a compact representation of the codebase structure\n    const codebaseStructure = this.getCodebaseStructure();\n\n    console.log('codebaseStructure', codebaseStructure)\n\n    // Use LLM to identify relevant files\n    const result = await generateObject({\n      model: customModel('openai/gpt-4.1-mini'), // Using a smaller model to reduce costs\n      temperature: 0.1,\n      schema: z.object({\n        files: z.array(z.string()),\n        reasoning: z.string().describe(\"Explanation of why these files were selected\")\n      }),\n      system: `You are a code analysis expert. Your task is to identify which files in a React Native Expo project are most relevant to a specific query.\n\nYou will be given:\n1. A query about the codebase\n2. A structured representation of files in the project with their types and exports\n\nReturn a JSON object with:\n1. An array of the most relevant file paths (maximum 20)\n2. Your reasoning for selecting these files\n̄3. AVOID selecting files that are not referenced anywhere (Dead code) unless relevant to the query or asked for it\n4. Make sure to select files to give a full picture and not just direct relevance, use relevance scores properly\n\n\nChoose files that would be most helpful for understanding or implementing the query.`,\n      prompt: `Query: ${query}\n      \nReason: ${reason}\n\nFiles in the project:\n${codebaseStructure}\n\n${excludedFiles.length > 0 ? `Note: The following files have been excluded from analysis: ${excludedFiles.join(', ')}\\n\\n` : ''}\nReturn the most relevant file paths (maximum 20) and your reasoning:`,\n    });\n\n    console.timeEnd('find-relevant-files');\n    console.log(`Selected files reasoning: ${result.object.reasoning}`);\n\n    // Filter out any files that don't exist in our index\n    return {files: result.object.files.filter(file => this.fileIndex.has(file)), reasoning: result.object.reasoning};\n  }\n\n  /**\n   * Second stage: Identify relevant snippets within files\n   */\n  async identifyRelevantSnippets(query: string, originalReason: string, relevantFiles: string[], previousStageReason: string): Promise<SnippetIdentification[]> {\n    console.time('identify-snippets');\n\n\n    // Prepare file contents with line numbers\n    const fileContents = relevantFiles.map(fileName => {\n      const file = this.fileIndex.get(fileName);\n      const content = file?.content || \"\";\n\n      // Add line numbers to help the LLM identify specific ranges\n      // Format with consistent padding to make line numbers stand out\n      const lines = content.split(\"\\n\");\n      const maxLineNumberWidth = String(lines.length).length;\n      const numberedContent = lines.map((line, i) => {\n        const lineNumber = i + 1;\n        const paddedLineNumber = String(lineNumber).padStart(maxLineNumberWidth, ' ');\n        return `${paddedLineNumber}: ${line}`;\n      }).join(\"\\n\");\n\n      return {\n        name: fileName,\n        content: numberedContent,\n        lineCount: lines.length\n      };\n    });\n\n    // Use LLM to identify relevant snippets within these files\n    const result = await generateObject({\n      model: customModel('openai/gpt-4.1-mini'), // Using a more capable model for this task\n      temperature: 0.1,\n      schema: z.object({\n        snippets: z.array(z.object({\n          fileName: z.string(),\n          startLine: z.number(),\n          endLine: z.number(),\n          snippetType: z.string(),\n          snippetName: z.string(),\n          relevanceScore: z.number().min(0).max(1),\n          reasoning: z.string()\n        }))\n      }),\n      system: `You are a code analysis expert. Your task is to identify the MINIMAL relevant code snippets within files for a specific query.\n\nReturn the exact line numbers for each snippet, along with metadata about the snippet.\n\nGuidelines for MINIMAL context extraction:\n1. Extract ONLY the specific lines directly relevant to the query - avoid including entire files or anything extra at all\n2. For imports, include ONLY the imports directly related to the query and is needed to reliable editing\n2. For style, include ONLY the styles directly related to the query and is needed to reliable editing\n3. For components, include ONLY the component declaration, props, and relevant JSX elements - NOT the entire implementation\n4. For functions, include ONLY the signature and critical logic - NOT entire function bodies\n5. Assign a relevance score from 0.0 to 1.0 based on how directly the snippet addresses the query. \n6. Keep snippets as SHORT as possible while maintaining necessary context\n7. Pay close attention to the line numbers at the beginning of each line (formatted as \"NUMBER: code\")\n8. For React errors, focus on component declarations, imports/exports, and JSX return statements\n9. NEVER include style definitions unless they are directly relevant to the query\n10. NEVER include helper functions unless they are directly relevant to the query\n11. When specific line numbers are requested, return only those line numbers\n\nToken efficiency guidelines:\n1. Maximum 30 lines per snippet unless absolutely necessary\n4. Omit implementation details of methods unless directly relevant\n5. For UI issues, include only the relevant JSX elements, not entire render methods\n6. When multiple similar components exist, include only one representative example\\``,\n      prompt: `Query: ${query}\n\nI need to identify the MINIMAL relevant code snippets in these files. For each relevant code block, provide:\n1. The file name\n2. Start and end line numbers (inclusive) - use the exact line numbers shown at the beginning of each line\n3. Type of snippet (function, component, hook, type, etc.)\n4. Name of the snippet (function name, component name, etc.)\n5. Relevance score (0.0 to 1.0)\n6. Brief reasoning for why this snippet is relevant\n\nReliable editing:\n- Please include import and styles if they need are needed to add imports\n- Include just enough context for clear understanding and editing \n\nCRITICAL TOKEN EFFICIENCY GUIDELINES:\n- Extract ONLY the specific lines directly relevant to the query\n- Each line in the files is prefixed with its line number (e.g., \"42: const foo = bar;\"). Use these exact line numbers.\n- For imports, include ONLY those directly related to the query\n- For components, include ONLY the declaration, props, and relevant JSX - NOT entire implementations\n- NEVER include style definitions unless directly relevant to the query\n- Keep snippets to a MAXIMUM of 30 lines when possible\n- For React errors, focus ONLY on component declarations, imports/exports, and JSX return statements\n- AVOID including entire component implementations - be extremely selective\n\nReasoning from the user/llm issuing the original query on why they want the result. This gets fed to the first engine:\n${originalReason}\n\nReasoning from the first engine on why each file was selected. Use the reasoning to understand the whole flow and to select snippets accordingly:\n${previousStageReason}\n\nFiles to analyze:\n${fileContents.map(file => `\n=== ${file.name} (${file.lineCount} lines) ===\n${file.content}\n`).join(\"\\n\\n\")}\n\nReturn an array of the most relevant code snippets with their exact line numbers and metadata:`,\n    });\n\n    console.timeEnd('identify-snippets');\n    return result.object.snippets;\n  }\n\n  /**\n   * Smart truncation to fit within line budget while preserving understanding context\n   */\n  smartTruncateSnippets(snippets: CodeSnippet[], maxLines: number = 600): {truncatedSnippets: CodeSnippet[], additionalFromTruncation: {fileName: string, reason: string, suggestedQuery: string}[]} {\n    // Categorize snippets by type for strategic selection\n    const implementation = snippets.filter(s => (s.score || 0) >= 0.9);\n    const usage = snippets.filter(s => (s.score || 0) >= 0.7 && (s.score || 0) < 0.9);\n    const context = snippets.filter(s => (s.score || 0) < 0.7);\n\n    let currentLines = 0;\n    const truncatedSnippets: CodeSnippet[] = [];\n    const additionalFromTruncation: {fileName: string, reason: string, suggestedQuery: string}[] = [];\n\n    // Strategy: Always include implementation, then usage, then context within budget\n    const prioritizedSnippets = [\n      ...orderBy(implementation, ['score'], ['desc']).slice(0, 2), // Max 2 implementation snippets\n      ...orderBy(usage, ['score'], ['desc']).slice(0, 2), // Max 2 usage examples\n      ...orderBy(context, ['score'], ['desc']).slice(0, 1)  // Max 1 context snippet\n    ];\n\n    for (const snippet of prioritizedSnippets) {\n      const snippetLines = snippet.content.split('\\n').length;\n\n      if (currentLines + snippetLines <= maxLines) {\n        truncatedSnippets.push(snippet);\n        currentLines += snippetLines;\n      } else {\n        // Add to additional files instead of truncating content\n        additionalFromTruncation.push({\n          fileName: snippet.filePath,\n          reason: `Relevant but truncated due to 1000-line budget (relevance: ${(snippet.score || 0).toFixed(2)}, ${snippetLines} lines)`,\n          suggestedQuery: `Show ${snippet.symbols.join(', ')} in ${snippet.filePath}`\n        });\n      }\n    }\n\n    // Add any remaining snippets to additional files\n    const remainingSnippets = snippets.filter(s => !truncatedSnippets.includes(s) && !additionalFromTruncation.some(a => a.fileName === s.filePath));\n    for (const snippet of remainingSnippets) {\n      additionalFromTruncation.push({\n        fileName: snippet.filePath,\n        reason: `Additional context available (relevance: ${(snippet.score || 0).toFixed(2)})`,\n        suggestedQuery: `Show ${snippet.symbols.join(', ')} in ${snippet.filePath}`\n      });\n    }\n\n    console.log(`Smart truncation: kept ${truncatedSnippets.length}/${snippets.length} snippets within ${maxLines} line budget (${currentLines} lines total)`);\n    console.log(`  - Implementation: ${implementation.length} found, ${truncatedSnippets.filter(s => (s.score || 0) >= 0.9).length} kept`);\n    console.log(`  - Usage: ${usage.length} found, ${truncatedSnippets.filter(s => (s.score || 0) >= 0.7 && (s.score || 0) < 0.9).length} kept`);\n    console.log(`  - Context: ${context.length} found, ${truncatedSnippets.filter(s => (s.score || 0) < 0.7).length} kept`);\n\n    return {truncatedSnippets, additionalFromTruncation};\n  }\n\n  /**\n   * Extract actual code snippets based on line numbers\n   */\n  extractSnippets(snippetIdentifications: SnippetIdentification[]): CodeSnippet[] {\n    // Group snippets by file to avoid duplicate processing\n    const snippetsByFile = new Map<string, SnippetIdentification[]>();\n\n    for (const snippet of snippetIdentifications) {\n      if (!snippetsByFile.has(snippet.fileName)) {\n        snippetsByFile.set(snippet.fileName, []);\n      }\n      snippetsByFile.get(snippet.fileName)?.push(snippet);\n    }\n\n    const results: CodeSnippet[] = [];\n\n    // Process each file's snippets\n    for (const [fileName, fileSnippets] of snippetsByFile.entries()) {\n      const file = this.fileIndex.get(fileName);\n      if (!file || !file.content) continue;\n\n      const lines = file.content.split(\"\\n\");\n\n      // Find import statements (usually at the top of the file)\n      const importEndLine = this.findImportEndLine(lines);\n      const hasImports = importEndLine > 0;\n\n      // Process each snippet in the file\n      for (const identification of fileSnippets) {\n        // Ensure line numbers are within bounds\n        const startLine = Math.max(1, Math.min(identification.startLine, lines.length));\n        const endLine = Math.max(startLine, Math.min(identification.endLine, lines.length));\n\n        // Removing as this is causing the llm issues to understand\n        const shouldIncludeImports = false;\n\n        // Determine if we should include imports\n        // const shouldIncludeImports = hasImports &&\n        //     identification.snippetType.toLowerCase() !== 'import' &&\n        //     startLine > importEndLine;\n\n        // Extract the snippet content with imports if needed\n        let snippetLines: string[];\n        let actualStartLine: number;\n\n        if (shouldIncludeImports) {\n          // Include imports and the actual snippet\n          const importLines = lines.slice(0, importEndLine);\n          const codeLines = lines.slice(startLine - 1, endLine);\n\n          // Add a separator between imports and code\n          snippetLines = [...importLines, '', '// Main snippet', ...codeLines];\n          actualStartLine = 1; // Starting from the beginning of the file\n        } else {\n          // Just include the snippet itself\n          snippetLines = lines.slice(startLine - 1, endLine);\n          actualStartLine = startLine;\n        }\n\n        const content = snippetLines.join(\"\\n\");\n\n        // Log the extraction for debugging\n        console.log(`Extracting snippet from ${identification.fileName}:`);\n        console.log(`  Lines ${startLine}-${endLine} (${snippetLines.length} lines)`);\n        console.log(`  Type: ${identification.snippetType}, Name: ${identification.snippetName}`);\n        console.log(`  Relevance: ${identification.relevanceScore.toFixed(2)}`);\n        if (shouldIncludeImports) {\n          console.log(`  Including imports from lines 1-${importEndLine}`);\n        }\n\n        results.push({\n          filePath: identification.fileName,\n          content,\n          startLine: actualStartLine,\n          endLine: shouldIncludeImports ? endLine + (importEndLine + 2) : endLine, // Adjust for added separator lines\n          type: this.mapSnippetType(identification.snippetType),\n          symbols: [identification.snippetName],\n          score: identification.relevanceScore,\n          context: identification.reasoning,\n          includesImports: shouldIncludeImports\n        } as CodeSnippet);\n      }\n    }\n\n    console.log('Total lines', results.reduce((acc, a) => {\n      return acc + ((a.endLine - a.startLine)) + 1;\n    }, 0));\n\n    return orderBy(results, ['score'], ['desc']);\n  }\n\n  /**\n   * Find the line where imports end in a file\n   */\n  private findImportEndLine(lines: string[]): number {\n    let lastImportLine = 0;\n\n    for (let i = 0; i < Math.min(lines.length, 50); i++) { // Only check first 50 lines\n      const line = lines[i].trim();\n      if (line.startsWith('import ')) {\n        lastImportLine = i + 1; // +1 because line numbers are 1-based\n      }\n    }\n\n    return lastImportLine;\n  }\n\n  /**\n   * Main method to get relevant snippets for a query\n   */\n  async getRelevantSnippets(query: string, reason: string, excludedFiles: string[] = []): Promise<CodeSnippet[]> {\n    // Stage 1: Find relevant files\n    const {files: relevantFiles, reasoning} = await this.findRelevantFiles(query, reason, excludedFiles);\n\n    if (relevantFiles.length === 0) {\n      console.log(\"No relevant files found\");\n      return [];\n    }\n\n    // Stage 2: Identify relevant snippets within those files\n    const snippetIdentifications = await this.identifyRelevantSnippets(query, reason, relevantFiles, reasoning);\n\n    // Stage 3: Extract the actual snippets\n    return this.extractSnippets(snippetIdentifications);\n  }\n\n  /**\n   * Helper methods\n   */\n  private isCodeFile(fileName: string): boolean {\n    const ext = path.extname(fileName).toLowerCase();\n    return ['.ts', '.tsx', '.js', '.jsx'].includes(ext);\n  }\n\n  private determineFileType(fileName: string, content: string): string {\n    const name = fileName.toLowerCase();\n\n    if (name.includes('screen') || name.includes('page')) {\n      return 'screen';\n    }\n\n    if (name.includes('context')) {\n      return 'context';\n    }\n\n    if (name.includes('hook') || (content && content.includes('use') && content.includes('return {'))) {\n      return 'hook';\n    }\n\n    if (name.includes('util') || name.includes('helper')) {\n      return 'util';\n    }\n\n    if (name.includes('type') || name.includes('interface') || name.includes('.d.ts')) {\n      return 'type';\n    }\n\n    if (name.includes('config') || name.includes('setup')) {\n      return 'config';\n    }\n\n    // Default to component for TSX/JSX files\n    if (name.endsWith('.tsx') || name.endsWith('.jsx')) {\n      return 'component';\n    }\n\n    return 'unknown';\n  }\n\n  private extractExports(content: string): string[] {\n    const exports: string[] = [];\n    const exportRegex = /export\\s+(const|function|class|interface|type|default)\\s+(\\w+)/g;\n\n    let match;\n    while ((match = exportRegex.exec(content)) !== null) {\n      exports.push(match[2]);\n    }\n\n    return exports;\n  }\n\n  private extractImports(content: string): string[] {\n    const imports: string[] = [];\n    // Fixed regex to handle all import patterns including relative paths, scoped packages, and complex paths\n    const importRegex = /import\\s+(?:{[^}]*}|\\*\\s+as\\s+\\w+|\\w+)\\s+from\\s+['\"]([^'\"]+)['\"];?/g;\n\n    let match;\n    while ((match = importRegex.exec(content)) !== null) {\n      const importPath = match[1];\n      imports.push(importPath);\n    }\n\n    return imports;\n  }\n\n  /**\n   * Resolve import path to actual file name in the project\n   */\n  private resolveImportPath(importPath: string, currentFile: string): string | null {\n    // Skip node_modules and external packages (anything that doesn't start with ./ or ../)\n    if (!importPath.startsWith('./') && !importPath.startsWith('../')) {\n      return null;\n    }\n\n    // Handle relative paths only\n    const currentDir = path.dirname(currentFile);\n    let resolvedPath = path.join(currentDir, importPath);\n    // Normalize path separators and remove leading ./\n    resolvedPath = resolvedPath.replace(/\\\\/g, '/').replace(/^\\.\\//, '');\n\n    // Try to find the actual file with common extensions\n    const possibleExtensions = ['.ts', '.tsx', '.js', '.jsx', '/index.ts', '/index.tsx', '/index.js', '/index.jsx'];\n\n    for (const ext of possibleExtensions) {\n      const candidatePath = resolvedPath + ext;\n      if (this.fileIndex.has(candidatePath)) {\n        return candidatePath;\n      }\n    }\n\n    // If no extension worked, try exact match\n    if (this.fileIndex.has(resolvedPath)) {\n      return resolvedPath;\n    }\n\n    return null;\n  }\n\n  private mapSnippetType(type: string): 'component' | 'hook' | 'screen' | 'util' | 'type' | 'context' | 'config' | 'unknown' {\n    const normalizedType = type.toLowerCase();\n\n    if (normalizedType.includes('component')) return 'component';\n    if (normalizedType.includes('hook')) return 'hook';\n    if (normalizedType.includes('screen')) return 'screen';\n    if (normalizedType.includes('util') || normalizedType.includes('function')) return 'util';\n    if (normalizedType.includes('type') || normalizedType.includes('interface')) return 'type';\n    if (normalizedType.includes('context') || normalizedType.includes('provider')) return 'context';\n    if (normalizedType.includes('config')) return 'config';\n\n    return 'unknown';\n  }", "startLine": 27, "endLine": 553, "type": "unknown", "symbols": ["TwoStageLLMContextEngine"], "score": 1, "context": "This class implements the two-stage context engine, showing how it selects relevant files in the first stage and relevant snippets in the second stage. It includes methods for building file index, getting codebase structure, finding relevant files, identifying snippets, and extracting snippets with minimal context, directly addressing the query.", "includesImports": false}, {"filePath": "src/lib/services/context-engine.ts", "content": "    private async findRelevantFiles(query: string, excludedFiles: string[]): Promise<string[]> {\n        // Get the list of files that are not excluded\n        const availableFiles = this.files.filter(file => !this.shouldExcludeFile(file.name, excludedFiles));\n\n        // Use AI to determine which files are most relevant to the query\n        const result = await generateObject({\n            model: customModel('openai/gpt-4.1'),\n            temperature: 0.1,\n            schema: z.object({files: z.array(z.string())}),\n            system: `You are a code analysis expert. Your task is to identify which files in a React Native Expo project are most relevant to a specific query.\n\nYou will be given:\n1. A query about the codebase\n2. A list of files in the project with their types\n\nReturn ONLY a JSON array of the most relevant file paths, with a maximum of 5 files. Choose files that would be most helpful for understanding or implementing the query.`,\n            prompt: `Query: ${query}\n\nFiles in the project:\n${availableFiles.map(file => `${file.name} - ${this.fileMetadata.get(file.name)?.type || 'unknown'}`).join('\\n')}\n\n${excludedFiles.length > 0 ? `Note: The following files have been excluded from analysis: ${excludedFiles.join(', ')}\\n\\n` : ''}\nReturn ONLY a JSON array of the most relevant file paths (maximum 5):`,\n        });\n\n        try {\n            // console.log('result', result.object)\n            // Parse the result as a JSON array\n            const fileList = result.object.files;\n            if (Array.isArray(fileList) && fileList.length > 0) {\n                return fileList.filter(file => this.fileIndex.has(file));\n            }\n        } catch (error) {\n            console.error('Error parsing relevant files:', error);\n        }\n\n        // Fallback: return the most common file types if AI parsing fails\n        return this.files\n            .filter(file => isCodeFile(file.name) && !this.shouldExcludeFile(file.name, excludedFiles))\n            .map(file => file.name)\n            .slice(0, 5);\n    }", "startLine": 564, "endLine": 605, "type": "util", "symbols": ["findRelevantFiles"], "score": 0.9, "context": "This private async method in ContextEngine uses AI to determine which files are most relevant to a query, filtering out excluded files. It is critical to understanding how relevant files are selected for queries, complementing the two-stage engine's file selection.", "includesImports": false}, {"filePath": "scripts/test-two-stage-context-engine.ts", "content": "async function testTwoStageContextEngine() {\n  console.log('🚀 Testing Two-Stage Context Engine with Current Codebase\\n');\n\n  // Parse command line arguments\n  const { useDefault, queries } = parseArguments();\n\n  if (useDefault) {\n    console.log('📋 Running default test queries');\n  } else {\n    console.log('📋 Running custom query');\n  }\n  console.log('📋 Results will be saved for comparison with Augment\\'s codebase-retrieval\\n');\n\n  // Read all files from the current directory\n  console.log('📁 Reading codebase files...');\n  const projectRoot = path.resolve(__dirname, '..');\n  const files = readFilesRecursively(projectRoot);\n\n  console.log(`📊 Found ${files.length} files to analyze`);\n  console.log(`📊 Total lines of code: ${files.reduce((sum, f) => sum + f.content.split('\\n').length, 0)}`);\n  console.log(`📊 File types: ${[...new Set(files.map(f => f.language))].join(', ')}`);\n  console.log(`📊 Average file size: ${Math.round(files.reduce((sum, f) => sum + f.content.split('\\n').length, 0) / files.length)} lines\\n`);\n\n  // Initialize the context engine\n  console.log('🔧 Initializing Two-Stage Context Engine...');\n  const engine = new TwoStageLLMContextEngine(files);\n  console.log('✅ Engine initialized\\n');\n\n  // Summary stats\n  const summaryStats = {\n    totalQueries: queries.length,\n    totalExecutionTime: 0,\n    totalSnippets: 0,\n    totalLines: 0,\n    averageExecutionTime: 0,\n    averageSnippetsPerQuery: 0,\n    averageLinesPerQuery: 0\n  };\n\n  // Run test queries\n  for (let i = 0; i < queries.length; i++) {\n    const { query, reason } = queries[i];\n\n    console.log('='.repeat(100));\n    console.log(`🔍 TEST QUERY ${i + 1}/${queries.length}`);\n    console.log('='.repeat(100));\n    console.log(`📝 Query: ${query}`);\n    console.log(`📝 Reason: ${reason}\\n`);\n\n    try {\n      const startTime = Date.now();\n      const snippets = await engine.getRelevantSnippets(query, reason);\n      const endTime = Date.now();\n      const executionTime = endTime - startTime;\n\n      // Create result object to match expected format\n      const result = {\n        snippets: snippets || [],\n        additionalFiles: [] // Two-stage engine doesn't return additional files\n      };\n\n      // Update summary stats\n      summaryStats.totalExecutionTime += executionTime;\n      summaryStats.totalSnippets += result.snippets.length;\n      const queryLines = result.snippets.reduce((sum, s) => sum + s.content.split('\\n').length, 0);\n      summaryStats.totalLines += queryLines;\n\n      console.log(`⏱️  Execution Time: ${executionTime}ms`);\n      console.log(`📊 Found ${result.snippets.length} snippets`);\n      console.log(`📋 ${result.additionalFiles.length} additional files suggested`);\n      console.log(`📏 Total lines returned: ${queryLines}`);\n      console.log(`💰 Estimated tokens: ~${Math.round(queryLines * 5)} tokens\\n`);\n\n      // Display snippets summary\n      if (result.snippets.length > 0) {\n        console.log('📄 SNIPPETS FOUND:');\n        result.snippets.forEach((snippet, index) => {\n          const lines = snippet.content.split('\\n').length;\n          console.log(`  ${index + 1}. ${snippet.filePath} (${lines} lines, score: ${snippet.score?.toFixed(2) || 'N/A'})`);\n          console.log(`     Type: ${snippet.type}, Context: ${snippet.context}`);\n          console.log(`     Lines: ${snippet.startLine}-${snippet.endLine}`);\n        });\n      }\n\n      // Display additional files\n      if (result.additionalFiles.length > 0) {\n        console.log('\\n🔍 ADDITIONAL FILES SUGGESTED:');\n        result.additionalFiles.forEach((file, index) => {\n          console.log(`  ${index + 1}. ${file.fileName}`);\n          console.log(`     Reason: ${file.reason}`);\n          console.log(`     Suggested Query: \"${file.suggestedQuery}\"`);\n        });\n      }\n\n      // Save detailed results\n      saveResultsToFile(i, query, result, executionTime);\n\n      console.log('\\n');\n\n    } catch (error) {\n      console.error(`❌ Error executing query: ${error}`);\n      console.log('\\n');\n    }\n  }\n\n  // Calculate and display summary statistics\n  summaryStats.averageExecutionTime = summaryStats.totalExecutionTime / summaryStats.totalQueries;\n  summaryStats.averageSnippetsPerQuery = summaryStats.totalSnippets / summaryStats.totalQueries;\n  summaryStats.averageLinesPerQuery = summaryStats.totalLines / summaryStats.totalQueries;\n\n  console.log('='.repeat(100));\n  console.log('📊 SUMMARY STATISTICS');\n  console.log('='.repeat(100));\n  console.log(`🔢 Total Queries: ${summaryStats.totalQueries}`);\n  console.log(`⏱️  Total Execution Time: ${summaryStats.totalExecutionTime}ms`);\n  console.log(`📄 Total Snippets Found: ${summaryStats.totalSnippets}`);\n  console.log(`📏 Total Lines Returned: ${summaryStats.totalLines}`);\n  console.log(`💰 Total Estimated Tokens: ~${Math.round(summaryStats.totalLines * 5)}`);\n  console.log(`⚡ Average Execution Time: ${Math.round(summaryStats.averageExecutionTime)}ms`);\n  console.log(`📊 Average Snippets per Query: ${summaryStats.averageSnippetsPerQuery.toFixed(1)}`);\n  console.log(`📏 Average Lines per Query: ${Math.round(summaryStats.averageLinesPerQuery)}`);\n  console.log(`💰 Average Tokens per Query: ~${Math.round(summaryStats.averageLinesPerQuery * 5)}`);\n\n  console.log('\\n🎉 Testing completed!');\n  console.log('📁 Check the results/ directory for detailed JSON outputs');\n  console.log('🔍 Compare these results with Augment\\'s codebase-retrieval tool performance');\n}\n\n// Run the test\nif (require.main === module) {\n  testTwoStageContextEngine()\n    .then(() => {", "startLine": 298, "endLine": 429, "type": "util", "symbols": ["testTwoStageContextEngine"], "score": 0.8, "context": "This script demonstrates usage of the TwoStageLLMContextEngine with real queries, showing how it is initialized, how queries are run, and how results are processed and saved. It provides practical insight into the two-stage engine's operation and file selection for queries.", "includesImports": false}], "additionalFiles": []}}