{"timestamp": "2025-06-14T17:31:19.641Z", "query": "Find all the chat tools and how they integrate with the streaming system. Show me the tool execution flow.", "executionTime": 52695, "snippetsCount": 15, "additionalFilesCount": 0, "totalLines": 1904, "snippets": [{"filePath": "src/lib/services/stream-service.ts", "type": "unknown", "context": "Core streaming service class that manages text streaming, tool call streaming, chunk processing, tool call repair, and finalization. Central to understanding how chat tools integrate with the streaming system and the tool execution flow.", "score": 1, "lines": 273, "startLine": 110, "endLine": 382, "symbols": ["StreamService"], "preview": "export class StreamService {\n    private config: StreamServiceConfig;\n    private toolCallCosts: ToolCallCost[] = [];\n\n    constructor(config: StreamServiceConfig) {\n..."}, {"filePath": "src/lib/chat/tools/query-codebase.ts", "type": "util", "context": "Defines the queryCodebase tool which queries the codebase and integrates with streaming and tool call tracking, critical for understanding tool execution flow.", "score": 0.9, "lines": 161, "startLine": 23, "endLine": 183, "symbols": ["queryCodebase"], "preview": "export const queryCodebase = ({\n                                  projectId,\n                                  files,\n                                  fileManager,\n                                  messageId = '',\n..."}, {"filePath": "src/lib/chat/tools/query-supabase-context.ts", "type": "util", "context": "Defines the querySupabaseContext tool which queries Supabase resources and integrates with streaming and tool call tracking, critical for understanding tool execution flow.", "score": 0.9, "lines": 203, "startLine": 23, "endLine": 225, "symbols": ["querySupabaseContext"], "preview": "export const querySupabaseContext = ({\n    projectId,\n    messageId = '',\n}: {\n    projectId: string, \n..."}, {"filePath": "src/components/base/chat.tsx", "type": "component", "context": "Main chat component showing how chat tools are invoked, how tool calls are handled in streaming, and how tool results are integrated into the chat UI.", "score": 0.9, "lines": 149, "startLine": 41, "endLine": 189, "symbols": ["Chat component (partial)"], "preview": "const Chat = observer(({\n                           id,\n                           initialMessages,\n                           isReadonly,\n                           initialPrompt,\n..."}, {"filePath": "src/components/base/message.tsx", "type": "component", "context": "Component rendering tool invocation parts and results in chat messages, showing UI integration of chat tools with streaming results.", "score": 0.9, "lines": 100, "startLine": 291, "endLine": 390, "symbols": ["PurePreviewMessage component (tool invocation rendering)"], "preview": "                                    case \"tool-invocation\":\n\n                                        const {toolInvocation} = part;\n                                        const {state, toolCallId, toolName, args} = toolInvocation;\n\n..."}, {"filePath": "src/lib/chat/tools/get-client-logs.ts", "type": "util", "context": "Defines a tool to fetch client logs with streaming and credit usage tracking, showing integration with streaming system and tool execution flow.", "score": 0.8, "lines": 105, "startLine": 13, "endLine": 117, "symbols": ["getClientLogs"], "preview": "export const getClientLogs = ({\n  dataStream,\n  creditUsageTracker,\n  logs,\n  messageId\n..."}, {"filePath": "src/lib/chat/tools/get-file-contents.ts", "type": "util", "context": "Defines a tool to get file contents with streaming and credit usage tracking, showing integration with streaming system and tool execution flow.", "score": 0.8, "lines": 235, "startLine": 7, "endLine": 241, "symbols": ["getFileContents"], "preview": "export const getFileContents = ({\n                                    fileManager,\n                                    dataStream,\n                                    creditUsageTracker\n                                }: {\n..."}, {"filePath": "src/lib/chat/tools/get-supabase-instructions.ts", "type": "util", "context": "Defines a tool to fetch Supabase instructions with streaming and credit usage tracking, showing integration with streaming system and tool execution flow.", "score": 0.8, "lines": 38, "startLine": 12, "endLine": 49, "symbols": ["getSupabaseInstructions"], "preview": "export const getSupabaseInstructions = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n..."}, {"filePath": "src/lib/chat/tools/get-supabase-logs.ts", "type": "util", "context": "Defines a tool to fetch Supabase logs with streaming and credit usage tracking, showing integration with streaming system and tool execution flow.", "score": 0.8, "lines": 111, "startLine": 14, "endLine": 124, "symbols": ["getSupabaseLogs"], "preview": "export const getSupabaseLogs = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n..."}, {"filePath": "src/lib/chat/tools/manage-supabase-auth.ts", "type": "util", "context": "Defines a tool to manage Supabase auth with streaming and credit usage tracking, showing integration with streaming system and tool execution flow.", "score": 0.8, "lines": 78, "startLine": 13, "endLine": 90, "symbols": ["manageSupabaseAuth"], "preview": "export const manageSupabaseAuth = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n..."}, {"filePath": "src/lib/chat/tools/multi-perspective-analysis.tool.ts", "type": "util", "context": "Defines a complex multi-perspective analysis tool that streams responses and integrates with the streaming system, showing detailed tool execution flow.", "score": 0.8, "lines": 261, "startLine": 55, "endLine": 315, "symbols": ["multiPerspectiveAnalysis"], "preview": "export const multiPerspectiveAnalysis = ({\n  dataStream,\n}: {\n  dataStream: DataStreamWriter;\n}) => {\n..."}, {"filePath": "src/lib/chat/tools/search-web.ts", "type": "util", "context": "Defines a web search tool that integrates with streaming and credit usage tracking, showing tool execution flow.", "score": 0.8, "lines": 85, "startLine": 11, "endLine": 95, "symbols": ["searchWeb"], "preview": "export const searchWeb = ({\n  dataStream,\n  creditUsageTracker\n}: {\n  dataStream: DataStreamWriter;\n..."}, {"filePath": "src/lib/chat/tools/client-testing.tool.ts", "type": "util", "context": "Defines a chat tool for human-in-the-loop client testing, showing a tool that integrates with streaming but requires user interaction.", "score": 0.7, "lines": 18, "startLine": 10, "endLine": 27, "symbols": ["clientTestingTool"], "preview": "export const clientTestingTool = ({\n                                      dataStream,\n                                      creditUsageTracker\n                                  }: {\n    dataStream: DataStreamWriter;\n..."}, {"filePath": "src/lib/chat/tools/add-ai-memory.ts", "type": "util", "context": "Defines a tool for adding AI memory with streaming data integration and credit tracking, relevant for understanding tool execution flow.", "score": 0.7, "lines": 72, "startLine": 12, "endLine": 83, "symbols": ["addAiMemory"], "preview": "export const addAiMemory = ({\n                                dataStream,\n                                creditUsageTracker,\n                                projectId\n                            }: {\n..."}, {"filePath": "src/lib/chat/tools/index.ts", "type": "unknown", "context": "Exports all chat tools, providing an overview of available tools that integrate with the streaming system.", "score": 0.6, "lines": 15, "startLine": 1, "endLine": 15, "symbols": ["tools index exports"], "preview": "// Export all tools from this directory\nexport * from './get-supabase-instructions';\nexport * from './get-supabase-logs';\nexport * from './get-client-logs';\nexport * from './get-file-contents';\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/services/stream-service.ts", "content": "export class StreamService {\n    private config: StreamServiceConfig;\n    private toolCallCosts: ToolCallCost[] = [];\n\n    constructor(config: StreamServiceConfig) {\n        this.config = config;\n    }\n\n    /**\n     * Start the text streaming process\n     * @returns The result of the streamText call\n     */\n    public startStream() {\n        const {\n            messages,\n            parser,\n            diffParser,\n            dataStream,\n            userMessageId,\n            abortSignal,\n            agentModeEnabled = false,\n            isFirstMessage = false,\n            onFinish,\n            tools = {},\n            temperature = 0,\n            maxSteps = 10,\n            generateMessageId = () => crypto.randomUUID(),\n            isSupabaseConnected = false,\n            enabledTools = [],\n            enableValidation,\n            transformChunking = \"line\",\n            modelId,\n            isConnectingSupabase\n        } = this.config;\n        let streamModelId = modelId\n        if(!modelId) {\n            streamModelId = \"anthropic/claude-sonnet-4\"\n            if (!isFirstMessage) {\n                if(agentModeEnabled) {\n                    // streamModelId = 'anthropic/claude-3.7-sonnet';\n                    streamModelId = 'anthropic/claude-sonnet-4';\n                    // streamModelId = 'openai/gpt-4.1';\n                } else {\n                    streamModelId = \"anthropic/claude-sonnet-4\"\n                }\n            }\n            if(isConnectingSupabase) {\n                streamModelId = \"anthropic/claude-sonnet-4\"\n            }\n        }\n\n        return streamText({\n            model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),\n            maxSteps,\n            experimental_activeTools: enabledTools,\n            experimental_toolCallStreaming: true,\n            toolCallStreaming: true,\n            temperature,\n            messages: messages.filter(m => !!m),\n            experimental_transform: smoothStream({chunking: transformChunking}),\n            maxRetries: 3,\n            abortSignal,\n            experimental_continueSteps: true,\n            onError: (error) => {\n                console.log('Error', error);\n                throw error;\n            },\n            onStepFinish: async (stepResult) => {\n                // console.log('stepResult', stepResult)\n                const body = await fetchFromOpenRouter(stepResult.response.id);\n                // console.log('Step result body', body)\n                \n                // Track tool call cost if data is available\n                if (body && stepResult.response.id) {\n                    const toolCallCost: ToolCallCost = {\n                        id: stepResult.response.id,\n                        model: body.data.model || streamModelId,\n                        tokens_prompt: body.data.tokens_prompt || 0,\n                        tokens_completion: body.data.tokens_completion || 0,\n                        native_tokens_prompt: body.data.native_tokens_prompt,\n                        native_tokens_completion: body.data.native_tokens_completion,\n                        total_cost: body.data.total_cost || 0,\n                        cache_discount: body.data.cache_discount,\n                        provider_name: body.data.provider_name,\n                        timestamp: new Date()\n                    };\n                    console.log('toolCallCost', toolCallCost)\n                    this.toolCallCosts.push(toolCallCost);\n                }\n            },\n            onChunk: ({chunk}) => {\n                if (chunk.type === \"text-delta\") {\n                    try {\n                        // First run the file parser\n                        const afterFileParser = parser.parse(userMessageId, chunk.textDelta);\n\n                        // Then run the diff parser on the output of the file parser\n                        diffParser.parse(userMessageId, afterFileParser);\n                    } catch (e) {\n                        console.error('Error in chunk processing', e);\n                    }\n                } else if (chunk.type === \"tool-call\") {\n                    // Send tool call information to the client\n                    dataStream.writeData({\n                        type: 'tool-call',\n                        content: {\n                            toolCallId: chunk.toolCallId,\n                            toolName: chunk.toolName,\n                            args: chunk.args\n                        }\n                    });\n                } else if (chunk.type === \"tool-result\") {\n                    // Send tool result information to the client\n                    dataStream.writeData({\n                        type: 'tool-result',\n                        content: {\n                            toolCallId: chunk.toolCallId,\n                            toolName: chunk.toolName,\n                            result: chunk.result\n                        }\n                    });\n                }\n            },\n            experimental_generateMessageId: generateMessageId,\n            experimental_repairToolCall: async ({\n                                                    toolCall,\n                                                    tools,\n                                                    error,\n                                                    messages,\n                                                    system,\n                                                }) => {\n                console.log('In repair', error);\n                const result = await generateText({\n                    model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),\n                    system,\n                    messages: [\n                        ...messages,\n                        {\n                            role: 'assistant',\n                            content: [\n                                {\n                                    type: 'tool-call',\n                                    toolCallId: toolCall.toolCallId,\n                                    toolName: toolCall.toolName,\n                                    args: toolCall.args,\n                                },\n                            ],\n                        },\n                        {\n                            role: 'tool' as const,\n                            content: [\n                                {\n                                    type: 'tool-result',\n                                    toolCallId: toolCall.toolCallId,\n                                    toolName: toolCall.toolName,\n                                    result: error.message,\n                                },\n                            ],\n                        },\n                    ],\n                    tools,\n                });\n\n                const newToolCall = result.toolCalls.find(\n                    newToolCall => newToolCall.toolName === toolCall.toolName,\n                );\n\n                return newToolCall != null\n                    ? {\n                        toolCallType: 'function' as const,\n                        toolCallId: toolCall.toolCallId,\n                        toolName: toolCall.toolName,\n                        args: JSON.stringify(newToolCall.args),\n                    }\n                    : null;\n            },\n            tools,\n            onFinish: async (params) => {\n                if (onFinish) {\n                    await onFinish({...params, enableValidation});\n                }\n            },\n        });\n    }\n\n    /**\n     * Sanitize response messages by removing incomplete tool calls\n     * @param messages The messages to sanitize\n     * @returns Sanitized messages\n     */\n    public static sanitizeResponseMessages(messages: AIMessage[]): AIMessage[] {\n        return messages.filter(message => {\n            // Filter out messages that are just incomplete tool calls\n            if (message.role === 'assistant' && Array.isArray(message.content)) {\n                const hasOnlyToolCalls = message.content.every(\n                    part => part.type === 'tool-call'\n                );\n\n                if (hasOnlyToolCalls) {\n                    return false;\n                }\n            }\n            return true;\n        });\n    }\n\n    /**\n     * Get the aggregated cost and token usage for all tool calls\n     * @returns Summary of token usage and costs\n     */\n    public getToolCallCostSummary() {\n        if (this.toolCallCosts.length === 0) {\n            return {\n                count: 0,\n                total_tokens_prompt: 0,\n                total_tokens_completion: 0,\n                total_cost: 0,\n                total_cache_discount: 0,\n                net_cost: 0,\n                tool_calls: []\n            };\n        }\n        \n        const total_tokens_prompt = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_prompt || 0), 0);\n        const total_tokens_completion = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_completion || 0), 0);\n        const total_cost = this.toolCallCosts.reduce((sum, call) => sum + (call.total_cost || 0), 0);\n        const total_cache_discount = this.toolCallCosts.reduce((sum, call) => sum + (call.cache_discount || 0), 0);\n        \n        return {\n            count: this.toolCallCosts.length,\n            total_tokens_prompt: round(total_tokens_prompt, 0),\n            total_tokens_completion: round(total_tokens_completion, 0),\n            total_tokens: round(total_tokens_prompt + total_tokens_completion, 0),\n            total_cost: round(total_cost, 6),\n            total_cache_discount: round(total_cache_discount, 6),\n            net_cost: round(total_cost + total_cache_discount, 6),\n            tool_calls: this.toolCallCosts\n        };\n    }\n    \n    /**\n     * Get a simple formatted string representation of the tool call costs\n     * @returns Formatted string with cost summary\n     */\n    public getToolCallCostReport(): string {\n        const summary = this.getToolCallCostSummary();\n        \n        if (summary.count === 0) {\n            return \"No tool calls recorded.\";\n        }\n        \n        return `Tool Call Summary:\n- Total Calls: ${summary.count}\n- Prompt Tokens: ${summary.total_tokens_prompt}\n- Completion Tokens: ${summary.total_tokens_completion}\n- Total Tokens: ${summary.total_tokens}\n- Total Cost: $${summary.total_cost}\n- Cache Discount: $${summary.total_cache_discount}\n- Net Cost: $${summary.net_cost}`;\n    }\n    \n    /**\n     * Create a new instance with updated configuration\n     * @param updatedConfig The updated configuration\n     * @returns A new StreamService instance\n     */\n    public withConfig(updatedConfig: Partial<StreamServiceConfig>): StreamService {\n        return new StreamService({\n            ...this.config,\n            ...updatedConfig,\n        });\n    }\n}", "startLine": 110, "endLine": 382, "type": "unknown", "symbols": ["StreamService"], "score": 1, "context": "Core streaming service class that manages text streaming, tool call streaming, chunk processing, tool call repair, and finalization. Central to understanding how chat tools integrate with the streaming system and the tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/query-codebase.ts", "content": "export const queryCodebase = ({\n                                  projectId,\n                                  files,\n                                  fileManager,\n                                  messageId = '',\n                                  userMessage = ''\n                              }: { projectId: string, files: FileItem[], fileManager: FileLineManager, messageId?: string, userMessage?: string }) => {\n    return tool({\n        description: 'Query the codebase to understand its structure, relationships, and relevant files for a specific task. ' +\n            'Use this instead of requesting individual files. ' +\n            'You can exclude specific files from the analysis by providing an array of file paths in the excludedFiles parameter. ' +\n            'Use filePath parameter to retrieve a SINGLE file in full ONLY when absolutely necessary. You are LIMITED to ONE file per request.',\n        parameters: QueryCodebaseSchema,\n        execute: async ({query, excludedFiles, reason}: z.infer<typeof QueryCodebaseSchema>) => {\n            console.log('Query:', query)\n            console.log('Reason:', reason)\n            console.log('Excluded files:', excludedFiles?.join())\n\n            if(!query) {\n                return {\n                    result: null,\n                    message: \"One of query or filePath is required. To look at the holistic view, use query and to look at a specific file, use filePath.\"\n                }\n            }\n            // Check if this is a \"continue\" message\n            const isContinueMessage = userMessage.toLowerCase().trim().startsWith('continue');\n\n            // Get the tool tracker instance\n            const toolTracker = ToolCountTracker.getInstance();\n\n            console.log('Tool call allowed (queryCodebase):', toolTracker.shouldAllowToolCall(messageId, 'queryCodebase'))\n\n            // Check if we should allow this tool call\n            if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'queryCodebase')) {\n                return {\n                    result: null,\n                    message: \"⚠️ Tool call limit reached. You've already made multiple codebase queries. Please implement or summarize with the information you already have.\",\n                };\n            }\n\n            // Increment the tool count if we have a chat ID\n            if (messageId) {\n                toolTracker.incrementToolCount(messageId, 'queryCodebase');\n            }\n            try {\n                if (!files || files.length === 0) {\n                    throw new Error('No files available for context engine');\n                }\n\n                // Get project information if available\n                let project: Project | null = null;\n                if (projectId) {\n                    try {\n                        project = await getProjectById({id: projectId});\n                    } catch (error) {\n                        console.warn('Could not fetch project information:', error);\n                        // Continue without project info\n                    }\n                }\n\n                if (!project) {\n                    throw new Error('No project information available');\n                }\n\n                // Check if we're requesting a specific file in full\n                // Note: filePath takes precedence over semantic query when both are provided\n                // if (filePath) {\n                //     console.log('Specific file requested:', filePath);\n                //\n                //     // Find the requested file\n                //     const fileItem = fileManager.getFileItems().find(file => file.name === filePath.trim());\n                //\n                //     if (!fileItem) {\n                //         throw new Error(`The requested file was not found: ${filePath}`);\n                //     }\n                //\n                //     const fileItems = [fileItem];\n                //\n                //     // Create snippets for each file\n                //     const snippets = fileItems.map(fileItem => ({\n                //         filePath: fileItem.name,\n                //         content: fileItem.content || '',\n                //         startLine: 1,\n                //         endLine: (fileItem.content || '').split('\\n').length,\n                //         type: 'unknown',\n                //         symbols: [],\n                //         score: 1.0\n                //     }));\n                //\n                //     // Create a simplified result with just the full file contents\n                //     const result = {\n                //         query,\n                //         snippets,\n                //         codebaseStructure: { components: [], screens: [], contexts: [], hooks: [], utils: [], types: [], configs: [] },\n                //         relationships: { imports: {}, exports: {}, dependencies: {}, dependents: {} }\n                //     };\n                //\n                //     let message = `Showing full content of the requested file: ${fileItems[0].name}`;\n                //\n                //     return {\n                //         result,\n                //         message,\n                //         toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')\n                //     };\n                // }\n                \n                // Initialize the context engine with the current files and project\n                const contextEngine = new ContextEngine(fileManager.getFileItems(), project);\n\n                // Default excluded files for Expo Snack projects if not provided\n                const defaultExcludedFiles = [\n                    // Large asset files\n                    'assets/',\n                    '.mp3',\n                    '.mp4',\n                    '.jpg',\n                    '.png',\n                    '.gif',\n                    // Generated type files\n                    'types/generated',\n                    // These don't exist in Expo Snack but kept as fallbacks\n                    'node_modules',\n                    'dist'\n                ];\n\n                // Combine user-provided excludedFiles with defaults\n                const combinedExcludedFiles = excludedFiles ?\n                    [...excludedFiles, ...defaultExcludedFiles] :\n                    defaultExcludedFiles;\n\n                // Query the context engine\n                const result = await contextEngine.query(query, reason, combinedExcludedFiles);\n\n                // Prepare a more informative response\n                let message = `Found ${result.snippets?.length} snippets for your query.`;\n\n                // Add information about excluded files if any\n                if (excludedFiles && excludedFiles.length > 0) {\n                    message += `\\n${excludedFiles.length} files were excluded from the analysis.`;\n                }\n\n                if (result.actionPlan) {\n                    message += `\\n\\nAction Plan: ${result.actionPlan.summary}\\nComplexity: ${result.actionPlan.complexity}\\nSteps: ${result.actionPlan.steps.length}`;\n                }\n\n                // if (result.supabaseSchema) {\n                //     message += `\\n\\nSupabase integration detected with ${result.supabaseSchema.tables.length} tables.`;\n                // }\n\n                return {\n                    result,\n                    message,\n                    toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')\n                };\n            } catch (error) {\n                console.error('Context engine tool error:', error);\n                throw new Error(`Failed to query context engine: ${error instanceof Error ? error.message : 'Unknown error'}`);\n            }\n        },\n    });\n}", "startLine": 23, "endLine": 183, "type": "util", "symbols": ["queryCodebase"], "score": 0.9, "context": "Defines the queryCodebase tool which queries the codebase and integrates with streaming and tool call tracking, critical for understanding tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/query-supabase-context.ts", "content": "export const querySupabaseContext = ({\n    projectId,\n    messageId = '',\n}: {\n    projectId: string, \n    messageId?: string, \n}) => {\n    return tool({\n        description: 'Query Supabase resources or execute SQL (either one at a time) to understand the project tables, dbFunctions, triggers, functions, RLS policies, storage buckets, and secrets. ' +\n            'Use this instead of requesting full Supabase instructions. Issue queries that are as specific as possible. ' +\n            'You can exclude specific resources using the excludedResources parameter in the format \"resourceType.resourceName\" or use wildcards like \"table.*\". ' +\n            'This tool uses a two-stage approach to efficiently retrieve only the relevant Supabase resources. ' +\n            'If the initial query returns incomplete information, the tool may indicate mustRetrieveAdditionalResources=true with guidance on what additional resources to request in a follow-up query. ' +\n            'You can also execute SELECT SQL queries directly against the database using the executeSQL parameter. Only SELECT queries are allowed for security reasons. When executing a SQL query, code exploration will not happen.',\n        parameters: QuerySupabaseContextSchema,\n        execute: async ({query, excludedResources, executeSQL, reason}: z.infer<typeof QuerySupabaseContextSchema>) => {\n            try {\n                console.log('Supabase Query:', query);\n                console.log('Excluded Resources:', excludedResources || []);\n                console.log('SQL Query:', executeSQL || 'None');\n                console.log('Reason:', reason);\n\n                // Validate inputs\n                if (!query && !executeSQL) {\n                    return {\n                        result: null,\n                        message: \"Either a natural language query or SQL query is required.\"\n                    };\n                }\n\n                // Get the tool tracker instance\n                const toolTracker = ToolCountTracker.getInstance();\n\n                console.log('Tool call allowed (querySupabaseContext):', toolTracker.shouldAllowToolCall(messageId, 'querySupabaseContext'));\n\n                // Check if we should allow this tool call\n                if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'querySupabaseContext')) {\n                    return {\n                        result: null,\n                        message: \"⚠️ Tool call limit reached. You've already made multiple Supabase context queries. Please implement or summarize with the information you already have.\",\n                    };\n                }\n\n                // Increment the tool count if we have a chat ID\n                if (messageId) {\n                    toolTracker.incrementToolCount(messageId, 'querySupabaseContext');\n                }\n\n                // Get project information\n                let project: Project | null = null;\n                try {\n                    if (!projectId) {\n                        return {\n                            result: null,\n                            message: \"Project ID is required\"\n                        };\n                    }\n                    \n                    project = await getProjectById({id: projectId});\n                    if (!project) {\n                        return {\n                            result: null,\n                            message: `Project not found: ${projectId}`\n                        };\n                    }\n                    \n                    // Check if project is connected to Supabase\n                    if (!project.connectionId || !project.supabaseProjectId) {\n                        return {\n                            result: null,\n                            message: 'This project is not connected to Supabase'\n                        };\n                    }\n                } catch (error) {\n                    console.error('Error fetching project:', error);\n                    return {\n                        result: null,\n                        message: `Failed to fetch project information: ${error instanceof Error ? error.message : 'Unknown error'}`\n                    };\n                }\n                \n                // Handle SQL execution if provided\n                if (executeSQL) {\n                    console.log('Executing SQL query:', executeSQL);\n                    \n                    // Validate that it's a SELECT query only\n                    const sqlLower = executeSQL.trim().toLowerCase();\n                    if (!sqlLower.startsWith('select ')) {\n                        return {\n                            result: null,\n                            message: '⚠️ Only SELECT queries are allowed for security reasons. Your query was rejected.'\n                        };\n                    }\n                    \n                    // Check for potentially harmful statements\n                    const disallowedKeywords = ['insert', 'update', 'delete', 'drop', 'alter', 'create', 'truncate'];\n                    if (disallowedKeywords.some(keyword => sqlLower.includes(keyword))) {\n                        return {\n                            result: null,\n                            message: '⚠️ SQL query contains disallowed keywords. Only pure SELECT queries are permitted. Your query was rejected.'\n                        };\n                    }\n                    \n                    try {\n                        // Execute the SQL query\n                        const supabaseProvider = new SupabaseIntegrationProvider();\n                        const result = await supabaseProvider.executeSQL({\n                            query: executeSQL,\n                            projectId\n                        });\n                        \n                        // Truncate the result if it's too large\n                        const resultStr = JSON.stringify(result || {}, null, 2);\n                        const MAX_LENGTH = 3000;\n                        \n                        let sqlResult;\n                        let message = '📊 SQL query executed successfully.';\n                        \n                        if (resultStr.length > MAX_LENGTH) {\n                            sqlResult = {\n                                data: result?.data,\n                                truncated: true,\n                                originalLength: resultStr.length,\n                                message: `SQL result was truncated from ${resultStr.length} to ${MAX_LENGTH} characters`\n                            };\n                            message += ` Results were truncated (${sqlResult.message}).`;\n                        } else {\n                            sqlResult = result;\n                        }\n                        \n                        return {\n                            result: { sqlResult },\n                            message,\n                            toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'querySupabaseContext')\n                        };\n                    } catch (error) {\n                        console.error('SQL execution error:', error);\n                        return {\n                            result: null,\n                            message: `⚠️ Failed to execute SQL query: ${error instanceof Error ? error.message : 'Unknown error'}`\n                        };\n                    }\n                } else {\n                    // Only query the context engine if no SQL was provided\n                    try {\n                        // Initialize the Supabase context engine\n                        const contextEngine = new SupabaseContextEngine(project);\n\n                        // Query the Supabase context engine\n                        const result = await contextEngine.getRelevantSupabaseResources(query, excludedResources);\n\n                        // Prepare a more informative response\n                        const resourceCount = result.resources?.length || 0;\n                        let message = `Found ${resourceCount} relevant Supabase resources for your query.`;\n\n                        // Add information about resource types\n                        const resourceTypes = result.resources?.map(r => r.resourceType) || [];\n                        const uniqueTypes = [...new Set(resourceTypes)];\n                        \n                        if (uniqueTypes.length > 0) {\n                            message += ` Types: ${uniqueTypes.join(', ')}.`;\n                        }\n                        \n                        // Add information about excluded resources if any\n                        if (excludedResources && excludedResources.length > 0) {\n                            message += `\\n${excludedResources.length} resources were excluded from the analysis.`;\n                        }\n                        \n                        // Add information about additional resources if needed\n                        if (result.hasAdditional) {\n                            message += `\\nThere are additional resources that may be relevant but weren't included due to the result limit.`;\n                            \n                            if (result.mustRetrieveAdditionalResources) {\n                                message += `\\n\\n⚠️ IMPORTANT: You MUST make another query to retrieve critical additional resources. ${result.additionalResourcesSummary}`;\n                            } else if (result.additionalResourcesSummary) {\n                                message += `\\n\\nConsider querying for: ${result.additionalResourcesSummary}`;\n                            }\n                        }\n                        \n                        return {\n                            result,\n                            message,\n                            toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'querySupabaseContext')\n                        };\n                    } catch (error) {\n                        console.error('Supabase context engine error:', error);\n                        return {\n                            result: null,\n                            message: `⚠️ Failed to query Supabase context engine: ${error instanceof Error ? error.message : 'Unknown error'}`\n                        };\n                    }\n                }\n            } catch (error) {\n                // Global error handler - ensures we never throw errors\n                console.error('Unexpected error in querySupabaseContext:', error);\n                return {\n                    result: null,\n                    message: `⚠️ An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`\n                };\n            }\n        },\n    });\n};", "startLine": 23, "endLine": 225, "type": "util", "symbols": ["querySupabaseContext"], "score": 0.9, "context": "Defines the querySupabaseContext tool which queries Supabase resources and integrates with streaming and tool call tracking, critical for understanding tool execution flow.", "includesImports": false}, {"filePath": "src/components/base/chat.tsx", "content": "const Chat = observer(({\n                           id,\n                           initialMessages,\n                           isReadonly,\n                           initialPrompt,\n                           onLoadingChange,\n                           projectId,\n                           runLastUserMessage,\n                           onInitialRunInitialized,\n                           hasMoreMessages,\n                           totalUserMessages\n                       }: {\n    id: string;\n    initialMessages: Array<UIMessage>;\n    initialPrompt?: string;\n    isReadonly: boolean;\n    onLoadingChange?: (isLoading: boolean) => any;\n    projectId: string;\n    runLastUserMessage: boolean;\n    onInitialRunInitialized: () => any;\n    hasMoreMessages?: boolean;\n    totalUserMessages?: number;\n}) => {\n    const {mutate} = useSWRConfig();\n\n    const {anonymousId, hasReachedLimit, incrementChatCount, storeChat, getStoredChat} = useAnonymousSession();\n    const {status: authStatus} = useSession();\n\n    const {generatorStore, integrationStore, logStore, snackStore} = useStores();\n    const session = generatorStore.getActiveSession(id);\n    const [integrationOpen, setIntegrationOpen] = useState(false);\n    const [isSecretOpen, setIsSecretOpen] = useState(false);\n    const [secretNames, setSecretNames] = useState<string[] | null>(null);\n    const [droppedFiles, setDroppedFiles]= useState<File[]>();\n    const [selectMode, setSelectMode] = useState(false);\n    const [sqlDialogOpen, setSqlDialogOpen] = useState(false);\n    // Define a proper type for SQL queries\n    type SqlQuery = {\n        query: string;\n        source: string;\n        table: string;\n    };\n    const [pendingSqlQueries, setPendingSqlQueries] = useState<SqlQuery[]>([]);\n    const [analysisResult, setAnalysisResult] = useState<{ analysis: CodeChangeAnalysis, message: string } | null>(null);\n    const [isAnalysisDialogOpen, setIsAnalysisDialogOpen] = useState(false);\n    const [isLoadingPrevious, setIsLoadingPrevious] = useState(false);\n    const [isLoadAllDialogOpen, setIsLoadAllDialogOpen] = useState(false);\n    const [hasMoreLocalOverride, setHasMoreLocalOverride] = useState<boolean | undefined>(undefined);\n    if (!session) {\n        return;\n    }\n\n    const {agentModeEnabled} = useAgentModeSettings();\n\n    // Track when a chat is started or resumed\n    useEffect(() => {\n        const isNewChat = initialMessages.length <= 1; // Only system message or empty\n\n        if (isNewChat) {\n            trackMessageEvent('CHAT_STARTED', {\n                chat_id: id,\n                project_id: projectId\n            });\n        } else {\n            trackMessageEvent('CHAT_RESUMED', {\n                chat_id: id,\n                time_since_last_activity: 0, // We don't have this info yet, but could add it\n                session_message_count: initialMessages.length\n            });\n        }\n    }, [id, initialMessages.length, projectId]);\n\n    const {\n        messages,\n        setMessages,\n        handleSubmit,\n        input,\n        setInput,\n        append,\n        isLoading,\n        stop,\n        reload,\n        data,\n        status,\n        experimental_resume,\n        addToolResult\n    } = useChat({\n        id,\n        keepLastMessageOnError: false,\n        experimental_prepareRequestBody: (options: {\n            id: string;\n            messages: UIMessage[];\n            requestData?: JSONValue;\n            requestBody?: object;\n        }) => {\n            options.messages = options.messages.map(message => {\n                message.parts = message.parts.filter(part => {\n                    if(part.type === \"tool-invocation\") {\n                        return part.toolInvocation.state !== \"partial-call\"\n                    }\n                    return true\n                })\n                return message\n            }).slice(-20);\n            return {\n                files: session.fileTree.length ? session.fileTree : DEFAULT_CODE,\n                activeFile: session.activeFile,\n                dependencies: session.dependencies,\n                linkSupabaseProjectId: integrationStore.currentSelectedProjectId,\n                linkSupabaseConnection: integrationStore.getConnection(\"supabase\")?.id,\n                projectId: session.projectId,\n                logs: logStore.getLogs(id),\n                agentModeEnabled: agentModeEnabled,\n                messages: options.messages,\n                id,\n                ...options.requestBody\n            }\n        },\n        headers: anonymousId ? {\n            'x-anonymous-id': anonymousId,\n            'x-chat-count': hasReachedLimit ? '2' : '1',\n        } : {},\n        initialMessages,\n        generateId: () => {\n            return generateUUID();\n        },\n        sendExtraMessageFields: true,\n        streamProtocol: \"data\",\n        onToolCall: async ({toolCall}) => {\n            if(toolCall.toolName === \"clientTesting\") {\n                console.log('[Chat] Client testing tool call received:', toolCall);\n                if (session) {\n                    // Start client testing with the tool call parameters\n                    session.startClientTesting(\n                        toolCall.toolCallId,\n                        (toolCall.args as any)?.featuresToTest || '',\n                        (toolCall.args as any)?.expectations || '',\n                        (toolCall.args as any)?.reason || ''\n                    );\n\n                    console.log('[Chat] Client testing started, waiting for user interaction');\n                    // For human-in-the-loop tools, we don't return anything here\n                    // The tool result will be added via addToolResult when user completes testing\n                    // This prevents the connection from closing prematurely\n                }\n                // Don't return anything for human-in-the-loop tools\n                // The result will be provided later via addToolResult\n            }\n        },", "startLine": 41, "endLine": 189, "type": "component", "symbols": ["Chat component (partial)"], "score": 0.9, "context": "Main chat component showing how chat tools are invoked, how tool calls are handled in streaming, and how tool results are integrated into the chat UI.", "includesImports": false}, {"filePath": "src/components/base/message.tsx", "content": "                                    case \"tool-invocation\":\n\n                                        const {toolInvocation} = part;\n                                        const {state, toolCallId, toolName, args} = toolInvocation;\n\n                                        if (state === 'result') {\n                                            const {result} = toolInvocation;\n\n                                            return (\n                                                <div key={`${message.id}_${index}_${toolCallId}`}>\n                                                    {\n                                                        (toolName === 'getFileContents') ? (\n                                                            <GetFileContentsToolResult\n                                                                path={args.path || 'unknown'}\n                                                                reason={args.reason || 'File content requested'}\n                                                                isDirectory={!args.path?.includes('.') || args.path?.endsWith('/')}\n                                                            />\n                                                        ) : toolName === 'queryCodebase' ? (\n                                                            <QueryCodebaseToolResult query={args.query} excludedFiles={args.excludedFiles} result={args?.result} state={state}/>\n                                                        ) : toolName === 'editFile' ? (\n                                                            <EditFileToolResult absolutePath={args.absolutePath} result={args} state={state}/>\n                                                        ) : toolName === 'getSupabaseInstructions' ? (\n                                                            <GetSupabaseInstructionsToolResult reason={args.reason || 'Fetching Supabase schema'} state={state}/>\n                                                        ) : toolName === 'getSupabaseLogs' ? (\n                                                            <GetSupabaseLogsToolResult reason={args.reason || 'Fetching Supabase logs'} service={args.service} state={'complete'} result={result}/>\n                                                         ) : toolName === 'querySupabaseContext' ? (\n                                                            <QuerySupabaseContextToolResult query={args?.query || 'Fetching Supabase resources'} state={'result'} result={result}/>\n                                                         ) : toolName === 'searchWeb' ? (\n                                                             <SearchWebToolResult query={args?.query || ''} reason={args?.reason || 'Searching the web'} state={'complete'} result={result}/>\n                                                         ) : toolName === 'generateDesign' ? (\n                                                             <DesignToolResult projectId={projectId} chatId={chatId} reason={args?.prompt || 'Design app screens'} state={'complete'} result={result}/>\n                                                          ) : toolName === 'getClientLogs' ? (\n                                                             <GetClientLogsToolResult reason={args.reason || 'Fetching client logs'} type={args.type} source={args.source} state={'complete'} result={result}/>\n                                                          ) : toolName === 'displayMultiPerspectiveAnalysis' ? (\n                                                             <MultiPerspectiveAnalysisToolResult projectId={projectId} chatId={chatId} reason={args?.reason || 'Multi-perspective analysis'} state={'complete'} result={result}/>\n                                                          ) : toolName === 'manageSupabaseAuth' ? (\n                                                             <ManageSupabaseAuthToolResult reason={args.reason || 'Managing Supabase auth'} action={args.action} state={'complete'} result={result}/>\n                                                          ) : toolName === 'clientTesting' ? (\n                                                             <InlineClientTestingToolResult\n                                                                 reason={args.reason || 'Testing application'}\n                                                                 state={'complete'}\n                                                                 chatId={chatId}\n                                                                 expectations={args.expectations}\n                                                                 featuresToTest={args.featuresToTest}\n                                                                 toolCallId={toolCallId}\n                                                                 addToolResult={addToolResult}\n                                                             />\n                                                          ) : (\n                                                            <></>\n                                                            // <pre>{JSON.stringify(result, null, 2)}</pre>\n                                                        )}\n                                                </div>\n                                            );\n                                        }\n                                        return (\n                                            <div\n                                                key={toolCallId}\n                                                className={cx({\n                                                    skeleton: ['getWeather', 'getFileContents', 'queryCodebase', 'editFile', 'getSupabaseInstructions', 'getSupabaseLogs', 'getClientLogs', 'manageSupabaseAuth'].includes(toolName),\n                                                })}\n                                            >\n                                                {toolName === 'getFileContents' ? (\n                                                    <GetFileContentsToolResult\n                                                        path={args?.path || 'unknown'}\n                                                        reason={args?.reason || 'File content requested'}\n                                                        isDirectory={!args?.path?.includes('.') || args?.path?.endsWith('/')}\n                                                    />\n                                                ) : toolName === 'queryCodebase' ? (\n                                                    <QueryCodebaseToolResult query={args?.query} excludedFiles={args?.excludedFiles} result={args?.result} state={state}/>\n                                                ) : toolName === 'editFile' ? (\n                                                    <EditFileToolResult absolutePath={args?.absolutePath} result={args} state={state}/>\n                                                ) : toolName === 'getSupabaseInstructions' ? (\n                                                    <GetSupabaseInstructionsToolResult reason={args?.reason || 'Fetching Supabase schema'} state={state}/>\n                                                ) : toolName === 'getSupabaseLogs' ? (\n                                                    <GetSupabaseLogsToolResult reason={args?.reason || 'Fetching Supabase logs'} service={args?.service} functionId={args?.functionId} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'}/>\n                                                ) : toolName === 'querySupabaseContext' ? (\n                                                    <QuerySupabaseContextToolResult query={args?.query || 'Fetching Supabase resources'} state={'call'} result={''}/>\n                                                ): toolName === 'searchWeb' ? (\n                                                     <SearchWebToolResult query={args?.query || ''} reason={args?.reason || 'Searching the web'} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>\n                                                 ) : toolName === 'generateDesign' ? (\n                                                     <DesignToolResult projectId={projectId} chatId={chatId} reason={args?.prompt || 'Design app screens'} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>\n                                                 ) : toolName === 'getClientLogs' ? (\n                                                    <GetClientLogsToolResult reason={args?.reason || 'Fetching client logs'} type={args?.type} source={args?.source} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>\n                                                ) : toolName === 'displayMultiPerspectiveAnalysis' ? (\n                                                    <MultiPerspectiveAnalysisToolResult projectId={projectId} chatId={chatId} reason={args?.reason || 'Multi-perspective analysis'} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>\n                                                ) : toolName === 'manageSupabaseAuth' ? (\n                                                    <ManageSupabaseAuthToolResult reason={args?.reason || 'Managing Supabase auth'} action={args?.action} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>\n                                                ):  toolName === 'clientTesting' ? (\n                                                    <ClientTestingToolResult\n                                                        reason={args?.reason || 'Testing application'}\n                                                        state={'loading'}\n                                                        chatId={chatId}\n                                                        expectations={args?.expectations}\n                                                        featuresToTest={args?.featuresToTest}\n                                                        toolCallId={toolCallId}\n                                                        addToolResult={addToolResult}\n                                                    />\n                                                ): null}\n                                            </div>\n                                        );", "startLine": 291, "endLine": 390, "type": "component", "symbols": ["PurePreviewMessage component (tool invocation rendering)"], "score": 0.9, "context": "Component rendering tool invocation parts and results in chat messages, showing UI integration of chat tools with streaming results.", "includesImports": false}, {"filePath": "src/lib/chat/tools/get-client-logs.ts", "content": "export const getClientLogs = ({\n  dataStream,\n  creditUsageTracker,\n  logs,\n  messageId\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n  logs: LogEntry[];\n  messageId: string;\n}) => {\n  return tool({\n    description: 'Gets client-side logs to help debug problems with the application. This will return the most recent logs (maximum 20) from the current session. You can filter by log type and source. [CRITICAL INSTRUCTION]: DO NOT CALL THIS TOOL MULTIPLE TIMES IN A ROW. The logs will be EXACTLY THE SAME each time you call this tool until the user interacts with the app again. Calling this tool multiple times is useless and wasteful. WAIT for the user to tell you they have tried your suggestion before calling this tool again. If you have already called this tool once, DO NOT call it again until the user confirms they have interacted with the app.',\n    parameters: z.object({\n      type: z.enum(['error', 'warn', 'info', 'log', 'debug', 'all']).default('all')\n        .describe('The type of logs to fetch. Use \"all\" to get logs of all types.'),\n      source: z.enum(['console', 'network', 'snack', 'all']).default('all')\n        .describe('The source of logs to fetch. Use \"all\" to get logs from all sources.'),\n      limit: z.number().min(1).max(20).default(10)\n        .describe('Maximum number of log entries to return (1-20)'),\n      reason: z.string()\n        .describe(\"Describe why you need these logs for debugging\")\n    }),\n    execute: async ({ type, source, limit, reason }: { \n      type: 'error' | 'warn' | 'info' | 'log' | 'debug' | 'all',\n      source: 'console' | 'network' | 'snack' | 'all',\n      limit: number,\n      reason: string \n    }) => {\n      try {\n\n        const toolTracker = ToolCountTracker.getInstance();\n        console.log('Tool call allowed (getClientLogs):', toolTracker.shouldAllowToolCall(messageId, 'getClientLogs'))\n\n        // Check if we should allow this tool call\n        if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'getClientLogs')) {\n          return {\n            result: null,\n            message: `⚠️ Tool call limit reached. \n            Client logs do not refresh while you are working on the code. You need to stop and ask the user to interact with the app in the preview to get new logs.\n            DO NOT CLAIM THAT YOU HAVE SUCCESSFULLY updated/fixed/created a task until you get a confirmation using logs. ASK the user to test the app and them tell them to confirm if the feature worked or not. \n            THEY are NON technical, they can't see the logs or give you the logs. HELP THEM, DO NOT CONFUSE or MISGUIDE THEM.\n            `,\n          };\n        }\n        console.log(`Fetching client logs. Type: ${type}, Source: ${source}, Limit: ${limit}, Reason: ${reason}`);\n\n        // Increment the tool count if we have a chat ID\n        if (messageId) {\n          toolTracker.incrementToolCount(messageId, 'getClientLogs');\n        }\n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n\n        // Filter logs based on parameters\n        let filteredLogs = [...logs];\n        \n        // Filter by type if not 'all'\n        if (type !== 'all') {\n          filteredLogs = filteredLogs.filter(log => log.type.toLowerCase() === type.toLowerCase());\n        }\n        \n        // Filter by source if not 'all'\n        if (source !== 'all') {\n          filteredLogs = filteredLogs.filter(log => log.source === source);\n        }\n        \n        // Sort by timestamp (newest first)\n        filteredLogs.sort((a, b) => b.timestamp - a.timestamp);\n        \n        // Limit the number of logs\n        filteredLogs = filteredLogs.slice(0, Math.min(limit, 20));\n        \n        // Format logs for readability and truncate large messages\n        const formattedLogs = filteredLogs.map(log => ({\n          timestamp: dayjs(log.timestamp).toISOString(),\n          type: log.type,\n          source: log.source,\n          message: truncateLogMessage(log.message)\n        }));\n        \n        // Check for critical issues in the logs\n        const criticalIssues = detectCriticalIssues(logs);\n        \n        return JSON.stringify({\n          logs: formattedLogs,\n          count: formattedLogs.length,\n          totalAvailable: logs.length,\n          criticalIssues: criticalIssues.length > 0 ? criticalIssues : undefined,\n          timestamp: new Date().toISOString(),\n          comment: formattedLogs.length > 0 \n            ? `These are the ${formattedLogs.length} most recent logs matching your criteria. Use them to diagnose any issues you're experiencing.`\n            : `No logs found matching your criteria. Try broadening your search or check if the issue might be elsewhere.`\n        });\n      } catch (e: any) {\n        console.error(`Error while fetching client logs`, e);\n        return JSON.stringify({\n          error: e.message,\n          timestamp: new Date().toISOString(),\n          comment: `Error while fetching client logs: ${e.message}. Please try again with different parameters.`\n        });\n      }\n    }\n  });\n};", "startLine": 13, "endLine": 117, "type": "util", "symbols": ["getClientLogs"], "score": 0.8, "context": "Defines a tool to fetch client logs with streaming and credit usage tracking, showing integration with streaming system and tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/get-file-contents.ts", "content": "export const getFileContents = ({\n                                    fileManager,\n                                    dataStream,\n                                    creditUsageTracker\n                                }: {\n    fileManager: FileLineManager,\n    dataStream: DataStreamWriter,\n    creditUsageTracker: CreditUsageTracker\n}) => {\n    return tool({\n        description: 'Get complete file content or list directory contents when queryCodebase snippets are insufficient. Use sparingly - only when you need to see the full file structure, all imports/exports, or complete implementation details that snippets miss.',\n        parameters: z.object({\n            path: z.string().describe(\"File or directory path to retrieve. For files: must match exactly a file from the project. For directories: use folder path like 'components' or 'screens' to list contents. Use '*' to see all top-level directories and files.\"),\n            lineRange: z.array(z.number()).length(2).optional().describe(\"Optional [start, end] line range (1-based, inclusive). Use [start, -1] for start to end of file. Only applicable for files.\"),\n            searchPattern: z.string().optional().describe(\"Optional regex pattern to search within the file. Shows matching lines with context. Only applicable for files.\"),\n            reason: z.string().describe(\"Why you need the full file content or directory listing instead of using queryCodebase snippets.\")\n        }),\n        execute: async ({path, lineRange, searchPattern, reason}: {\n            path: string,\n            lineRange?: [number, number],\n            searchPattern?: string,\n            reason: string\n        }) => {\n            try {\n                const files = fileManager.getFileItems();\n                // Normalize path - handle both /libs and libs formats\n                const normalizedPath = path.startsWith('/') ? path.slice(1) : path;\n\n                // Check if this is a directory listing request\n                const isDirectoryRequest = !normalizedPath.includes('.') || normalizedPath.endsWith('/') || normalizedPath === '*';\n\n                if (isDirectoryRequest) {\n                    // Directory listing\n                    const dirPath = normalizedPath === '*' ? '' : normalizedPath.replace(/\\/$/, ''); // Remove trailing slash\n\n                    // Find all files that start with this directory path\n                    const matchingFiles = files.filter(f => {\n                        const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                        if (dirPath === '') {\n                            // For root or * pattern, include all files\n                            return true;\n                        }\n                        return fileName.startsWith(dirPath + '/');\n                    });\n\n                    if (matchingFiles.length === 0) {\n                        // Show available top-level directories\n                        const topLevelDirs = new Set<string>();\n                        files.forEach(f => {\n                            const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                            const firstSlash = fileName.indexOf('/');\n                            if (firstSlash > 0) {\n                                topLevelDirs.add(fileName.substring(0, firstSlash));\n                            }\n                        });\n\n                        return `Directory ${path} not found. Available directories: ${Array.from(topLevelDirs).join(', ')}`;\n                    }\n\n                    // Group files by subdirectory with depth 2 support\n                    const structure = new Map<string, { files: string[], subdirs: Map<string, string[]> }>();\n\n                    matchingFiles.forEach(f => {\n                        const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                        const relativePath = fileName.substring(dirPath.length + (dirPath ? 1 : 0));\n                        const pathParts = relativePath.split('/');\n\n                        if (pathParts.length === 1) {\n                            // Direct file in this directory\n                            if (!structure.has('.')) structure.set('.', { files: [], subdirs: new Map() });\n                            structure.get('.')!.files.push(pathParts[0]);\n                        } else if (pathParts.length === 2) {\n                            // File in immediate subdirectory (depth 1)\n                            const subDir = pathParts[0];\n                            if (!structure.has(subDir)) structure.set(subDir, { files: [], subdirs: new Map() });\n                            structure.get(subDir)!.files.push(pathParts[1]);\n                        } else if (pathParts.length >= 3) {\n                            // File in nested subdirectory (depth 2+)\n                            const subDir = pathParts[0];\n                            const nestedSubDir = pathParts[1];\n                            if (!structure.has(subDir)) structure.set(subDir, { files: [], subdirs: new Map() });\n\n                            const subDirData = structure.get(subDir)!;\n                            if (!subDirData.subdirs.has(nestedSubDir)) {\n                                subDirData.subdirs.set(nestedSubDir, []);\n                            }\n                            subDirData.subdirs.get(nestedSubDir)!.push(pathParts.slice(2).join('/'));\n                        }\n                    });\n\n                    // Format directory listing\n                    const displayPath = path === '*' ? 'Project Root' : path;\n                    let listing = `Directory: ${displayPath} (${matchingFiles.length} items)\\n\\n`;\n\n                    // Show subdirectories first\n                    const sortedKeys = Array.from(structure.keys()).sort((a, b) => {\n                        if (a === '.') return 1; // Files last\n                        if (b === '.') return -1;\n                        return a.localeCompare(b);\n                    });\n\n                    sortedKeys.forEach(key => {\n                        if (key === '.') {\n                            // Direct files\n                            const directFiles = structure.get(key)!.files.sort();\n                            directFiles.forEach(file => {\n                                const fullPath = dirPath ? `${dirPath}/${file}` : file;\n                                listing += `📄 ${file} (${fullPath})\\n`;\n                            });\n                        } else {\n                            // Subdirectory\n                            const subDirData = structure.get(key)!;\n                            const subDirPath = dirPath ? `${dirPath}/${key}` : key;\n                            const totalFiles = subDirData.files.length +\n                                Array.from(subDirData.subdirs.values()).reduce((sum, files) => sum + files.length, 0);\n\n                            listing += `📁 ${key}/ (${totalFiles} files) - ${subDirPath}/\\n`;\n\n                            // Show files in this subdirectory\n                            subDirData.files.sort().forEach(file => {\n                                listing += `  📄 ${file} (${subDirPath}/${file})\\n`;\n                            });\n\n                            // Show nested subdirectories (depth 2)\n                            const sortedSubDirs = Array.from(subDirData.subdirs.keys()).sort();\n                            sortedSubDirs.forEach(nestedDir => {\n                                const nestedFiles = subDirData.subdirs.get(nestedDir)!;\n                                const nestedPath = `${subDirPath}/${nestedDir}`;\n                                listing += `  📁 ${nestedDir}/ (${nestedFiles.length} files) - ${nestedPath}/\\n`;\n\n                                // Show first few files in nested directory\n                                const filesToShow = nestedFiles.slice(0, 3);\n                                filesToShow.forEach(file => {\n                                    listing += `    📄 ${file} (${nestedPath}/${file})\\n`;\n                                });\n\n                                if (nestedFiles.length > 3) {\n                                    listing += `    ... and ${nestedFiles.length - 3} more files\\n`;\n                                }\n                            });\n                        }\n                    });\n\n                    return listing;\n                }\n\n                // File content request\n                const fileItem = files.find(f => f.name === normalizedPath || f.name === `/${normalizedPath}`);\n\n                if (!fileItem) {\n                    return `File ${path} not found. Available files: ${files.slice(0, 10).map(f => f.name).join(', ')}${files.length > 10 ? '...' : ''}`;\n                }\n\n                let content = fileItem.content;\n                const lines = content.split('\\n');\n                const totalLines = lines.length;\n\n                // Apply search pattern if specified\n                if (searchPattern) {\n                    try {\n                        const regex = new RegExp(searchPattern, 'gi');\n                        const matchingLines: string[] = [];\n                        const contextBefore = 3;\n                        const contextAfter = 3;\n\n                        let lastIncludedLine = -1;\n\n                        lines.forEach((line, index) => {\n                            if (regex.test(line)) {\n                                const startContext = Math.max(0, index - contextBefore);\n                                const endContext = Math.min(lines.length - 1, index + contextAfter);\n\n                                // Add gap indicator if there's a gap\n                                if (startContext > lastIncludedLine + 1) {\n                                    matchingLines.push('...');\n                                }\n\n                                // Add context and matching line\n                                for (let i = startContext; i <= endContext; i++) {\n                                    if (i > lastIncludedLine) {\n                                        const lineNumber = i + 1;\n                                        const prefix = i === index ? '>' : ' ';\n                                        matchingLines.push(`${prefix}${lineNumber.toString().padStart(4)}: ${lines[i]}`);\n                                        lastIncludedLine = i;\n                                    }\n                                }\n                            }\n                        });\n\n                        content = matchingLines.join('\\n');\n                    } catch (regexError) {\n                        return `Invalid regex pattern: ${searchPattern}`;\n                    }\n                } else if (lineRange) {\n                    // Apply line range if specified\n                    const [start, end] = lineRange;\n                    const startLine = Math.max(1, start) - 1; // Convert to 0-based\n                    const endLine = end === -1 ? lines.length : Math.min(end, lines.length);\n\n                    const selectedLines = lines.slice(startLine, endLine);\n                    content = selectedLines.map((line, index) => {\n                        const lineNumber = startLine + index + 1;\n                        return `${lineNumber.toString().padStart(4)}: ${line}`;\n                    }).join('\\n');\n                } else {\n                    // Return full file with line numbers\n                    content = lines.map((line, index) => {\n                        const lineNumber = index + 1;\n                        return `${lineNumber.toString().padStart(4)}: ${line}`;\n                    }).join('\\n');\n                }\n\n                creditUsageTracker.trackOperation('tool_call');\n\n                const resultLines = content.split('\\n').length;\n                const truncated = resultLines > 500;\n\n                if (truncated) {\n                    const truncatedLines = content.split('\\n').slice(0, 500);\n                    content = truncatedLines.join('\\n') + '\\n\\n<response clipped - file too long>';\n                }\n\n                return `File: ${path} (${totalLines} lines total, showing ${truncated ? '500' : resultLines} lines)\nReason: ${reason}\n${lineRange ? `Line range: ${lineRange[0]}-${lineRange[1] === -1 ? 'end' : lineRange[1]}` : ''}\n${searchPattern ? `Search pattern: ${searchPattern}` : ''}\n\n${content}`;\n\n            } catch (error: any) {\n                console.error('Error in getFileContents:', error);\n                return `Error retrieving file content: ${error.message}`;\n            }\n        }\n    });", "startLine": 7, "endLine": 241, "type": "util", "symbols": ["getFileContents"], "score": 0.8, "context": "Defines a tool to get file contents with streaming and credit usage tracking, showing integration with streaming system and tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/get-supabase-instructions.ts", "content": "export const getSupabaseInstructions = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n  project: Project;\n}) => {\n  return tool({\n    description: 'Get Supabase project instructions. This provides you with the supabase specific guides and instructions to understand and plan how to integrate supabase into the project.',\n    parameters: z.object({\n      reason: z.string().describe(\"Describe why you need the Supabase instructions\")\n    }),\n    execute: async ({ reason }: { reason: string }) => {\n      try {\n        console.log(`Fetching Supabase instructions. Reason: ${reason}`);\n        \n        // Create a new instance of the SupabaseIntegrationProvider\n        const supabaseIntegrationProvider = new SupabaseIntegrationProvider();\n        \n        // Get the latest instructions for the chat\n        const result =  supabaseIntegrationProvider.getSupabaseInitialInstructions();\n        \n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n        \n        return JSON.stringify({\n          instructions: result,\n          comment: 'Use this Supabase instruction to implement supabase features correctly. Call querySupabaseContext tool to fetch schema structure and proper RLS policies, secrets, functions, database functions. triggers.'\n        });\n      } catch (e: any) {\n        console.error('Error while fetching Supabase instructions', e);\n        return `Error while fetching Supabase instructions. Please try again. Error: ${e.message}`;\n      }\n    }\n  });\n};", "startLine": 12, "endLine": 49, "type": "util", "symbols": ["getSupabaseInstructions"], "score": 0.8, "context": "Defines a tool to fetch Supabase instructions with streaming and credit usage tracking, showing integration with streaming system and tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/get-supabase-logs.ts", "content": "export const getSupabaseLogs = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n  project: Project;\n}) => {\n  return tool({\n    description: 'Gets logs for a Supabase project by service type. Use this to help debug problems with your app. There are two ways to fetch edge function logs:\\n\\n1. By function name: Use `functionName: \"your-function-name\"` to fetch logs by the function name (easier for users to understand)\\n2. By function ID: Use `functionId: \"ce62b3db-daf3-44ca-b935-957570435829\"` if you know the specific ID\\n\\nThis will return logs from the last hour. If no logs are found, ask the user to test the functionality again to generate new logs, as only the user can trigger new function executions.',\n    parameters: z.object({\n      service: z.enum([\n        'api',\n        'branch-action',\n        'postgres',\n        'edge-function',\n        'auth',\n        'storage',\n        'realtime',\n      ]).describe('The service to fetch logs for'),\n      limit: z.number().optional().default(100).describe('Maximum number of log entries to return'),\n      functionId: z.string().optional().describe('Specific function ID to filter logs for a single edge function (only applicable when service is \"edge-function\"). If you know the ID, provide it directly.'),\n      functionName: z.string().optional().describe('Name of the function to fetch logs for (only applicable when service is \"edge-function\"). The tool will automatically find the matching function ID.'),\n      reason: z.string().describe(\"Describe why you need these logs for debugging\")\n    }),\n    execute: async ({ service, limit, functionId, functionName, reason }: { \n      service: 'api' | 'branch-action' | 'postgres' | 'edge-function' | 'auth' | 'storage' | 'realtime',\n      limit?: number,\n      functionId?: string,\n      functionName?: string,\n      reason: string \n    }) => {\n      try {\n        console.log(`Fetching Supabase ${service} logs. Reason: ${reason}. Limit: ${limit || 100}. ${functionId ? `Function ID: ${functionId}` : ''}${functionName ? `Function Name: ${functionName}` : ''}`);\n        \n        if (!project.connectionId || !project.supabaseProjectId) {\n          throw new Error('Project is not linked to a Supabase project');\n        }\n        \n        // Create a new instance of the SupabaseIntegrationProvider\n        const supabaseIntegrationProvider = new SupabaseIntegrationProvider();\n        \n        let resolvedFunctionId = functionId;\n        \n        // If functionName is provided but not functionId, try to find the function ID\n        if (service === 'edge-function' && functionName && !functionId) {\n          try {\n            // Get the functions list\n            const functionsResult = await supabaseIntegrationProvider.getProjectResources({\n              projectId: project.id,\n              resourceType: 'functions'\n            });\n            \n            // Find the function with the matching name\n            const functions = functionsResult?.functions || [];\n            const matchingFunction = functions.find(\n              (func: any) => func.name?.toLowerCase() === functionName.toLowerCase()\n            );\n            \n            if (matchingFunction) {\n              resolvedFunctionId = matchingFunction.id;\n              console.log(`Found function ID ${resolvedFunctionId} for function name ${functionName}`);\n            } else {\n              console.log(`Could not find function ID for function name ${functionName}`);\n            }\n          } catch (error) {\n            console.error('Error finding function ID from name:', error);\n          }\n        }\n        \n        // Use the getLogs method from SupabaseIntegrationProvider which uses SupabaseDebuggingTools\n        const logsData = await supabaseIntegrationProvider.getLogs({\n          projectId: project.id,\n          service,\n          limit: limit || 100,\n          functionId: resolvedFunctionId\n        });\n        \n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n\n        // For debugging\n        console.log('service', service);\n        console.log('logsData', logsData);\n        \n        // Format the response using the actual logs data from SupabaseIntegrationProvider\n        return JSON.stringify({\n          logs: logsData,\n          service,\n          ...(resolvedFunctionId && { functionId: resolvedFunctionId }),\n          ...(functionName && { functionName }),\n          timestamp: new Date().toISOString(),\n          comment: resolvedFunctionId\n            ? `These are the most recent logs for function ${functionName || ''} (ID: ${resolvedFunctionId}) from your Supabase project. Use them to diagnose any issues you're experiencing.`\n            : `These are the most recent ${service} logs from your Supabase project. Use them to diagnose any issues you're experiencing.`\n        });\n      } catch (e: any) {\n        console.error(`Error while fetching Supabase ${service} logs`, e);\n        return JSON.stringify({\n          error: e.message,\n          service,\n          ...(functionId && { functionId }),\n          ...(functionName && { functionName }),\n          timestamp: new Date().toISOString(),\n          comment: `Error while fetching Supabase ${service} logs: ${e.message}. Please try again or check your Supabase connection.`\n        });\n      }\n    }\n  });\n};", "startLine": 14, "endLine": 124, "type": "util", "symbols": ["getSupabaseLogs"], "score": 0.8, "context": "Defines a tool to fetch Supabase logs with streaming and credit usage tracking, showing integration with streaming system and tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/manage-supabase-auth.ts", "content": "export const manageSupabaseAuth = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n  project: Project;\n}) => {\n  return tool({\n    description: 'Manages Supabase auth configuration. Use this tool to view current auth settings, update auth configuration, or retrieve SSO provider information. This helps with configuring authentication options like email templates, redirect URLs, and external OAuth providers.',\n    parameters: z.object({\n      action: z.enum([\n        'getAuthConfig',\n        'updateAuthConfig',\n        'getSSOProviders'\n      ]).describe('The action to perform on the auth configuration'),\n      authConfig: z.record(z.any()).optional().describe('The auth configuration to update (only required for updateAuthConfig action). Same format as the response from getAuthConfig. Pass only the fields to update.'),\n      reason: z.string().describe(\"Describe why you need to access or modify the auth configuration\")\n    }),\n    execute: async ({ action, authConfig, reason }: { \n      action: 'getAuthConfig' | 'updateAuthConfig' | 'getSSOProviders',\n      authConfig?: UpdateProjectAuthConfigRequestBody,\n      reason: string \n    }) => {\n      try {\n        console.log(`Performing Supabase auth action: ${action}. Reason: ${reason}.`);\n        if(authConfig) {\n          console.log('Updating', authConfig)\n        }\n        \n        if (!project.connectionId || !project.supabaseProjectId) {\n          throw new Error('Project is not linked to a Supabase project');\n        }\n        \n        // Create a new instance of the SupabaseIntegrationProvider\n        const supabaseIntegrationProvider = new SupabaseIntegrationProvider();\n        \n        let result;\n        \n        // Perform the requested action\n        switch (action) {\n          case 'getAuthConfig':\n            result = await supabaseIntegrationProvider.getAuthConfig(project.id);\n            break;\n          case 'updateAuthConfig':\n            if (!authConfig) {\n              throw new Error('Auth configuration is required for updateAuthConfig action');\n            }\n            result = await supabaseIntegrationProvider.updateAuthConfig(project.id, authConfig);\n            break;\n          case 'getSSOProviders':\n            result = await supabaseIntegrationProvider.getSSOProviders(project.id);\n            break;\n        }\n        \n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n        \n        // Format the response\n        return JSON.stringify({\n          action,\n          result,\n          timestamp: new Date().toISOString(),\n          comment: getActionComment(action, result)\n        });\n      } catch (e: any) {\n        console.error(`Error while performing Supabase auth action: ${action}`, e);\n        return JSON.stringify({\n          error: e.message,\n          action,\n          timestamp: new Date().toISOString(),\n          comment: `Error while performing Supabase auth action ${action}: ${e.message}. Please try again or check your Supabase connection.`\n        });\n      }\n    }\n  });\n};", "startLine": 13, "endLine": 90, "type": "util", "symbols": ["manageSupabaseAuth"], "score": 0.8, "context": "Defines a tool to manage Supabase auth with streaming and credit usage tracking, showing integration with streaming system and tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/multi-perspective-analysis.tool.ts", "content": "export const multiPerspectiveAnalysis = ({\n  dataStream,\n}: {\n  dataStream: DataStreamWriter;\n}) => {\n  return tool({\n    description: 'Analyze a topic from multiple perspectives using different AI personas to reach a more comprehensive and balanced understanding',\n    parameters: multiPerspectiveAnalysisSchema,\n    execute: async ({ topic, context = '', focusAreas = [], requiredPersonas = [], analysisDepth = 'standard', discussionRounds = 3 }) => {\n      try {\n        // Stream the initial setup information\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `# Multi-Perspective Analysis: ${topic}\\n\\nInitiating analysis with multiple perspectives...\\n\\n`\n        });\n\n        // Select which personas to use\n        const selectedPersonaNames = requiredPersonas.length > 0 ? requiredPersonas : personaList;\n        \n        if (selectedPersonaNames.length === 0) {\n          throw new Error('No valid personas selected for analysis');\n        }\n\n        // Determine token allocation based on analysis depth\n        let maxTokensPerPersona;\n        switch (analysisDepth) {\n          case 'brief':\n            maxTokensPerPersona = 500;\n            break;\n          case 'comprehensive':\n            maxTokensPerPersona = 1500;\n            break;\n          case 'standard':\n          default:\n            maxTokensPerPersona = 1000;\n        }\n\n        // Track all contributions to the discussion\n        type Contribution = {\n          persona: PersonaName;\n          content: string;\n          round: number;\n          replyTo?: PersonaName[];\n        };\n\n        const contributions: Contribution[] = [];\n        \n        // Initialize the discussion with the topic introduction\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `## Multi-Perspective Discussion\\n\\n`\n        });\n\n        // PHASE 1: Initial perspectives from each persona\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `### Round 1: Initial Perspectives\\n\\n`\n        });\n\n        // Generate initial perspectives from each persona\n        for (const personaName of selectedPersonaNames) {\n          dataStream.writeData({\n            type: 'multi-perspective-update',\n            content: `#### ${personaName}:\\n\\n`\n          });\n\n          // Create the prompt for this persona\n          const personaPrompt = `\nYou are the \"${personaName}\" persona with the following characteristics:\n${personaDescriptions[personaName]}\n\nAnalyze the following topic from your unique perspective:\nTopic: ${topic}\n${context ? `Context: ${context}` : ''}\n${focusAreas.length > 0 ? `Focus Areas: ${focusAreas.join(', ')}` : ''}\n\nProvide your analysis in a clear, concise manner that reflects your perspective. Be insightful but concise.\n`;\n\n          // Generate the persona's perspective\n          const result = streamText({\n            model: customModel(\"anthropic/claude-sonnet-4\"),\n            temperature: 0.7,\n            messages: [\n              {\n                role: 'system',\n                content: personaPrompt\n              }\n            ]\n          });\n          \n          let perspective = '';\n          for await (const chunk of result.textStream) {\n            perspective += chunk;\n          }\n\n          // Add this perspective to the contributions\n          contributions.push({\n            persona: personaName,\n            content: perspective,\n            round: 1\n          });\n\n          dataStream.writeData({\n            type: 'multi-perspective-update',\n            content: `${perspective}\\n\\n`\n          });\n        }\n\n        // PHASE 2: Interactive discussion rounds\n        // Each round, personas respond to previous contributions\n        for (let round = 2; round <= discussionRounds; round++) {\n          dataStream.writeData({\n            type: 'multi-perspective-update',\n            content: `### Round ${round}: Developing the Discussion\\n\\n`\n          });\n\n          // Get all contributions from the previous round\n          const previousRoundContributions = contributions.filter(c => c.round === round - 1);\n          \n          // Each persona responds to the previous round\n          for (const personaName of selectedPersonaNames) {\n            // Get all previous contributions except this persona's own contribution\n            const relevantContributions = previousRoundContributions\n              .filter(c => c.persona !== personaName);\n              \n            if (relevantContributions.length === 0) continue;\n            \n            dataStream.writeData({\n              type: 'multi-perspective-update',\n              content: `#### ${personaName}:\\n\\n`\n            });\n\n            // Format the previous contributions for the prompt\n            const previousContributionsText = relevantContributions\n              .map(c => `${c.persona}: ${c.content}`)\n              .join('\\n\\n');\n\n            // Create a prompt that encourages building on previous ideas\n            const dialoguePrompt = `\nYou are the \"${personaName}\" persona with the following characteristics:\n${personaDescriptions[personaName]}\n\nYou're participating in round ${round} of a discussion on \"${topic}\".\n\nHere are the most recent contributions from other participants:\n\n${previousContributionsText}\n\nBased on your unique perspective and the discussion so far:\n1. Respond to at least 2 specific points made by others\n2. Build upon or challenge ideas in a constructive way\n3. Introduce a new insight or question that advances the discussion\n4. Maintain your distinct perspective while seeking common ground\n\nBe concise but insightful. Your goal is to deepen the collective understanding.\n`;\n\n            // Generate the persona's response\n            const result = streamText({\n              model: customModel(\"anthropic/claude-sonnet-4\"),\n              temperature: 0.7,\n              messages: [\n                {\n                  role: 'system',\n                  content: dialoguePrompt\n                }\n              ]\n            });\n            \n            let response = '';\n            for await (const chunk of result.textStream) {\n              response += chunk;\n            }\n\n            // Add this response to the contributions\n            contributions.push({\n              persona: personaName,\n              content: response,\n              round: round,\n              replyTo: relevantContributions.map(c => c.persona)\n            });\n\n            dataStream.writeData({\n              type: 'multi-perspective-update',\n              content: `${response}\\n\\n`\n            });\n          }\n        }\n\n        // PHASE 3: Generate synthesis and consensus\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `## Synthesis and Consensus\\n\\n`\n        });\n\n        // Compile all contributions for synthesis\n        const allContent = contributions\n          .map(c => `Round ${c.round} - ${c.persona}: ${c.content}`)\n          .join('\\n\\n');\n\n        // Create the synthesis prompt\n        const synthesisPrompt = `\nYou are a neutral synthesizer tasked with finding common ground and key insights from multiple perspectives on the topic: \"${topic}\"\n\nHere are all the perspectives and dialogue exchanges:\n\n${allContent}\n\nPlease provide:\n1. A summary of key points of agreement across perspectives\n2. Important points of disagreement or tension\n3. Unique insights contributed by each perspective\n4. A balanced synthesis that incorporates the strongest elements from each viewpoint\n5. Remaining questions or areas for further exploration\n\nYour synthesis should be fair to all perspectives while highlighting the most valuable insights from each.\n`;\n\n        // Generate the synthesis\n        const synthesisResult = streamText({\n          model: customModel(\"anthropic/claude-sonnet-4\"),\n          temperature: 0.7,\n          messages: [\n            {\n              role: 'system',\n              content: synthesisPrompt\n            }\n          ]\n        });\n        \n        let synthesis = '';\n        for await (const chunk of synthesisResult.textStream) {\n          synthesis += chunk;\n        }\n\n        // Synthesis is built in the streaming loop above\n\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: synthesis\n        });\n\n        // Return the complete analysis results\n        return {\n          topic,\n          contributions,\n          synthesis,\n          result: 'success'\n        };\n      } catch (error) {\n        console.error('Error in multi-perspective analysis:', error);\n        dataStream.writeData({\n          type: 'error',\n          error: error instanceof Error ? error.message : 'Failed to complete multi-perspective analysis'\n        });\n        throw error;\n      }\n    }\n  });\n};", "startLine": 55, "endLine": 315, "type": "util", "symbols": ["multiPerspectiveAnalysis"], "score": 0.8, "context": "Defines a complex multi-perspective analysis tool that streams responses and integrates with the streaming system, showing detailed tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/search-web.ts", "content": "export const searchWeb = ({\n  dataStream,\n  creditUsageTracker\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n}) => {\n  return tool({\n    description: 'Performs a web search to get a list of relevant web documentation for the given query and optional domain filter. Use this tool ONLY when you need up-to-date information that is not available in the codebase or when the user explicitly asks for external information. IT WILL ALWAYS be used to search for documentation. Do not use this tool for general coding questions or tasks that can be solved with your existing knowledge.',\n    parameters: z.object({\n      query: z.string().describe(\"The search query to find relevant information. Make sure to filter for documentation and be as specific as possible\"),\n      domain: z.string().optional().describe(\"Optional domain to recommend the search prioritize. Try to include it as the number of results is limited to 2 and truncated heavily to save tokens.\"),\n      reason: z.string().describe(\"Explain why this information cannot be found in the codebase and why it's essential for completing the user's task\")\n    }),\n    execute: async ({ query, domain, reason }: { \n      query: string;\n      domain?: string;\n      reason: string;\n    }) => {\n      try {\n        console.log(`Searching web for: \"${query}\". Reason: ${reason}, Domain: ${domain}`);\n\n        // Validate the search reason to ensure the tool is not being misused\n        // const validReasons = [\n        //   \"current events\",\n        //   \"up-to-date information\",\n        //   \"external documentation\",\n        //   \"third-party API details\",\n        //   \"user explicitly requested\",\n        //   \"package documentation\"\n        // ];\n        //\n        // const isValidReason = validReasons.some(validReason =>\n        //   reason.toLowerCase().includes(validReason.toLowerCase())\n        // );\n        \n        // if (!isValidReason) {\n        //   return JSON.stringify({\n        //     error: \"Invalid search reason. Web search should only be used for obtaining up-to-date information that cannot be found in the codebase or when explicitly requested by the user.\",\n        //     suggestedAction: \"Use your existing knowledge or codebase search tools instead.\"\n        //   });\n        // }\n        \n        // Create a new instance of the TavilyService\n        const tavilyService = new TavilyService();\n        \n        // Configure search options\n        const searchOptions = {\n          numberOfTries: 1,\n          includeRawContent: true,\n          includedDomains: domain ? [domain] : undefined\n        };\n        \n        // Perform the search\n        const searchResponse = await tavilyService.search(\n          query,\n          'advanced',\n          2, // Limit to 5 results to conserve tokens\n          searchOptions,\n        );\n\n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n        \n        // Format the results\n        const formattedResults = searchResponse.results.map(result => ({\n          title: result.title,\n          url: result.url,\n          content: (result.rawContent || result.content).substring(0, 500) + ((result.rawContent || result.content).length > 500 ? '... // Truncated to save tokens' : ''),\n        }));\n\n\n        return JSON.stringify({\n          query,\n          results: formattedResults,\n          answer: searchResponse.answer || \"No direct answer available.\",\n          comment: 'Use this information to supplement your existing knowledge. Always cite sources when providing information from web searches.'\n        });\n      } catch (e: any) {\n        console.error('Error while searching the web', e);\n        return `Error while searching the web. Please try again. Error: ${e.message}`;\n      }\n    }\n  });\n};", "startLine": 11, "endLine": 95, "type": "util", "symbols": ["searchWeb"], "score": 0.8, "context": "Defines a web search tool that integrates with streaming and credit usage tracking, showing tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/client-testing.tool.ts", "content": "export const clientTestingTool = ({\n                                      dataStream,\n                                      creditUsageTracker\n                                  }: {\n    dataStream: DataStreamWriter;\n    creditUsageTracker: CreditUsageTracker;\n}) => {\n    return tool({\n        description: 'After every complex step, ask the user to test the functionality.',\n        parameters: z.object({\n            featuresToTest: z.string().describe(\"What should the user test? Be concise, noon-technical and focussed\"),\n            expectations: z.string().describe(\"What is the expected outcome?\"),\n            reason: z.string().describe(\"Why do you need the user to test the app right now?\")\n        })\n        // No execute function - this enables human-in-the-loop pattern\n        // The tool result will be added via addToolResult when user completes testing\n    });\n};", "startLine": 10, "endLine": 27, "type": "util", "symbols": ["clientTestingTool"], "score": 0.7, "context": "Defines a chat tool for human-in-the-loop client testing, showing a tool that integrates with streaming but requires user interaction.", "includesImports": false}, {"filePath": "src/lib/chat/tools/add-ai-memory.ts", "content": "export const addAiMemory = ({\n                                dataStream,\n                                creditUsageTracker,\n                                projectId\n                            }: {\n    dataStream: DataStreamWriter,\n    creditUsageTracker: CreditUsageTracker,\n    projectId: string\n}) => {\n    return tool({\n        description: `Use the addAiMemory tool to store critical information for future interactions.\n        WHEN TO USE:\n        - After completing part of a multi-step task to track remaining steps\n        - To record user preferences, project requirements, or design decisions\n        - To capture technical constraints or architectural decisions\n        - To maintain context across multiple interactions\n\n        HOW TO USE:\n        - Use clear prefixes for different types of memories:\n          * USER PREFERENCE: For user style/feature preferences\n          * REQUIREMENT: For project requirements\n          * COMPLETED/NEXT: For task progress tracking\n          * DECISION: For design/technical decisions\n          * CONSTRAINT: For technical limitations\n        - Be specific and concise (1-2 sentences per memory)\n        - Always use at the END of your response\n        `,\n        parameters: z.object({\n            knowledge: z.string().describe(`Concise, specific information to remember for future interactions. Use prefixes like USER PREFERENCE:, REQUIREMENT:, COMPLETED:/NEXT:, DECISION:, or CONSTRAINT: to categorize the memory. Be specific and actionable - this information will be available in future interactions.`)\n        }),\n        execute: async ({knowledge}: { knowledge: string }) => {\n            try {\n                const project = await getProjectById({id: projectId})\n                if (!project) {\n                    return \"Project not found\"\n                }\n\n                // Constants for memory management\n                const MAX_MEMORY_CHARS = 8000; // About 2000 tokens\n                const MAX_CATEGORY_ENTRIES = 10; // Maximum entries per category before summarization\n\n                // Process the new knowledge entry\n                const timestamp = dayjs().toISOString();\n                const newEntry = {\n                    timestamp,\n                    content: knowledge.trim(),\n                    category: extractCategory(knowledge)\n                };\n\n                // Parse existing memory into structured format\n                const existingMemories = parseMemories(project.aiGeneratedMemory || '');\n\n                // Add new entry\n                existingMemories.push(newEntry);\n\n                // Manage memory size through categorization and summarization\n                const managedMemories = manageMemorySize(existingMemories, MAX_MEMORY_CHARS, MAX_CATEGORY_ENTRIES);\n\n                // Convert back to string format\n                const updatedMemory = memoriesToString(managedMemories);\n\n                // Update the project with the new memory\n                creditUsageTracker.trackOperation(\"add_ai_memory\", 1);\n                await updateProject({id: projectId, aiGeneratedMemory: updatedMemory});\n                return \"Memory saved and optimized\"\n            } catch (e: any) {\n                console.log('Error while saving memory', e);\n                return `Failed to save memory: ${e.message}`\n            }\n        }\n    });\n};", "startLine": 12, "endLine": 83, "type": "util", "symbols": ["addAiMemory"], "score": 0.7, "context": "Defines a tool for adding AI memory with streaming data integration and credit tracking, relevant for understanding tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/index.ts", "content": "// Export all tools from this directory\nexport * from './get-supabase-instructions';\nexport * from './get-supabase-logs';\nexport * from './get-client-logs';\nexport * from './get-file-contents';\nexport * from './create-file.tool';\nexport * from './edit-file.tool';\nexport * from './query-codebase';\nexport * from './add-ai-memory';\nexport * from './tool-count-tracker';\nexport * from './search-web';\nexport * from './generate-design.tool';\nexport * from './multi-perspective-analysis.tool';\nexport * from './display-multi-perspective-analysis';\n", "startLine": 1, "endLine": 15, "type": "unknown", "symbols": ["tools index exports"], "score": 0.6, "context": "Exports all chat tools, providing an overview of available tools that integrate with the streaming system.", "includesImports": false}], "additionalFiles": []}}