{"timestamp": "2025-06-14T16:49:51.367Z", "query": "Show me the addMessage function in sessionStore and how messages are added to the local state and database", "executionTime": 4411, "snippetsCount": 1, "additionalFilesCount": 0, "totalLines": 13, "snippets": [{"filePath": "src/lib/store/session-store.ts", "type": "util", "context": "This function shows how content chunks (messages) are added to the local session state and how the full HTML is updated, which is directly relevant to understanding how messages are added to the local state and database in the session store.", "score": 1, "lines": 13, "startLine": 73, "endLine": 85, "symbols": ["addContentChunk"], "preview": "export function addContentChunk(sessionId: string, chunk: string): void {\n  const session = sessions.get(sessionId);\n  if (session) {\n    session.contentChunks.push(chunk);\n    // Also update the full HTML for clients that don't support incremental updates\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/store/session-store.ts", "content": "export function addContentChunk(sessionId: string, chunk: string): void {\n  const session = sessions.get(sessionId);\n  if (session) {\n    session.contentChunks.push(chunk);\n    // Also update the full HTML for clients that don't support incremental updates\n    const extractedHtml = extractHtmlFromChunk(chunk);\n    if (extractedHtml) {\n      session.html = extractedHtml;\n    }\n    session.lastUpdated = new Date();\n    sessions.set(sessionId, session);\n  }\n}", "startLine": 73, "endLine": 85, "type": "util", "symbols": ["addContentChunk"], "score": 1, "context": "This function shows how content chunks (messages) are added to the local session state and how the full HTML is updated, which is directly relevant to understanding how messages are added to the local state and database in the session store.", "includesImports": false}], "additionalFiles": []}}