{"timestamp": "2025-06-14T17:24:13.235Z", "query": "How does the AI streaming and response generation work? Show me the complete flow from user input to AI response", "executionTime": 12785, "snippetsCount": 4, "additionalFilesCount": 0, "totalLines": 1437, "snippets": [{"filePath": "src/lib/ai/index.ts", "type": "util", "context": "This function custom<PERSON><PERSON><PERSON> modifies the request body for AI streaming, handles message caching, merges messages for specific models, and performs the actual fetch call to the AI provider. It is central to how AI streaming and response generation works from user input to AI response.", "score": 1, "lines": 371, "startLine": 37, "endLine": 407, "symbols": ["customFetch"], "preview": "const customFetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {\n    // Log request details\n    // console.log('\\n=== OpenRouter Request ===');\n    // @ts-ignore\n    // console.log('URL:', typeof input === 'string' ? input : input.url);\n..."}, {"filePath": "src/lib/services/stream-service.ts", "type": "unknown", "context": "This class StreamService manages the AI text streaming process, including starting the stream with streamText, handling chunks, tool calls, errors, and finishing. It encapsulates the flow from user messages to streaming AI responses.", "score": 1, "lines": 273, "startLine": 110, "endLine": 382, "symbols": ["StreamService"], "preview": "export class StreamService {\n    private config: StreamServiceConfig;\n    private toolCallCosts: ToolCallCost[] = [];\n\n    constructor(config: StreamServiceConfig) {\n..."}, {"filePath": "src/lib/chat/handlers/message.handler.ts", "type": "unknown", "context": "This class MessageHandler prepares and manages messages for AI processing, including system prompts, user messages, filtering, caching, and message transformations. It is part of the flow from user input to AI response generation.", "score": 0.9, "lines": 632, "startLine": 34, "endLine": 665, "symbols": ["MessageHandler"], "preview": "export class MessageHandler {\n    private messagesWithTurns: Array<Message> = [];\n    private coreMessages: CoreMessage[] = [];\n    private userMessage: CoreMessage | null = null;\n    private filteredMessages: CoreMessage[] = [];\n..."}, {"filePath": "src/lib/chat/tools/query-codebase.ts", "type": "util", "context": "This tool queryCodebase uses the ContextEngine to query the codebase for relevant files and snippets based on a natural language query. It is part of the AI's process to understand user input and generate relevant code responses.", "score": 0.6, "lines": 161, "startLine": 23, "endLine": 183, "symbols": ["queryCodebase"], "preview": "export const queryCodebase = ({\n                                  projectId,\n                                  files,\n                                  fileManager,\n                                  messageId = '',\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/ai/index.ts", "content": "const customFetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {\n    // Log request details\n    // console.log('\\n=== OpenRouter Request ===');\n    // @ts-ignore\n    // console.log('URL:', typeof input === 'string' ? input : input.url);\n    // console.log('Method:', init?.method);\n    // console.log('Headers:', JSON.stringify(init?.headers, null, 2));\n\n    let modifiedInit = {...init};\n\n    if (init?.body) {\n        const bodyObj = JSON.parse(init.body as string);\n\n        bodyObj.parallel_tool_calls = false;\n        if (bodyObj.model.includes(\"claude\") || bodyObj.model.includes(\"gemini\") ) {\n            // Add cache control to system messages\n            if (bodyObj.messages && Array.isArray(bodyObj.messages)) {\n                if (bodyObj.messages[0].role === 'system') {\n                    updateMessageWithCaching(bodyObj.messages[0]);\n                }\n                \n                // For Claude Sonnet 4, merge consecutive assistant messages if the second one has tool calls\n                if (bodyObj.model.includes(\"claude-sonnet-4\")) {\n                    const mergedMessages: any[] = [];\n                    \n                    for (let i = 0; i < bodyObj.messages.length; i++) {\n                        const currentMessage = bodyObj.messages[i];\n                        \n                        // Check if this is an assistant message and the next one is also an assistant with tool calls\n                        if (\n                            currentMessage.role === 'assistant' && \n                            i + 1 < bodyObj.messages.length && \n                            bodyObj.messages[i + 1].role === 'assistant' && \n                            bodyObj.messages[i + 1].tool_calls && \n                            bodyObj.messages[i + 1].tool_calls.length > 0\n                        ) {\n                            // Create a merged message\n                            const nextMessage = bodyObj.messages[i + 1];\n                            \n                            // Handle content merging with proper type handling\n                            let mergedContent;\n                            if (Array.isArray(currentMessage.content) && Array.isArray(nextMessage.content)) {\n                                // Both are arrays, concatenate them with proper type handling\n                                // Use type assertion to help TypeScript understand the structure\n                                const currentContent = currentMessage.content as {type: string, text: string}[];\n                                const nextContent = nextMessage.content as {type: string, text: string}[];\n                                mergedContent = [...currentContent, ...nextContent];\n                            } else if (Array.isArray(currentMessage.content)) {\n                                // Current is array, next is string or other\n                                mergedContent = currentMessage.content as {type: string, text: string}[];\n                            } else if (Array.isArray(nextMessage.content)) {\n                                // Next is array, current is string or other\n                                mergedContent = nextMessage.content as {type: string, text: string}[];\n                            } else {\n                                // Neither is array, use current or fallback to next\n                                mergedContent = currentMessage.content || nextMessage.content;\n                            }\n                            \n                            const mergedMessage = {\n                                ...currentMessage,\n                                tool_calls: nextMessage.tool_calls,\n                                content: mergedContent\n                            };\n                            \n                            mergedMessages.push(mergedMessage);\n                            // Skip the next message since we've merged it\n                            i++;\n                        } else {\n                            mergedMessages.push(currentMessage);\n                        }\n                    }\n                    \n                    bodyObj.messages = mergedMessages;\n                }\n\n                // Find the last system message and mark is cached\n                // const lastSystemMessage = bodyObj.messages.findLast((message: any) => message.role === \"system\");\n                // if (lastSystemMessage) {\n                //     updateMessageWithCaching(lastSystemMessage)\n                // }\n\n                const fileMessage = bodyObj.messages.find((message: any) => {\n                    if (message.role === \"user\") {\n                        return Array.isArray(message.content) ?\n                            message.content.some(\n                                (text: any) => {\n                                    // console.log('text.text', text.text)\n                                    return text.text?.includes(\"<FILE_MESSAGE>\")\n                                }\n                            ) : message.content.includes(\"<FILE_MESSAGE>\")\n                    }\n                    return false;\n                });\n\n                // console.log('Found fileMessage')\n                if (fileMessage) {\n                    updateMessageWithCaching(fileMessage)\n                }\n\n\n                // Find first user message\n                // const firstAssistantResponse = bodyObj.messages.find((message: any) => message.role === \"assistant\");\n                // if (firstAssistantResponse) {\n                //     updateMessageWithCaching(firstAssistantResponse);\n                // }\n                // Detect if we are tool calling loop\n\n\n\n\n                // Find the last message that happened before the current turn\n                // const lastCompleteMessageIndex = bodyObj.messages.findLastIndex((message: any) => {\n                //     return Array.isArray(message.content) && message.content.some((c: any) => c.text.includes(\"Today's date\"))\n                // })\n\n                // bodyObj['provider'] = {\n                //     'order': [\n                //         'Google',\n                //         'Amazon Bedrock',\n                //         'Anthropic',\n                //     ]\n                // };\n\n                bodyObj.messages = bodyObj.messages.map((message: any, index: number) => {\n                    if ([\"user\", \"system\", \"assistant\"].includes(message.role)) {\n                        if(message.tool_calls && message.tool_calls.length > 0) {\n                            message.content = ((message.content?.[0])?.text || message.content);\n                            if(!message.content) {\n                                message.content = ' <hidden></hidden> '\n                            }\n                        } else if (typeof message.content === \"string\") {\n                            if(!bodyObj.model.includes(\"claude-sonnet-4\")) {\n                                message.content = [\n                                    {\n                                        type: \"text\",\n                                        text: message.content || ' <hidden></hidden> '\n                                    }\n                                ]\n                            }\n\n                        }\n                    }\n                    return message;\n                })\n\n                bodyObj.messages = bodyObj.messages.map((message: any, index: number) => {\n                    if(message.role === \"tool\") {\n                        message.content = [\n                            {\n                                type: 'text',\n                                text: (message.content?.[0] as TextPart).text || message.content\n                            }\n                        ]\n                    }\n                    return message;\n                });\n\n                    // this is to ensure we don't get the empty content error\n                bodyObj.messages = bodyObj.messages.map((message: any, index: number) => {\n                    if (Array.isArray(message.content)) {\n                        if(!message.content.length) {\n                            message.content = [\n                                {\n                                    type: 'text'\n                                }\n                            ]\n                        }\n                        // message.content[0].text = 'Empty content';\n                        if(!message.content?.[0].text) {\n                            message.content = 'Empty content';\n                        }\n                    }\n                    return message;\n                })\n\n                const isToolCallingLoop = bodyObj.messages.filter((message: any) => message.role === \"tool\").length > 0;\n\n                if(isToolCallingLoop) {\n                    // We need to prioritize caching these\n                    // find second last tool call\n                    const secondLastToolCall = bodyObj.messages.filter((message: any) => message.role === \"tool\").slice(-2)[0];\n                    if (secondLastToolCall) {\n                        updateMessageWithCaching(secondLastToolCall);\n                    }\n                    const lastToolCallResponse = bodyObj.messages.findLast((message: any) => message.role === \"tool\");\n                    if (lastToolCallResponse) {\n                        updateMessageWithCaching(lastToolCallResponse);\n                    }\n                } else {\n                    // find second last user message\n                    const secondLastUserMessage = bodyObj.messages.filter((message: any) => message.role === \"user\").slice(-2)[0];\n                    if (secondLastUserMessage) {\n                        updateMessageWithCaching(secondLastUserMessage);\n                    }\n                    const lastUserMessage = bodyObj.messages.findLast((message: any) => message.role === \"user\");\n                    if (lastUserMessage) {\n                        updateMessageWithCaching(lastUserMessage);\n                    }\n                }\n\n                // if(lastCompleteMessageIndex !== -1) {\n                //     console.log(`Found message at index ${lastCompleteMessageIndex}`)\n                // Update the previous message as the one with Today's date is the latest user message\n                // updateMessageWithCaching(bodyObj.messages[lastCompleteMessageIndex]);\n                // }\n            }\n\n\n            // Add cache control to tools and function calls\n            if (bodyObj.tools && Array.isArray(bodyObj.tools)) {\n                bodyObj.tools = bodyObj.tools.map((tool: any) => {\n                    // Handle function objects\n                    if (tool.function) {\n                        // tool[\"cache_control\"] = {\n                        //     \"type\": \"ephemeral\"\n                        // };\n                        return {\n                            ...tool,\n                            function: {\n                                ...tool.function,\n                                // \"cache_control\": {\n                                //     \"type\": \"ephemeral\"\n                                // }\n                            },\n                            // \"cache_control\": {\n                            //     \"type\": \"ephemeral\"\n                            // }\n                        };\n                    }\n                    // Handle direct tool objects\n                    return {\n                        ...tool,\n                        // \"cache_control\": {\n                        //     \"type\": \"ephemeral\"\n                        // }\n                    };\n                });\n            }\n        }\n        // Only apply the message truncation logic if not using Claude Sonnet 4\n        // if (!odyObj.model.includes(\"claude-sonnet-4\")) {\n        //     let m: any[] = bodyObj.messages\n        //         // .slice(0, 18);\n        //     m.splice(20, 1)\n        //     bodyObj.messages = [...m]\n        // }\n        // await exportData(bodyObj.messages, 'request-messages')\n\n        modifiedInit.body = JSON.stringify(bodyObj);\n        // console.log('Modified Body:', JSON.stringify(bodyObj));\n\n        // // Request tracker - summarize the request body\n        if (bodyObj.messages && Array.isArray(bodyObj.messages)) {\n            const messageCount = bodyObj.messages.length;\n            const roleCounts: Record<string, number> = {};\n\n            // console.log('\\n=== Request Summary ===');\n            // console.log(`Model: ${bodyObj.model}`);\n            // console.log(`Total Messages: ${messageCount}`);\n\n            let totalTokens = 0;\n\n            // Track message details\n            bodyObj.messages.forEach((message: any, index: number) => {\n                // Count roles\n                roleCounts[message.role] = (roleCounts[message.role] || 0) + 1;\n\n                // Extract content for summary\n                let contentSummary = '';\n                let hasCaching = false;\n\n                if (typeof message.content === 'string') {\n                    contentSummary = message.content.substring(0, 500) + (message.content.length > 500 ? '...' : '');\n                } else if (Array.isArray(message.content)) {\n                    const firstContent = message.content[0];\n                    if (firstContent && firstContent.text) {\n                        contentSummary = firstContent.text.substring(0, 500) + (firstContent.text.length > 500 ? '...' : '');\n                    }\n                    // Check for caching in content array\n                    hasCaching = message.content.some((item: any) => item.cache_control && item.cache_control.type === 'ephemeral');\n                }\n\n                // Check for caching at message level\n                if (message.cache_control && message.cache_control.type === 'ephemeral') {\n                    hasCaching = true;\n                }\n\n                // Estimate token count (very rough estimate: ~4 chars per token)\n                let tokenEstimate = 0;\n                if (typeof message.content === 'string') {\n                    tokenEstimate = Math.ceil(message.content.length / 4);\n                } else if (Array.isArray(message.content)) {\n                    tokenEstimate = Math.ceil(message.content.reduce((sum: number, item: any) => {\n                        return sum + (item.text ? item.text.length : 0);\n                    }, 0) / 4);\n                }\n\n                totalTokens += tokenEstimate;\n                // console.log(`\\n----\\nMessage ${index + 1}:`);\n                // console.log(`  Role: ${message.role}`);\n                // console.log(`  Content: ${contentSummary}`);\n                // console.log(`  Caching: ${hasCaching ? 'Enabled' : 'Not enabled'}`);\n                // console.log(`  Est. Tokens: ~${tokenEstimate}\\n----\\n`);\n\n                // Log tool calls if present\n                if (message.tool_calls && message.tool_calls.length > 0) {\n                    // console.log(`  Tool Calls: ${message.tool_calls.length}`);\n                }\n            });\n\n            // Summary of role distribution\n            // console.log('\\nTotal Tokens:', totalTokens);\n            // console.log('\\nRole Distribution:');\n            // Object.entries(roleCounts).forEach(([role, count]) => {\n            //     console.log(`  ${role}: ${count}`);\n            // });\n\n            // console.log('=== End Request Summary ===\\n');\n        }\n\n\n\n    }\n\n    // Make the actual request\n    const response = await fetch(input, modifiedInit);\n\n    // Clone the response to read it twice\n    // const responseClone = response.clone();\n\n    // Log the entire stream\n    // if (responseClone.body) {\n    //     const reader = responseClone.body.getReader();\n    //     console.log('\\n=== Start of Stream ===');\n    //\n    //     try {\n    //         while (true) {\n    //             const { value, done } = await reader.read();\n    //\n    //             if (done) {\n    //                 console.log('=== End of Stream ===\\n');\n    //                 break;\n    //             }\n    //\n    //             const chunk = new TextDecoder().decode(value);\n    //             // Split by lines to handle SSE format\n    //             const lines = chunk.split('\\n');\n    //             lines.forEach(line => {\n    //                 if (line.trim()) {\n    //                     if (line.startsWith('data: ')) {\n    //                         try {\n    //                             // Try to parse as JSON if possible\n    //                             const jsonData = JSON.parse(line.slice(6));\n    //                             console.log('Parsed JSON:', JSON.stringify(jsonData, null, 2));\n    //                         } catch {\n    //                             // If not JSON, log as is\n    //                             console.log('Raw data:', line.slice(6));\n    //                         }\n    //                     } else {\n    //                         console.log('Non-data line:', line);\n    //                     }\n    //                 }\n    //             });\n    //         }\n    //     } catch (error) {\n    //         console.error('Error reading stream:', error);\n    //     }\n    // }\n\n    return response;\n};", "startLine": 37, "endLine": 407, "type": "util", "symbols": ["customFetch"], "score": 1, "context": "This function custom<PERSON><PERSON><PERSON> modifies the request body for AI streaming, handles message caching, merges messages for specific models, and performs the actual fetch call to the AI provider. It is central to how AI streaming and response generation works from user input to AI response.", "includesImports": false}, {"filePath": "src/lib/services/stream-service.ts", "content": "export class StreamService {\n    private config: StreamServiceConfig;\n    private toolCallCosts: ToolCallCost[] = [];\n\n    constructor(config: StreamServiceConfig) {\n        this.config = config;\n    }\n\n    /**\n     * Start the text streaming process\n     * @returns The result of the streamText call\n     */\n    public startStream() {\n        const {\n            messages,\n            parser,\n            diffParser,\n            dataStream,\n            userMessageId,\n            abortSignal,\n            agentModeEnabled = false,\n            isFirstMessage = false,\n            onFinish,\n            tools = {},\n            temperature = 0,\n            maxSteps = 10,\n            generateMessageId = () => crypto.randomUUID(),\n            isSupabaseConnected = false,\n            enabledTools = [],\n            enableValidation,\n            transformChunking = \"line\",\n            modelId,\n            isConnectingSupabase\n        } = this.config;\n        let streamModelId = modelId\n        if(!modelId) {\n            streamModelId = \"anthropic/claude-sonnet-4\"\n            if (!isFirstMessage) {\n                if(agentModeEnabled) {\n                    // streamModelId = 'anthropic/claude-3.7-sonnet';\n                    streamModelId = 'anthropic/claude-sonnet-4';\n                    // streamModelId = 'openai/gpt-4.1';\n                } else {\n                    streamModelId = \"anthropic/claude-sonnet-4\"\n                }\n            }\n            if(isConnectingSupabase) {\n                streamModelId = \"anthropic/claude-sonnet-4\"\n            }\n        }\n\n        return streamText({\n            model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),\n            maxSteps,\n            experimental_activeTools: enabledTools,\n            experimental_toolCallStreaming: true,\n            toolCallStreaming: true,\n            temperature,\n            messages: messages.filter(m => !!m),\n            experimental_transform: smoothStream({chunking: transformChunking}),\n            maxRetries: 3,\n            abortSignal,\n            experimental_continueSteps: true,\n            onError: (error) => {\n                console.log('Error', error);\n                throw error;\n            },\n            onStepFinish: async (stepResult) => {\n                // console.log('stepResult', stepResult)\n                const body = await fetchFromOpenRouter(stepResult.response.id);\n                // console.log('Step result body', body)\n                \n                // Track tool call cost if data is available\n                if (body && stepResult.response.id) {\n                    const toolCallCost: ToolCallCost = {\n                        id: stepResult.response.id,\n                        model: body.data.model || streamModelId,\n                        tokens_prompt: body.data.tokens_prompt || 0,\n                        tokens_completion: body.data.tokens_completion || 0,\n                        native_tokens_prompt: body.data.native_tokens_prompt,\n                        native_tokens_completion: body.data.native_tokens_completion,\n                        total_cost: body.data.total_cost || 0,\n                        cache_discount: body.data.cache_discount,\n                        provider_name: body.data.provider_name,\n                        timestamp: new Date()\n                    };\n                    console.log('toolCallCost', toolCallCost)\n                    this.toolCallCosts.push(toolCallCost);\n                }\n            },\n            onChunk: ({chunk}) => {\n                if (chunk.type === \"text-delta\") {\n                    try {\n                        // First run the file parser\n                        const afterFileParser = parser.parse(userMessageId, chunk.textDelta);\n\n                        // Then run the diff parser on the output of the file parser\n                        diffParser.parse(userMessageId, afterFileParser);\n                    } catch (e) {\n                        console.error('Error in chunk processing', e);\n                    }\n                } else if (chunk.type === \"tool-call\") {\n                    // Send tool call information to the client\n                    dataStream.writeData({\n                        type: 'tool-call',\n                        content: {\n                            toolCallId: chunk.toolCallId,\n                            toolName: chunk.toolName,\n                            args: chunk.args\n                        }\n                    });\n                } else if (chunk.type === \"tool-result\") {\n                    // Send tool result information to the client\n                    dataStream.writeData({\n                        type: 'tool-result',\n                        content: {\n                            toolCallId: chunk.toolCallId,\n                            toolName: chunk.toolName,\n                            result: chunk.result\n                        }\n                    });\n                }\n            },\n            experimental_generateMessageId: generateMessageId,\n            experimental_repairToolCall: async ({\n                                                    toolCall,\n                                                    tools,\n                                                    error,\n                                                    messages,\n                                                    system,\n                                                }) => {\n                console.log('In repair', error);\n                const result = await generateText({\n                    model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),\n                    system,\n                    messages: [\n                        ...messages,\n                        {\n                            role: 'assistant',\n                            content: [\n                                {\n                                    type: 'tool-call',\n                                    toolCallId: toolCall.toolCallId,\n                                    toolName: toolCall.toolName,\n                                    args: toolCall.args,\n                                },\n                            ],\n                        },\n                        {\n                            role: 'tool' as const,\n                            content: [\n                                {\n                                    type: 'tool-result',\n                                    toolCallId: toolCall.toolCallId,\n                                    toolName: toolCall.toolName,\n                                    result: error.message,\n                                },\n                            ],\n                        },\n                    ],\n                    tools,\n                });\n\n                const newToolCall = result.toolCalls.find(\n                    newToolCall => newToolCall.toolName === toolCall.toolName,\n                );\n\n                return newToolCall != null\n                    ? {\n                        toolCallType: 'function' as const,\n                        toolCallId: toolCall.toolCallId,\n                        toolName: toolCall.toolName,\n                        args: JSON.stringify(newToolCall.args),\n                    }\n                    : null;\n            },\n            tools,\n            onFinish: async (params) => {\n                if (onFinish) {\n                    await onFinish({...params, enableValidation});\n                }\n            },\n        });\n    }\n\n    /**\n     * Sanitize response messages by removing incomplete tool calls\n     * @param messages The messages to sanitize\n     * @returns Sanitized messages\n     */\n    public static sanitizeResponseMessages(messages: AIMessage[]): AIMessage[] {\n        return messages.filter(message => {\n            // Filter out messages that are just incomplete tool calls\n            if (message.role === 'assistant' && Array.isArray(message.content)) {\n                const hasOnlyToolCalls = message.content.every(\n                    part => part.type === 'tool-call'\n                );\n\n                if (hasOnlyToolCalls) {\n                    return false;\n                }\n            }\n            return true;\n        });\n    }\n\n    /**\n     * Get the aggregated cost and token usage for all tool calls\n     * @returns Summary of token usage and costs\n     */\n    public getToolCallCostSummary() {\n        if (this.toolCallCosts.length === 0) {\n            return {\n                count: 0,\n                total_tokens_prompt: 0,\n                total_tokens_completion: 0,\n                total_cost: 0,\n                total_cache_discount: 0,\n                net_cost: 0,\n                tool_calls: []\n            };\n        }\n        \n        const total_tokens_prompt = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_prompt || 0), 0);\n        const total_tokens_completion = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_completion || 0), 0);\n        const total_cost = this.toolCallCosts.reduce((sum, call) => sum + (call.total_cost || 0), 0);\n        const total_cache_discount = this.toolCallCosts.reduce((sum, call) => sum + (call.cache_discount || 0), 0);\n        \n        return {\n            count: this.toolCallCosts.length,\n            total_tokens_prompt: round(total_tokens_prompt, 0),\n            total_tokens_completion: round(total_tokens_completion, 0),\n            total_tokens: round(total_tokens_prompt + total_tokens_completion, 0),\n            total_cost: round(total_cost, 6),\n            total_cache_discount: round(total_cache_discount, 6),\n            net_cost: round(total_cost + total_cache_discount, 6),\n            tool_calls: this.toolCallCosts\n        };\n    }\n    \n    /**\n     * Get a simple formatted string representation of the tool call costs\n     * @returns Formatted string with cost summary\n     */\n    public getToolCallCostReport(): string {\n        const summary = this.getToolCallCostSummary();\n        \n        if (summary.count === 0) {\n            return \"No tool calls recorded.\";\n        }\n        \n        return `Tool Call Summary:\n- Total Calls: ${summary.count}\n- Prompt Tokens: ${summary.total_tokens_prompt}\n- Completion Tokens: ${summary.total_tokens_completion}\n- Total Tokens: ${summary.total_tokens}\n- Total Cost: $${summary.total_cost}\n- Cache Discount: $${summary.total_cache_discount}\n- Net Cost: $${summary.net_cost}`;\n    }\n    \n    /**\n     * Create a new instance with updated configuration\n     * @param updatedConfig The updated configuration\n     * @returns A new StreamService instance\n     */\n    public withConfig(updatedConfig: Partial<StreamServiceConfig>): StreamService {\n        return new StreamService({\n            ...this.config,\n            ...updatedConfig,\n        });\n    }\n}", "startLine": 110, "endLine": 382, "type": "unknown", "symbols": ["StreamService"], "score": 1, "context": "This class StreamService manages the AI text streaming process, including starting the stream with streamText, handling chunks, tool calls, errors, and finishing. It encapsulates the flow from user messages to streaming AI responses.", "includesImports": false}, {"filePath": "src/lib/chat/handlers/message.handler.ts", "content": "export class MessageHandler {\n    private messagesWithTurns: Array<Message> = [];\n    private coreMessages: CoreMessage[] = [];\n    private userMessage: CoreMessage | null = null;\n    private filteredMessages: CoreMessage[] = [];\n    private systemPrompts: CoreSystemMessage[] = []\n    private fileMessage: CoreMessage | null = null;\n    private componentContexts: {componentName: string;\n    element: string;\n    sourceFile: string;\n    lineNumber: number;\n    imageUrl?: string;}[] = []\n    private extractor: Extractor = new Extractor();\n\n    /**\n     * Initialize the handler with messages\n     * This sets up the internal state for processing\n     */\n    public async initialize(messages: Array<Message>, minMessages: number = 5, options: {\n        projectId?: string,\n        backendEnabled?: boolean,\n        componentContexts?: ComponentContext[],\n        isFirstUserMessage?: boolean,\n        agentModeEnabled?: boolean,\n        userId: string,\n        isDiscussion?: boolean,\n        discussionType?: 'error-fix' | 'code-review' | 'general-discussion'}): Promise<void> {\n        let system;\n        const matchedFlagPayload = await posthogClient.getFeatureFlagPayload('design-preview-feature', options.userId)\n\n        // Handle discussion mode first (highest priority)\n        if (options?.isDiscussion) {\n            switch (options.discussionType) {\n                case 'error-fix':\n                    system = DISCUSS_MODE_ERROR_FIX_PROMPT;\n                    break;\n                case 'code-review':\n                    system = DISCUSS_MODE_CODE_REVIEW_PROMPT;\n                    break;\n                default:\n                    system = DISCUSS_MODE_PROMPT;\n                    break;\n            }\n        } else if (options?.isFirstUserMessage) {\n            system = STREAMLINED_V1_IMPLEMENTATION_PROMPT;\n        } else {\n            if (options?.agentModeEnabled) {\n                system = STREAMLINED_AGENT_PROMPT\n            } else {\n                system = systemPrompt;\n            }\n        }\n\n            // .replace(/{{BACKEND_PROMPT}}/g, options.backendEnabled ? backendPrompt : \"There is no backend configured. NO MATTER WHAT, don't write backend code even if the user asks for it.\");\n\n        if(options.backendEnabled) {\n            system = system.replace(/{{BACKEND_URL}}/g, `https://${process.env.VERCEL_URL || process.env.BACKEND_BASE_URL}/api/dynamic-backend/${options.projectId}/latest`);\n        }\n\n        // console.log('Replacing backend prompt, enabled:', options.backendEnabled)\n\n        this.systemPrompts = [\n            {\n                role: 'system',\n                content: system\n            },\n    //         {\n    //             role: 'system',\n    //             content: ` <extended_system_prompt>\n    //     <title>From the system to you:</title>\n    //     <extended_system_prompt_reminder>Remember, you are building a mobile app and the user needs to get a wow factor. Use spacing as a design element and keep it minimal and clean.</extended_system_prompt_reminder>\n    //     <reminders>\n    //         <reminder id=\"1\">Read the system prompt very carefully.</reminder>\n    //         <reminder id=\"2\">[ULTRA IMPORTANT]: Always follow the MO_FILE response formats. Use MO_DIFF whenever you can to save costs. </reminder>\n    //         <reminder id=\"3\">[ULTRA IMPORTANT]: NEVER change the existing design/layout/functionality of a screen when making edits unless explicitly specified.</reminder>\n    //         <reminder id=\"4\">[ULTRA IMPORTANT]: Return complete implementations of every file, do not leave it for the user to implement. The user does not have access to the code.</reminder>\n    //         <reminder id=\"5\">ALWAYS adhere to the \"NEVER ASSUME THAT THE USER WILL MAKE CHANGES TO A FILE\" section no matter what</reminder>\n    //         <reminder id=\"6\">ALWAYS provide complete implementations and never partial updates to files</reminder>\n    //\n    // </reminders>\n    // </extended_system_prompt>`\n    //         }\n        ]\n        this.messagesWithTurns = this.getMessagesWithCompleteTurns(messages, minMessages);\n        this.coreMessages = convertToCoreMessages(this.messagesWithTurns);\n        this.userMessage = this.getMostRecentUserMessage(this.coreMessages);\n        this.filteredMessages = this.filterCoreMessages(this.coreMessages);\n        if(options.componentContexts) {\n            this.componentContexts = options.componentContexts;\n        }\n        this.appendMessageCountSystemMessage();\n    }\n\n\n    appendToSystemPrompt(content: string) {\n        this.systemPrompts.push({\n            role: 'system',\n            content: content\n        })\n    }\n\n    private appendMessageCountSystemMessage() {\n        // Count how many user messages we've had\n        const userMessageCount = this.messagesWithTurns.filter(m => m.role === 'assistant').length;\n\n        // if (userMessageCount <= 3) {\n        //     this.systemPrompts.push({\n        //         role: \"system\",\n        //         content: `${onboardingPrompt}\n        //     <CurrentPhase>Message: ${userMessageCount}</CurrentPhase>`\n        //     })\n        // }\n    }\n\n    /**\n     * Replace MO_FILE and MO_DIFF tags with a summary message\n     * @param text The text to process\n     * @returns The text with tags replaced\n     */\n    public replaceMoFileTags(text: string) {\n        // Create proper RegExp objects with correct character classes to match any character including newlines\n        const mofileregex = new RegExp(`<MO_FILE\\\\s+path=\"([^\"]+)\"[^>]*?>([\\\\s\\\\S]*?)</MO_FILE>`, 'g');\n        const modiffregex = new RegExp(`<MO_DIFF\\\\s+path=\"([^\"]+)\"[^>]*?>([\\\\s\\\\S]*?)</MO_DIFF>`, 'g');\n\n        // For MO_FILE tags, extract the path and replace with a summary\n        const fileReplacementContent = (match: string, path: string, content: string) => {\n            // Create a summary of what was changed\n            return `[Created/Modified file: ${path}]`;\n        };\n\n        // For MO_DIFF tags, extract the path and replace with a summary\n        const diffReplacementContent = (match: string, path: string, content: string) => {\n            // Create a summary of what was changed\n            return `[Modified file: ${path}]`;\n        };\n\n        // Replace both MO_FILE and MO_DIFF tags with summaries\n        let replaced = text.replace(mofileregex, fileReplacementContent);\n        return replaced.replace(modiffregex, diffReplacementContent);\n    }\n\n    private removeCode(message: CoreMessage) {\n        if (typeof message.content === \"string\") {\n            message.content = this.replaceMoFileTags(message.content);\n        } else {\n            message.content = message.content.map(cont => {\n                if (cont.type === \"text\") {\n                    cont.text = this.replaceMoFileTags(cont.text);\n                }\n                return cont;\n            }) as any\n        }\n        return message;\n    }\n    \n    /**\n     * Truncate tool message content to save context window space\n     * @param message The tool message to truncate\n     * @param maxLength Maximum allowed content length (default: 3000)\n     * @returns The message with truncated content\n     */\n    private truncateToolContent(message: CoreMessage, maxLength: number = 3000): CoreMessage {\n        if (!message) return message;\n        \n        if (typeof message.content === \"string\") {\n            if (message.content.length > maxLength) {\n                message.content = message.content.slice(0, maxLength) + \n                    '\\n// Tool response truncated to save context window size. Focus on the available information.';\n            }\n        } else if (Array.isArray(message.content)) {\n            message.content = message.content.map(cont => {\n                // Handle text type content\n                if (cont.type === \"text\" && cont.text.length > maxLength) {\n                    return {\n                        ...cont,\n                        text: cont.text.slice(0, maxLength) + \n                            '\\n// Tool response truncated to save context window size. Focus on the available information.'\n                    };\n                }\n                \n                // Handle tool-result type content\n                if (cont.type === \"tool-result\" && cont.result && typeof cont.result === 'string' && cont.result.length > maxLength) {\n                    return {\n                        ...cont,\n                        result: cont.result.slice(0, maxLength) + \n                            '\\n// Tool response truncated to save context window size. Focus on the available information.'\n                    };\n                }\n                \n                return cont;\n            }) as any;\n        }\n        \n        return message;\n    }\n\n\n    public getMessagesForRequest({agentModeEnabled, applyCaching}: { agentModeEnabled: boolean, applyCaching?: boolean }) {\n        let messages: CoreMessage[] = [\n            ...this.systemPrompts\n        ]\n\n        if (this.fileMessage) {\n            messages.push(this.fileMessage)\n        }\n\n        const latestUserMessageIndex = this.getMostRecentUserMessageIndex(this.filteredMessages);\n        const clonedFilteredMessages = [...this.filteredMessages.toSpliced(latestUserMessageIndex, 1)];\n        if (latestUserMessageIndex !== -1 && this.userMessage) {\n            clonedFilteredMessages[latestUserMessageIndex] = this.userMessage;\n        }\n\n        messages = messages.concat(clonedFilteredMessages)\n        if(applyCaching) {\n            messages = this.applyCaching(messages);\n        }\n        messages = messages.filter(m => !!m);\n\n        if (agentModeEnabled) {\n            // In agent mode, remove MO tags from all messages\n            messages = messages.map(message => {\n                if(message.role === \"assistant\") {\n                    message = this.removeCode(message);\n                }\n                if(message.role === \"tool\") {\n                    message = this.truncateToolContent(message);\n                }\n                return message;\n            });\n        } else {\n            // Find all assistant messages with MO_FILE tags\n            const assistantMessagesWithMOFILE = messages.map((message, index) => {\n                const content: string = (message.content?.[0] as TextPart)?.text || message.content as string;\n                return {\n                    index,\n                    hasMOFILE: message.role === \"assistant\" && content.includes(\"MO_FILE\")\n                };\n            }).filter(item => item.hasMOFILE);\n\n            // Keep the last 2 messages with MO_FILE tags intact\n            const keepLastN = 2;\n            const messagesToKeep = assistantMessagesWithMOFILE.slice(-keepLastN).map(item => item.index);\n\n            messages = messages.map((message, index) => {\n                return messagesToKeep.includes(index) ? message : this.removeCode(message);\n            });\n        }\n\n        return messages\n    }\n\n    private applyCaching(messages: CoreMessage[]) {\n        const lastSystemMessageIndex = messages.findIndex(m => m.role === 'system');\n        if (lastSystemMessageIndex !== -1) {\n            console.log('Adding cache to last system message')\n            messages[lastSystemMessageIndex] = this.appendCacheTag(messages[lastSystemMessageIndex]);\n        }\n\n        // const fileMessageIndex = messages.findIndex((message: any) => {\n        //     if (message.role === \"system\") {\n        //         return Array.isArray(message.content) ?\n        //             message.content.some(\n        //                 (text: any) => {\n        //                     return text.text?.includes(\"<FILE_MESSAGE>\")\n        //                 }\n        //             ) : message.content.includes(\"<FILE_MESSAGE>\")\n        //     }\n        //     return false;\n        // });\n        // if (fileMessageIndex !== -1) {\n        //     console.log('Adding cache to file message')\n        //     messages[fileMessageIndex] = this.appendCacheTag(messages[fileMessageIndex]);\n        // }\n\n        // Find first user message\n        const firstAssistantResponseIndex = messages.findIndex((message: any) => message?.role === \"assistant\");\n        if (firstAssistantResponseIndex !== -1) {\n            console.log('Adding cache first assistant response')\n            messages[firstAssistantResponseIndex] = this.appendCacheTag(messages[firstAssistantResponseIndex]);\n        }\n\n        const lastAssistantResponseIndex = messages.findLastIndex((message: any) => message?.role === \"assistant\");\n        if (lastAssistantResponseIndex !== -1) {\n            console.log('Adding cache to last assistant response')\n            messages[lastAssistantResponseIndex] = this.appendCacheTag(messages[lastAssistantResponseIndex]);\n        }\n        return messages;\n    }\n\n    private appendCacheTag(message: CoreMessage) {\n        if (typeof message.content === \"string\") {\n            message =  {\n                ...message,\n                providerOptions: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n                experimental_providerMetadata: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n                // @ts-ignore\n                providerMetadata: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n            }\n        } else {\n            message.content[message.content.length - 1] = {\n                ...message.content[message.content.length - 1],\n                // @ts-ignore\n                providerOptions: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n                experimental_providerMetadata: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n                // @ts-ignore\n                providerMetadata: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n            }\n        }\n\n        return message;\n    }\n\n    /**\n     * Get the current user message\n     */\n    public getCurrentUserMessage(): CoreMessage | null {\n        return this.userMessage;\n    }\n\n    public getCurrentUserMessageForUI() {\n        const messages =  convertToUIMessages([this.userMessage || {} as any])\n        return messages[0];\n    }\n\n    /**\n     * Set the current user message\n     * Useful when the message has been enhanced with additional context\n     */\n    public setCurrentUserMessage(message: CoreMessage): void {\n        this.userMessage = message;\n    }\n\n    /**\n     * Get messages with complete conversation turns\n     * Ensures we have complete context by including the most recent messages\n     * plus any older messages that form complete conversation turns\n     */\n    public getMessagesWithCompleteTurns(messages: Array<Message>, minMessages: number = 10): Array<Message> {\n        if (messages.length <= minMessages) {\n            return messages;\n        }\n\n        const recentMessages = messages.slice(-minMessages);\n        const remainingMessages = messages.slice(0, -minMessages);\n        const oldestUserMessageIndex = remainingMessages.map(m => m.role).lastIndexOf('user');\n\n        if (oldestUserMessageIndex === -1) {\n            return recentMessages;\n        }\n\n        const additionalMessages = remainingMessages.slice(oldestUserMessageIndex);\n        return [...additionalMessages, ...recentMessages];\n    }\n\n    /**\n     * Get the most recent user message from a list of messages\n     */\n    public getMostRecentUserMessage(messages: CoreMessage[]): CoreMessage | null {\n        for (let i = messages.length - 1; i >= 0; i--) {\n            if (messages[i].role === 'user') {\n                return messages[i];\n            }\n        }\n        return null;\n    }\n\n    public getMostRecentUserMessageIndex(messages: CoreMessage[]): number {\n        for (let i = messages.length - 1; i >= 0; i--) {\n            if (messages[i].role === 'user') {\n                return i;\n            }\n        }\n        return -1;\n    }\n\n    /**\n     * Filter core messages to optimize context window usage\n     * - Keeps all user messages\n     * - Keeps assistant messages without tool calls\n     * - For assistant messages with tool calls, keeps the last 4 and removes tool-call content\n     * - Removes all tool messages\n     */\n    public filterCoreMessages(messages: CoreMessage[]): CoreMessage[] {\n        // Find last 4 assistant messages that have tool-call content (increased from 2)\n        const findSecondLastUserMessageIndexArray: number[] = messages\n            .map((message, index) => message.role === \"user\" ? index : undefined)\n            .filter(m => typeof m !== \"undefined\");\n        const secondLastUserMessageIndex = findSecondLastUserMessageIndexArray.slice(-3)[0];\n        const assistantMessagesWithTools = messages\n            .filter((msg, index) => msg.role === 'assistant' &&\n                index > secondLastUserMessageIndex)\n             // Increased from 2 to 4 to provide more context\n\n        const toolCallIDs = assistantMessagesWithTools.reduce((acc, message) => {\n            if (Array.isArray(message.content)) {\n                const ids = message.content.map(cont => {\n                    return cont.type === \"tool-call\" ? cont.toolCallId : null;\n                }).filter(id => !!id);\n                acc = acc.concat(ids);\n            }\n            return acc;\n        }, [] as string[])\n\n\n        return messages.filter(msg => {\n            // Keep user messages\n            if (msg.role === 'user') return true;\n\n            // For assistant messages\n            if (msg.role === 'assistant') {\n                // If it has tool calls, only keep last 2 and remove tool-call content\n                if (Array.isArray(msg.content) && msg.content.some(c => c.type === 'tool-call')) {\n                    if (assistantMessagesWithTools.includes(msg)) {\n                        // Keep only text content\n                        // msg.content = msg.content.filter(c => c.type === 'text');\n                        return true;\n                    }\n                    return false;\n                }\n                return true; // Keep assistant messages without tool calls\n            }\n\n            // Remove tool messages not in the whitelisted ids\n            if (msg.role === 'tool') {\n                const allowed = Array.isArray(msg.content) && msg.content.some(cont => toolCallIDs.includes(cont.toolCallId));\n                return allowed;\n            }\n\n            return false;\n        });\n    }\n\n    /**\n     * Prepare user message for LLM processing\n     * - Handles both array and string content formats\n     * - Ensures image parts have correct type information\n     */\n    public prepareUserMessage(userMessage: CoreMessage): CoreMessage {\n        if (!Array.isArray(userMessage.content)) {\n            return userMessage;\n        }\n\n        // Process array content\n        const processedContent = userMessage.content.map(content => {\n            if (content.type === \"image\") {\n                return {\n                    type: 'image',\n                    mimeType: \"image/png\",\n                    image: content.image\n                } as ImagePart;\n            }\n            return content;\n        }) as UserContent;\n\n        return {\n            ...userMessage,\n            content: processedContent\n        } as CoreMessage;\n    }\n\n    /**\n     * Extracts import statements from message content\n     * Useful for analyzing code snippets and understanding dependencies\n     * @param content Array of messages to analyze\n     * @returns Array of extracted import statements\n     */\n    public extractImportsFromContent(content: string): string[] {\n        const importStatements: string[] = [];\n        const importRegex = /import\\s+(?:{[^}]*}|\\*\\s+as\\s+[^;]+|[^;{]*)\\s+from\\s+['\"][^'\"]+['\"];?|import\\s+['\"][^'\"]+['\"];?/g;\n\n        const matches = content.match(importRegex)\n        if (matches) {\n            importStatements.push(...matches);\n        }\n\n        // Remove duplicates and return\n        return [...new Set(importStatements)];\n    }\n\n    /**\n     * Append additional context to user message\n     * This can be used to add system instructions or other context\n     */\n    public appendToUserMessage(userMessage: CoreMessage, additionalContext: string): CoreMessage {\n        if (Array.isArray(userMessage.content)) {\n            return {\n                ...userMessage,\n                content: userMessage.content.map(content => {\n                    if (content.type === \"text\") {\n                        return {\n                            ...content,\n                            text: content.text + additionalContext\n                        } as TextPart;\n                    }\n                    return content;\n                }) as UserContent\n            } as CoreMessage;\n        } else {\n            return {\n                ...userMessage,\n                content: userMessage.content + additionalContext\n            } as CoreMessage;\n        }\n    }\n\n    /**\n     * Create a file message from the provided files\n     * This formats the files in a way that can be included in the message context\n     */\n    public createFileMessage(files: any[], fileManager: FileLineManager, useCache: boolean = false, agentModeEnabled = false) {\n        const textContent = `<FILE_MESSAGE>\nYou have access to only these files and you cannot create any assets like mp3 or fonts or lottie. Remember, the project is running in expo snack.\nFiles like app.json, package.json, babel.config, metro.config or any other configuration files are automatically taken care of.\n\nGiven the following files in a Expo React Native project:\n\n${files.map((file) => {\n    const fileCount = this.extractor.getFileLineCount(file.content);\n    const warnings: string[] = [];\n\n    if(fileCount.warning) {\n        warnings.push(fileCount.warning);\n    }\n                        return `\n\n---- File: ------------\nPath: ${file.name}\nFileType: ${this.extractor.getFileType(file.name)}\nNumber of lines: ${fileCount.count}\nWarnings to solve: ${warnings.join(',')}\nFile Contents\n---------\n    ${\n       agentModeEnabled ?\n       this.extractor.extractMinimalFileStructure(file.content) :\n       file.content\n    }\n    `;\n                    }).join('')}\n${agentModeEnabled ? this.extractor.generateMinimalDependencyGraph(files) : ''}\nAnswer the user's question only to be able write code and nothing else.\n                    `;\n\n        const messageContent: TextPart = {\n            type: \"text\",\n            text: textContent\n        };\n\n        // // Only add cache_control if this is a base version\n        // if (useCache) {\n        //     messageContent.cache_control = { type: \"ephemeral\" };\n        // }\n\n        this.fileMessage = {\n            role: \"user\",\n            content: textContent\n        };\n    }\n\n    /**\n     * Enhance user message with additional context\n     * - Adds Supabase prompt if available\n     * - Can be extended to add other context as needed\n     */\n    public enhanceUserMessage(supabasePrompt?: string) {\n        if (!supabasePrompt || !this.userMessage) {\n            return;\n        }\n        this.userMessage = this.appendToUserMessage(this.userMessage, `\\n${supabasePrompt}`);\n\n    }\n\n    /**\n     * Create a message object ready for saving to the database\n     * - Formats the message with all required fields\n     * - Handles proper processing of content\n     */\n    public createMessageForSaving(\n        message: CoreMessage,\n        messageId: string,\n        chatId: string,\n        userId: string,\n        autoFixed: boolean,\n    ): any {\n        return {\n            ...this.prepareUserMessage(message),\n            id: messageId,\n            createdAt: new Date(),\n            chatId: chatId,\n            userId: userId,\n            componentContexts: this.componentContexts,\n            autoFixed,\n            hidden: autoFixed\n        };\n    }\n}", "startLine": 34, "endLine": 665, "type": "unknown", "symbols": ["MessageHandler"], "score": 0.9, "context": "This class MessageHandler prepares and manages messages for AI processing, including system prompts, user messages, filtering, caching, and message transformations. It is part of the flow from user input to AI response generation.", "includesImports": false}, {"filePath": "src/lib/chat/tools/query-codebase.ts", "content": "export const queryCodebase = ({\n                                  projectId,\n                                  files,\n                                  fileManager,\n                                  messageId = '',\n                                  userMessage = ''\n                              }: { projectId: string, files: FileItem[], fileManager: FileLineManager, messageId?: string, userMessage?: string }) => {\n    return tool({\n        description: 'Query the codebase to understand its structure, relationships, and relevant files for a specific task. ' +\n            'Use this instead of requesting individual files. ' +\n            'You can exclude specific files from the analysis by providing an array of file paths in the excludedFiles parameter. ' +\n            'Use filePath parameter to retrieve a SINGLE file in full ONLY when absolutely necessary. You are LIMITED to ONE file per request.',\n        parameters: QueryCodebaseSchema,\n        execute: async ({query, excludedFiles, reason}: z.infer<typeof QueryCodebaseSchema>) => {\n            console.log('Query:', query)\n            console.log('Reason:', reason)\n            console.log('Excluded files:', excludedFiles?.join())\n\n            if(!query) {\n                return {\n                    result: null,\n                    message: \"One of query or filePath is required. To look at the holistic view, use query and to look at a specific file, use filePath.\"\n                }\n            }\n            // Check if this is a \"continue\" message\n            const isContinueMessage = userMessage.toLowerCase().trim().startsWith('continue');\n\n            // Get the tool tracker instance\n            const toolTracker = ToolCountTracker.getInstance();\n\n            console.log('Tool call allowed (queryCodebase):', toolTracker.shouldAllowToolCall(messageId, 'queryCodebase'))\n\n            // Check if we should allow this tool call\n            if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'queryCodebase')) {\n                return {\n                    result: null,\n                    message: \"⚠️ Tool call limit reached. You've already made multiple codebase queries. Please implement or summarize with the information you already have.\",\n                };\n            }\n\n            // Increment the tool count if we have a chat ID\n            if (messageId) {\n                toolTracker.incrementToolCount(messageId, 'queryCodebase');\n            }\n            try {\n                if (!files || files.length === 0) {\n                    throw new Error('No files available for context engine');\n                }\n\n                // Get project information if available\n                let project: Project | null = null;\n                if (projectId) {\n                    try {\n                        project = await getProjectById({id: projectId});\n                    } catch (error) {\n                        console.warn('Could not fetch project information:', error);\n                        // Continue without project info\n                    }\n                }\n\n                if (!project) {\n                    throw new Error('No project information available');\n                }\n\n                // Check if we're requesting a specific file in full\n                // Note: filePath takes precedence over semantic query when both are provided\n                // if (filePath) {\n                //     console.log('Specific file requested:', filePath);\n                //\n                //     // Find the requested file\n                //     const fileItem = fileManager.getFileItems().find(file => file.name === filePath.trim());\n                //\n                //     if (!fileItem) {\n                //         throw new Error(`The requested file was not found: ${filePath}`);\n                //     }\n                //\n                //     const fileItems = [fileItem];\n                //\n                //     // Create snippets for each file\n                //     const snippets = fileItems.map(fileItem => ({\n                //         filePath: fileItem.name,\n                //         content: fileItem.content || '',\n                //         startLine: 1,\n                //         endLine: (fileItem.content || '').split('\\n').length,\n                //         type: 'unknown',\n                //         symbols: [],\n                //         score: 1.0\n                //     }));\n                //\n                //     // Create a simplified result with just the full file contents\n                //     const result = {\n                //         query,\n                //         snippets,\n                //         codebaseStructure: { components: [], screens: [], contexts: [], hooks: [], utils: [], types: [], configs: [] },\n                //         relationships: { imports: {}, exports: {}, dependencies: {}, dependents: {} }\n                //     };\n                //\n                //     let message = `Showing full content of the requested file: ${fileItems[0].name}`;\n                //\n                //     return {\n                //         result,\n                //         message,\n                //         toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')\n                //     };\n                // }\n                \n                // Initialize the context engine with the current files and project\n                const contextEngine = new ContextEngine(fileManager.getFileItems(), project);\n\n                // Default excluded files for Expo Snack projects if not provided\n                const defaultExcludedFiles = [\n                    // Large asset files\n                    'assets/',\n                    '.mp3',\n                    '.mp4',\n                    '.jpg',\n                    '.png',\n                    '.gif',\n                    // Generated type files\n                    'types/generated',\n                    // These don't exist in Expo Snack but kept as fallbacks\n                    'node_modules',\n                    'dist'\n                ];\n\n                // Combine user-provided excludedFiles with defaults\n                const combinedExcludedFiles = excludedFiles ?\n                    [...excludedFiles, ...defaultExcludedFiles] :\n                    defaultExcludedFiles;\n\n                // Query the context engine\n                const result = await contextEngine.query(query, reason, combinedExcludedFiles);\n\n                // Prepare a more informative response\n                let message = `Found ${result.snippets?.length} snippets for your query.`;\n\n                // Add information about excluded files if any\n                if (excludedFiles && excludedFiles.length > 0) {\n                    message += `\\n${excludedFiles.length} files were excluded from the analysis.`;\n                }\n\n                if (result.actionPlan) {\n                    message += `\\n\\nAction Plan: ${result.actionPlan.summary}\\nComplexity: ${result.actionPlan.complexity}\\nSteps: ${result.actionPlan.steps.length}`;\n                }\n\n                // if (result.supabaseSchema) {\n                //     message += `\\n\\nSupabase integration detected with ${result.supabaseSchema.tables.length} tables.`;\n                // }\n\n                return {\n                    result,\n                    message,\n                    toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')\n                };\n            } catch (error) {\n                console.error('Context engine tool error:', error);\n                throw new Error(`Failed to query context engine: ${error instanceof Error ? error.message : 'Unknown error'}`);\n            }\n        },\n    });\n}", "startLine": 23, "endLine": 183, "type": "util", "symbols": ["queryCodebase"], "score": 0.6, "context": "This tool queryCodebase uses the ContextEngine to query the codebase for relevant files and snippets based on a natural language query. It is part of the AI's process to understand user input and generate relevant code responses.", "includesImports": false}], "additionalFiles": []}}