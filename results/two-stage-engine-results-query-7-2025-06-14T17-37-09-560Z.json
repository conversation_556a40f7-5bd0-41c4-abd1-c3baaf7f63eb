{"timestamp": "2025-06-14T17:37:09.560Z", "query": "Show me how the file system and project management works, including file operations and state management", "executionTime": 33867, "snippetsCount": 17, "additionalFilesCount": 0, "totalLines": 4999, "snippets": [{"filePath": "src/lib/editor/FileContentManager.ts", "type": "unknown", "context": "This class manages file content, applies diffs, tracks pending diffs, and provides file content retrieval and state management, directly related to file operations and state management.", "score": 1, "lines": 252, "startLine": 9, "endLine": 260, "symbols": ["FileContentManager"], "preview": "export class FileContentManager {\n    private contentStorage: FileContentStorage;\n    private diffService: DiffApplicationService;\n    // Track diffs by their path to avoid double-counting\n    private pendingDiffPaths: Set<string> = new Set();\n..."}, {"filePath": "src/lib/editor/FileLineManager.ts", "type": "unknown", "context": "This class manages files with line numbers, applies edits, rollbacks, tracks dirty files, and provides file content retrieval, essential for file operations and state management.", "score": 1, "lines": 288, "startLine": 19, "endLine": 306, "symbols": ["FileLineManager"], "preview": "export class FileLineManager {\n    private filesMap: Record<string, string> = {};\n    private originalFiles: Record<string, string> = {};\n    private lineShifts: Record<string, LineShift[]> = {};\n    private dirtyFiles: string[] = [];\n..."}, {"filePath": "src/stores/ProjectSessionStore.ts", "type": "unknown", "context": "This MobX store manages project session state, including file tree, active file, file operations, dependencies, and methods for loading, saving, and updating files, crucial for project and file state management.", "score": 1, "lines": 1098, "startLine": 71, "endLine": 1168, "symbols": ["ProjectSession"], "preview": "export class ProjectSession {\n    private readonly rootStore: RootStore;\n    // Core State\n    @observable id: string;\n    @observable messages: Message[] = [];\n..."}, {"filePath": "src/lib/chat/tools/edit-file.tool.ts", "type": "util", "context": "Tool function to edit files using FileLineManager and FileContentManager, applies diffs, validates edits, handles SQL migrations, and streams file operation events, directly related to file editing and state management.", "score": 1, "lines": 238, "startLine": 25, "endLine": 262, "symbols": ["editFileTool"], "preview": "export const editFileTool = ({\n                                 fileManager,\n                                 contentManager,\n                                 processImagePlaceholders,\n                                 processVideoPlaceholders,\n..."}, {"filePath": "src/lib/services/context-engine.ts", "type": "unknown", "context": "ContextEngine class manages project files, builds file index, analyzes dependencies, extracts metadata, and queries codebase context, directly related to project and file state management.", "score": 1, "lines": 585, "startLine": 106, "endLine": 690, "symbols": ["ContextEngine"], "preview": "export class ContextEngine {\n    private files: FileItem[] = [];\n    private fileIndex: Map<string, FileItem> = new Map();\n    private dependencyGraph: Map<string, Set<string>> = new Map();\n    private fileMetadata: Map<string, FileMetadata> = new Map();\n..."}, {"filePath": "src/lib/services/file-storage.ts", "type": "unknown", "context": "Provides file storage operations (save, get, clear) using localStorage, relevant for file system persistence and management.", "score": 0.9, "lines": 53, "startLine": 11, "endLine": 63, "symbols": ["fileStorage"], "preview": "export const fileStorage = {\n  saveFiles: (files: FileItem[], groupId: string) => {\n    try {\n      if (typeof window === 'undefined') return;\n      \n..."}, {"filePath": "src/lib/chat/tools/get-file-contents.ts", "type": "util", "context": "Tool function to retrieve full file content or directory listings from FileLineManager, with options for line ranges and search patterns, relevant for file content retrieval.", "score": 0.9, "lines": 235, "startLine": 7, "endLine": 241, "symbols": ["getFileContents"], "preview": "export const getFileContents = ({\n                                    fileManager,\n                                    dataStream,\n                                    creditUsageTracker\n                                }: {\n..."}, {"filePath": "src/lib/chat/tools/query-codebase.ts", "type": "util", "context": "Tool to query the codebase using ContextEngine, which manages files and project context, relevant for understanding project and file state management.", "score": 0.9, "lines": 161, "startLine": 23, "endLine": 183, "symbols": ["queryCodebase"], "preview": "export const queryCodebase = ({\n                                  projectId,\n                                  files,\n                                  fileManager,\n                                  messageId = '',\n..."}, {"filePath": "src/lib/chat/tools/create-file.tool.ts", "type": "util", "context": "Tool function to create new files using FileLineManager and stream file operation events, relevant for file creation operations.", "score": 0.8, "lines": 42, "startLine": 5, "endLine": 46, "symbols": ["createFileTool"], "preview": "export const createFileTool = ({\n                            fileManager,\n                            processImagePlaceholders,\n                            processVideoPlaceholders,\n                            dataStream\n..."}, {"filePath": "src/lib/services/two-stage-context-engine.ts", "type": "unknown", "context": "Two-stage context engine for identifying relevant files and snippets in the project, relevant for project and file state management.", "score": 0.8, "lines": 529, "startLine": 27, "endLine": 555, "symbols": ["TwoStageLLMContextEngine"], "preview": "export class TwoStageLLMContextEngine {\n  private files: FileItem[] = [];\n  private fileIndex: Map<string, FileItem> = new Map();\n\n  /**\n..."}, {"filePath": "src/lib/terminal/helpers.ts", "type": "util", "context": "Functions to prepare project files for build, upload files to sandbox, and handle terminal commands, relevant for project file operations and state management.", "score": 0.8, "lines": 514, "startLine": 275, "endLine": 788, "symbols": ["prepareProjectForBuild and related functions"], "preview": "export const PROJECT_DIR = '/project';\n\nexport const prepareProject = async (fileState: FileState, options: {\n    projectId: string,\n    title: string,\n..."}, {"filePath": "src/lib/services/supabase-context-engine.ts", "type": "unknown", "context": "Manages Supabase resource indexing and querying, relevant for project management but less directly about file system and file operations.", "score": 0.7, "lines": 469, "startLine": 22, "endLine": 490, "symbols": ["SupabaseContextEngine"], "preview": "export class SupabaseContextEngine {\n    private supabaseProvider: SupabaseIntegrationProvider;\n    private project: Project;\n    private resourceIndex: Record<string, string[]> = {};\n    private resourceDetails: Record<string, any> = {};\n..."}, {"filePath": "src/lib/db/schema.ts", "type": "type", "context": "Defines database schema for projects and related entities, relevant for understanding project data structure and management.", "score": 0.7, "lines": 69, "startLine": 17, "endLine": 85, "symbols": ["Project and related types"], "preview": "export const user = pgTable('User', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  email: varchar('email', { length: 64 }).notNull(),\n  password: varchar('password', { length: 64 }),\n  name: varchar(),\n..."}, {"filePath": "src/lib/db/project-queries.ts", "type": "util", "context": "Database queries for fetching and updating project data, relevant for project management.", "score": 0.7, "lines": 94, "startLine": 8, "endLine": 101, "symbols": ["getProjectById and related queries"], "preview": "export async function getProjectById({id}: {id: string}): Promise<Project> {\n  try {\n    const [project] = await db.select().from(projects).where(eq(projects.id, id));\n    return project as Project;\n  } catch (error) {\n..."}, {"filePath": "src/lib/chat/tools/manage-supabase-auth.ts", "type": "util", "context": "Tool to manage Supabase auth configuration, relevant for project management related to authentication but less directly about file system.", "score": 0.6, "lines": 78, "startLine": 13, "endLine": 90, "symbols": ["manageSupabaseAuth"], "preview": "export const manageSupabaseAuth = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n..."}, {"filePath": "src/lib/chat/tools/query-supabase-context.ts", "type": "util", "context": "Tool to query Supabase resources and execute SQL queries, relevant for project management but less directly about file system and file operations.", "score": 0.6, "lines": 203, "startLine": 23, "endLine": 225, "symbols": ["querySupabaseContext"], "preview": "export const querySupabaseContext = ({\n    projectId,\n    messageId = '',\n}: {\n    projectId: string, \n..."}, {"filePath": "src/lib/terminal/index.ts", "type": "util", "context": "Terminal commands to initialize APK build environment and run build workflows, related to project management but less about file system and state management.", "score": 0.5, "lines": 91, "startLine": 36, "endLine": 126, "symbols": ["initApkBuildEnvironment and runApkBuildWorkflow"], "preview": "export async function initApkBuildEnvironment(\n  session: { sandbox: Sandbox; terminalId: number | null }\n) {\n  try {\n    // Copy the unified-apk-build.js script to the project directory\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/editor/FileContentManager.ts", "content": "export class FileContentManager {\n    private contentStorage: FileContentStorage;\n    private diffService: DiffApplicationService;\n    // Track diffs by their path to avoid double-counting\n    private pendingDiffPaths: Set<string> = new Set();\n\n    constructor() {\n        this.contentStorage = new FileContentStorage();\n        this.diffService = new DiffApplicationService();\n    }\n\n    /**\n     * Set the content of a file\n     */\n    public setFileContent(path: string, content: string): void {\n        this.contentStorage.setFileContent(path, content);\n    }\n\n    /**\n     * Get the current content of a file\n     */\n    public getFileContent(path: string): string | null {\n        return this.contentStorage.getFileContent(path) || null;\n    }\n\n    /**\n     * Get all file items\n     */\n    public getFileItems(): Array<{name: string, content: string}> {\n        return this.contentStorage.getAllFilePaths().map(path => ({\n            name: path,\n            content: this.contentStorage.getFileContent(path) || ''\n        }));\n    }\n\n    /**\n     * Check if all diff operations have been completed\n     * @returns True if all started diffs have completed, false otherwise\n     */\n    public areDiffsComplete(): boolean {\n        return this.pendingDiffPaths.size === 0;\n    }\n\n    /**\n     * Wait until all diff operations have completed\n     * @param maxWaitMs Maximum time to wait in milliseconds\n     * @returns Promise that resolves when all diffs are complete or timeout is reached\n     */\n    public async waitForDiffsToComplete(maxWaitMs: number = 5000): Promise<boolean> {\n        const startTime = Date.now();\n\n        // Helper function to wait a short time\n        const wait = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\n\n        // Poll until diffs are complete or timeout\n        while (!this.areDiffsComplete()) {\n            if (Date.now() - startTime > maxWaitMs) {\n                console.warn(`[FileContentManager] Timed out waiting for diffs to complete after ${maxWaitMs}ms. Pending paths: ${Array.from(this.pendingDiffPaths).join(', ')}`);\n                return false;\n            }\n            await wait(100); // Poll every 100ms\n        }\n\n        console.log(`[FileContentManager] All diffs completed successfully.`);\n        return true;\n    }\n\n    /**\n     * Apply a diff to a file\n     * @param diff The diff metadata\n     * @param options Options for diff application\n     * @returns Result object with success flag, any error message, and optional credit usage information\n     */\n    public async applyDiff(diff: DiffMeta, options: { bestEffort?: boolean; forceAICompletion?: boolean, filePath?: string } = {}): Promise<{ success: boolean; message?: string; content?: string; usedAICorrection?: boolean; creditUsage?: any }> {\n        // Track this diff by its path\n        const diffPath = options.filePath || diff.path;\n        this.pendingDiffPaths.add(diffPath);\n\n        // Create a cleanup function to mark this diff as complete\n        const markComplete = () => {\n            this.pendingDiffPaths.delete(diffPath);\n            console.log(`[FileContentManager] Completed diff for ${diffPath}. Remaining: ${this.pendingDiffPaths.size}`);\n        };\n        const { path, searches, replacements } = diff;\n        const { bestEffort = false, forceAICompletion = false } = options;\n        // Use the provided filePath or fall back to the diff path\n        const filePath = options.filePath || path;\n\n        // Get current file content\n        const currentContent = this.getFileContent(path);\n\n        // Check for empty search string, which indicates a full file replacement\n        if (searches.length === 1 && searches[0].trim() === '') {\n            if (replacements.length !== 1) {\n                return {\n                    success: false,\n                    message: 'Full replacement requires exactly one replacement content block'\n                };\n            }\n\n            const newContent = replacements[0];\n            this.setFileContent(path, newContent);\n            // Mark this diff as complete\n            markComplete();\n            return {\n                success: true,\n                content: newContent\n            };\n        }\n\n        // Otherwise, handle search/replace pairs\n        if (!currentContent) {\n            return {\n                success: false,\n                message: `File ${path} not found. Cannot apply partial edits to a non-existent file.`\n            };\n        }\n\n        if (searches.length !== replacements.length) {\n            return {\n                success: false,\n                message: `Mismatched search/replace pairs (${searches.length} searches, ${replacements.length} replacements)`\n            };\n        }\n\n        let updatedContent = currentContent;\n        const failedSearches: number[] = [];\n        const successfulSearches: number[] = [];\n\n        // Apply each search/replace pair\n        for (let i = 0; i < searches.length; i++) {\n            const search = searches[i];\n            const replace = replacements[i];\n\n            try {\n                // Use the DiffApplicationService to apply the diff\n                const result = await this.diffService.applyDiff(updatedContent, search, replace, { forceAICompletion, filePath: filePath });\n\n                if (result.success) {\n                    // console.log(`[FileContentManager] Successfully applied search pattern #${i+1}`);\n                    updatedContent = result.content;\n                    successfulSearches.push(i + 1);\n                } else {\n                    // console.log(`[FileContentManager] Failed to apply search pattern #${i+1}: ${result.message}`);\n                    failedSearches.push(i + 1);\n                }\n            } catch (error) {\n                console.error(`[FileContentManager] Error applying search pattern #${i+1}:`, error);\n                failedSearches.push(i + 1);\n            }\n        }\n\n        // If any searches failed and we're not in best effort mode, try AI correction if not already forced\n        if (failedSearches.length > 0) {\n            if (!forceAICompletion) {\n                console.log('[FileContentManager] Attempting AI correction after standard strategies failed');\n\n                // Collect all failed search/replace pairs\n                const failedSearches: string[] = [];\n                const failedReplacements: string[] = [];\n\n                for (let i = 0; i < searches.length; i++) {\n                    if (!successfulSearches.includes(i + 1)) {\n                        failedSearches.push(searches[i]);\n                        failedReplacements.push(replacements[i]);\n                    }\n                }\n\n                // Try AI correction with all failed patterns at once\n                try {\n                    // We'll use the first failed search/replace pair as the main one\n                    // but provide all pairs to the AI strategy\n                    const result = await this.diffService.applyDiff(\n                        currentContent,\n                        failedSearches[0],\n                        failedReplacements[0],\n                        {\n                            forceAICompletion: true,\n                            allSearches: failedSearches,\n                            allReplacements: failedReplacements,\n                            filePath: filePath // Pass the full path for proper context\n                        }\n                    );\n\n                    if (result.success) {\n                        this.setFileContent(filePath, result.content);\n                        // Mark this diff as complete\n                        markComplete();\n                        return {\n                            success: true,\n                            content: result.content,\n                            message: 'Applied changes using AI correction',\n                            usedAICorrection: true,\n                            creditUsage: (result as any).creditUsage // Pass along any credit usage information\n                        };\n                    }\n                } catch (error) {\n                    console.error(`[FileContentManager] Error in AI correction:`, error);\n                }\n            }\n\n            // Mark this diff as complete even for failures\n            markComplete();\n            return {\n                success: false,\n                message: 'Failed to apply diff: ' + failedSearches.map((s, i) => `Search #${i+1}`).join(', ')\n            };\n        }\n\n        // Update the file content even if some searches failed in best effort mode\n        this.setFileContent(path, updatedContent);\n\n        // Mark this diff as complete\n        markComplete();\n\n        // Return success with a warning if some patterns failed in best effort mode\n        if (failedSearches.length > 0 && bestEffort) {\n            return {\n                success: true,\n                content: updatedContent,\n                message: `Applied ${successfulSearches.length} patterns successfully. Failed patterns: ${failedSearches.join(', ')}`\n            };\n        }\n\n        return {\n            success: true,\n            content: updatedContent,\n            usedAICorrection: forceAICompletion\n        };\n    }\n\n    /**\n     * Count the number of files in storage\n     */\n    public countFiles(): number {\n        return this.contentStorage.countFiles();\n    }\n\n    /**\n     * Get all file paths\n     */\n    public getAllFilePaths(): string[] {\n        return this.contentStorage.getAllFilePaths();\n    }\n\n    /**\n     * Clear all file contents\n     */\n    public clear(): void {\n        this.contentStorage.clear();\n    }\n}", "startLine": 9, "endLine": 260, "type": "unknown", "symbols": ["FileContentManager"], "score": 1, "context": "This class manages file content, applies diffs, tracks pending diffs, and provides file content retrieval and state management, directly related to file operations and state management.", "includesImports": false}, {"filePath": "src/lib/editor/FileLineManager.ts", "content": "export class FileLineManager {\n    private filesMap: Record<string, string> = {};\n    private originalFiles: Record<string, string> = {};\n    private lineShifts: Record<string, LineShift[]> = {};\n    private dirtyFiles: string[] = [];\n\n    /**\n     * Initialize with file tree and convert to line-numbered format\n     */\n    public initializeFiles(files: FileItem[]) {\n        files.forEach(file => {\n            // Store original content\n            this.originalFiles[file.name] = file.content;\n            // Store line-numbered content\n            this.filesMap[file.name] = this.addLineNumbers(file.content);\n            // Initialize empty line shifts for this file\n            this.lineShifts[file.name] = [];\n        });\n    }\n\n    /**\n     * Add line numbers to content\n     */\n    private addLineNumbers(content: string): string {\n        return content.split('\\n')\n            .map((line, idx) => `${String(idx + 1).padStart(3, ' ')}| ${line}`)\n            .join('\\n');\n    }\n\n    /**\n     * Get line-numbered content for a file\n     */\n    public getNumberedContent(filePath: string): string {\n        return this.filesMap[filePath] || '';\n    }\n\n    /**\n     * Remove line numbers from content\n     */\n    private removeLineNumbers(content: string): string {\n        if (!content) return '';\n        return content.split('\\n')\n            .map(line => line.replace(/^\\s*\\d+\\|\\s/, ''))\n            .join('\\n');\n    }\n\n    /**\n     * Calculate actual line number after all shifts\n     */\n    private getAdjustedLineNumber(filePath: string, originalLine: number): number {\n        let adjustedLine = originalLine;\n        const shifts = this.lineShifts[filePath] || [];\n        \n        // Apply shifts in order\n        let cumulativeShift = 0;\n        for (const shift of shifts) {\n            if (originalLine > shift.afterLine) {\n                cumulativeShift += shift.shiftAmount;\n            }\n        }\n        return adjustedLine + cumulativeShift;\n    }\n\n    /**\n     * @description Add files to the tree\n     * @param filePath\n     * @param content\n     * @private\n     */\n    addFiles(filePath: string, content: string) {\n        this.filesMap[filePath] = content;\n    }\n\n    replaceFile(filePath: string, content: string, markDirty?: boolean) {\n        this.filesMap[filePath] = content;\n        this.dirtyFiles.push(filePath);\n    }\n    \n    /**\n     * Rollback a file to its original state or to a specified content\n     * @param filePath Path to the file to rollback\n     * @param content Optional content to rollback to. If not provided, rolls back to original content\n     */\n    rollbackFile(filePath: string, content?: string) {\n        if (content) {\n            // Rollback to specified content\n            this.filesMap[filePath] = this.addLineNumbers(content);\n        } else if (this.originalFiles[filePath]) {\n            // Rollback to original content\n            this.filesMap[filePath] = this.addLineNumbers(this.originalFiles[filePath]);\n        } else {\n            // If no original content, remove the file\n            delete this.filesMap[filePath];\n        }\n        \n        // Reset line shifts for this file\n        this.lineShifts[filePath] = [];\n        \n        // Remove from dirty files if it was marked as dirty\n        const dirtyIndex = this.dirtyFiles.indexOf(filePath);\n        if (dirtyIndex !== -1) {\n            this.dirtyFiles.splice(dirtyIndex, 1);\n        }\n    }\n\n    getDirtyFiles(): {path: string, contents: string}[] {\n        return Object.entries(this.filesMap).map(([key, contents]) => {\n            if(this.dirtyFiles.includes(key)) {\n                return {\n                    path: key,\n                    contents: this.getNumberedContent(key)\n                };\n            }\n            return null;\n        }).filter(f => !!f);\n    }\n\n\n    private getFileType(extension: string) {\n        let fileExt = \"text\";\n        if(['tsx','ts'].includes(extension)) {\n            fileExt = \"typescript\"\n        } else if(['jsx','js'].includes(extension)) {\n            fileExt = \"javascript\"\n        } else if(['json'].includes(extension)) {\n            fileExt = \"json\"\n        } else if(['md'].includes(extension)) {\n            fileExt = \"markdown\"\n        } else if(['css','scss','less'].includes(extension)) {\n            fileExt = \"css\"\n        } else if(['html'].includes(extension)) {\n            fileExt = \"html\"\n        } else if(['xml'].includes(extension)) {\n            fileExt = \"xml\"\n        } else if(['yaml','yml'].includes(extension)) {\n            fileExt = \"yaml\"\n        } else if(['sh','bash'].includes(extension)) {\n            fileExt = \"shell\"\n        } else if(['gradle'].includes(extension)) {\n            fileExt = \"groovy\"\n        } else if(['swift'].includes(extension)) {\n            fileExt = \"swift\"\n        } else if(['java'].includes(extension)) {\n            fileExt = \"java\"\n        } else if(['kt'].includes(extension)) {\n            fileExt = \"kotlin\"\n        } else if(['m','h'].includes(extension)) {\n            fileExt = \"objectivec\"\n        }\n        return fileExt;\n    }\n\n    /**\n     * Get all files in FileItem[] format\n     */\n    getFileItems(): FileItem[] {\n        return Object.entries(this.filesMap).map(([path, content]) => ({\n            name: path,\n            type: 'file' as const,\n            language: this.getFileType(path.split('.').pop() || 'text'),\n            content: this.getFinalContent(path),\n            changes: 0\n        }));\n    }\n\n    /**\n     * Apply a single edit to a file\n     */\n    private applyEdit(filePath: string, edit: LineEdit): void {\n        // Parse line range (e.g., \"L10-L15\")\n        const matches = edit.lineRange.match(/L(\\d+)-L(\\d+)/);\n        if (!matches) {\n            throw new Error(`Invalid line range format: ${edit.lineRange}`);\n        }\n\n        const originalStart = parseInt(matches[1]);\n        const originalEnd = parseInt(matches[2]);\n\n        // Get adjusted line numbers based on previous edits\n        const adjustedStart = this.getAdjustedLineNumber(filePath, originalStart);\n        const adjustedEnd = this.getAdjustedLineNumber(filePath, originalEnd);\n\n        // Get current content and split into lines\n        const currentContent = this.removeLineNumbers(this.filesMap[filePath]);\n        const lines = currentContent.split('\\n');\n\n        // Validate line range\n        if (adjustedStart < 1 || adjustedStart > lines.length || adjustedEnd < 1 || adjustedEnd > lines.length) {\n            throw new Error(`Invalid line range (1-${lines.length}): ${edit.lineRange}`);\n        }\n\n        // Remove line numbers from edited content if they exist\n        const cleanedContent = this.removeLineNumbers(edit.editedContent);\n        const newLines = cleanedContent.split('\\n');\n\n        // Calculate lines being added/removed\n        const lineChange = newLines.length - (adjustedEnd - adjustedStart + 1);\n\n        // Apply the edit - either append before or replace\n        if (edit.append) {\n            lines.splice(adjustedStart - 1, 0, ...newLines); // Insert before target line\n        } else {\n            lines.splice(adjustedStart - 1, adjustedEnd - adjustedStart + 1, ...newLines); // Replace target lines\n        }\n\n        // Handle empty lines\n        let lastNonEmptyIndex = lines.length - 1;\n        while (lastNonEmptyIndex >= 0 && lines[lastNonEmptyIndex] === '') {\n            lastNonEmptyIndex--;\n        }\n\n        // Keep only one empty line at the end if needed\n        if (lastNonEmptyIndex < lines.length - 1) {\n            lines.splice(lastNonEmptyIndex + 2);\n        }\n\n        // Update line shifts\n        if (lineChange !== 0) {\n            // Update existing shifts\n            const updatedShifts = this.lineShifts[filePath].filter(shift => shift.afterLine < originalStart);\n            \n            // Add the new shift\n            updatedShifts.push({\n                afterLine: originalEnd,\n                shiftAmount: lineChange\n            });\n            \n            // Sort shifts by line number\n            updatedShifts.sort((a, b) => a.afterLine - b.afterLine);\n            \n            this.lineShifts[filePath] = updatedShifts;\n        }\n\n        // Update file content with new line numbers\n        this.filesMap[filePath] = this.addLineNumbers(lines.join('\\n'));\n    }\n\n    /**\n     * Apply multiple edits to a file\n     */\n    public applyFileEdits(fileEdit: FileEdit): void {\n        const { absolutePath, edits } = fileEdit;\n        \n        if (!this.filesMap[absolutePath]) {\n            throw new Error(`File ${absolutePath} not found`);\n        }\n\n        // Sort edits - appends in ascending order, replacements in descending order\n        const sortedEdits = [...edits].sort((a, b) => {\n            const aMatch = a.lineRange.match(/L(\\d+)/);\n            const bMatch = b.lineRange.match(/L(\\d+)/);\n            if (!aMatch || !bMatch) {\n                throw new Error(`Invalid line range format in one of the edits`);\n            }\n            // If both are appends or both are not appends, sort by line number\n            if (a.append === b.append) {\n                // For appends, use ascending order\n                if (a.append) {\n                    return parseInt(aMatch[1]) - parseInt(bMatch[1]);\n                }\n                // For replacements, use descending order\n                return parseInt(bMatch[1]) - parseInt(aMatch[1]);\n            }\n            // If only one is append, put it first\n            return a.append ? -1 : 1;\n        });\n\n        // Apply each edit\n        sortedEdits.forEach(edit => {\n            this.applyEdit(absolutePath, edit);\n        });\n    }\n\n    /**\n     * Get final content of a file without line numbers\n     * @returns The entire file content after all edits have been applied\n     */\n    public getFinalContent(filePath: string): string {\n        if (!this.filesMap[filePath]) {\n            throw new Error(`File ${filePath} not found`);\n        }\n        return this.removeLineNumbers(this.filesMap[filePath]);\n    }\n\n    public hasFile(filePath: string): boolean {\n        return !!this.filesMap[filePath]\n    }\n}", "startLine": 19, "endLine": 306, "type": "unknown", "symbols": ["FileLineManager"], "score": 1, "context": "This class manages files with line numbers, applies edits, rollbacks, tracks dirty files, and provides file content retrieval, essential for file operations and state management.", "includesImports": false}, {"filePath": "src/stores/ProjectSessionStore.ts", "content": "export class ProjectSession {\n    private readonly rootStore: RootStore;\n    // Core State\n    @observable id: string;\n    @observable messages: Message[] = [];\n    @observable state: ProjectSessionState = {\n        status: 'idle',\n        view: 'preview',\n        previewDialogOpen: false\n    };\n    @observable secretsStatus: 'idle' | 'saving' | 'error' = 'idle';\n    @observable currentMessage: string = '';\n    @observable currentFile: File | null = null;\n    @observable isInitial = false;\n    @observable projectId: string | null = null;\n    @observable needsContinuation: boolean = false;\n\n    // Component Context\n    @observable componentContexts: ComponentContext[] = [];\n    @observable componentContextUploading: boolean = false;\n\n    // File Management\n    @observable fileTree: FileItem[] = [];\n    @observable activeFile: FileNode | null = null;\n    @observable dependencies: Record<string, { version: string }> = DEFAULT_DEPENDENCIES;\n    @observable fileOperations = new Map<string, FileOperation>();\n\n    // UI State\n    @observable previewUrl?: string;\n    @observable errors: Error[] = [];\n    @observable snackError: SnackError | null = null;\n    @observable tabSelection: 'preview' | 'code' | 'terminal' | 'supabase' = 'preview'\n    @observable chatLoading = false;\n    @observable initialPrompt = '';\n    @observable fullScreen = false;\n    @observable hasStartedStreaming = false;\n    @observable changesWhileInactive = false;\n\n    // Progress State\n    @observable progress: ProgressState = {\n        status: \"Ready to start...\",\n        percent: 0\n    };\n\n    @observable isViewingHistoricalVersion = false;\n    @observable historicalMessageId: string | null = null;\n    @observable isLoadingHistoricalVersion = false;\n    @observable updatedUserMessage = '';\n\n    @observable postStreamAnalysis: {analysis: string, message: string} = {\n        analysis: '',\n        message: ''\n    }\n    @observable postStreamAnalysisStatus: 'idle' | 'loading' | 'loaded' | 'errored' = 'idle';\n    \n    // Client Testing State\n    @observable clientTesting: ClientTestingState = {\n        isActive: false,\n        toolCallId: null,\n        featuresToTest: '',\n        expectations: '',\n        reason: '',\n        startTime: 0,\n        endTime: null\n    };\n\n    constructor(rootStore: RootStore, id: string, initialState?: Partial<ProjectSession>) {\n        this.id = id;\n        if (initialState) {\n            Object.assign(this, initialState);\n        }\n        console.log('Init session', id)\n        makeAutoObservable(this);\n        this.rootStore = rootStore;\n    }\n\n    @action\n    async setStatus(status: ProjectSessionState['status']) {\n        if(this.state.status === \"streaming\" && status === \"idle\") {\n            await this.rootStore.snackStore.updateFiles(this.id, this.fileTree, this.dependencies)\n        }\n        this.state.status = status;\n    }\n\n    @action\n    setView(view: ProjectSessionState['view']) {\n        this.state.view = view;\n    }\n\n    @action\n    setDependencies(dependencies: Record<string, { version: string }>) {\n        this.dependencies = dependencies;\n        console.log('Dependencies updated in ProjectSessionStore:', dependencies);\n    }\n    \n    /**\n     * Adds a new dependency to the project\n     * @param packageName Name of the npm package to add\n     * @param version Version of the package (defaults to '*')\n     * @returns Promise resolving to true if successful, false otherwise\n     */\n    @action\n    async addDependency(packageName: string, version: string = '*'): Promise<boolean> {\n        try {\n            console.log(`Adding dependency ${packageName}@${version}`);\n            \n            // Create updated dependencies object\n            const updatedDependencies = { \n                ...this.dependencies,\n                [packageName]: { version }\n            };\n            \n            // Update dependencies in the session\n            this.setDependencies(updatedDependencies);\n            \n            // Update Snack with the new dependencies\n            await this.rootStore.snackStore.updateFiles(this.id, this.fileTree, updatedDependencies);\n            \n            // Save to API if project ID exists\n            if (this.projectId) {\n                await this.saveToAPI();\n            }\n            \n            console.log(`Successfully added dependency ${packageName}@${version}`);\n            return true;\n        } catch (error) {\n            console.error(`Error adding dependency ${packageName}:`, error);\n            return false;\n        }\n    }\n    \n    /**\n     * Removes a dependency from the project\n     * @param packageName Name of the npm package to remove\n     * @returns Promise resolving to true if successful, false otherwise\n     */\n    @action\n    async removeDependency(packageName: string): Promise<boolean> {\n        try {\n            console.log(`Removing dependency ${packageName}`);\n            \n            // Check if the dependency exists\n            if (!this.dependencies[packageName]) {\n                console.warn(`Dependency ${packageName} not found in project`);\n                return false;\n            }\n            \n            // Create a new dependencies object without the removed package\n            const { [packageName]: removed, ...remainingDependencies } = this.dependencies;\n            \n            // Update dependencies in the session\n            this.setDependencies(remainingDependencies);\n            \n            // Update Snack with the updated dependencies\n            await this.rootStore.snackStore.updateFiles(this.id, this.fileTree, remainingDependencies);\n            \n            // Save to API if project ID exists\n            if (this.projectId) {\n                await this.saveToAPI();\n            }\n            \n            console.log(`Successfully removed dependency ${packageName}`);\n            return true;\n        } catch (error) {\n            console.error(`Error removing dependency ${packageName}:`, error);\n            return false;\n        }\n    }\n    \n    /**\n     * Detects dependencies from code files and updates the dependencies in the session\n     * @param files Array of files to scan for dependencies\n     */\n    @action\n    detectAndUpdateDependencies(files: FileItem[]) {\n        try {\n            console.log('Detecting dependencies from files:', files.length);\n            \n            // Detect dependencies from code\n            const detectedDependencies = this.detectDependenciesFromCode(files);\n            console.log('Detected dependencies:', detectedDependencies);\n            \n            // Get current dependencies\n            const currentDependencies = this.dependencies || {};\n            \n            // Create update object with only new dependencies\n            const dependencyUpdates = this.createDependencyUpdateObject(currentDependencies, detectedDependencies);\n            \n            // Only update if there are new dependencies\n            if (Object.keys(dependencyUpdates).length > 0) {\n                console.log('New dependencies detected:', dependencyUpdates);\n                \n                // Update dependencies in the session\n                const updatedDependencies = { ...currentDependencies };\n                \n                // Add new dependencies\n                for (const [pkg, info] of Object.entries(dependencyUpdates)) {\n                    if (info !== null) {\n                        updatedDependencies[pkg] = info;\n                    }\n                }\n                \n                // Update the session dependencies\n                this.setDependencies(updatedDependencies);\n                \n                // Save to API if project ID exists\n                // if (this.projectId) {\n                //     this.saveToAPI().catch(err => {\n                //         console.error('Failed to save dependencies to API:', err);\n                //     });\n                // }\n            }\n        } catch (error) {\n            console.error('Error detecting dependencies:', error);\n        }\n    }\n    \n    /**\n     * Saves the current file tree and dependencies to the API\n     */\n    @action\n    async saveToAPI() {\n        if (!this.projectId) return null;\n        \n        try {\n            const response = await fetch(`/api/project/${this.projectId}/files`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify({\n                    files: this.fileTree,\n                    dependencies: this.dependencies,\n                    chatId: this.id\n                })\n            });\n            \n            if (!response.ok) {\n                throw new Error(`API error: ${response.status}`);\n            }\n            \n            const result = await response.json();\n            console.log('Project state saved to API:', result);\n            return result;\n        } catch (error) {\n            console.error('Error saving project state to API:', error);\n            return null;\n        }\n    }\n    \n    /**\n     * Detects dependencies from code files\n     * @param files Array of file items to scan\n     * @returns Record of dependencies\n     */\n    private detectDependenciesFromCode(files: FileItem[]): Record<string, { version: string }> {\n        const dependencies: Record<string, { version: string }> = {};\n        \n        // Only process JS/TS files\n        const jsFiles = files.filter(file => {\n            const fileName = file.name.toLowerCase();\n            return fileName.endsWith('.js') || \n                   fileName.endsWith('.jsx') || \n                   fileName.endsWith('.ts') || \n                   fileName.endsWith('.tsx');\n        });\n        \n        for (const file of jsFiles) {\n            const content = file.content;\n            if (!content) continue;\n            \n            // Extract all imports using different patterns\n            const extractImports = (regex: RegExp, content: string): string[] => {\n                const matches: string[] = [];\n                let match;\n                while ((match = regex.exec(content)) !== null) {\n                    if (match[1]) matches.push(match[1]);\n                }\n                return matches;\n            };\n            \n            const imports = [\n                ...extractImports(IMPORT_REGEX.es6, content),\n                ...extractImports(IMPORT_REGEX.commonjs, content),\n                ...extractImports(IMPORT_REGEX.dynamic, content),\n            ];\n            \n            // Process imports to get package names\n            for (let importPath of imports) {\n                // Skip relative imports\n                if (importPath.startsWith('./') || importPath.startsWith('../')) {\n                    continue;\n                }\n                \n                // Handle scoped packages and subpaths\n                let packageName = importPath;\n                \n                // Extract the main package name from subpaths (e.g., 'lodash/map' -> 'lodash')\n                if (!importPath.startsWith('@')) {\n                    packageName = importPath.split('/')[0];\n                } else {\n                    // For scoped packages, get the scope + package name (e.g., '@expo/vector-icons')\n                    const parts = importPath.split('/');\n                    if (parts.length >= 2) {\n                        packageName = `${parts[0]}/${parts[1]}`;\n                    }\n                }\n                \n                // Map to actual package name if needed\n                const mappedPackage = PACKAGE_MAPPING[packageName] || packageName;\n                \n                // Skip React Native internal packages\n                if (mappedPackage.startsWith('react-native/') || \n                    mappedPackage === 'react/jsx-runtime' ||\n                    mappedPackage === 'react/jsx-dev-runtime') {\n                    continue;\n                }\n                \n                // Add to dependencies - accept any valid package name\n                dependencies[mappedPackage] = { version: '*' };\n            }\n        }\n        \n        return dependencies;\n    }\n    \n    /**\n     * Creates a dependency update object by comparing current and detected dependencies\n     */\n    private createDependencyUpdateObject(\n        currentDeps: Record<string, any>,\n        detectedDeps: Record<string, { version: string }>\n    ): Record<string, { version: string } | null> {\n        const updates: Record<string, { version: string } | null> = {};\n        \n        // Add new dependencies\n        for (const [pkg, info] of Object.entries(detectedDeps)) {\n            if (!currentDeps[pkg]) {\n                updates[pkg] = info;\n            }\n        }\n        \n        return updates;\n    }\n\n    @action\n    setPreviewDialogOpen(open: boolean) {\n        this.state.previewDialogOpen = open;\n    }\n\n    @action\n    setProgress(progress: Partial<ProgressState>) {\n        this.progress = {...this.progress, ...progress};\n    }\n\n    @action\n    setFileTree(fileTree: FileItem[], dependencies?: Record<string, { version: string }> ) {\n        if (dependencies) {\n            this.dependencies = dependencies;\n        }\n        // Detect dependencies from the updated file tree\n        // this.detectAndUpdateDependencies(fileTree);\n\n        this.fileTree = fileTree;\n\n        // Update the Snack with the new files and dependencies\n        // this.rootStore.snackStore.updateFiles(this.id, fileTree, this.dependencies);\n        this.pushUpdatesToPreview();\n    }\n\n    @action\n    pushUpdatesToPreview() {\n        this.rootStore.snackStore.updateFiles(this.id, this.fileTree, this.dependencies);\n    }\n\n    @action\n    setActiveFile(file: FileNode | undefined) {\n        if (file) {\n            if(!this.fileTree.find(f => f.name === file.name)) {\n                this.fileTree.push(file);\n            }\n            this.activeFile = file;\n        }\n    }\n\n    @action\n    setMessages(messages: Message[]) {\n        this.messages = messages;\n    }\n\n    @action\n    setCurrentTab(tabSelection: 'code' | 'preview' | 'terminal' | 'supabase') {\n        this.tabSelection = tabSelection;\n    }\n\n    @action\n    addMessage(message: Message) {\n        this.messages.push(message);\n    }\n\n    @action\n    setCurrentMessage(message: string) {\n        this.currentMessage = message;\n    }\n\n    @action\n    setCurrentFile = (currentFile: File) => {\n        this.currentFile = currentFile;\n    }\n    \n    @action\n    setNeedsContinuation = (needsContinuation: boolean) => {\n        this.needsContinuation = needsContinuation;\n        console.log(`[ProjectSession] Setting needsContinuation to ${needsContinuation}`);\n    }\n\n    @action\n    onUserMessageUpdate(userMessage: string) {\n        this.updatedUserMessage  = userMessage;\n    }\n    \n    @action\n    startClientTesting(toolCallId: string, featuresToTest: string, expectations: string, reason: string) {\n        console.log(`[ProjectSession] Starting client testing for tool call ${toolCallId}`);\n        this.clientTesting = {\n            isActive: true,\n            toolCallId,\n            featuresToTest,\n            expectations,\n            reason,\n            startTime: Date.now(),\n            endTime: null\n        };\n    }\n    \n    @action\n    completeClientTesting(toolCallId: string) {\n        console.log(`[ProjectSession] Completing client testing for tool call ${toolCallId}`);\n        if (this.clientTesting.toolCallId === toolCallId) {\n            this.clientTesting = {\n                ...this.clientTesting,\n                isActive: false,\n                endTime: Date.now()\n            };\n            \n            // Return the testing result to the AI\n            return {\n                result: \"DONE\",\n                toolCallId\n            };\n        }\n        return null;\n    }\n    \n    @computed\n    get isClientTestingActive() {\n        return this.clientTesting.isActive;\n    }\n    \n    @action\n    addComponentContext(context: Omit<ComponentContext, 'id' | 'isUploading'>) {\n        const id = `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;\n        \n        // Create a new array to ensure observers detect the change\n        const newContext = {\n            ...context,\n            id,\n            isUploading: !!context.screenshot\n        };\n        \n        // Replace the entire array to ensure MobX triggers updates\n        this.componentContexts = [...this.componentContexts, newContext];\n\n        // console.log('this.componentContexts', this.componentContexts)\n        \n        // Start uploading the screenshot\n        this.uploadComponentScreenshot(id);\n        \n        return id;\n    }\n    \n    @action\n    removeComponentContext(id: string) {\n        console.log('ProjectSessionStore: Removing component context with ID:', id);\n        console.log('Before removal:', this.componentContexts.length, 'contexts');\n        \n        this.componentContexts = this.componentContexts.filter(context => context.id !== id);\n        \n        console.log('After removal:', this.componentContexts.length, 'contexts');\n    }\n    \n    @action\n    clearComponentContexts() {\n        console.log('ProjectSessionStore: Clearing all component contexts');\n        console.log('Before clearing:', this.componentContexts.length, 'contexts');\n        \n        // Create a new empty array to ensure observers detect the change\n        this.componentContexts = [];\n        \n        console.log('After clearing:', this.componentContexts.length, 'contexts');\n    }\n    \n    @action\n    private async uploadComponentScreenshot(contextId: string) {\n        const context = this.componentContexts.find(c => c.id === contextId);\n        if (!context || !context.screenshot) return;\n        \n        try {\n            // Convert base64 to file\n            const file = base64ToFile(context.screenshot, `component-${context.sourceFile}-${context.componentName}.jpg`);\n            \n            // Upload the file\n            const result = await uploadFile(file);\n            \n            if (result) {\n                // Find the context and update it directly\n                const index = this.componentContexts.findIndex(c => c.id === contextId);\n                if (index !== -1) {\n                    this.componentContexts[index].imageUrl = result.url;\n                    this.componentContexts[index].isUploading = false;\n                }\n            } else {\n                throw new Error('Upload failed');\n            }\n        } catch (error) {\n            console.error('Error uploading component screenshot:', error);\n            \n            // Find the context and update it directly\n            const index = this.componentContexts.findIndex(c => c.id === contextId);\n            if (index !== -1) {\n                this.componentContexts[index].isUploading = false;\n                this.componentContexts[index].uploadError = error instanceof Error ? error.message : 'Unknown error';\n            }\n        }\n    }\n    \n    @computed\n    get isComponentContextUploading(): boolean {\n        return this.componentContexts.some(context => context.isUploading);\n    }\n    \n    @computed\n    get componentContextAttachments(): ComponentContextAttachment[] {\n        return this.componentContexts\n            .filter(context => !context.isUploading && context.imageUrl)\n            .map(context => ({\n                name: `${context.componentName} (${context.sourceFile})`,\n                url: context.imageUrl!,\n                contentType: 'image/jpeg',\n                componentContext: {\n                    componentName: context.componentName,\n                    element: context.element,\n                    sourceFile: context.sourceFile,\n                    lineNumber: context.lineNumber\n                }\n            }));\n    }\n\n    @action\n    setChatLoading(chatLoading: boolean) {\n        this.chatLoading = chatLoading;\n    }\n\n    @action\n    setPreviewUrl(url: string) {\n        this.previewUrl = url;\n    }\n\n    @action\n    setInitialPrompt(initialPrompt: string) {\n        this.initialPrompt = initialPrompt\n    }\n\n    @action\n    toggleFullScreen() {\n        this.fullScreen = !this.fullScreen;\n    }\n\n    @action\n    setSnackError(message: string, type?: 'default'| 'supabase') {\n        // Only set if different from current error or if current error was handled\n        if (!this.snackError || this.snackError.handled || this.snackError.message !== message) {\n            this.snackError = {\n                message,\n                timestamp: Date.now(),\n                handled: false,\n                type\n            };\n        }\n    }\n\n    @action\n    markErrorAsHandled() {\n        if (this.snackError) {\n            this.snackError.handled = true;\n        }\n    }\n\n    @action\n    handleErrorFix() {\n        if (!this.snackError) return;\n\n        let errorMessage;\n        if(this.snackError.type === \"supabase\") {\n            errorMessage = `I encountered an error while running the SQL query: \"${this.snackError.message}\"\n\nPlease analyze this error carefully and help me fix it. Consider:\n1. The specific error message and its root cause\n2. Database schema, triggers, functions, RLS policies\n3. Any related code that might be affected\n4. Dependencies that might be missing or misconfigured\n\nPlease provide a detailed solution.`\n        } else {\n            errorMessage = `I encountered an error in Expo: \"${this.snackError.message}\"\n\nPlease analyze this error carefully and help me fix it. Consider:\n1. The specific error message and its root cause\n2. Any related code that might be affected\n3. Dependencies that might be missing or misconfigured\n4. Similar patterns in the codebase that work correctly\n5. Minified react errors need to traced using where the issue is happening\n\nPlease provide a detailed solution.`;\n        }\n\n\n        this.currentMessage = errorMessage;\n        this.markErrorAsHandled();\n    }\n\n    @action\n    async sendAutoFixError(error: any) {\n        console.log('error', error)\n        const message = error?.prettyStack || error?.stack || error?.message || error\n        // const autoFixErrors = [\n        //     'is not defined',\n        //     'Unexpected token, expected \",\" ',\n        //     'Cannot read properties of undefined'\n        // ]\n        //\n        // if(autoFixErrors.some(errorMessage => message.includes(errorMessage))) {\n        //   // Attempt to auto fix\n        //\n        //\n        //\n        //   // Mark the error as handled\n        //   this.markErrorAsHandled();\n        //   return; // Skip sending the error message to the chat\n        // }\n\n        const errorMessage = `I encountered an error: \n${message}\n\nPlatform: ${error?.plaform || 'web'}\n\n%%Error code: ${generateUUID()}%%\n\nPlease analyze this error carefully and help me fix it. Consider:\n1. The specific error message and its root cause\n2. Any related code that might be affected\n3. Dependencies that might be missing or misconfigured\n4. Similar patterns in the codebase that work correctly\n5. Minified react errors need to traced using where the issue is happening\n6. Examining means examining, don't write code to examine. Try to understand why things might be breaking and apply the most probable fix.\n`;\n\n        this.currentMessage = errorMessage;\n        this.markErrorAsHandled();\n    }\n\n    @action\n    addFileOperation(operation: FileOperation) {\n        this.fileOperations.set(operation.path, operation);\n    }\n\n    @action\n    async deleteMessage(messageId: string, isGroupedMessage: boolean = false) {\n        try {\n            // Add isGroupedMessage as a query parameter\n            const url = isGroupedMessage \n                ? `/api/chat/${this.id}/message/${messageId}?isGroupedMessage=true` \n                : `/api/chat/${this.id}/message/${messageId}`;\n            \n            const result = await fetch(url, {method: \"DELETE\"})\n            const response = await result.json();\n            if(!response.success) {\n                throw new Error('Error restoring checkpoint. Please contact support.');\n            }\n            await this.loadFiles();\n            return response.latestMessageId;\n        } catch (error) {\n            throw new Error('Error restoring checkpoint. Please contact support.');\n        }\n    }\n\n    @action\n    async removeLatestFileState() {\n        try {\n            const result = await fetch(`/api/chat/${this.id}/files`, {method: \"DELETE\"})\n            await result.json();\n            await this.loadFiles();\n            return \"ok\";\n        } catch (error) {\n            return null;\n        }\n    }\n\n    @action\n    markStreamingComplete() {\n        this.hasStartedStreaming = false;\n        // Detect if the user is in the background. If yes, store the update and on visibility change, update the snack.\n    }\n\n    @action\n    markAsFailed() {\n        if(this.hasStartedStreaming) {\n            // There are code changes available. Let's try to continue from where we left off.\n            this.hasStartedStreaming = false;\n        }\n    }\n\n    @action\n    setChangesWhileInactive(value: boolean) {\n        this.changesWhileInactive = value;\n    }\n\n    @action\n    async updateFileTree(codeBlock: CodeBlock) {\n        if (codeBlock && codeBlock.type) {\n            this.hasStartedStreaming = true;\n            const filePath = codeBlock.absolutePath || codeBlock.fileName;\n            if (!filePath) return\n            const updatedTree = [...this.fileTree];\n\n            // If we have a path with directories, create them\n\n            // Single file at root level\n            const existingFileIndex = updatedTree.findIndex(item =>\n                item.name === filePath\n            );\n\n            if (existingFileIndex !== -1) {\n                updatedTree[existingFileIndex] = {\n                    ...updatedTree[existingFileIndex],\n                    // @ts-ignore\n                    content: codeBlock.content as unknown as string,\n                    changes: ((updatedTree[existingFileIndex] as FileNode).changes || 0) + 1\n                };\n            } else {\n                updatedTree.push({\n                    type: 'file',\n                    name: filePath,\n                    content: codeBlock.content,\n                    changes: 0,\n                    language: 'typescript'\n                });\n            }\n\n            this.setFileTree(updatedTree);\n            this.updateActiveFile();\n        }\n    }\n\n    @action\n    updateActiveFile() {\n        const currentFile = this.fileTree.find(item =>\n             item.name === this.activeFile?.name\n        );\n\n        if (currentFile && currentFile.name) {\n            this.setActiveFile({\n                name: currentFile.name,\n                content: currentFile.content,\n                language: currentFile.language || 'typescript',\n            } as FileNode);\n        }\n    }\n\n    @action\n    async loadFiles(options?: {anonymousId?: string}) {\n        const {anonymousId} = options || {};\n        try {\n            return fetch(`/api/project/${this.projectId}/files`, {\n                headers: {\n                    'x-anonymous-id': anonymousId || '',\n                }\n            })\n                .then(action((response) => {\n                    if (response.ok) {\n                        return response.json();\n                    }\n                }))\n                .then(action((data) => {\n                    if (data) {\n                        this.setFileTree(data.files, data.dependencies)\n                        // this.setDependencies(data.dependencies);\n                        this.pushUpdatesToPreview()\n                    }\n                    return \"ok\"\n                }))\n        } catch (error) {\n            console.error('Error loading file state:', error);\n        }\n    }\n\n    @computed\n    get activeOperations() {\n        return Array.from(this.fileOperations.values()).filter(\n            op => op.status === 'pending'\n        );\n    }\n\n    @computed\n    get isProcessing() {\n        return this.state.status === 'loading' || this.state.status === 'streaming';\n    }\n\n    @computed\n    get v2FileTree() {\n        return this.fileTree\n    }\n    \n    @action\n    async fetchChatData() {\n        try {\n            const response = await fetch(`/api/chat/${this.id}`);\n            if (response.ok) {\n                const chatData = await response.json();\n                if (chatData?.projectId) {\n                    this.projectId = chatData.projectId;\n                    return chatData;\n                }\n            }\n            return null;\n        } catch (error) {\n            console.error('Failed to fetch chat data:', error);\n            return null;\n        }\n    }\n    \n    @action\n    async fetchProjectChats() {\n        if (!this.projectId) {\n            await this.fetchChatData();\n        }\n        \n        if (this.projectId) {\n            try {\n                const response = await fetch(`/api/project/${this.projectId}/chat`);\n                if (response.ok) {\n                    return await response.json();\n                }\n            } catch (error) {\n                console.error('Failed to fetch project chats:', error);\n            }\n        }\n        return [];\n    }\n\n\n    /**\n     * Loads a historical version of a project\n     * @param projectId Project ID\n     * @param chatId Chat ID\n     * @param messageId Message ID of the historical version\n     * @param activeMessageId Currently active message ID\n     * @returns Promise resolving to true if successful, false otherwise\n     */\n    // @action\n    loadHistoricalVersion = async (projectId: string, chatId: string, messageId: string, activeMessageId: string): Promise<boolean> => {\n    return false;\n        //     try {\n        //     this.isLoadingHistoricalVersion = true;\n        //     this.isViewingHistoricalVersion = true;\n        //     this.historicalMessageId = activeMessageId;\n        //\n        //     // Fetch historical file state from API\n        //     const response = await fetch(`/api/project/${projectId}/chat/${chatId}/message/${messageId}`);\n        //\n        //     if (!response.ok) {\n        //         const errorText = await response.text();\n        //         console.error('Failed to load historical version:', errorText);\n        //\n        //         this.historicalMessageId = null;\n        //\n        //         this.isLoadingHistoricalVersion = false;\n        //         this.isViewingHistoricalVersion = false;\n        //         this.historicalMessageId = null;\n        //\n        //         return false;\n        //     }\n        //\n        //     const fileState = await response.json();\n        //\n        //     // Update the Snack with historical files\n        //     const instance = this.rootStore.snackStore.snackInstances.get(chatId);\n        //     if (instance) {\n        //         instance.snack.updateFiles(this.rootStore.snackStore.convertToSnackFiles(fileState.files));\n        //\n        //         // Detect and update dependencies for historical version\n        //         // await this.rootStore.snackStore.updateDependenciesFromFiles(instance.snack, fileState.files, chatId);\n        //\n        //         instance.snack.sendCodeChanges();\n        //\n        //         // Update UI state\n        //         this.rootStore.snackStore.updateSnackState(chatId, {\n        //             isLoading: false,\n        //             error: null,\n        //             historicalMessageId: activeMessageId\n        //         });\n        //\n        //         this.isViewingHistoricalVersion = true;\n        //         this.historicalMessageId = activeMessageId;\n        //         this.isLoadingHistoricalVersion = false;\n        //\n        //         return true;\n        //     }\n        //\n        //     this.isLoadingHistoricalVersion = false;\n        //     return false;\n        // } catch (error) {\n        //     console.error('Error loading historical version:', error);\n        //\n        //         this.rootStore.snackStore.updateSnackState(chatId, {\n        //         isLoading: false,\n        //         error: `Error loading historical version: ${error}`,\n        //         isHistoricalVersion: false,\n        //         historicalMessageId: null\n        //     });\n        //\n        //     this.isLoadingHistoricalVersion = false;\n        //     this.isViewingHistoricalVersion = false;\n        //     this.historicalMessageId = null;\n        //\n        //     return false;\n        // }\n    }\n\n    @action\n    async performAnalysis(message: any, messages: UIMessage[]) {\n\n                this.postStreamAnalysisStatus = 'idle';\n                if (typeof message.content === 'string' &&\n                    message.content.length > 200 &&\n                    (message.content.includes('<MO_FILE') || message.content.includes('<MO_DIFF') || message.content.includes('<MO_DATABASE_QUERY'))) {\n                    try {\n                        runInAction(() => {\n                            this.postStreamAnalysisStatus = 'loading';\n                        })\n                        // Call the post-stream analysis API\n                        const response = await fetch('/api/post-stream-analysis', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json',\n                            },\n                            body: JSON.stringify({\n                                chatId: this.id,\n                                messageId: message.id,\n                                files: this.fileTree,\n                                messages\n                            })\n                        });\n\n                        if (response.ok) {\n                            const result = await response.json();\n                            runInAction(() => {\n                                this.postStreamAnalysis = {\n                                    analysis: result.analysis,\n                                    message: result.userFeedbackMessage\n                                }\n                                this.postStreamAnalysisStatus = 'loaded';\n                            })\n\n                            // Log analysis details for debugging\n                            console.log('Code analysis completed:', {\n                                recommended_action: result.analysis.recommendedAction,\n                                has_issues: result.analysis.potentialIssues.length > 0\n                            });\n                        } else {\n                            console.error('Failed to analyze code changes:', await response.text());\n                            runInAction(() => {\n                                this.postStreamAnalysisStatus = 'errored';\n                            })\n                        }\n                    } catch (error) {\n                        console.error('Error during post-stream analysis:', error);\n                        runInAction(() => {\n                            this.postStreamAnalysisStatus = 'errored';\n                        })\n                        Sentry.captureException(error);\n                    }\n                }\n    }\n    //\n    // @action\n    /**\n     * Save secrets to Supabase project\n     * @param secrets Record of secret names and values\n     * @param projectId The project ID\n     * @returns Promise that resolves when secrets are saved\n     */\n    @action\n    saveSecrets = async (secrets: Record<string, string>, projectId: string, append: (message: Message | CreateMessage, chatRequestOptions?: ChatRequestOptions) => Promise<string | null | undefined>): Promise<boolean> => {\n        try {\n            // For each secret, make an API call to save it\n            const secretEntries = Object.entries(secrets);\n\n            await fetch(`/api/project/${projectId}/supabase/secrets`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify({\n                    secrets: secretEntries.map(secret => ({name: secret[0], value: secret[1]})),\n                    scope: 'project', // Default scope\n                    environment: 'all'  // Default environment\n                })\n            });\n\n            const secretsNames = Object.keys(secrets);\n            // Notify the user that secrets were saved\n            append({\n                content: `I have added the ${secretsNames.join(',')} secret(s). Please continue.`,\n                role: 'user'\n            });\n            return true;\n        } catch (error) {\n            console.error('Error saving secrets:', error);\n            toast.error(`Failed to save secrets: ${error instanceof Error ? error.message : 'Unknown error'}`)\n            return false;\n        }\n    };\n    \n    restoreLatestVersion = async (projectId: string, chatId: string): Promise<boolean> => {\n   return  false\n    //     try {\n    //         // Update UI to show loading state\n    //         this.updateSnackState(chatId, {\n    //             isLoading: true,\n    //             error: null\n    //         });\n    //\n    //         // Fetch latest file state from API\n    //         const response = await fetch(`/api/project/${projectId}/files`);\n    //\n    //         if (!response.ok) {\n    //             const errorText = await response.text();\n    //             console.error('Failed to restore latest version:', errorText);\n    //\n    //             this.updateSnackState(chatId, {\n    //                 isLoading: false,\n    //                 error: `Failed to restore latest version: ${errorText}`\n    //             });\n    //\n    //             return false;\n    //         }\n    //\n    //         const fileState = await response.json();\n    //\n    //         // Update the Snack with latest files\n    //         const instance = this.snackInstances.get(chatId);\n    //         if (instance) {\n    //             instance.snack.updateFiles(this.convertToSnackFiles(fileState.files));\n    //\n    //             // Detect and update dependencies for historical version\n    //             await this.updateDependenciesFromFiles(instance.snack, fileState.files, chatId);\n    //\n    //             instance.snack.sendCodeChanges();\n    //\n    //             // Update UI state\n    //             this.updateSnackState(chatId, {\n    //                 isLoading: false,\n    //                 error: null,\n    //                 isHistoricalVersion: false,\n    //                 historicalMessageId: null\n    //             });\n    //\n    //             this.isViewingHistoricalVersion = false;\n    //             this.historicalMessageId = null;\n    //\n    //             return true;\n    //         }\n    //\n    //         return false;\n    //     } catch (error) {\n    //         console.error('Error restoring latest version:', error);\n    //\n    //         this.updateSnackState(chatId, {\n    //             isLoading: false,\n    //             error: `Error restoring latest version: ${error}`\n    //         });\n    //\n    //         return false;\n    //     }\n    };\n\n}", "startLine": 71, "endLine": 1168, "type": "unknown", "symbols": ["ProjectSession"], "score": 1, "context": "This MobX store manages project session state, including file tree, active file, file operations, dependencies, and methods for loading, saving, and updating files, crucial for project and file state management.", "includesImports": false}, {"filePath": "src/lib/chat/tools/edit-file.tool.ts", "content": "export const editFileTool = ({\n                                 fileManager,\n                                 contentManager,\n                                 processImagePlaceholders,\n                                 processVideoPlaceholders,\n                                 dataStream,\n                                 creditUsageTracker,\n                                 projectId\n                             }: {\n    fileManager: FileLineManager,\n    contentManager: FileContentManager,\n    dataStream: DataStreamWriter,\n    processImagePlaceholders: (text: string) => Promise<string>,\n    processVideoPlaceholders: (text: string) => Promise<string>,\n    creditUsageTracker?: any,\n    projectId: string\n}) => {\n    return tool({\n        description: 'Edit an existing file using search and replace operations, similar to MO_DIFF. This eliminates line shift issues and is more reliable. IMPORTANT: When making multiple edits to the same file, batch them together in a single call to avoid conflicts. Always validate your edits with the TargetedValidator after making changes.',\n        parameters: fileEditSchema,\n        execute: async ({\n                            absolutePath,\n                            edits\n                        }: any) => {\n            try {\n                console.log('Editing file:', absolutePath);\n                console.log('Number of edits:', edits.length);\n\n                // Add checks to detect if the llm is giving a sql file or a invalid file not supported,\n                // we will return immediately without applying to course correct\n                const errors: string[] = VALIDATORS.reduce((acc, validator) => {\n                    if (validator.check(absolutePath, fileManager.hasFile(absolutePath) ? fileManager.getFinalContent(absolutePath): '')) {\n                        acc.push(validator.error);\n                    }\n                    return acc;\n                }, [] as string[]);\n\n                if (errors.length) {\n                    console.log('Errors found. Rejecting file edit', errors.join(','))\n                    return errors.join(',')\n                }\n\n                // Check if file exists in the file manager\n                let fileExists = true;\n                let currentContent: string | null = \"\";\n                try {\n                    currentContent = fileManager.getFinalContent(absolutePath);\n                } catch (e) {\n                    fileExists = false;\n                }\n\n                // Prevent editing existing SQL files - SQL migrations should never be edited\n                const isSqlMigration = absolutePath.toLowerCase().endsWith('.sql');\n                if (isSqlMigration && fileExists) {\n                    return `Cannot edit existing SQL migration ${absolutePath}. Please create a new migration file instead.`;\n                }\n\n                // If file doesn't exist, create it with empty content\n                if (!fileExists) {\n                    fileManager.addFiles(absolutePath, \"\");\n                    contentManager.setFileContent(absolutePath, \"\");\n                    console.log(`Created new file ${absolutePath} as it didn't exist`);\n                } else {\n                    // Ensure the content manager has the current content\n                    contentManager.setFileContent(absolutePath, currentContent);\n                }\n\n                // Create a diff meta object to use with the DiffApplicationService\n                const diffMeta: DiffMeta = {\n                    path: absolutePath,\n                    lang: path.extname(absolutePath).substring(1) || 'text',\n                    searches: [],\n                    replacements: [],\n                    currentPair: 0,\n                    lineCount: 0\n                };\n\n                // Process each edit as a search/replace pair\n                for (const edit of edits) {\n                    // Get the current content again as it might have changed\n                    currentContent = contentManager.getFileContent(absolutePath);\n\n                    // For new files with no content, handle differently\n                    if (!currentContent && edit.searchPattern === '') {\n                        diffMeta.searches.push('');\n                        diffMeta.replacements.push(edit.replacementContent);\n                        continue;\n                    }\n\n                    // Add the search/replace pair to the diff meta\n                    diffMeta.searches.push(edit.searchPattern);\n\n                    // If isAppend is true, we're inserting before the search pattern\n                    if (edit.isAppend) {\n                        diffMeta.replacements.push(edit.replacementContent + edit.searchPattern);\n                    } else {\n                        diffMeta.replacements.push(edit.replacementContent);\n                    }\n\n                    // Log the edit for debugging\n                    console.log(`Edit ${diffMeta.searches.length}: ${edit.description || 'No description'}`);\n                }\n\n                // Apply the diff using the content manager\n                console.log(`Applying ${diffMeta.searches.length} search/replace pairs to ${absolutePath}`);\n                const result = await contentManager.applyDiff(diffMeta, {bestEffort: true, filePath: absolutePath});\n\n                if (!result.success) {\n                    return `Error applying changes to ${absolutePath}: ${result.message}`;\n                }\n\n                // Get the updated content\n                let updatedContent: string | null = contentManager.getFileContent(absolutePath);\n\n                if (!updatedContent) {\n                    return `Error applying changes to ${absolutePath}: Something wrong with the replacement. Please ask the user <NAME_EMAIL> to get it fixed immediately.`;\n                }\n\n                // Update the file manager to keep it in sync\n                fileManager.replaceFile(absolutePath, updatedContent);\n\n                // Process placeholders\n                updatedContent = await processImagePlaceholders(updatedContent);\n                updatedContent = await processVideoPlaceholders(updatedContent);\n\n                // Update content after processing placeholders\n                contentManager.setFileContent(absolutePath, updatedContent);\n                fileManager.replaceFile(absolutePath, updatedContent);\n\n                // Validate the file for syntax errors\n                const fileName = path.basename(absolutePath);\n                const validationResult = TargetedValidator.validateFiles([{\n                    name: fileName,\n                    content: updatedContent\n                }]);\n\n                // Send validation results to the client\n                dataStream.writeData({\n                    type: 'validation-result',\n                    content: {\n                        path: absolutePath,\n                        isValid: validationResult.isValid,\n                        summary: validationResult.summary\n                    }\n                });\n\n                // Track credit usage if available\n                if (creditUsageTracker) {\n                    creditUsageTracker.trackOperation('file_change');\n                }\n\n                // Check if this is a SQL file and run the migration if it is\n                const isSqlFile = absolutePath.toLowerCase().endsWith('.sql');\n                let sqlResult = '';\n                let migrationStatus: 'success' | 'failed' = 'failed';\n                \n                // Save original content for potential rollback\n                const originalContent = fileExists ? fileManager.getFinalContent(absolutePath) : '';\n                \n                if (isSqlFile) {\n                    try {\n                        // Use the project ID passed to the tool\n                        if (projectId) {\n                            // Execute the SQL migration\n                            const supabase = new SupabaseIntegrationProvider();\n                            const result = await supabase.executeSQL({\n                                projectId,\n                                query: updatedContent,\n                            });\n\n                            sqlResult = `SQL migration executed successfully for ${absolutePath}. Result: ${JSON.stringify(result)}`;\n                            migrationStatus = 'success';\n                            console.log(sqlResult);\n                        } else {\n                            sqlResult = `Could not execute SQL migration: No project ID provided`;\n                            console.warn(sqlResult);\n                            migrationStatus = 'failed';\n                            \n                            // Rollback the file if migration failed due to missing project ID\n                            if (fileExists) {\n                                fileManager.rollbackFile(absolutePath, originalContent);\n                                contentManager.setFileContent(absolutePath, originalContent);\n                            } else {\n                                // For new files, just remove them\n                                fileManager.rollbackFile(absolutePath);\n                                contentManager.setFileContent(absolutePath, '');\n                            }\n                            \n                            return `SQL migration failed: ${sqlResult}. Changes to ${absolutePath} were not applied and the file has been rolled back.`;\n                        }\n                    } catch (error: any) {\n                        sqlResult = `Error executing SQL migration: ${error.message}`;\n                        migrationStatus = 'failed';\n                        console.error(sqlResult);\n                        \n                        // Rollback the file if migration failed due to an error\n                        if (fileExists) {\n                            fileManager.rollbackFile(absolutePath, originalContent);\n                            contentManager.setFileContent(absolutePath, originalContent);\n                        } else {\n                            // For new files, just remove them\n                            fileManager.rollbackFile(absolutePath);\n                            contentManager.setFileContent(absolutePath, '');\n                        }\n                        \n                        return `SQL migration failed: ${sqlResult}. Changes to ${absolutePath} were not applied and the file has been rolled back.`;\n                    }\n                }\n\n                // Send the file operation to the client\n                dataStream.writeData({\n                    type: 'file-operation',\n                    content: {\n                        type: fileExists ? 'edit' : 'create',\n                        absolutePath,\n                        content: updatedContent\n                    }\n                });\n\n                if (!validationResult.isValid) {\n                    return `Updated file ${absolutePath}, but found validation issues: ${validationResult.summary}`;\n                }\n\n                // Add SQL-specific success message\n                if (absolutePath.toLowerCase().endsWith('.sql')) {\n                    if (migrationStatus === 'failed') {\n                        return `File ${fileExists ? 'Updated' : 'Created'} SQL file ${absolutePath} successfully. Migration failed: ${sqlResult}`;\n                    }\n                    return `${fileExists ? 'Updated' : 'Created'} SQL file ${absolutePath} successfully and executed the migration. Migration ran successfully.`;\n                }\n\n                return `${fileExists ? 'Updated' : 'Created'} file ${absolutePath} successfully using search and replace!`;\n            } catch (e: any) {\n                console.log('Error while applying edits', e);\n                return `Error while applying changes. Please rethink and make the changes again. Error: ${e.message}`\n            }\n        }\n    })", "startLine": 25, "endLine": 262, "type": "util", "symbols": ["editFileTool"], "score": 1, "context": "Tool function to edit files using FileLineManager and FileContentManager, applies diffs, validates edits, handles SQL migrations, and streams file operation events, directly related to file editing and state management.", "includesImports": false}, {"filePath": "src/lib/services/context-engine.ts", "content": "export class ContextEngine {\n    private files: FileItem[] = [];\n    private fileIndex: Map<string, FileItem> = new Map();\n    private dependencyGraph: Map<string, Set<string>> = new Map();\n    private fileMetadata: Map<string, FileMetadata> = new Map();\n    private codebaseEmbedding: any = null;\n    private supabaseSchema: SupabaseSchema | null = null;\n    private project: Project | null = null;\n    private actionPlan: z.infer<typeof ActionPlanSchema> | null = null;\n    private snippetCache: Map<string, CodeSnippet[]> = new Map(); // Cache for extracted snippets\n    private astCache: Map<string, any> = new Map(); // Cache for AST data\n\n    /**\n     * Initialize the context engine with the project files\n     * @param files Array of file items from the project\n     * @param project Optional project information for Supabase integration\n     */\n    constructor(files: FileItem[], project?: Project) {\n        this.files = files;\n        this.project = project || null;\n        this.buildIndex();\n        this.analyzeDependencies();\n        this.extractMetadata();\n    }\n\n    /**\n     * Build an index of files for quick lookup\n     */\n    private buildIndex(): void {\n        for (const file of this.files) {\n            this.fileIndex.set(file.name, file);\n        }\n    }\n\n    /**\n     * Check if a file should be excluded based on the excludedFiles patterns\n     * @param filePath Path of the file to check\n     * @param excludedFiles Array of file paths to exclude\n     * @returns True if the file should be excluded\n     */\n    private shouldExcludeFile(filePath: string, excludedFiles: string[]): boolean {\n        if (!excludedFiles || excludedFiles.length === 0) {\n            return false;\n        }\n\n        // Normalize the file path (remove leading slashes, etc.)\n        const normalizedPath = filePath.replace(/^\\/+/, '');\n        const fileName = path.basename(normalizedPath);\n\n        for (const pattern of excludedFiles) {\n            // Normalize the pattern\n            const normalizedPattern = pattern.replace(/^\\/+/, '');\n\n            // Case 1: Exact match (absolute path)\n            if (normalizedPath === normalizedPattern) {\n                return true;\n            }\n\n            // Case 2: Filename match (for unique filenames)\n            if (fileName === path.basename(normalizedPattern)) {\n                return true;\n            }\n\n            // Case 3: Pattern is contained in the path\n            if (normalizedPath.includes(normalizedPattern)) {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Analyze dependencies between files\n     * Builds a graph of which files import/depend on other files\n     */\n    private analyzeDependencies(): void {\n        const importRegex = /import\\s+(?:{[^}]*}|\\*\\s+as\\s+\\w+|\\w+)\\s+from\\s+['\"]([^'\"]+)['\"]/g;\n\n        for (const file of this.files) {\n            if (!file.content || !isCodeFile(file.name)) continue;\n\n            const dependencies = new Set<string>();\n            this.dependencyGraph.set(file.name, dependencies);\n\n            let match;\n            while ((match = importRegex.exec(file.content)) !== null) {\n                const importPath = match[1];\n\n                // Handle relative imports\n                if (importPath.startsWith('.')) {\n                    const resolvedPath = resolveRelativePath(file.name, importPath);\n                    if (this.fileIndex.has(resolvedPath)) {\n                        dependencies.add(resolvedPath);\n                    }\n                }\n\n                // Handle absolute imports (e.g., @/components/...)\n                if (importPath.startsWith('@/')) {\n                    const absolutePath = importPath.replace('@/', '');\n                    const possibleFiles = this.files\n                        .map(f => f.name)\n                        .filter(name => name.includes(absolutePath));\n\n                    if (possibleFiles.length > 0) {\n                        dependencies.add(possibleFiles[0]);\n                    }\n                }\n            }\n        }\n    }\n\n    /**\n     * Extract metadata from files (components, hooks, contexts, etc.)\n     */\n    private extractMetadata(): void {\n        for (const file of this.files) {\n            if (!file.content || !isCodeFile(file.name)) continue;\n\n            const metadata: FileMetadata = {\n                type: determineFileType(file.name, file.content),\n                exports: extractExports(file.content),\n                imports: extractImports(file.content),\n                lineCount: file.content.split('\\n').length,\n                hasJSX: file.content.includes('return (') || file.content.includes('return <'),\n                hasHooks: file.content.includes('useState') || file.content.includes('useEffect'),\n                hasContext: file.content.includes('createContext'),\n                hasStyles: file.content.includes('StyleSheet.create'),\n            };\n\n            this.fileMetadata.set(file.name, metadata);\n        }\n    }\n\n    /**\n     * Query the context engine for information about the codebase\n     * @param query Natural language query about the codebase\n     * @param reason\n     * @param excludedFiles Optional array of file paths to exclude from analysis\n     * @returns Comprehensive information about the relevant parts of the codebase\n     */\n    async query(query: string, reason: string, excludedFiles?: string[]): Promise<ContextResult> {\n        console.time('context-engine-query');\n        console.log(`Context engine query started for: \"${query}\"`, `Excluded file: ${(excludedFiles || []).join(',')}`);\n\n        // First, determine which files are most relevant to the query\n        // console.time('find-relevant-files');\n        // const relevantFiles = await this.findRelevantFiles(query, excludedFiles || []);\n        // console.timeEnd('find-relevant-files');\n\n        // Start parallel operations\n        const parallelOperations: any[] = [];\n\n        // 1. Process file contents\n        // const fileContentsPromise = Promise.resolve().then(() => {\n        //     console.time('process-file-contents');\n        //     // Get the content and metadata for these files\n        //     const result = relevantFiles.map(fileName => {\n        //         const file = this.fileIndex.get(fileName);\n        //         const metadata = this.fileMetadata.get(fileName);\n        //\n        //         return {\n        //             name: fileName,\n        //             content: file?.content || '',\n        //             // metadata: metadata || {type: 'unknown'},\n        //             // dependencies: Array.from(this.dependencyGraph.get(fileName) || []),\n        //             // dependents: this.findDependents(fileName),\n        //         };\n        //     });\n        //     console.timeEnd('process-file-contents');\n        //     return result;\n        // });\n        // parallelOperations.push(fileContentsPromise);\n\n        // 2. Get the overall codebase structure\n        // const codebaseStructurePromise = Promise.resolve().then(() => {\n        //     console.time('get-codebase-structure');\n        //     const structure = this.getCodebaseStructure();\n        //     console.timeEnd('get-codebase-structure');\n        //     return structure;\n        // });\n        // parallelOperations.push(codebaseStructurePromise);\n\n        // 3. Get file relationships (prepare the promise but don't execute yet)\n        // const relationshipsPromise = Promise.resolve().then(() => {\n        //     console.time('get-file-relationships');\n        //     const relationships = this.getFileRelationships(relevantFiles);\n        //     console.timeEnd('get-file-relationships');\n        //     return relationships;\n        // });\n        // parallelOperations.push(relationshipsPromise);\n\n        // 4. Extract code snippets using the two-stage approach\n        const snippetsPromise = Promise.resolve().then(async () => {\n            console.time('extract-code-snippets');\n\n            // Check if we have cached snippets for this query\n            const cachedSnippets = this.snippetCache.get(query);\n            if (cachedSnippets) {\n                console.log('Using cached snippets for query');\n                console.timeEnd('extract-code-snippets');\n                return cachedSnippets;\n            }\n\n            try {\n                // Use the TwoStageLLMContextEngine for better snippet extraction\n                const twoStageEngine = new TwoStageLLMContextEngine(this.files);\n                const snippets = await twoStageEngine.getRelevantSnippets(query, reason, excludedFiles || []);\n\n                if (snippets && snippets.length > 0) {\n                    // Cache the snippets for future use\n                    this.snippetCache.set(query, snippets);\n                    console.log(`Found ${snippets.length} snippets using two-stage approach`);\n                    console.timeEnd('extract-code-snippets');\n                    return snippets;\n                }\n            } catch (error) {\n                console.error('Error using TwoStageLLMContextEngine for snippets:', error);\n                // Fall through to the original implementation if the new approach fails\n            }\n\n            // Fallback to original regex-based extraction\n            // console.log('Falling back to regex-based snippet extraction');\n            // const allSnippets: CodeSnippet[] = [];\n            // for (const fileName of relevantFiles) {\n            //     const fileSnippets = this.extractCodeSnippets(fileName, query);\n            //     allSnippets.push(...fileSnippets);\n            // }\n\n            // Sort snippets by score and take top 10\n            // const topSnippets = allSnippets\n            //     .sort((a, b) => (b.score || 0) - (a.score || 0))\n            //     .slice(0, 10);\n\n            // Cache the snippets for future use\n            // this.snippetCache.set(query, topSnippets);\n\n            // console.timeEnd('extract-code-snippets');\n            // return topSnippets;\n        });\n        parallelOperations.push(snippetsPromise);\n\n        // Wait for all parallel operations to complete\n        console.time('parallel-operations');\n        const [snippets] = await Promise.all(parallelOperations);\n        console.timeEnd('parallel-operations');\n\n        // Performance metrics for debugging\n        console.timeEnd('context-engine-query');\n        console.log(`Context engine query completed for: \"${query}\"`);\n        // console.log(`- Found ${fileContents?.length} relevant files`);\n        console.log(`- Found ${snippets?.length} relevant code snippets`);\n\n        return {\n            query,\n            // relevantFiles: fileContents,\n            snippets,\n            // codebaseStructure,\n            // relationships,\n            // supabaseSchema,\n            // actionPlan,\n        } as ContextResult;\n    }\n\n    /**\n     * Get Supabase schema information if available\n     * @returns Supabase schema information\n     */\n    async getSupabaseSchema(): Promise<SupabaseSchema | null> {\n        if (!this.project || !this.project.supabaseProjectId) {\n            return null;\n        }\n\n        // If we already have the schema cached, return it\n        if (this.supabaseSchema) {\n            return this.supabaseSchema;\n        }\n\n        try {\n            const supabaseProvider = new SupabaseIntegrationProvider();\n            const result = await supabaseProvider.getLatestInstructionsForChat({project: this.project});\n\n            const {schema, functions, secrets, dbFunctions, triggers, rlsPolicies, storageBuckets} = result as any;\n            // console.log('result', result)\n            // Parse the instructions to extract schema information\n\n\n            // Create a structured representation of the schema\n            const parsedSchema: SupabaseSchema = {\n                tables: schema,\n                functions,\n                secrets,\n                dbFunctions,\n                triggers,\n                rlsPolicies,\n                storageBuckets\n            };\n\n            console.log('parsedSchema', parsedSchema)\n\n            this.supabaseSchema = parsedSchema;\n            return parsedSchema;\n        } catch (error) {\n            console.error('Error fetching Supabase schema:', error);\n            return null;\n        }\n    }\n\n    /**\n     * Generate an action plan based on the query and context\n     * @param query The user's query\n     * @param relevantFiles The relevant files for the query\n     * @param supabaseSchema The Supabase schema if available\n     * @returns An action plan with steps to implement the requested changes\n     */\n    async generateActionPlan(query: string, relevantFiles: any[], supabaseSchema: SupabaseSchema | null): Promise<z.infer<typeof ActionPlanSchema>> {\n        try {\n            // Define the schema for the action plan\n\n            // Prepare context for the AI\n            const fileContext = relevantFiles.map(file => {\n                return `File: ${file.name}\\nType: ${file.metadata.type}\\nExports: ${file.metadata.exports?.join(', ') || 'None'}\\nImports: ${file.metadata.imports?.slice(0, 5)?.join(', ') || 'None'}${file.metadata.imports?.length > 5 ? '...' : ''}\\n`;\n            }).join('\\n');\n\n            // Prepare Supabase context if available\n            let supabaseContext = '';\n            if (supabaseSchema) {\n                // Add tables information\n                if (supabaseSchema.tables && supabaseSchema.tables.length > 0) {\n                    supabaseContext += 'Supabase Tables:\\n' +\n                        supabaseSchema.tables.map(table => {\n                            return `Table: ${table.table_name}\\nColumns: ${table.columns.map((col: any) =>\n                                `${col.column_name} (${col.data_type})`).join(', ')}\\n`;\n                        }).join('\\n');\n                }\n\n                // Add Edge Functions information\n                if (supabaseSchema.functions && supabaseSchema.functions.length > 0) {\n                    supabaseContext += '\\nSupabase Edge Functions:\\n' +\n                        supabaseSchema.functions.map((func: any) =>\n                            `- ${func.name || func.slug || 'Unknown function'}${func.entrypoint ? ` (Entrypoint: ${func.entrypoint})` : ''}`\n                        ).join('\\n');\n                }\n\n                // Add Database Functions information\n                if (supabaseSchema.dbFunctions && supabaseSchema.dbFunctions.length > 0) {\n                    supabaseContext += '\\nSupabase Database Functions:\\n' +\n                        supabaseSchema.dbFunctions.map((func: any) =>\n                            `- ${func.name || 'Unknown function'}${func.schema ? ` (Schema: ${func.schema})` : ''}`\n                        ).join('\\n');\n                }\n\n                // Add RLS Policies information\n                if (supabaseSchema.rlsPolicies && supabaseSchema.rlsPolicies.length > 0) {\n                    supabaseContext += '\\nSupabase RLS Policies:\\n' +\n                        supabaseSchema.rlsPolicies.map((policy: any) =>\n                            `- ${policy.name || 'Unknown policy'} on ${policy.table || 'Unknown table'} (${policy.action || 'Unknown action'})`\n                        ).join('\\n');\n                }\n\n                // Add Triggers information\n                if (supabaseSchema.triggers && supabaseSchema.triggers.length > 0) {\n                    supabaseContext += '\\nSupabase Triggers:\\n' +\n                        supabaseSchema.triggers.map((trigger: any) =>\n                            `- ${trigger.name || 'Unknown trigger'} on ${trigger.table || 'Unknown table'}`\n                        ).join('\\n');\n                }\n\n                // Add Storage Buckets information\n                if (supabaseSchema.storageBuckets && supabaseSchema.storageBuckets.length > 0) {\n                    supabaseContext += '\\nSupabase Storage Buckets:\\n' +\n                        supabaseSchema.storageBuckets.map((bucket: any) =>\n                            `- ${bucket.name || 'Unknown bucket'} (Public: ${bucket.public ? 'Yes' : 'No'})${bucket.file_size_limit ? ` (Size Limit: ${bucket.file_size_limit} bytes)` : ''}`\n                        ).join('\\n');\n                }\n            }\n\n            // Generate the action plan using AI\n            const result = await generateObject({\n                model: customModel('openai/gpt-4.1'),\n                temperature: 0.1,\n                schema: ActionPlanSchema,\n                system: `You are an expert software architect and developer specializing in React Native Expo applications with Supabase integration.\n      Your task is to create a detailed action plan for implementing the requested changes based on the provided codebase context.\n      Focus on creating a practical, step-by-step plan that addresses all aspects of the request.\n      Be specific about which files need to be changed and why.\n\n      If Supabase integration is involved:\n      1. Consider all available Supabase resources (tables, edge functions, database functions, RLS policies, triggers, storage buckets)\n      2. Recommend appropriate changes to database schema when needed\n      3. Suggest Edge Functions for complex server-side operations\n      4. Include necessary RLS policy updates for proper security\n      5. Utilize Storage buckets for file uploads when appropriate\n      6. Consider database triggers for automated operations\n\n      Provide a comprehensive plan that leverages all available resources efficiently.`,\n                prompt: `Based on the following query and codebase context, create a detailed action plan for implementing the requested changes.\n\nQuery: ${query}\n\nCodebase Context:\n${fileContext}\n\n${supabaseContext ? supabaseContext : 'No Supabase integration detected.'}\n\nPlease provide a comprehensive action plan with specific steps, file changes, and considerations.`\n            });\n\n            return result.object;\n        } catch (error) {\n            console.error('Error generating action plan:', error);\n            // Return a basic action plan if generation fails\n            return {\n                summary: `Implement changes for: ${query}`,\n                complexity: \"moderate\",\n                steps: [{\n                    description: \"Analyze and implement the requested changes\",\n                    fileChanges: relevantFiles.slice(0, 3).map(file => ({\n                        path: file.name,\n                        action: \"modify\",\n                        purpose: \"Implement requested changes\",\n                        priority: \"high\"\n                    }))\n                }],\n                considerations: [\"Consider the existing codebase structure\", \"Ensure compatibility with current implementation\"],\n                supabaseChanges: supabaseSchema ? [\n                    {\n                        type: \"table\",\n                        description: \"May need database schema changes to support the new feature\",\n                        priority: \"medium\"\n                    },\n                    supabaseSchema.functions?.length > 0 ? {\n                        type: \"edge_function\",\n                        description: \"May need to update Edge Functions to support the new feature\",\n                        priority: \"medium\"\n                    } : null,\n                    supabaseSchema.storageBuckets?.length > 0 ? {\n                        type: \"storage_bucket\",\n                        description: \"May need to configure Storage for file uploads\",\n                        priority: \"medium\"\n                    } : null,\n                    supabaseSchema.rlsPolicies?.length > 0 ? {\n                        type: \"policy\",\n                        description: \"May need to update RLS policies for proper access control\",\n                        priority: \"high\"\n                    } : null\n                ].filter(Boolean) as any : []\n            };\n        }\n    }\n\n\n    /**\n     * Find files that are relevant to the given query\n     * @param query Natural language query\n     * @param excludedFiles Array of file paths to exclude from analysis\n     * @returns Array of file names that are relevant to the query\n     */\n    private async findRelevantFiles(query: string, excludedFiles: string[]): Promise<string[]> {\n        // Get the list of files that are not excluded\n        const availableFiles = this.files.filter(file => !this.shouldExcludeFile(file.name, excludedFiles));\n\n        // Use AI to determine which files are most relevant to the query\n        const result = await generateObject({\n            model: customModel('openai/gpt-4.1'),\n            temperature: 0.1,\n            schema: z.object({files: z.array(z.string())}),\n            system: `You are a code analysis expert. Your task is to identify which files in a React Native Expo project are most relevant to a specific query.\n\nYou will be given:\n1. A query about the codebase\n2. A list of files in the project with their types\n\nReturn ONLY a JSON array of the most relevant file paths, with a maximum of 5 files. Choose files that would be most helpful for understanding or implementing the query.`,\n            prompt: `Query: ${query}\n\nFiles in the project:\n${availableFiles.map(file => `${file.name} - ${this.fileMetadata.get(file.name)?.type || 'unknown'}`).join('\\n')}\n\n${excludedFiles.length > 0 ? `Note: The following files have been excluded from analysis: ${excludedFiles.join(', ')}\\n\\n` : ''}\nReturn ONLY a JSON array of the most relevant file paths (maximum 5):`,\n        });\n\n        try {\n            // console.log('result', result.object)\n            // Parse the result as a JSON array\n            const fileList = result.object.files;\n            if (Array.isArray(fileList) && fileList.length > 0) {\n                return fileList.filter(file => this.fileIndex.has(file));\n            }\n        } catch (error) {\n            console.error('Error parsing relevant files:', error);\n        }\n\n        // Fallback: return the most common file types if AI parsing fails\n        return this.files\n            .filter(file => isCodeFile(file.name) && !this.shouldExcludeFile(file.name, excludedFiles))\n            .map(file => file.name)\n            .slice(0, 5);\n    }\n\n    /**\n     * Find all files that depend on the given file\n     * @param fileName Name of the file\n     * @returns Array of file names that depend on the given file\n     */\n    private findDependents(fileName: string): string[] {\n        const dependents: string[] = [];\n\n        for (const [file, dependencies] of this.dependencyGraph.entries()) {\n            if (dependencies.has(fileName)) {\n                dependents.push(file);\n            }\n        }\n\n        return dependents;\n    }\n\n    /**\n     * Get the overall structure of the codebase\n     * @returns Object representing the codebase structure\n     */\n    private getCodebaseStructure(): CodebaseStructure {\n        const structure: CodebaseStructure = {\n            components: [],\n            screens: [],\n            contexts: [],\n            hooks: [],\n            utils: [],\n            types: [],\n            configs: [],\n        };\n\n        for (const [fileName, metadata] of this.fileMetadata.entries()) {\n            switch (metadata.type) {\n                case 'component':\n                    structure.components.push(fileName);\n                    break;\n                case 'screen':\n                    structure.screens.push(fileName);\n                    break;\n                case 'context':\n                    structure.contexts.push(fileName);\n                    break;\n                case 'hook':\n                    structure.hooks.push(fileName);\n                    break;\n                case 'util':\n                    structure.utils.push(fileName);\n                    break;\n                case 'type':\n                    structure.types.push(fileName);\n                    break;\n                case 'config':\n                    structure.configs.push(fileName);\n                    break;\n            }\n        }\n\n        return structure;\n    }\n\n    /**\n     * Get relationships between files\n     * @param fileNames Array of file names\n     * @returns Object representing relationships between files\n     */\n    private getFileRelationships(fileNames: string[]): FileRelationships {\n        const relationships: FileRelationships = {\n            imports: {},\n            exports: {},\n            dependencies: {},\n            dependents: {},\n        };\n\n        for (const fileName of fileNames) {\n            relationships.imports[fileName] = this.fileMetadata.get(fileName)?.imports || [];\n            relationships.exports[fileName] = this.fileMetadata.get(fileName)?.exports || [];\n            relationships.dependencies[fileName] = Array.from(this.dependencyGraph.get(fileName) || []);\n            relationships.dependents[fileName] = this.findDependents(fileName);\n        }\n\n        return relationships;\n    }\n}", "startLine": 106, "endLine": 690, "type": "unknown", "symbols": ["ContextEngine"], "score": 1, "context": "ContextEngine class manages project files, builds file index, analyzes dependencies, extracts metadata, and queries codebase context, directly related to project and file state management.", "includesImports": false}, {"filePath": "src/lib/services/file-storage.ts", "content": "export const fileStorage = {\n  saveFiles: (files: FileItem[], groupId: string) => {\n    try {\n      if (typeof window === 'undefined') return;\n      \n      const data: StoredFileData = {\n        files,\n        timestamp: Date.now(),\n        groupId\n      };\n      \n      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));\n    } catch (error) {\n      console.error('Error saving files to localStorage:', error);\n    }\n  },\n\n  getFiles: (groupId: string): FileItem[] | null => {\n    try {\n      if (typeof window === 'undefined') return null;\n      \n      const storedData = localStorage.getItem(STORAGE_KEY);\n      if (!storedData) return null;\n\n      const data: StoredFileData = JSON.parse(storedData);\n      \n      // Check if data is stale (older than 1 hour)\n      if (Date.now() - data.timestamp > 60 * 60 * 1000) {\n        localStorage.removeItem(STORAGE_KEY);\n        return null;\n      }\n\n      // Check if group ID matches\n      if (data.groupId !== groupId) {\n        return null;\n      }\n\n      return data.files;\n    } catch (error) {\n      console.error('Error reading files from localStorage:', error);\n      return null;\n    }\n  },\n\n  clearFiles: () => {\n    try {\n      if (typeof window === 'undefined') return;\n      localStorage.removeItem(STORAGE_KEY);\n    } catch (error) {\n      console.error('Error clearing files from localStorage:', error);\n    }\n  }\n};", "startLine": 11, "endLine": 63, "type": "unknown", "symbols": ["fileStorage"], "score": 0.9, "context": "Provides file storage operations (save, get, clear) using localStorage, relevant for file system persistence and management.", "includesImports": false}, {"filePath": "src/lib/chat/tools/get-file-contents.ts", "content": "export const getFileContents = ({\n                                    fileManager,\n                                    dataStream,\n                                    creditUsageTracker\n                                }: {\n    fileManager: FileLineManager,\n    dataStream: DataStreamWriter,\n    creditUsageTracker: CreditUsageTracker\n}) => {\n    return tool({\n        description: 'Get complete file content or list directory contents when queryCodebase snippets are insufficient. Use sparingly - only when you need to see the full file structure, all imports/exports, or complete implementation details that snippets miss.',\n        parameters: z.object({\n            path: z.string().describe(\"File or directory path to retrieve. For files: must match exactly a file from the project. For directories: use folder path like 'components' or 'screens' to list contents. Use '*' to see all top-level directories and files.\"),\n            lineRange: z.array(z.number()).length(2).optional().describe(\"Optional [start, end] line range (1-based, inclusive). Use [start, -1] for start to end of file. Only applicable for files.\"),\n            searchPattern: z.string().optional().describe(\"Optional regex pattern to search within the file. Shows matching lines with context. Only applicable for files.\"),\n            reason: z.string().describe(\"Why you need the full file content or directory listing instead of using queryCodebase snippets.\")\n        }),\n        execute: async ({path, lineRange, searchPattern, reason}: {\n            path: string,\n            lineRange?: [number, number],\n            searchPattern?: string,\n            reason: string\n        }) => {\n            try {\n                const files = fileManager.getFileItems();\n                // Normalize path - handle both /libs and libs formats\n                const normalizedPath = path.startsWith('/') ? path.slice(1) : path;\n\n                // Check if this is a directory listing request\n                const isDirectoryRequest = !normalizedPath.includes('.') || normalizedPath.endsWith('/') || normalizedPath === '*';\n\n                if (isDirectoryRequest) {\n                    // Directory listing\n                    const dirPath = normalizedPath === '*' ? '' : normalizedPath.replace(/\\/$/, ''); // Remove trailing slash\n\n                    // Find all files that start with this directory path\n                    const matchingFiles = files.filter(f => {\n                        const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                        if (dirPath === '') {\n                            // For root or * pattern, include all files\n                            return true;\n                        }\n                        return fileName.startsWith(dirPath + '/');\n                    });\n\n                    if (matchingFiles.length === 0) {\n                        // Show available top-level directories\n                        const topLevelDirs = new Set<string>();\n                        files.forEach(f => {\n                            const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                            const firstSlash = fileName.indexOf('/');\n                            if (firstSlash > 0) {\n                                topLevelDirs.add(fileName.substring(0, firstSlash));\n                            }\n                        });\n\n                        return `Directory ${path} not found. Available directories: ${Array.from(topLevelDirs).join(', ')}`;\n                    }\n\n                    // Group files by subdirectory with depth 2 support\n                    const structure = new Map<string, { files: string[], subdirs: Map<string, string[]> }>();\n\n                    matchingFiles.forEach(f => {\n                        const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                        const relativePath = fileName.substring(dirPath.length + (dirPath ? 1 : 0));\n                        const pathParts = relativePath.split('/');\n\n                        if (pathParts.length === 1) {\n                            // Direct file in this directory\n                            if (!structure.has('.')) structure.set('.', { files: [], subdirs: new Map() });\n                            structure.get('.')!.files.push(pathParts[0]);\n                        } else if (pathParts.length === 2) {\n                            // File in immediate subdirectory (depth 1)\n                            const subDir = pathParts[0];\n                            if (!structure.has(subDir)) structure.set(subDir, { files: [], subdirs: new Map() });\n                            structure.get(subDir)!.files.push(pathParts[1]);\n                        } else if (pathParts.length >= 3) {\n                            // File in nested subdirectory (depth 2+)\n                            const subDir = pathParts[0];\n                            const nestedSubDir = pathParts[1];\n                            if (!structure.has(subDir)) structure.set(subDir, { files: [], subdirs: new Map() });\n\n                            const subDirData = structure.get(subDir)!;\n                            if (!subDirData.subdirs.has(nestedSubDir)) {\n                                subDirData.subdirs.set(nestedSubDir, []);\n                            }\n                            subDirData.subdirs.get(nestedSubDir)!.push(pathParts.slice(2).join('/'));\n                        }\n                    });\n\n                    // Format directory listing\n                    const displayPath = path === '*' ? 'Project Root' : path;\n                    let listing = `Directory: ${displayPath} (${matchingFiles.length} items)\\n\\n`;\n\n                    // Show subdirectories first\n                    const sortedKeys = Array.from(structure.keys()).sort((a, b) => {\n                        if (a === '.') return 1; // Files last\n                        if (b === '.') return -1;\n                        return a.localeCompare(b);\n                    });\n\n                    sortedKeys.forEach(key => {\n                        if (key === '.') {\n                            // Direct files\n                            const directFiles = structure.get(key)!.files.sort();\n                            directFiles.forEach(file => {\n                                const fullPath = dirPath ? `${dirPath}/${file}` : file;\n                                listing += `📄 ${file} (${fullPath})\\n`;\n                            });\n                        } else {\n                            // Subdirectory\n                            const subDirData = structure.get(key)!;\n                            const subDirPath = dirPath ? `${dirPath}/${key}` : key;\n                            const totalFiles = subDirData.files.length +\n                                Array.from(subDirData.subdirs.values()).reduce((sum, files) => sum + files.length, 0);\n\n                            listing += `📁 ${key}/ (${totalFiles} files) - ${subDirPath}/\\n`;\n\n                            // Show files in this subdirectory\n                            subDirData.files.sort().forEach(file => {\n                                listing += `  📄 ${file} (${subDirPath}/${file})\\n`;\n                            });\n\n                            // Show nested subdirectories (depth 2)\n                            const sortedSubDirs = Array.from(subDirData.subdirs.keys()).sort();\n                            sortedSubDirs.forEach(nestedDir => {\n                                const nestedFiles = subDirData.subdirs.get(nestedDir)!;\n                                const nestedPath = `${subDirPath}/${nestedDir}`;\n                                listing += `  📁 ${nestedDir}/ (${nestedFiles.length} files) - ${nestedPath}/\\n`;\n\n                                // Show first few files in nested directory\n                                const filesToShow = nestedFiles.slice(0, 3);\n                                filesToShow.forEach(file => {\n                                    listing += `    📄 ${file} (${nestedPath}/${file})\\n`;\n                                });\n\n                                if (nestedFiles.length > 3) {\n                                    listing += `    ... and ${nestedFiles.length - 3} more files\\n`;\n                                }\n                            });\n                        }\n                    });\n\n                    return listing;\n                }\n\n                // File content request\n                const fileItem = files.find(f => f.name === normalizedPath || f.name === `/${normalizedPath}`);\n\n                if (!fileItem) {\n                    return `File ${path} not found. Available files: ${files.slice(0, 10).map(f => f.name).join(', ')}${files.length > 10 ? '...' : ''}`;\n                }\n\n                let content = fileItem.content;\n                const lines = content.split('\\n');\n                const totalLines = lines.length;\n\n                // Apply search pattern if specified\n                if (searchPattern) {\n                    try {\n                        const regex = new RegExp(searchPattern, 'gi');\n                        const matchingLines: string[] = [];\n                        const contextBefore = 3;\n                        const contextAfter = 3;\n\n                        let lastIncludedLine = -1;\n\n                        lines.forEach((line, index) => {\n                            if (regex.test(line)) {\n                                const startContext = Math.max(0, index - contextBefore);\n                                const endContext = Math.min(lines.length - 1, index + contextAfter);\n\n                                // Add gap indicator if there's a gap\n                                if (startContext > lastIncludedLine + 1) {\n                                    matchingLines.push('...');\n                                }\n\n                                // Add context and matching line\n                                for (let i = startContext; i <= endContext; i++) {\n                                    if (i > lastIncludedLine) {\n                                        const lineNumber = i + 1;\n                                        const prefix = i === index ? '>' : ' ';\n                                        matchingLines.push(`${prefix}${lineNumber.toString().padStart(4)}: ${lines[i]}`);\n                                        lastIncludedLine = i;\n                                    }\n                                }\n                            }\n                        });\n\n                        content = matchingLines.join('\\n');\n                    } catch (regexError) {\n                        return `Invalid regex pattern: ${searchPattern}`;\n                    }\n                } else if (lineRange) {\n                    // Apply line range if specified\n                    const [start, end] = lineRange;\n                    const startLine = Math.max(1, start) - 1; // Convert to 0-based\n                    const endLine = end === -1 ? lines.length : Math.min(end, lines.length);\n\n                    const selectedLines = lines.slice(startLine, endLine);\n                    content = selectedLines.map((line, index) => {\n                        const lineNumber = startLine + index + 1;\n                        return `${lineNumber.toString().padStart(4)}: ${line}`;\n                    }).join('\\n');\n                } else {\n                    // Return full file with line numbers\n                    content = lines.map((line, index) => {\n                        const lineNumber = index + 1;\n                        return `${lineNumber.toString().padStart(4)}: ${line}`;\n                    }).join('\\n');\n                }\n\n                creditUsageTracker.trackOperation('tool_call');\n\n                const resultLines = content.split('\\n').length;\n                const truncated = resultLines > 500;\n\n                if (truncated) {\n                    const truncatedLines = content.split('\\n').slice(0, 500);\n                    content = truncatedLines.join('\\n') + '\\n\\n<response clipped - file too long>';\n                }\n\n                return `File: ${path} (${totalLines} lines total, showing ${truncated ? '500' : resultLines} lines)\nReason: ${reason}\n${lineRange ? `Line range: ${lineRange[0]}-${lineRange[1] === -1 ? 'end' : lineRange[1]}` : ''}\n${searchPattern ? `Search pattern: ${searchPattern}` : ''}\n\n${content}`;\n\n            } catch (error: any) {\n                console.error('Error in getFileContents:', error);\n                return `Error retrieving file content: ${error.message}`;\n            }\n        }\n    });", "startLine": 7, "endLine": 241, "type": "util", "symbols": ["getFileContents"], "score": 0.9, "context": "Tool function to retrieve full file content or directory listings from FileLineManager, with options for line ranges and search patterns, relevant for file content retrieval.", "includesImports": false}, {"filePath": "src/lib/chat/tools/query-codebase.ts", "content": "export const queryCodebase = ({\n                                  projectId,\n                                  files,\n                                  fileManager,\n                                  messageId = '',\n                                  userMessage = ''\n                              }: { projectId: string, files: FileItem[], fileManager: FileLineManager, messageId?: string, userMessage?: string }) => {\n    return tool({\n        description: 'Query the codebase to understand its structure, relationships, and relevant files for a specific task. ' +\n            'Use this instead of requesting individual files. ' +\n            'You can exclude specific files from the analysis by providing an array of file paths in the excludedFiles parameter. ' +\n            'Use filePath parameter to retrieve a SINGLE file in full ONLY when absolutely necessary. You are LIMITED to ONE file per request.',\n        parameters: QueryCodebaseSchema,\n        execute: async ({query, excludedFiles, reason}: z.infer<typeof QueryCodebaseSchema>) => {\n            console.log('Query:', query)\n            console.log('Reason:', reason)\n            console.log('Excluded files:', excludedFiles?.join())\n\n            if(!query) {\n                return {\n                    result: null,\n                    message: \"One of query or filePath is required. To look at the holistic view, use query and to look at a specific file, use filePath.\"\n                }\n            }\n            // Check if this is a \"continue\" message\n            const isContinueMessage = userMessage.toLowerCase().trim().startsWith('continue');\n\n            // Get the tool tracker instance\n            const toolTracker = ToolCountTracker.getInstance();\n\n            console.log('Tool call allowed (queryCodebase):', toolTracker.shouldAllowToolCall(messageId, 'queryCodebase'))\n\n            // Check if we should allow this tool call\n            if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'queryCodebase')) {\n                return {\n                    result: null,\n                    message: \"⚠️ Tool call limit reached. You've already made multiple codebase queries. Please implement or summarize with the information you already have.\",\n                };\n            }\n\n            // Increment the tool count if we have a chat ID\n            if (messageId) {\n                toolTracker.incrementToolCount(messageId, 'queryCodebase');\n            }\n            try {\n                if (!files || files.length === 0) {\n                    throw new Error('No files available for context engine');\n                }\n\n                // Get project information if available\n                let project: Project | null = null;\n                if (projectId) {\n                    try {\n                        project = await getProjectById({id: projectId});\n                    } catch (error) {\n                        console.warn('Could not fetch project information:', error);\n                        // Continue without project info\n                    }\n                }\n\n                if (!project) {\n                    throw new Error('No project information available');\n                }\n\n                // Check if we're requesting a specific file in full\n                // Note: filePath takes precedence over semantic query when both are provided\n                // if (filePath) {\n                //     console.log('Specific file requested:', filePath);\n                //\n                //     // Find the requested file\n                //     const fileItem = fileManager.getFileItems().find(file => file.name === filePath.trim());\n                //\n                //     if (!fileItem) {\n                //         throw new Error(`The requested file was not found: ${filePath}`);\n                //     }\n                //\n                //     const fileItems = [fileItem];\n                //\n                //     // Create snippets for each file\n                //     const snippets = fileItems.map(fileItem => ({\n                //         filePath: fileItem.name,\n                //         content: fileItem.content || '',\n                //         startLine: 1,\n                //         endLine: (fileItem.content || '').split('\\n').length,\n                //         type: 'unknown',\n                //         symbols: [],\n                //         score: 1.0\n                //     }));\n                //\n                //     // Create a simplified result with just the full file contents\n                //     const result = {\n                //         query,\n                //         snippets,\n                //         codebaseStructure: { components: [], screens: [], contexts: [], hooks: [], utils: [], types: [], configs: [] },\n                //         relationships: { imports: {}, exports: {}, dependencies: {}, dependents: {} }\n                //     };\n                //\n                //     let message = `Showing full content of the requested file: ${fileItems[0].name}`;\n                //\n                //     return {\n                //         result,\n                //         message,\n                //         toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')\n                //     };\n                // }\n                \n                // Initialize the context engine with the current files and project\n                const contextEngine = new ContextEngine(fileManager.getFileItems(), project);\n\n                // Default excluded files for Expo Snack projects if not provided\n                const defaultExcludedFiles = [\n                    // Large asset files\n                    'assets/',\n                    '.mp3',\n                    '.mp4',\n                    '.jpg',\n                    '.png',\n                    '.gif',\n                    // Generated type files\n                    'types/generated',\n                    // These don't exist in Expo Snack but kept as fallbacks\n                    'node_modules',\n                    'dist'\n                ];\n\n                // Combine user-provided excludedFiles with defaults\n                const combinedExcludedFiles = excludedFiles ?\n                    [...excludedFiles, ...defaultExcludedFiles] :\n                    defaultExcludedFiles;\n\n                // Query the context engine\n                const result = await contextEngine.query(query, reason, combinedExcludedFiles);\n\n                // Prepare a more informative response\n                let message = `Found ${result.snippets?.length} snippets for your query.`;\n\n                // Add information about excluded files if any\n                if (excludedFiles && excludedFiles.length > 0) {\n                    message += `\\n${excludedFiles.length} files were excluded from the analysis.`;\n                }\n\n                if (result.actionPlan) {\n                    message += `\\n\\nAction Plan: ${result.actionPlan.summary}\\nComplexity: ${result.actionPlan.complexity}\\nSteps: ${result.actionPlan.steps.length}`;\n                }\n\n                // if (result.supabaseSchema) {\n                //     message += `\\n\\nSupabase integration detected with ${result.supabaseSchema.tables.length} tables.`;\n                // }\n\n                return {\n                    result,\n                    message,\n                    toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')\n                };\n            } catch (error) {\n                console.error('Context engine tool error:', error);\n                throw new Error(`Failed to query context engine: ${error instanceof Error ? error.message : 'Unknown error'}`);\n            }\n        },\n    });\n}", "startLine": 23, "endLine": 183, "type": "util", "symbols": ["queryCodebase"], "score": 0.9, "context": "Tool to query the codebase using ContextEngine, which manages files and project context, relevant for understanding project and file state management.", "includesImports": false}, {"filePath": "src/lib/chat/tools/create-file.tool.ts", "content": "export const createFileTool = ({\n                            fileManager,\n                            processImagePlaceholders,\n                            processVideoPlaceholders,\n                            dataStream\n                        }: {\n    fileManager: FileLineManager,\n    dataStream: DataStreamWriter,\n    processImagePlaceholders: (text: string) => Promise<string>,\n    processVideoPlaceholders: (text: string) => Promise<string>\n\n}) => {\n    return tool({\n        description: 'Create a new file',\n        parameters: fileCreateSchema,\n        execute: async ({absolutePath, content}) => {\n\n            // await updateFile({\n            //     files,\n            //     snackBaseId,\n            //     snackId,\n            //     absolutePath,\n            //     contents: content\n            // })\n\n            fileManager.addFiles(absolutePath, content);\n\n            content = await processImagePlaceholders(content);\n            content = await processVideoPlaceholders(content);\n\n            dataStream.writeData({\n                type: 'file-operation',\n                content: {\n                    type: 'create',\n                    absolutePath,\n                    content\n                }\n            });\n            return `Added file ${absolutePath} successfully!`;\n        }\n    })\n}", "startLine": 5, "endLine": 46, "type": "util", "symbols": ["createFileTool"], "score": 0.8, "context": "Tool function to create new files using FileLineManager and stream file operation events, relevant for file creation operations.", "includesImports": false}, {"filePath": "src/lib/services/two-stage-context-engine.ts", "content": "export class TwoStageLLMContextEngine {\n  private files: FileItem[] = [];\n  private fileIndex: Map<string, FileItem> = new Map();\n\n  /**\n   * Initialize with project files\n   */\n  constructor(files: FileItem[]) {\n    this.files = files;\n    this.buildIndex();\n  }\n\n  /**\n   * Build an index of files for quick lookup\n   */\n  private buildIndex(): void {\n    for (const file of this.files) {\n      this.fileIndex.set(file.name, file);\n    }\n  }\n\n  /**\n   * Get a minimal structural representation of the codebase\n   * This provides enough context for the first LLM while keeping token usage low\n   */\n  private getCodebaseStructure(): string {\n    // Build a map of imports for quick lookup\n    const importMap = new Map<string, Set<string>>();\n\n    // First pass: extract imports from all files\n    this.files\n      .forEach(file => {\n        const imports = this.extractImports(file.content || \"\");\n        imports.forEach(importPath => {\n          // Resolve import path to actual file name\n          const resolvedPath = this.resolveImportPath(importPath, file.name);\n\n          if (resolvedPath) {\n            // Add to the import map\n            if (!importMap.has(resolvedPath)) {\n              importMap.set(resolvedPath, new Set());\n            }\n            importMap.get(resolvedPath)?.add(file.name);\n          }\n        });\n      });\n\n    // Second pass: create the structure with import relationships\n    return this.files\n      .map(file => {\n        const fileType = this.determineFileType(file.name, file.content || \"\");\n        const exports = this.extractExports(file.content || \"\");\n\n        // Find files that import this file (limited to 3 for brevity)\n        const importedBy = Array.from(importMap.get(file.name) || []).slice(0, 3);\n\n        let result = `${file.name} - ${fileType}`;\n\n        if (exports.length > 0) {\n          result += ` - Exports: ${exports.join(\", \")}`;\n        }\n\n        if (importedBy.length > 0) {\n          result += ` - Used by: ${importedBy.join(\", \")}`;\n        }\n\n        return result;\n      })\n      .join(\"\\n\");\n  }\n\n  /**\n   * First stage: Find relevant files for a query\n   */\n  async findRelevantFiles(query: string, reason: string, excludedFiles: string[] = []): Promise<{files: string[], reasoning: string }> {\n    console.time('find-relevant-files');\n\n    // Get a compact representation of the codebase structure\n    const codebaseStructure = this.getCodebaseStructure();\n\n    // console.log('codebaseStructure', codebaseStructure)\n\n    // Use LLM to identify relevant files\n    const result = await generateObject({\n      model: customModel('openai/gpt-4.1-mini'), // Using a smaller model to reduce costs\n      temperature: 0.1,\n      schema: z.object({\n        files: z.array(z.string()),\n        reasoning: z.string().describe(\"Explanation of why these files were selected\")\n      }),\n      system: `You are a code analysis expert. Your task is to identify which files in a React Native Expo project are most relevant to a specific query.\n\nYou will be given:\n1. A query about the codebase\n2. A structured representation of files in the project with their types and exports\n\nReturn a JSON object with:\n1. An array of the most relevant file paths (maximum 20)\n2. Your reasoning for selecting these files\n̄3. AVOID selecting files that are not referenced anywhere (Dead code) unless relevant to the query or asked for it\n4. Make sure to select files to give a full picture and not just direct relevance, use relevance scores properly\n\n\nChoose files that would be most helpful for understanding or implementing the query.`,\n      prompt: `Query: ${query}\n      \nReason: ${reason}\n\nFiles in the project:\n${codebaseStructure}\n\n${excludedFiles.length > 0 ? `Note: The following files have been excluded from analysis: ${excludedFiles.join(', ')}\\n\\n` : ''}\nReturn the most relevant file paths (maximum 20) and your reasoning:`,\n    });\n\n    console.timeEnd('find-relevant-files');\n    console.log('Relevant files', result.object.files.join(','))\n    console.log(`Selected files reasoning: ${result.object.reasoning}`);\n\n    // Filter out any files that don't exist in our index\n    return {files: result.object.files.filter(file => this.fileIndex.has(file)), reasoning: result.object.reasoning};\n  }\n\n  /**\n   * Second stage: Identify relevant snippets within files\n   */\n  async identifyRelevantSnippets(query: string, originalReason: string, relevantFiles: string[], previousStageReason: string): Promise<SnippetIdentification[]> {\n    console.time('identify-snippets');\n\n\n    // Prepare file contents with line numbers\n    const fileContents = relevantFiles.map(fileName => {\n      const file = this.fileIndex.get(fileName);\n      const content = file?.content || \"\";\n\n      // Add line numbers to help the LLM identify specific ranges\n      // Format with consistent padding to make line numbers stand out\n      const lines = content.split(\"\\n\");\n      const maxLineNumberWidth = String(lines.length).length;\n      const numberedContent = lines.map((line, i) => {\n        const lineNumber = i + 1;\n        const paddedLineNumber = String(lineNumber).padStart(maxLineNumberWidth, ' ');\n        return `${paddedLineNumber}: ${line}`;\n      }).join(\"\\n\");\n\n      return {\n        name: fileName,\n        content: numberedContent,\n        lineCount: lines.length\n      };\n    });\n\n    // Use LLM to identify relevant snippets within these files\n    const result = await generateObject({\n      model: customModel('openai/gpt-4.1-mini'), // Using a more capable model for this task\n      temperature: 0.1,\n      schema: z.object({\n        snippets: z.array(z.object({\n          fileName: z.string(),\n          startLine: z.number(),\n          endLine: z.number(),\n          snippetType: z.string(),\n          snippetName: z.string(),\n          relevanceScore: z.number().min(0).max(1),\n          reasoning: z.string()\n        }))\n      }),\n      system: `You are a code analysis expert. Your task is to identify the MINIMAL relevant code snippets within files for a specific query.\n\nReturn the exact line numbers for each snippet, along with metadata about the snippet.\n\nGuidelines for MINIMAL context extraction:\n1. Extract ONLY the specific lines directly relevant to the query - avoid including entire files or anything extra at all\n2. For imports, include ONLY the imports directly related to the query and is needed to reliable editing\n2. For style, include ONLY the styles directly related to the query and is needed to reliable editing\n3. For components, include ONLY the component declaration, props, and relevant JSX elements - NOT the entire implementation\n4. For functions, include ONLY the signature and critical logic - NOT entire function bodies\n5. Assign a relevance score from 0.0 to 1.0 based on how directly the snippet addresses the query. \n6. Keep snippets as SHORT as possible while maintaining necessary context\n7. Pay close attention to the line numbers at the beginning of each line (formatted as \"NUMBER: code\")\n8. For React errors, focus on component declarations, imports/exports, and JSX return statements\n9. NEVER include style definitions unless they are directly relevant to the query\n10. NEVER include helper functions unless they are directly relevant to the query\n11. When specific line numbers are requested, return only those line numbers\n\nToken efficiency guidelines:\n1. Maximum 30 lines per snippet unless absolutely necessary\n4. Omit implementation details of methods unless directly relevant\n5. For UI issues, include only the relevant JSX elements, not entire render methods\n6. When multiple similar components exist, include only one representative example\\``,\n      prompt: `Query: ${query}\n\nI need to identify the MINIMAL relevant code snippets in these files. For each relevant code block, provide:\n1. The file name\n2. Start and end line numbers (inclusive) - use the exact line numbers shown at the beginning of each line\n3. Type of snippet (function, component, hook, type, etc.)\n4. Name of the snippet (function name, component name, etc.)\n5. Relevance score (0.0 to 1.0)\n6. Brief reasoning for why this snippet is relevant\n\nReliable editing:\n- Please include import and styles if they need are needed to add imports\n- Include just enough context for clear understanding and editing \n\nCRITICAL TOKEN EFFICIENCY GUIDELINES:\n- Extract ONLY the specific lines directly relevant to the query\n- Each line in the files is prefixed with its line number (e.g., \"42: const foo = bar;\"). Use these exact line numbers.\n- For imports, include ONLY those directly related to the query\n- For components, include ONLY the declaration, props, and relevant JSX - NOT entire implementations\n- NEVER include style definitions unless directly relevant to the query\n- Keep snippets to a MAXIMUM of 30 lines when possible\n- For React errors, focus ONLY on component declarations, imports/exports, and JSX return statements\n- AVOID including entire component implementations - be extremely selective\n\nReasoning from the user/llm issuing the original query on why they want the result. This gets fed to the first engine:\n${originalReason}\n\nReasoning from the first engine on why each file was selected. Use the reasoning to understand the whole flow and to select snippets accordingly:\n${previousStageReason}\n\nFiles to analyze:\n${fileContents.map(file => `\n=== ${file.name} (${file.lineCount} lines) ===\n${file.content}\n`).join(\"\\n\\n\")}\n\nReturn an array of the most relevant code snippets with their exact line numbers and metadata:`,\n    });\n\n    console.timeEnd('identify-snippets');\n    return result.object.snippets;\n  }\n\n  /**\n   * Smart truncation to fit within line budget while preserving understanding context\n   */\n  smartTruncateSnippets(snippets: CodeSnippet[], maxLines: number = 600): {truncatedSnippets: CodeSnippet[], additionalFromTruncation: {fileName: string, reason: string, suggestedQuery: string}[]} {\n    // Categorize snippets by type for strategic selection\n    const implementation = snippets.filter(s => (s.score || 0) >= 0.9);\n    const usage = snippets.filter(s => (s.score || 0) >= 0.7 && (s.score || 0) < 0.9);\n    const context = snippets.filter(s => (s.score || 0) < 0.7);\n\n    let currentLines = 0;\n    const truncatedSnippets: CodeSnippet[] = [];\n    const additionalFromTruncation: {fileName: string, reason: string, suggestedQuery: string}[] = [];\n\n    // Strategy: Always include implementation, then usage, then context within budget\n    const prioritizedSnippets = [\n      ...orderBy(implementation, ['score'], ['desc']).slice(0, 2), // Max 2 implementation snippets\n      ...orderBy(usage, ['score'], ['desc']).slice(0, 2), // Max 2 usage examples\n      ...orderBy(context, ['score'], ['desc']).slice(0, 1)  // Max 1 context snippet\n    ];\n\n    for (const snippet of prioritizedSnippets) {\n      const snippetLines = snippet.content.split('\\n').length;\n\n      if (currentLines + snippetLines <= maxLines) {\n        truncatedSnippets.push(snippet);\n        currentLines += snippetLines;\n      } else {\n        // Add to additional files instead of truncating content\n        additionalFromTruncation.push({\n          fileName: snippet.filePath,\n          reason: `Relevant but truncated due to 1000-line budget (relevance: ${(snippet.score || 0).toFixed(2)}, ${snippetLines} lines)`,\n          suggestedQuery: `Show ${snippet.symbols.join(', ')} in ${snippet.filePath}`\n        });\n      }\n    }\n\n    // Add any remaining snippets to additional files\n    const remainingSnippets = snippets.filter(s => !truncatedSnippets.includes(s) && !additionalFromTruncation.some(a => a.fileName === s.filePath));\n    for (const snippet of remainingSnippets) {\n      additionalFromTruncation.push({\n        fileName: snippet.filePath,\n        reason: `Additional context available (relevance: ${(snippet.score || 0).toFixed(2)})`,\n        suggestedQuery: `Show ${snippet.symbols.join(', ')} in ${snippet.filePath}`\n      });\n    }\n\n    console.log(`Smart truncation: kept ${truncatedSnippets.length}/${snippets.length} snippets within ${maxLines} line budget (${currentLines} lines total)`);\n    console.log(`  - Implementation: ${implementation.length} found, ${truncatedSnippets.filter(s => (s.score || 0) >= 0.9).length} kept`);\n    console.log(`  - Usage: ${usage.length} found, ${truncatedSnippets.filter(s => (s.score || 0) >= 0.7 && (s.score || 0) < 0.9).length} kept`);\n    console.log(`  - Context: ${context.length} found, ${truncatedSnippets.filter(s => (s.score || 0) < 0.7).length} kept`);\n\n    return {truncatedSnippets, additionalFromTruncation};\n  }\n\n  /**\n   * Extract actual code snippets based on line numbers\n   */\n  extractSnippets(snippetIdentifications: SnippetIdentification[]): CodeSnippet[] {\n    // Group snippets by file to avoid duplicate processing\n    const snippetsByFile = new Map<string, SnippetIdentification[]>();\n\n    for (const snippet of snippetIdentifications) {\n      if (!snippetsByFile.has(snippet.fileName)) {\n        snippetsByFile.set(snippet.fileName, []);\n      }\n      snippetsByFile.get(snippet.fileName)?.push(snippet);\n    }\n\n    const results: CodeSnippet[] = [];\n\n    // Process each file's snippets\n    for (const [fileName, fileSnippets] of snippetsByFile.entries()) {\n      const file = this.fileIndex.get(fileName);\n      if (!file || !file.content) continue;\n\n      const lines = file.content.split(\"\\n\");\n\n      // Find import statements (usually at the top of the file)\n      const importEndLine = this.findImportEndLine(lines);\n      const hasImports = importEndLine > 0;\n\n      // Process each snippet in the file\n      for (const identification of fileSnippets) {\n        // Ensure line numbers are within bounds\n        const startLine = Math.max(1, Math.min(identification.startLine, lines.length));\n        const endLine = Math.max(startLine, Math.min(identification.endLine, lines.length));\n\n        // Removing as this is causing the llm issues to understand\n        const shouldIncludeImports = false;\n\n        // Determine if we should include imports\n        // const shouldIncludeImports = hasImports &&\n        //     identification.snippetType.toLowerCase() !== 'import' &&\n        //     startLine > importEndLine;\n\n        // Extract the snippet content with imports if needed\n        let snippetLines: string[];\n        let actualStartLine: number;\n\n        if (shouldIncludeImports) {\n          // Include imports and the actual snippet\n          const importLines = lines.slice(0, importEndLine);\n          const codeLines = lines.slice(startLine - 1, endLine);\n\n          // Add a separator between imports and code\n          snippetLines = [...importLines, '', '// Main snippet', ...codeLines];\n          actualStartLine = 1; // Starting from the beginning of the file\n        } else {\n          // Just include the snippet itself\n          snippetLines = lines.slice(startLine - 1, endLine);\n          actualStartLine = startLine;\n        }\n\n        const content = snippetLines.join(\"\\n\");\n\n        // Log the extraction for debugging\n        console.log(`Extracting snippet from ${identification.fileName}:`);\n        console.log(`  Lines ${startLine}-${endLine} (${snippetLines.length} lines)`);\n        console.log(`  Type: ${identification.snippetType}, Name: ${identification.snippetName}`);\n        console.log(`  Relevance: ${identification.relevanceScore.toFixed(2)}`);\n        if (shouldIncludeImports) {\n          console.log(`  Including imports from lines 1-${importEndLine}`);\n        }\n\n        results.push({\n          filePath: identification.fileName,\n          content,\n          startLine: actualStartLine,\n          endLine: shouldIncludeImports ? endLine + (importEndLine + 2) : endLine, // Adjust for added separator lines\n          type: this.mapSnippetType(identification.snippetType),\n          symbols: [identification.snippetName],\n          score: identification.relevanceScore,\n          context: identification.reasoning,\n          includesImports: shouldIncludeImports\n        } as CodeSnippet);\n      }\n    }\n\n    console.log('Total lines', results.reduce((acc, a) => {\n      return acc + ((a.endLine - a.startLine)) + 1;\n    }, 0));\n\n    return orderBy(results, ['score'], ['desc']);\n  }\n\n  /**\n   * Find the line where imports end in a file\n   */\n  private findImportEndLine(lines: string[]): number {\n    let lastImportLine = 0;\n\n    for (let i = 0; i < Math.min(lines.length, 50); i++) { // Only check first 50 lines\n      const line = lines[i].trim();\n      if (line.startsWith('import ')) {\n        lastImportLine = i + 1; // +1 because line numbers are 1-based\n      }\n    }\n\n    return lastImportLine;\n  }\n\n  /**\n   * Main method to get relevant snippets for a query\n   */\n  async getRelevantSnippets(query: string, reason: string, excludedFiles: string[] = []): Promise<CodeSnippet[]> {\n    // Stage 1: Find relevant files\n    const {files: relevantFiles, reasoning} = await this.findRelevantFiles(query, reason, excludedFiles);\n\n    if (relevantFiles.length === 0) {\n      console.log(\"No relevant files found\");\n      return [];\n    }\n\n    // Stage 2: Identify relevant snippets within those files\n    const snippetIdentifications = await this.identifyRelevantSnippets(query, reason, relevantFiles, reasoning);\n\n    // Stage 3: Extract the actual snippets\n    return this.extractSnippets(snippetIdentifications);\n  }\n\n  /**\n   * Helper methods\n   */\n  private isCodeFile(fileName: string): boolean {\n    const ext = path.extname(fileName).toLowerCase();\n    return ['.ts', '.tsx', '.js', '.jsx'].includes(ext);\n  }\n\n  private determineFileType(fileName: string, content: string): string {\n    const name = fileName.toLowerCase();\n\n    if (name.includes('screen') || name.includes('page')) {\n      return 'screen';\n    }\n\n    if (name.includes('context')) {\n      return 'context';\n    }\n\n    if (name.includes('hook') || (content && content.includes('use') && content.includes('return {'))) {\n      return 'hook';\n    }\n\n    if (name.includes('util') || name.includes('helper')) {\n      return 'util';\n    }\n\n    if (name.includes('type') || name.includes('interface') || name.includes('.d.ts')) {\n      return 'type';\n    }\n\n    if (name.includes('config') || name.includes('setup')) {\n      return 'config';\n    }\n\n    // Default to component for TSX/JSX files\n    if (name.endsWith('.tsx') || name.endsWith('.jsx')) {\n      return 'component';\n    }\n\n    return 'unknown';\n  }\n\n  private extractExports(content: string): string[] {\n    const exports: string[] = [];\n    const exportRegex = /export\\s+(const|function|class|interface|type|default)\\s+(\\w+)/g;\n\n    let match;\n    while ((match = exportRegex.exec(content)) !== null) {\n      exports.push(match[2]);\n    }\n\n    return exports;\n  }\n\n  private extractImports(content: string): string[] {\n    const imports: string[] = [];\n    // Fixed regex to handle all import patterns including relative paths, scoped packages, and complex paths\n    const importRegex = /import\\s+(?:{[^}]*}|\\*\\s+as\\s+\\w+|\\w+)\\s+from\\s+['\"]([^'\"]+)['\"];?/g;\n\n    let match;\n    while ((match = importRegex.exec(content)) !== null) {\n      const importPath = match[1];\n      imports.push(importPath);\n    }\n\n    return imports;\n  }\n\n  /**\n   * Resolve import path to actual file name in the project\n   */\n  private resolveImportPath(importPath: string, currentFile: string): string | null {\n    // Skip node_modules and external packages (anything that doesn't start with ./ or ../)\n    if (!importPath.startsWith('./') && !importPath.startsWith('../')) {\n      return null;\n    }\n\n    // Handle relative paths only\n    const currentDir = path.dirname(currentFile);\n    let resolvedPath = path.join(currentDir, importPath);\n    // Normalize path separators and remove leading ./\n    resolvedPath = resolvedPath.replace(/\\\\/g, '/').replace(/^\\.\\//, '');\n\n    // Try to find the actual file with common extensions\n    const possibleExtensions = ['.ts', '.tsx', '.js', '.jsx', '/index.ts', '/index.tsx', '/index.js', '/index.jsx'];\n\n    for (const ext of possibleExtensions) {\n      const candidatePath = resolvedPath + ext;\n      if (this.fileIndex.has(candidatePath)) {\n        return candidatePath;\n      }\n    }\n\n    // If no extension worked, try exact match\n    if (this.fileIndex.has(resolvedPath)) {\n      return resolvedPath;\n    }\n\n    return null;\n  }\n\n  private mapSnippetType(type: string): 'component' | 'hook' | 'screen' | 'util' | 'type' | 'context' | 'config' | 'unknown' {\n    const normalizedType = type.toLowerCase();\n\n    if (normalizedType.includes('component')) return 'component';\n    if (normalizedType.includes('hook')) return 'hook';\n    if (normalizedType.includes('screen')) return 'screen';\n    if (normalizedType.includes('util') || normalizedType.includes('function')) return 'util';\n    if (normalizedType.includes('type') || normalizedType.includes('interface')) return 'type';\n    if (normalizedType.includes('context') || normalizedType.includes('provider')) return 'context';\n    if (normalizedType.includes('config')) return 'config';\n\n    return 'unknown';\n  }\n}", "startLine": 27, "endLine": 555, "type": "unknown", "symbols": ["TwoStageLLMContextEngine"], "score": 0.8, "context": "Two-stage context engine for identifying relevant files and snippets in the project, relevant for project and file state management.", "includesImports": false}, {"filePath": "src/lib/terminal/helpers.ts", "content": "export const PROJECT_DIR = '/project';\n\nexport const prepareProject = async (fileState: FileState, options: {\n    projectId: string,\n    title: string,\n    slug: string,\n    bundleIdentifier: string,\n    packageName: string,\n    scheme: string,\n    versionCode: number,\n    versionName: string\n}) => {\n    const {files, dependencies} = fileState;\n    if (!process.env.GITHUB_PAT) {\n        throw new Error('GitHub Personal Access Token not configured');\n    }\n\n    // Fetch all template files\n    console.log('Fetching all template files...');\n    const templateFiles = await fetchAllRepoContents('base-v1');\n    console.log('Found files:', Object.keys(templateFiles).join(', '));\n\n    // Create files map starting with template files\n    const filesMap: Record<string, string | Buffer<ArrayBufferLike>> = {};\n\n    // Add template files with correct paths\n    for (const [path, content] of Object.entries(templateFiles)) {\n        filesMap[path] = content;\n    }\n\n    // Add user files\n    (files as FileItem[]).filter(file => !!file.name).forEach(file => {\n        filesMap[`src/${file.name}`] = file.content;\n    });\n\n    // Update package.json with dependencies\n    if (!filesMap['package.json']) {\n        throw new Error('package.json not found in template');\n    }\n\n    const {title, slug, bundleIdentifier, packageName, scheme, versionName, versionCode} = options;\n    try {\n        const appJSON = JSON.parse(filesMap['app.json'] as string);\n        console.log('appJSON', appJSON)\n        if(appJSON?.expo) {\n            appJSON.expo.name = title;\n            appJSON.expo.slug = slug;\n            appJSON.expo.version = versionName || \"1.0.0\";\n            appJSON.expo.scheme = scheme || slug;\n            if(!appJSON.expo.ios) {\n                appJSON.expo.ios = {};\n            }\n            appJSON.expo.ios.bundleIdentifier = bundleIdentifier;\n            appJSON.expo.ios.buildNumber = versionName || \"1.0.0\";\n            if(!appJSON.expo.android) {\n                appJSON.expo.android = {};\n            }\n            appJSON.expo.android.package = packageName;\n        }\n        filesMap['app.json'] = JSON.stringify(appJSON, null, 2);\n    } catch (error) {\n        console.error('Error parsing app.json:', filesMap['app.json']);\n        throw new Error('Invalid app.json in template');\n    }\n\n    try {\n        const packageJson = JSON.parse(filesMap['package.json'] as string);\n        packageJson.name = options.title.toLowerCase().replace(/\\s/g, '-');\n        packageJson.version = versionName || \"1.0.0\";\n        packageJson.dependencies = {\n            ...packageJson.dependencies,\n            ...Object.entries(dependencies as Record<string, { version: \"*\" }>)\n                .reduce((acc, [dependency, version]) => {\n                    // Remove the partial packages to ensure it doesn't cause build issues\n                    if(['zustand/middleware', 'date-fns/locale'].includes(dependency)) {\n                        return acc;\n                    }\n                    acc[dependency] = version.version || \"*\";\n                    return acc;\n                }, {} as { [key: string]: string })\n        };\n        filesMap['package.json'] = JSON.stringify(packageJson, null, 2);\n    } catch (error) {\n        console.error('Error parsing package.json:', filesMap['package.json']);\n        throw new Error('Invalid package.json in template');\n    }\n\n    // Create manifest\n    const magicallyManifest = {\n        version: versionName || \"1.0.0\",\n        projectId: options.projectId,\n        projectName: options.title,\n        fileManifest: (files as FileItem[]).map(file => {\n            if (file.type === \"file\") {\n                const hash = crypto\n                    .createHash('sha256')\n                    .update(file.content)\n                    .digest('hex');\n\n                return {\n                    path: file.name,\n                    language: file.language,\n                    hash: hash\n                };\n            }\n            return null;\n        }).filter(f => !!f)\n    };\n\n    // Add manifest to files\n    filesMap['magically.json'] = JSON.stringify(magicallyManifest, null, 2);\n\n    return filesMap;\n}\n\n\n\n// Helper function to create a zip buffer from files\nexport async function createZipFromFiles(files: Record<string, string | Buffer>): Promise<Buffer> {\n    return new Promise((resolve, reject) => {\n        const archive = archiver('zip', {zlib: {level: 9}});\n        const chunks: any[] = [];\n\n        archive.on('error', (err) => reject(err));\n        archive.on('data', (chunk) => chunks.push(chunk));\n        archive.on('end', () => resolve(Buffer.concat(chunks)));\n\n        // Add each file to the archive\n        for (const [filePath, content] of Object.entries(files)) {\n            archive.append(content, {name: filePath});\n        }\n\n        archive.finalize();\n    });\n}\n\n// Helper function to recursively list and fetch all repository contents\nexport async function fetchAllRepoContents(path: string = ''): Promise<Record<string, string | Buffer<ArrayBufferLike>>> {\n    if (!process.env.GITHUB_PAT) {\n        throw new Error('GitHub Personal Access Token not configured');\n    }\n\n    const response = await fetch(\n        `https://api.github.com/repos/magically-life/magically-templates/contents/${path}`,\n        {\n            headers: {\n                'Accept': 'application/vnd.github.v3+json',\n                'Authorization': `token ${process.env.GITHUB_PAT}`,\n                'X-GitHub-Api-Version': '2022-11-28'\n            }\n        }\n    );\n\n    if (!response.ok) {\n        throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);\n    }\n\n    const contents = await response.json();\n    const files: Record<string, string | Buffer<ArrayBufferLike>> = {};\n\n    for (const item of contents) {\n        if (item.type === 'file') {\n            const fileResponse = await fetch(item.download_url, {\n                headers: {\n                    'Authorization': `token ${process.env.GITHUB_PAT}`,\n                }\n            });\n            if (!fileResponse.ok) {\n                throw new Error(`Failed to fetch file ${item.path}: ${fileResponse.status} ${fileResponse.statusText}`);\n            }\n            // Remove the base-v1/ prefix from paths\n            const relativePath = item.path.replace('base-v1/', '');\n            // Check if file is binary (images, etc.)\n            const isBinary = /\\.(png|jpg|jpeg|gif|ico|webp|bmp|tiff|svg)$/i.test(item.path);\n\n            if (isBinary) {\n                // Handle binary files as ArrayBuffer\n                const arrayBuffer = await fileResponse.arrayBuffer();\n                files[relativePath] = Buffer.from(arrayBuffer);\n            } else {\n                // Handle text files as before\n                files[relativePath] = await fileResponse.text();\n            }\n        } else if (item.type === 'dir') {\n            const subFiles = await fetchAllRepoContents(item.path);\n            Object.assign(files, subFiles);\n        }\n    }\n\n    return files;\n}\n\n/**\n * Helper function to create app.json for EAS build\n */\nexport function createAppJson(appName: string = 'Magically App', bundleId: string = 'life.magically.app', version: string = '1.0.0', buildNumber: string = '1') {\n    return JSON.stringify({\n        expo: {\n            name: appName,\n            slug: appName.toLowerCase().replace(/\\s+/g, '-'),\n            version: version,\n            orientation: 'portrait',\n            icon: './assets/icon.png',\n            userInterfaceStyle: 'light',\n            splash: {\n                image: './assets/splash.png',\n                resizeMode: 'contain',\n                backgroundColor: '#ffffff'\n            },\n            assetBundlePatterns: [\n                '**/*'\n            ],\n            ios: {\n                supportsTablet: true,\n                bundleIdentifier: bundleId,\n                buildNumber: buildNumber\n            },\n            android: {\n                adaptiveIcon: {\n                    foregroundImage: './assets/adaptive-icon.png',\n                    backgroundColor: '#ffffff'\n                },\n                package: bundleId\n            },\n            web: {\n                favicon: './assets/favicon.png'\n            },\n            runtimeVersion: {\n                policy: 'appVersion'\n            }\n        }\n    }, null, 2);\n}\n\n/**\n * Helper function to create eas.json for EAS Build configuration\n */\nexport function createEasJson() {\n    return JSON.stringify({\n        cli: {\n            version: '>= 5.4.0'\n        },\n        build: {\n            \"base\": {\n                \"android\": {\n                    \"image\": \"latest\",\n                    resourceClass: 'large',\n                    \"buildType\": \"apk\"\n                },\n            },\n            development: {\n                developmentClient: true,\n                distribution: 'internal',\n                ios: {\n                    resourceClass: 'large'\n                }\n            },\n            preview: {\n                distribution: 'internal',\n                ios: {\n                    resourceClass: 'large',\n                    simulator: true\n                }\n            },\n            production: {\n                ios: {\n                    resourceClass: 'large'\n                }\n            }\n        },\n        submit: {\n            production: {\n                ios: {}\n            }\n        }\n    }, null, 2);\n}\n\n/**\n * Helper function to prepare project for build\n */\nexport async function prepareProjectForBuild(sandbox: Sandbox, options: {\n    files: FileItem[];\n    appJsonContent: string;\n    easJsonContent: string;\n    dependencies: Record<string, { version: string }>;\n    projectDir: string;\n    onFileUploaded?: () => void;\n}) {\n    const { files, appJsonContent, easJsonContent, dependencies, projectDir } = options;\n\n    try {\n        // Create project directory\n        await sandbox.files.makeDir(projectDir);\n\n        // Prepare files for bulk upload\n        const filesToUpload: any[] = [];\n\n        // Add all project files\n        for (const file of files) {\n            // Add the file to the bulk upload list\n            filesToUpload.push({\n                path: `${projectDir}/src/${file.name}`,\n                data: file.content\n            });\n            \n            // Notify about file upload if callback provided\n            if (options.onFileUploaded) {\n                options.onFileUploaded();\n            }\n\n            // Track directories that need to be created\n            const dirPath = `${projectDir}/${file.name}`.substring(0, `${projectDir}/${file.name}`.lastIndexOf('/'));\n            if (dirPath !== projectDir) {\n                // Create directory structure as needed\n                // Create each directory in the path if needed\n                const pathParts = dirPath.split('/');\n                let currentPath = '';\n\n                for (const part of pathParts) {\n                    if (!part) continue; // Skip empty parts\n                    currentPath += '/' + 'src/' + part;\n                    try {\n                        await sandbox.files.makeDir(`${currentPath}`);\n                    } catch (error) {\n                        // Directory might already exist, continue\n                        console.log(`Directory ${currentPath} might already exist, continuing...`);\n                    }\n                }\n            }\n        }\n\n        // // Add configuration files\n        // filesToUpload.push({\n        //     path: `${projectDir}/app.json`,\n        //     data: appJsonContent\n        // });\n        //\n        filesToUpload.push({\n            path: `${projectDir}/eas.json`,\n            data: easJsonContent\n        });\n\n        filesToUpload.push({\n            path: `${projectDir}/scripts/fast-apk-build.js`,\n            data: script\n        })\n\n        console.log('filesToUpload', filesToUpload)\n\n        // Bulk upload all files at once\n        console.log(`Bulk uploading ${filesToUpload.length} files to sandbox...`);\n        await sandbox.files.write(filesToUpload);\n        console.log('Bulk file upload completed successfully');\n\n        // Create or update package.json with dependencies\n        let packageJson: any = {};\n\n        try {\n            // Check if package.json already exists from uploaded files\n            const packageJsonCheck = await sandbox.commands.run(`test -f ${projectDir}/package.json && cat ${projectDir}/package.json || echo '{}'`);\n            if (packageJsonCheck.stdout.trim() !== '{}') {\n                packageJson = JSON.parse(packageJsonCheck.stdout);\n            } else {\n                // Create basic package.json if it doesn't exist\n                packageJson = {\n                    name: 'magically-app',\n                    version: '1.0.0',\n                    main: 'index.js',\n                    scripts: {\n                        start: 'expo start',\n                        android: 'expo start --android',\n                        ios: 'expo start --ios',\n                        web: 'expo start --web',\n                        build: 'eas build --platform ios --profile preview'\n                    }\n                };\n            }\n\n            // Add dependencies\n            packageJson.dependencies = packageJson.dependencies || {};\n            packageJson.devDependencies = packageJson.devDependencies || {};\n\n            // Merge dependencies from the provided dependencies object\n            for (const [name, { version }] of Object.entries(dependencies)) {\n                packageJson.dependencies[name] = version;\n            }\n\n            // Ensure essential dependencies are included\n            const essentialDeps = {\n                'react': '^18.2.0',\n                'react-native': '0.6.9',\n                'expo': 'latest',\n                \"react-native-screens\": \"^4.4.0\"\n            };\n\n            for (const [name, version] of Object.entries(essentialDeps)) {\n                if (!packageJson.dependencies[name]) {\n                    packageJson.dependencies[name] = version;\n                }\n            }\n\n            // Ensure development dependencies\n            const devDeps = {\n                '@types/react': '^18.2.14',\n                'typescript': '^5.1.6',\n                'eas-cli': '^5.4.0'\n            };\n\n            for (const [name, version] of Object.entries(devDeps)) {\n                if (!packageJson.devDependencies[name]) {\n                    packageJson.devDependencies[name] = version;\n                }\n            }\n\n            // Write updated package.json\n            await sandbox.files.write([\n                {\n                    path: `${projectDir}/package.json`,\n                    data: JSON.stringify(packageJson),\n                }\n            ]);\n            console.log('Updated package.json with dependencies');\n\n        } catch (error) {\n            console.error('Error processing package.json:', error);\n            throw error;\n        }\n\n        return true;\n    } catch (error) {\n        console.error('Error preparing project for build:', error);\n        throw error;\n    }\n}\n\n/**\n * Helper function to upload files to sandbox\n * @param sandbox - The sandbox instance\n * @param chatId - The chat ID to get files from\n * @param progressCallback - Optional callback for progress updates\n */\nexport async function uploadFilesToSandbox(\n    sandbox: Sandbox, \n    chatId: string,\n    progressCallback?: (progress: number, status: string) => void\n) {\n    try {\n        // Report progress\n        progressCallback?.(5, 'Creating project directories...');\n        \n        // Create project directory\n        await sandbox.files.makeDir(`${PROJECT_DIR}/assets`);\n\n        // Fetch the latest file state from the database\n        progressCallback?.(10, 'Fetching project files...');\n        console.log(`Fetching latest file state for chat ID: ${chatId}`);\n        const fileState = await getLatestFileState(chatId);\n\n        if (!fileState) {\n            console.error(`No file state found for chat ID: ${chatId}`);\n            progressCallback?.(100, 'Error: No files found');\n            return false;\n        }\n\n        console.log(`Found file state with ID: ${fileState.id}`);\n        progressCallback?.(20, 'Files found, preparing configuration...');\n\n        // Create configuration files\n        const appJsonContent = createAppJson();\n        const easJsonContent = createEasJson();\n\n        // Parse files and dependencies from file state\n        const files = (fileState.files as FileItem[]).filter(f => !!f.name);\n        const dependencies = fileState.dependencies as Record<string, { version: string }>;\n\n        console.log(`Uploading ${files.length} files to sandbox`);\n        progressCallback?.(30, `Preparing to upload ${files.length} files...`);\n\n        // Track progress for file uploads\n        let uploadedFiles = 0;\n        const totalFiles = files.length;\n        \n        // Use the prepareProjectForBuild function to set up the project\n        await prepareProjectForBuild(sandbox, {\n            files,\n            appJsonContent,\n            easJsonContent,\n            dependencies,\n            projectDir: PROJECT_DIR,\n            onFileUploaded: () => {\n                uploadedFiles++;\n                const fileProgress = Math.min(30 + Math.floor((uploadedFiles / totalFiles) * 50), 80);\n                progressCallback?.(fileProgress, `Uploaded ${uploadedFiles}/${totalFiles} files...`);\n            }\n        });\n\n        // Install required tools\n        // console.log('Installing EAS CLI and yarn...');\n        // await sandbox.commands.run('npm install -g eas-cli yarn', { timeoutMs: 120000 });\n\n        // Initialize git repository\n        // console.log('Initializing git repository...');\n        // await sandbox.commands.run(`git init && git config --global user.email \"<EMAIL>\" && git config --global user.name \"Example User\" && git add . && git commit -m \"Initial commit\"`, { timeoutMs: 30000, cwd: PROJECT_DIR });\n\n        progressCallback?.(90, 'Finalizing project setup...');\n        console.log('Project setup completed successfully');\n        progressCallback?.(100, 'Project setup completed successfully!');\n        return true;\n    } catch (error) {\n        console.error('Error setting up project:', JSON.stringify(error));\n        return false;\n    }\n}", "startLine": 275, "endLine": 788, "type": "util", "symbols": ["prepareProjectForBuild and related functions"], "score": 0.8, "context": "Functions to prepare project files for build, upload files to sandbox, and handle terminal commands, relevant for project file operations and state management.", "includesImports": false}, {"filePath": "src/lib/services/supabase-context-engine.ts", "content": "export class SupabaseContextEngine {\n    private supabaseProvider: SupabaseIntegrationProvider;\n    private project: Project;\n    private resourceIndex: Record<string, string[]> = {};\n    private resourceDetails: Record<string, any> = {};\n    private fullResources: {\n        schema: any[];\n        functions: any[];\n        secrets: any[];\n        rlsPolicies: any[];\n        dbFunctions: any[];\n        triggers: any[];\n        storageBuckets: any[];\n    } | null = null;\n\n    /**\n     * Initialize with project\n     */\n    constructor(project: Project) {\n        this.project = project;\n        this.supabaseProvider = new SupabaseIntegrationProvider();\n    }\n\n    /**\n     * Build a lightweight index of all Supabase resources\n     * This is the first stage of the two-stage approach\n     */\n    async buildResourceIndex(): Promise<Record<string, string[]>> {\n        console.time('build-resource-index');\n\n        if (!this.project.connectionId || !this.project.supabaseProjectId) {\n            throw new Error('Project is not connected to Supabase');\n        }\n\n        console.log(`[SupabaseContextEngine] Building resource index for project: ${this.project.supabaseProjectId}`);\n\n        try {\n            // Use the existing getLatestInstructionsForChat method to fetch all resources at once\n            const {\n                schema,\n                functions,\n                secrets,\n                rlsPolicies,\n                dbFunctions,\n                triggers,\n                storageBuckets\n            } = await this.supabaseProvider.getLatestInstructionsForChat({\n                project: this.project\n            });\n\n            // Initialize resource index with all resource types\n            this.resourceIndex = {\n                tables: [],\n                functions: [],\n                policies: [],\n                buckets: [],\n                dbFunctions: [],\n                triggers: [],\n                secrets: []\n            };\n\n            // Extract resource names for the index\n            if (Array.isArray(schema)) {\n                this.resourceIndex.tables = schema\n                    .filter(table => table && typeof table === 'object' && 'table_name' in table)\n                    .map(table => (table as any).table_name);\n            }\n\n            if (Array.isArray(functions)) {\n                this.resourceIndex.functions = functions\n                    .filter(func => func && typeof func === 'object' && 'name' in func)\n                    .map(func => func.name);\n            }\n\n            if (Array.isArray(rlsPolicies)) {\n                this.resourceIndex.policies = rlsPolicies\n                    .filter(policy => policy && typeof policy === 'object' && 'table' in policy && 'policy_name' in policy)\n                    .map(policy => `${(policy as any).table}:${(policy as any).policy_name}`);\n            }\n\n            if (Array.isArray(storageBuckets)) {\n                this.resourceIndex.buckets = storageBuckets\n                    .filter(bucket => bucket && typeof bucket === 'object' && 'name' in bucket)\n                    .map(bucket => bucket.name);\n            }\n\n            if (Array.isArray(dbFunctions)) {\n                this.resourceIndex.dbFunctions = dbFunctions\n                    .filter(func => func && typeof func === 'object' && 'function_name' in func)\n                    .map(func => (func as any).function_name);\n            }\n\n            if (Array.isArray(triggers)) {\n                this.resourceIndex.triggers = triggers\n                    .filter(trigger => trigger && typeof trigger === 'object' && 'trigger_name' in trigger)\n                    .map(trigger => (trigger as any).trigger_name);\n            }\n\n            if (Array.isArray(secrets)) {\n                this.resourceIndex.secrets = secrets\n                    .filter(secret => secret && typeof secret === 'object' && 'name' in secret)\n                    .map(secret => secret.name);\n            }\n\n            // Store the full resources for later detailed retrieval\n            this.fullResources = {\n                schema: Array.isArray(schema) ? schema : [],\n                functions: Array.isArray(functions) ? functions : [],\n                secrets: Array.isArray(secrets) ? secrets : [],\n                rlsPolicies: Array.isArray(rlsPolicies) ? rlsPolicies : [],\n                dbFunctions: Array.isArray(dbFunctions) ? dbFunctions : [],\n                triggers: Array.isArray(triggers) ? triggers : [],\n                storageBuckets: Array.isArray(storageBuckets) ? storageBuckets : []\n            };\n\n            console.log('[SupabaseContextEngine] Resource index built:', {\n                tables: this.resourceIndex.tables.length,\n                functions: this.resourceIndex.functions.length,\n                policies: this.resourceIndex.policies.length,\n                buckets: this.resourceIndex.buckets.length,\n                dbFunctions: this.resourceIndex.dbFunctions.length,\n                triggers: this.resourceIndex.triggers.length,\n                secrets: this.resourceIndex.secrets.length\n            });\n\n            return this.resourceIndex;\n        } catch (error) {\n            console.error('[SupabaseContextEngine] Error building resource index:', error);\n            throw error;\n        } finally {\n            console.timeEnd('build-resource-index');\n        }\n    }\n\n\n    /**\n     * First stage: Find relevant resources for a query\n     */\n    async findRelevantResources(query: string): Promise<{\n        resources: ResourceIdentification[],\n        reasoning: string,\n        hasAdditional: boolean,\n        additionalResourcesSummary?: string,\n        mustRetrieveAdditionalResources?: boolean\n    }> {\n        console.time('find-relevant-resources');\n\n        // Make sure we have the resource index\n        if (Object.keys(this.resourceIndex).length === 0) {\n            await this.buildResourceIndex();\n        }\n\n        const prompt = `Query: \"${query}\"\n\nAvailable Supabase Resources:\n${Object.entries(this.resourceIndex)\n            .map(([type, names]) => `${type.toUpperCase()}: ${names.join('\\n ')}`)\n            .join('\\n\\n')}\n\nSelect the most relevant resources for this query. Limit your selection to at most 10 resources total.`\n\n        // Use LLM to identify relevant resources\n        const result = await generateObject({\n            model: customModel('openai/gpt-4.1'), // Using a smaller model to reduce costs\n            temperature: 0.1,\n            schema: z.object({\n                resources: z.array(z.object({\n                    resourceType: z.enum(['table', 'function', 'policy', 'bucket', 'dbFunction', 'trigger', 'secret']).describe(\"Type of resource. Must match the exact terms.\"),\n                    resourceName: z.string().describe(\"Name of the resource\"),\n                    relevanceScore: z.number().min(1).max(10).describe(\"Relevance score from 1-10\"),\n                    reasoning: z.string().describe(\"Why this resource is relevant to the query\")\n                })),\n                reasoning: z.string().describe(\"Explanation of why these resources were selected\"),\n                hasAdditional: z.boolean().describe('Apart from the 10 returned, are there more possible resources that may relate to the query?'),\n                additionalResourcesSummary: z.string().optional().describe(`List the SPECIFIC additional resources from the provided resource list that are relevant but weren't included in your selection due to the 10-resource limit.\n        Be definitive about which resources exist and are relevant - do NOT use hypothetical language like \"if X exists\" when referring to resources you can see in the provided list.\n        Use precise resource names from the available resources list.\n        Explain exactly why each additional resource is relevant and how it relates to the resources you've already selected.\n        If certain resources are critical for understanding the system, use direct language like \"You MUST query for X because it is essential for understanding Y.\"\n        `),\n                mustRetrieveAdditionalResources: z.boolean().optional().describe('If additional resources must be retrieved, set this to true. This will force LLM to request additional resources on the basis of the additionalResourcesSummary.'),\n            }),\n            system: `You are a Supabase database expert. Your task is to identify which resources in a Supabase project are most relevant to a specific query.\n\nYou will be given:\n1. A query about the Supabase project\n2. A list of available resources categorized by type\n\nYour job is to select the most relevant resources that would help answer the query, including resources that reveal important relationships between entities. Consider the following guidelines:\n\n1. IDENTIFY CORE RESOURCES: First identify the primary resources directly mentioned in the query.\n\n2. IDENTIFY RELATED RESOURCES: Then identify resources that have relationships with the primary resources:\n   - Tables that reference each other through foreign keys\n   - Functions that operate on identified tables\n   - Policies that control access to identified tables\n   - Triggers that affect identified tables\n   - Secrets that might be used by identified functions\n\n3. CONSIDER DATA FLOW: Think about how data flows through the application. If a query mentions user authentication, consider not just the users table, but also related tables like profiles, sessions, or audit logs.\n\n4. SET mustRetrieveAdditionalResources=true when:\n   - Critical relationship information is missing\n   - The query cannot be properly answered without additional context\n   - There are clear dependencies on resources not included in the initial selection\n\n5. PROVIDE CLEAR REASONING: For each resource, explain why it's relevant and how it relates to other resources.\n\nIMPORTANT: Balance between being comprehensive and focused. Don't include everything, but don't miss critical relationships either.`,\n            prompt\n        });\n\n        console.timeEnd('find-relevant-resources');\n        return result.object;\n    }\n\n    /**\n     * Second stage: Get detailed information about specific resources\n     */\n    async getResourceDetails(resources: ResourceIdentification[]): Promise<Record<string, any>> {\n        console.time('get-resource-details');\n\n        if (!this.fullResources) {\n            // If we don't have the full resources cached, rebuild the index\n            await this.buildResourceIndex();\n\n            if (!this.fullResources) {\n                throw new Error('Failed to build resource index');\n            }\n        }\n\n        const details: Record<string, any> = {\n            tables: [],\n            functions: [],\n            policies: [],\n            buckets: [],\n            dbFunctions: [],\n            triggers: [],\n            secrets: []\n        };\n\n        // Group resources by type\n        const resourcesByType: Record<string, string[]> = {\n            table: [],\n            function: [],\n            policy: [],\n            bucket: [],\n            dbFunction: [],\n            trigger: [],\n            secret: []\n        };\n\n        // Populate resourcesByType\n        for (const resource of resources) {\n            if (resourcesByType[resource.resourceType]) {\n                resourcesByType[resource.resourceType].push(resource.resourceName);\n            }\n        }\n\n        // Get details for each resource type from the cached full resources\n        if (resourcesByType.table.length > 0 && Array.isArray(this.fullResources.schema)) {\n            details.tables = this.fullResources.schema.filter(table =>\n                table && typeof table === 'object' && 'table_name' in table &&\n                resourcesByType.table.includes(table.table_name)\n            );\n        }\n\n        if (resourcesByType.function.length > 0 && Array.isArray(this.fullResources.functions)) {\n            details.functions = this.fullResources.functions.filter(func =>\n                func && typeof func === 'object' && 'name' in func &&\n                resourcesByType.function.includes(func.name)\n            );\n        }\n\n        if (resourcesByType.policy.length > 0 && Array.isArray(this.fullResources.rlsPolicies)) {\n            // Policies are in format \"table:policy\"\n            const policyMap = resourcesByType.policy.map(p => {\n                const [tableName, policyName] = p.split(':');\n                return {tableName, policyName};\n            });\n\n            details.policies = this.fullResources.rlsPolicies.filter(policy =>\n                policy && typeof policy === 'object' && 'table' in policy && 'policy_name' in policy &&\n                policyMap.some(p => p.tableName === policy.table && p.policyName === policy.policy_name)\n            );\n        }\n\n        if (resourcesByType.bucket.length > 0 && Array.isArray(this.fullResources.storageBuckets)) {\n            details.buckets = this.fullResources.storageBuckets.filter(bucket =>\n                bucket && typeof bucket === 'object' && 'name' in bucket &&\n                resourcesByType.bucket.includes(bucket.name)\n            );\n        }\n\n        if (resourcesByType.dbFunction.length > 0 && Array.isArray(this.fullResources.dbFunctions)) {\n            details.dbFunctions = this.fullResources.dbFunctions.filter(func =>\n                func && typeof func === 'object' && 'function_name' in func &&\n                resourcesByType.dbFunction.includes(func.function_name)\n            );\n        }\n\n        if (resourcesByType.trigger.length > 0 && Array.isArray(this.fullResources.triggers)) {\n            details.triggers = this.fullResources.triggers.filter(trigger =>\n                trigger && typeof trigger === 'object' && 'trigger_name' in trigger &&\n                resourcesByType.trigger.includes(trigger.trigger_name)\n            );\n        }\n\n        if (resourcesByType.secret.length > 0 && Array.isArray(this.fullResources.secrets)) {\n            details.secrets = this.fullResources.secrets.filter(secret =>\n                secret && typeof secret === 'object' && 'name' in secret &&\n                resourcesByType.secret.includes(secret.name)\n            );\n        }\n\n        console.timeEnd('get-resource-details');\n        this.resourceDetails = details;\n        return details;\n    }\n\n    // The fetch methods are no longer needed as we're using the cached resources from SupabaseIntegrationProvider\n\n    /**\n     * Helper function to normalize resource type for matching\n     * Handles case insensitivity and partial matches like 'function', 'functions', 'fun', etc.\n     */\n    private normalizeResourceType(resourceType: string): string | null {\n        resourceType = resourceType.toLowerCase();\n\n        // Map of partial/alternative names to canonical resource types\n        const resourceTypeMap: Record<string, string> = {\n            'table': 'table',\n            'tables': 'table',\n            'tab': 'table',\n            'tbl': 'table',\n\n            'function': 'function',\n            'functions': 'function',\n            'func': 'function',\n            'fun': 'function',\n            'fn': 'function',\n\n            'policy': 'policy',\n            'policies': 'policy',\n            'pol': 'policy',\n\n            'bucket': 'bucket',\n            'buckets': 'bucket',\n            'bkt': 'bucket',\n            'storage': 'bucket',\n\n            'dbfunction': 'dbFunction',\n            'dbfunctions': 'dbFunction',\n            'dbfunc': 'dbFunction',\n            'dbfn': 'dbFunction',\n            'db_function': 'dbFunction',\n\n            'trigger': 'trigger',\n            'triggers': 'trigger',\n            'trig': 'trigger',\n\n            'secret': 'secret',\n            'secrets': 'secret',\n            'sec': 'secret'\n        };\n\n        // Try exact match first\n        if (resourceTypeMap[resourceType]) {\n            return resourceTypeMap[resourceType];\n        }\n\n        // Try partial match\n        for (const [key, value] of Object.entries(resourceTypeMap)) {\n            if (key.startsWith(resourceType) || resourceType.startsWith(key)) {\n                return value;\n            }\n        }\n\n        return null; // No match found\n    }\n\n    /**\n     * Check if a resource should be excluded based on the excludedResources list\n     */\n    private shouldExcludeResource(resource: ResourceIdentification, excludedResources: string[]): boolean {\n        if (!excludedResources || excludedResources.length === 0) {\n            return false;\n        }\n\n        for (const excludedResource of excludedResources) {\n            const parts = excludedResource.split('.');\n            if (parts.length !== 2) continue;\n\n            const [excludedType, excludedName] = parts;\n            const normalizedExcludedType = this.normalizeResourceType(excludedType);\n\n            if (!normalizedExcludedType) continue;\n\n            // Check if the resource type matches and the name matches or is a wildcard\n            if (normalizedExcludedType === resource.resourceType &&\n                (excludedName === '*' || excludedName.toLowerCase() === resource.resourceName.toLowerCase())) {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Main method to get relevant Supabase resources for a query\n     * @param query The natural language query to find relevant resources for\n     * @param excludedResources Optional array of resources to exclude in the format \"resourceType.resourceName\"\n     */\n    async getRelevantSupabaseResources(query: string, excludedResources?: string[]): Promise<any> {\n        // Stage 1: Find relevant resources\n        const {\n            resources: allResources,\n            reasoning,\n            additionalResourcesSummary,\n            hasAdditional,\n            mustRetrieveAdditionalResources\n        } = await this.findRelevantResources(query);\n\n        // Filter out excluded resources if any\n        let excludedCount = 0;\n        const resources = excludedResources && excludedResources.length > 0\n            ? allResources.filter(resource => {\n                const shouldExclude = this.shouldExcludeResource(resource, excludedResources);\n                if (shouldExclude) excludedCount++;\n                return !shouldExclude;\n            })\n            : allResources;\n\n        if (excludedCount > 0) {\n            console.log(`[SupabaseContextEngine] Excluded ${excludedCount} resources based on user preferences`);\n        }\n\n        if (resources.length === 0) {\n            console.log(\"[SupabaseContextEngine] No relevant resources found\");\n            return {\n                query,\n                resources: [],\n                reasoning: reasoning || \"No relevant resources found\",\n                details: {}\n            };\n        }\n\n        // Stage 2: Get details for those resources\n        const details = await this.getResourceDetails(resources);\n\n        // Calculate token usage metrics\n        const result = {\n            query,\n            resources,\n            reasoning,\n            details,\n            additionalResourcesSummary,\n            hasAdditional,\n            mustRetrieveAdditionalResources,\n            excludedResources: excludedResources || []\n        };\n\n        // Log metrics\n        const resultString = JSON.stringify(result);\n        console.log(`[SupabaseContextEngine] Result size: ${resultString.length} chars / ~${Math.ceil(resultString.length / 4)} tokens`);\n\n        return result;\n    }\n}", "startLine": 22, "endLine": 490, "type": "unknown", "symbols": ["SupabaseContextEngine"], "score": 0.7, "context": "Manages Supabase resource indexing and querying, relevant for project management but less directly about file system and file operations.", "includesImports": false}, {"filePath": "src/lib/db/schema.ts", "content": "export const user = pgTable('User', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  email: varchar('email', { length: 64 }).notNull(),\n  password: varchar('password', { length: 64 }),\n  name: varchar(),\n  firstName: varchar(),\n  lastName: varchar(),\n  linkedin: varchar('linkedin', { length: 255 }),\n  provider: varchar('provider', { enum: ['credentials', 'google'] }).default('credentials'),\n  isAnonymous: boolean('isAnonymous').default(false),\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n});\n\nexport type User = InferSelectModel<typeof user>;\n\n// Forward declare projects table to avoid circular references\nexport const projects = pgTable('Project', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  appName: text('appName'), // New field for project/app name\n  slug: text('slug'), // URL-friendly identifier\n  scheme: text('scheme'), // URL scheme for deep linking\n\n  // App identifiers\n  bundleIdentifier: text('bundleIdentifier'), // iOS bundle ID (com.company.app)\n  packageName: text('packageName'), // Android package name\n\n  isMigratedv1: boolean('isMigratedv1').default(false),\n  // App assets\n  icon: text('icon'), // URL to app icon\n  splashImage: text('splashImage'), // URL to splash screen image\n  primaryColor: text('primaryColor').default('#000000'), // Primary brand color\n\n  // App configuration\n  description: text('description'), // App store description\n  privacyPolicyUrl: text('privacyPolicyUrl'),\n  termsOfServiceUrl: text('termsOfServiceUrl'),\n\n  // Ownership\n  userId: uuid('userId')\n    .notNull()\n    .references(() => user.id, { onDelete: 'cascade' }),\n  connectionId: uuid('connectionId')\n    .references(() => connection.id, { onDelete: 'cascade' }),\n  visibility: varchar('visibility', { enum: ['public', 'private', 'hidden'] })\n    .notNull()\n    .default('private'),\n\n  // Timestamps\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n\n  prompt: text('prompt'),\n  initialUXGuidelines: text('initialUXGuidelines'),\n\n  knowledgeBase: text('knowledgeBase'),\n  aiGeneratedMemory: text('aiGeneratedMemory'),\n\n  // Design preview fields\n  approvedDesignHtml: text('approvedDesignHtml'),\n  designChatId: uuid('designChatId').references(() => chat.id),\n\n  // Supabase fields at project level\n  supabaseProjectId: text('supabaseProjectId'),\n  supabaseAnonKey: text('supabaseAnonKey'),\n  supabaseServiceKey: text('supabaseServiceKey'),\n});\n\nexport type Project = InferSelectModel<typeof projects>;", "startLine": 17, "endLine": 85, "type": "type", "symbols": ["Project and related types"], "score": 0.7, "context": "Defines database schema for projects and related entities, relevant for understanding project data structure and management.", "includesImports": false}, {"filePath": "src/lib/db/project-queries.ts", "content": "export async function getProjectById({id}: {id: string}): Promise<Project> {\n  try {\n    const [project] = await db.select().from(projects).where(eq(projects.id, id));\n    return project as Project;\n  } catch (error) {\n    console.error('Failed to get project by id from database');\n    throw error;\n  }\n\n\n}/**\n * Get a project by its ID\n */\nexport async function getSanitizedProjectById({id}: {id: string}): Promise<Project> {\n  try {\n    const [project] = await db.select().from(projects).where(eq(projects.id, id));\n      const {supabaseAnonKey, supabaseServiceKey, ...rest} = project;\n      return rest as Project;\n  } catch (error) {\n    console.error('Failed to get project by id from database');\n    throw error;\n  }\n}\n\n/**\n * Update project metadata\n */\nexport async function updateProjectMetadata(\n  id: string,\n  metadata: {\n    slug?: string;\n    appName?: string;\n    description?: string;\n    primaryColor?: string;\n    bundleIdentifier?: string;\n    packageName?: string;\n    appIdentifier?: string;\n    icon?: string;\n    splashImage?: string;\n    isMigratedv1?: boolean;\n  }\n) {\n  try {\n    const updateData = {\n      ...metadata,\n      updatedAt: new Date()\n    };\n\n    const [updatedProject] = await db\n      .update(projects)\n      .set(updateData)\n      .where(eq(projects.id, id))\n      .returning();\n    \n    return updatedProject;\n  } catch (error) {\n    console.error('Failed to update project metadata in database');\n    throw error;\n  }\n}\n\n/**\n * Check if user owns a project\n */\nexport async function isProjectOwner(projectId: string, userId: string) {\n  try {\n    const project = await getProjectById({id: projectId});\n    return project && project.userId === userId;\n  } catch (error) {\n    console.error('Failed to check project ownership');\n    throw error;\n  }\n}\n\n/**\n * Get all projects for a user\n */\nexport async function getUserProjects(userId: string) {\n  try {\n    const userProjects = await db\n      .select()\n      .from(projects)\n      .where(and(\n          eq(projects.userId, userId),\n          ne(projects.visibility, 'hidden')\n      ))\n      .orderBy(desc(projects.updatedAt));\n    \n    return userProjects;\n  } catch (error) {\n    console.error('Failed to get user projects from database');\n    throw error;\n  }\n}", "startLine": 8, "endLine": 101, "type": "util", "symbols": ["getProjectById and related queries"], "score": 0.7, "context": "Database queries for fetching and updating project data, relevant for project management.", "includesImports": false}, {"filePath": "src/lib/chat/tools/manage-supabase-auth.ts", "content": "export const manageSupabaseAuth = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n  project: Project;\n}) => {\n  return tool({\n    description: 'Manages Supabase auth configuration. Use this tool to view current auth settings, update auth configuration, or retrieve SSO provider information. This helps with configuring authentication options like email templates, redirect URLs, and external OAuth providers.',\n    parameters: z.object({\n      action: z.enum([\n        'getAuthConfig',\n        'updateAuthConfig',\n        'getSSOProviders'\n      ]).describe('The action to perform on the auth configuration'),\n      authConfig: z.record(z.any()).optional().describe('The auth configuration to update (only required for updateAuthConfig action). Same format as the response from getAuthConfig. Pass only the fields to update.'),\n      reason: z.string().describe(\"Describe why you need to access or modify the auth configuration\")\n    }),\n    execute: async ({ action, authConfig, reason }: { \n      action: 'getAuthConfig' | 'updateAuthConfig' | 'getSSOProviders',\n      authConfig?: UpdateProjectAuthConfigRequestBody,\n      reason: string \n    }) => {\n      try {\n        console.log(`Performing Supabase auth action: ${action}. Reason: ${reason}.`);\n        if(authConfig) {\n          console.log('Updating', authConfig)\n        }\n        \n        if (!project.connectionId || !project.supabaseProjectId) {\n          throw new Error('Project is not linked to a Supabase project');\n        }\n        \n        // Create a new instance of the SupabaseIntegrationProvider\n        const supabaseIntegrationProvider = new SupabaseIntegrationProvider();\n        \n        let result;\n        \n        // Perform the requested action\n        switch (action) {\n          case 'getAuthConfig':\n            result = await supabaseIntegrationProvider.getAuthConfig(project.id);\n            break;\n          case 'updateAuthConfig':\n            if (!authConfig) {\n              throw new Error('Auth configuration is required for updateAuthConfig action');\n            }\n            result = await supabaseIntegrationProvider.updateAuthConfig(project.id, authConfig);\n            break;\n          case 'getSSOProviders':\n            result = await supabaseIntegrationProvider.getSSOProviders(project.id);\n            break;\n        }\n        \n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n        \n        // Format the response\n        return JSON.stringify({\n          action,\n          result,\n          timestamp: new Date().toISOString(),\n          comment: getActionComment(action, result)\n        });\n      } catch (e: any) {\n        console.error(`Error while performing Supabase auth action: ${action}`, e);\n        return JSON.stringify({\n          error: e.message,\n          action,\n          timestamp: new Date().toISOString(),\n          comment: `Error while performing Supabase auth action ${action}: ${e.message}. Please try again or check your Supabase connection.`\n        });\n      }\n    }\n  });\n};", "startLine": 13, "endLine": 90, "type": "util", "symbols": ["manageSupabaseAuth"], "score": 0.6, "context": "Tool to manage Supabase auth configuration, relevant for project management related to authentication but less directly about file system.", "includesImports": false}, {"filePath": "src/lib/chat/tools/query-supabase-context.ts", "content": "export const querySupabaseContext = ({\n    projectId,\n    messageId = '',\n}: {\n    projectId: string, \n    messageId?: string, \n}) => {\n    return tool({\n        description: 'Query Supabase resources or execute SQL (either one at a time) to understand the project tables, dbFunctions, triggers, functions, RLS policies, storage buckets, and secrets. ' +\n            'Use this instead of requesting full Supabase instructions. Issue queries that are as specific as possible. ' +\n            'You can exclude specific resources using the excludedResources parameter in the format \"resourceType.resourceName\" or use wildcards like \"table.*\". ' +\n            'This tool uses a two-stage approach to efficiently retrieve only the relevant Supabase resources. ' +\n            'If the initial query returns incomplete information, the tool may indicate mustRetrieveAdditionalResources=true with guidance on what additional resources to request in a follow-up query. ' +\n            'You can also execute SELECT SQL queries directly against the database using the executeSQL parameter. Only SELECT queries are allowed for security reasons. When executing a SQL query, code exploration will not happen.',\n        parameters: QuerySupabaseContextSchema,\n        execute: async ({query, excludedResources, executeSQL, reason}: z.infer<typeof QuerySupabaseContextSchema>) => {\n            try {\n                console.log('Supabase Query:', query);\n                console.log('Excluded Resources:', excludedResources || []);\n                console.log('SQL Query:', executeSQL || 'None');\n                console.log('Reason:', reason);\n\n                // Validate inputs\n                if (!query && !executeSQL) {\n                    return {\n                        result: null,\n                        message: \"Either a natural language query or SQL query is required.\"\n                    };\n                }\n\n                // Get the tool tracker instance\n                const toolTracker = ToolCountTracker.getInstance();\n\n                console.log('Tool call allowed (querySupabaseContext):', toolTracker.shouldAllowToolCall(messageId, 'querySupabaseContext'));\n\n                // Check if we should allow this tool call\n                if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'querySupabaseContext')) {\n                    return {\n                        result: null,\n                        message: \"⚠️ Tool call limit reached. You've already made multiple Supabase context queries. Please implement or summarize with the information you already have.\",\n                    };\n                }\n\n                // Increment the tool count if we have a chat ID\n                if (messageId) {\n                    toolTracker.incrementToolCount(messageId, 'querySupabaseContext');\n                }\n\n                // Get project information\n                let project: Project | null = null;\n                try {\n                    if (!projectId) {\n                        return {\n                            result: null,\n                            message: \"Project ID is required\"\n                        };\n                    }\n                    \n                    project = await getProjectById({id: projectId});\n                    if (!project) {\n                        return {\n                            result: null,\n                            message: `Project not found: ${projectId}`\n                        };\n                    }\n                    \n                    // Check if project is connected to Supabase\n                    if (!project.connectionId || !project.supabaseProjectId) {\n                        return {\n                            result: null,\n                            message: 'This project is not connected to Supabase'\n                        };\n                    }\n                } catch (error) {\n                    console.error('Error fetching project:', error);\n                    return {\n                        result: null,\n                        message: `Failed to fetch project information: ${error instanceof Error ? error.message : 'Unknown error'}`\n                    };\n                }\n                \n                // Handle SQL execution if provided\n                if (executeSQL) {\n                    console.log('Executing SQL query:', executeSQL);\n                    \n                    // Validate that it's a SELECT query only\n                    const sqlLower = executeSQL.trim().toLowerCase();\n                    if (!sqlLower.startsWith('select ')) {\n                        return {\n                            result: null,\n                            message: '⚠️ Only SELECT queries are allowed for security reasons. Your query was rejected.'\n                        };\n                    }\n                    \n                    // Check for potentially harmful statements\n                    const disallowedKeywords = ['insert', 'update', 'delete', 'drop', 'alter', 'create', 'truncate'];\n                    if (disallowedKeywords.some(keyword => sqlLower.includes(keyword))) {\n                        return {\n                            result: null,\n                            message: '⚠️ SQL query contains disallowed keywords. Only pure SELECT queries are permitted. Your query was rejected.'\n                        };\n                    }\n                    \n                    try {\n                        // Execute the SQL query\n                        const supabaseProvider = new SupabaseIntegrationProvider();\n                        const result = await supabaseProvider.executeSQL({\n                            query: executeSQL,\n                            projectId\n                        });\n                        \n                        // Truncate the result if it's too large\n                        const resultStr = JSON.stringify(result || {}, null, 2);\n                        const MAX_LENGTH = 3000;\n                        \n                        let sqlResult;\n                        let message = '📊 SQL query executed successfully.';\n                        \n                        if (resultStr.length > MAX_LENGTH) {\n                            sqlResult = {\n                                data: result?.data,\n                                truncated: true,\n                                originalLength: resultStr.length,\n                                message: `SQL result was truncated from ${resultStr.length} to ${MAX_LENGTH} characters`\n                            };\n                            message += ` Results were truncated (${sqlResult.message}).`;\n                        } else {\n                            sqlResult = result;\n                        }\n                        \n                        return {\n                            result: { sqlResult },\n                            message,\n                            toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'querySupabaseContext')\n                        };\n                    } catch (error) {\n                        console.error('SQL execution error:', error);\n                        return {\n                            result: null,\n                            message: `⚠️ Failed to execute SQL query: ${error instanceof Error ? error.message : 'Unknown error'}`\n                        };\n                    }\n                } else {\n                    // Only query the context engine if no SQL was provided\n                    try {\n                        // Initialize the Supabase context engine\n                        const contextEngine = new SupabaseContextEngine(project);\n\n                        // Query the Supabase context engine\n                        const result = await contextEngine.getRelevantSupabaseResources(query, excludedResources);\n\n                        // Prepare a more informative response\n                        const resourceCount = result.resources?.length || 0;\n                        let message = `Found ${resourceCount} relevant Supabase resources for your query.`;\n\n                        // Add information about resource types\n                        const resourceTypes = result.resources?.map(r => r.resourceType) || [];\n                        const uniqueTypes = [...new Set(resourceTypes)];\n                        \n                        if (uniqueTypes.length > 0) {\n                            message += ` Types: ${uniqueTypes.join(', ')}.`;\n                        }\n                        \n                        // Add information about excluded resources if any\n                        if (excludedResources && excludedResources.length > 0) {\n                            message += `\\n${excludedResources.length} resources were excluded from the analysis.`;\n                        }\n                        \n                        // Add information about additional resources if needed\n                        if (result.hasAdditional) {\n                            message += `\\nThere are additional resources that may be relevant but weren't included due to the result limit.`;\n                            \n                            if (result.mustRetrieveAdditionalResources) {\n                                message += `\\n\\n⚠️ IMPORTANT: You MUST make another query to retrieve critical additional resources. ${result.additionalResourcesSummary}`;\n                            } else if (result.additionalResourcesSummary) {\n                                message += `\\n\\nConsider querying for: ${result.additionalResourcesSummary}`;\n                            }\n                        }\n                        \n                        return {\n                            result,\n                            message,\n                            toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'querySupabaseContext')\n                        };\n                    } catch (error) {\n                        console.error('Supabase context engine error:', error);\n                        return {\n                            result: null,\n                            message: `⚠️ Failed to query Supabase context engine: ${error instanceof Error ? error.message : 'Unknown error'}`\n                        };\n                    }\n                }\n            } catch (error) {\n                // Global error handler - ensures we never throw errors\n                console.error('Unexpected error in querySupabaseContext:', error);\n                return {\n                    result: null,\n                    message: `⚠️ An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`\n                };\n            }\n        },\n    });\n};", "startLine": 23, "endLine": 225, "type": "util", "symbols": ["querySupabaseContext"], "score": 0.6, "context": "Tool to query Supabase resources and execute SQL queries, relevant for project management but less directly about file system and file operations.", "includesImports": false}, {"filePath": "src/lib/terminal/index.ts", "content": "export async function initApkBuildEnvironment(\n  session: { sandbox: Sandbox; terminalId: number | null }\n) {\n  try {\n    // Copy the unified-apk-build.js script to the project directory\n    const copyScriptCommand = `cp /tmp/unified-apk-build.js ${PROJECT_DIR}/scripts/`;\n    await handleTerminalCommand(session, copyScriptCommand);\n    \n    // Make the script executable\n    const chmodCommand = `chmod +x ${PROJECT_DIR}/scripts/unified-apk-build.js`;\n    await handleTerminalCommand(session, chmodCommand);\n    \n    // Create a simple wrapper script for easy access\n    const createWrapperCommand = `\ncat > ${PROJECT_DIR}/build-apk << 'EOL'\n#!/bin/bash\n\n# Simple wrapper for the unified-apk-build.js script\n# Usage: ./build-apk [fast|full|install] [options]\n\nNODE_PATH=${PROJECT_DIR}/node_modules node ${PROJECT_DIR}/scripts/unified-apk-build.js \"$@\"\nEOL\n\nchmod +x ${PROJECT_DIR}/build-apk\n`;\n    await handleTerminalCommand(session, createWrapperCommand);\n    \n    console.log('APK build environment initialized successfully');\n    return true;\n  } catch (error) {\n    console.error('Error initializing APK build environment:', error);\n    return false;\n  }\n}\n\n/**\n * Run a complete APK build workflow\n * This handles the entire process from fixing rendering issues to building and installing the APK\n * \n * @param session - The sandbox session\n * @param options - Build options\n * @returns Promise<boolean> - Whether the workflow was successful\n */\nexport async function runApkBuildWorkflow(\n  session: { sandbox: Sandbox; terminalId: number | null },\n  options: {\n    method?: 'fast' | 'full';\n    dev?: boolean;\n    useHermes?: boolean;\n    fixRendering?: boolean;\n    install?: boolean;\n    outputPath?: string;\n  } = {}\n) {\n  try {\n    const { \n      method = 'fast',\n      dev = false,\n      useHermes = true,\n      fixRendering = true,\n      install = true,\n      outputPath = `${PROJECT_DIR}/build`\n    } = options;\n    \n    // Initialize the build environment\n    await initApkBuildEnvironment(session);\n    \n    // Fix rendering issues if requested\n    if (fixRendering) {\n      console.log('Fixing rendering issues...');\n      await fixRenderingIssues(session);\n    }\n    \n    // Build and optionally install the APK\n    if (install) {\n      return await buildAndInstallApk(session, {\n        method,\n        dev,\n        useHermes,\n        outputPath\n      });\n    } else {\n      return method === 'fast'\n        ? await buildApkFast(session, { dev, useHermes, outputPath })\n        : await buildApkFull(session, { dev, outputPath });\n    }\n  } catch (error) {\n    console.error('Error running APK build workflow:', error);\n    return false;\n  }\n}", "startLine": 36, "endLine": 126, "type": "util", "symbols": ["initApkBuildEnvironment and runApkBuildWorkflow"], "score": 0.5, "context": "Terminal commands to initialize APK build environment and run build workflows, related to project management but less about file system and state management.", "includesImports": false}], "additionalFiles": []}}