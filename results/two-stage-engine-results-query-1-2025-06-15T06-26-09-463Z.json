{"timestamp": "2025-06-15T06:26:09.463Z", "query": "Show me how authentication works in this app", "executionTime": 8512, "snippetsCount": 6, "additionalFilesCount": 0, "totalLines": 321, "snippets": [{"filePath": "src/app/(auth)/auth.ts", "type": "unknown", "context": "This file contains the core authentication setup using NextAuth, including providers (Google and Credentials), user authorization logic, JWT and session callbacks, and user creation for Google OAuth. It directly implements the authentication flow.", "score": 1, "lines": 128, "startLine": 1, "endLine": 128, "symbols": ["NextAuth configuration and providers"], "preview": "import NextAuth, { type User, type Session } from 'next-auth';\nimport { JWT } from '@auth/core/jwt';\nimport Google from 'next-auth/providers/google';\nimport Credentials from 'next-auth/providers/credentials';\nimport { compare } from 'bcrypt-ts';\n..."}, {"filePath": "src/app/(auth)/actions.ts", "type": "util", "context": "This function handles user login by validating form data and calling the signIn method from the auth module, which is part of the authentication process.", "score": 1, "lines": 25, "startLine": 25, "endLine": 49, "symbols": ["login"], "preview": "export const login = async (\n  _: LoginActionState,\n  formData: FormData,\n): Promise<LoginActionState> => {\n  try {\n..."}, {"filePath": "src/app/(auth)/actions.ts", "type": "util", "context": "This function handles user registration, including validation, checking for existing users, creating a new user, signing in, and sending a welcome email. It is a key part of the authentication flow.", "score": 1, "lines": 57, "startLine": 61, "endLine": 117, "symbols": ["register"], "preview": "export const register = async (\n  _: RegisterActionState,\n  formData: FormData,\n): Promise<RegisterActionState> => {\n  try {\n..."}, {"filePath": "src/app/(auth)/actions.ts", "type": "util", "context": "This function handles the password reset request process, including validation, rate limiting, token creation, and sending the reset email. It is part of the authentication workflow.", "score": 1, "lines": 52, "startLine": 136, "endLine": 187, "symbols": ["requestPasswordReset"], "preview": "export const requestPasswordReset = async (\n  _: ResetPasswordRequestState,\n  formData: FormData,\n): Promise<ResetPasswordRequestState> => {\n  try {\n..."}, {"filePath": "src/app/(auth)/actions.ts", "type": "util", "context": "This function handles the password reset process by validating the token and new password, updating the user's password, and deleting the reset token. It is essential for authentication management.", "score": 1, "lines": 37, "startLine": 189, "endLine": 225, "symbols": ["resetPassword"], "preview": "export const resetPassword = async (\n  _: ResetPasswordState,\n  formData: FormData,\n): Promise<ResetPasswordState> => {\n  try {\n..."}, {"filePath": "src/hooks/use-auth.ts", "type": "hook", "context": "This React hook manages authentication state using next-auth's useSession hook and provides redirect logic based on authentication status. It is relevant for client-side auth state management.", "score": 0.8, "lines": 22, "startLine": 6, "endLine": 27, "symbols": ["useAuth"], "preview": "export function useAuth() {\n    const { data: session, status } = useSession();\n    const router = useRouter();\n\n    const isAuthenticated = status === 'authenticated';\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/app/(auth)/auth.ts", "content": "import NextAuth, { type User, type Session } from 'next-auth';\nimport { JWT } from '@auth/core/jwt';\nimport Google from 'next-auth/providers/google';\nimport Credentials from 'next-auth/providers/credentials';\nimport { compare } from 'bcrypt-ts';\n\nimport {createUser, getUser} from '@/lib/db/queries';\nimport { authConfig } from './auth.config';\nimport { sendWelcomeEmail } from '@/lib/email';\n\nexport const {\n  handlers: { GET, POST },\n  auth,\n  signIn,\n  signOut,\n} = NextAuth({\n  ...authConfig,\n  providers: [\n    Google({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n      authorization: {\n        params: {\n          prompt: \"select_account\"\n        }\n      }\n    }),\n    Credentials({\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize({ email, password }: any) {\n        const users = await getUser(email);\n        if (users.length === 0) return null;\n\n        const passwordsMatch = await compare(password, users[0].password!);\n        if (!passwordsMatch) return null;\n\n        // Return user without password\n        const { password: _, ...userWithoutPassword } = users[0];\n        return userWithoutPassword as User;\n      },\n    })\n  ],\n  callbacks: {\n    async jwt({ token, user, account }: { token: any, user: User | null, account: any }) {\n      if (user) {\n        if (account?.provider === 'google') {\n          try {\n            // Check if user exists\n            const existingUsers = await getUser(user.email!);\n            let userId: string;\n            let isNewUser = false;\n\n            if (existingUsers.length === 0) {\n              // Create new user for Google OAuth\n              const userName = user.name || user.email!.split('@')[0]; // Use name or email prefix as name\n              const createdUser = await createUser(\n                user.email!,\n                '', // No password for Google OAuth\n                userName,\n                'google' // Set provider as google\n              );\n              userId = createdUser.id;\n              isNewUser = true;\n\n              // Send welcome email for new Google OAuth users\n              try {\n                await sendWelcomeEmail({\n                  email: user.email!,\n                  name: userName,\n                });\n              } catch (emailError) {\n                // Log the error but don't fail the authentication process\n                console.error('Failed to send welcome email for Google OAuth user:', emailError);\n              }\n            } else {\n              // User exists, treat as login\n              userId = existingUsers[0].id;\n            }\n\n            // Set token properties for Google user\n            token.id = userId;\n            token.isNewUser = isNewUser;\n            token.email = user.email!;\n            token.name = user.name;\n            token.picture = user.image;\n            token.provider = account.provider;\n          } catch (error) {\n            console.error('Error in Google OAuth flow:', error);\n            throw error;\n          }\n        } else {\n          // Credentials provider sign in\n          token.id = user.id as string;\n          token.email = user.email!;\n          token.name = user.name;\n          token.provider = account?.provider || 'credentials';\n          token.isNewUser = false;\n        }\n      }\n      return token;\n    },\n    // @ts-ignore - Bypassing type check for build\n    async session({\n      session,\n      token,\n    }) {\n      if (session.user) {\n        // @ts-ignore\n        session.user.id = token.id;\n        // @ts-ignore\n        session.user.email = token.email;\n        // @ts-ignore\n        session.user.name = token.name;\n        // @ts-ignore\n        session.user.image = token.picture;\n        // @ts-ignore\n        session.user.provider = token.provider;\n        // @ts-ignore\n        session.user.isNewUser = token.isNewUser;\n      }\n      return session;\n    },\n  },\n});\n", "startLine": 1, "endLine": 128, "type": "unknown", "symbols": ["NextAuth configuration and providers"], "score": 1, "context": "This file contains the core authentication setup using NextAuth, including providers (Google and Credentials), user authorization logic, JWT and session callbacks, and user creation for Google OAuth. It directly implements the authentication flow.", "includesImports": false}, {"filePath": "src/app/(auth)/actions.ts", "content": "export const login = async (\n  _: LoginActionState,\n  formData: FormData,\n): Promise<LoginActionState> => {\n  try {\n    const validatedData = authFormSchema.parse({\n      email: formData.get('email'),\n      password: formData.get('password'),\n    });\n\n    await signIn('credentials', {\n      email: validatedData.email,\n      password: validatedData.password,\n      redirect: false,\n    });\n\n    return { status: 'success' };\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { status: 'invalid_data' };\n    }\n\n    return { status: 'failed' };\n  }\n};", "startLine": 25, "endLine": 49, "type": "util", "symbols": ["login"], "score": 1, "context": "This function handles user login by validating form data and calling the signIn method from the auth module, which is part of the authentication process.", "includesImports": false}, {"filePath": "src/app/(auth)/actions.ts", "content": "export const register = async (\n  _: RegisterActionState,\n  formData: FormData,\n): Promise<RegisterActionState> => {\n  try {\n    const validatedData = signupFormSchema.parse({\n      email: formData.get('email'),\n      password: formData.get('password'),\n      name: formData.get(\"name\")\n    });\n\n    const [existingUser] = await getUser(validatedData.email);\n\n    if (existingUser) {\n      return { status: 'user_exists' };\n    }\n\n    // Create the user first\n    const newUser = await createUser(\n      validatedData.email,\n      validatedData.password,\n      validatedData.name,\n        'credentials'\n    );\n\n    // Then sign them in\n    const signInResult = await signIn('credentials', {\n      email: validatedData.email,\n      password: validatedData.password,\n      redirect: false,\n    });\n\n    if (signInResult?.error) {\n      console.error('Failed to sign in after registration:', signInResult.error);\n      return { status: 'failed' };\n    }\n\n    // Send welcome email\n    try {\n      await sendWelcomeEmail({\n        email: validatedData.email,\n        name: validatedData.name,\n      });\n    } catch (emailError) {\n      // Log the error but don't fail the registration process\n      console.error('Failed to send welcome email:', emailError);\n    }\n\n    return { status: 'success' };\n  } catch (error) {\n    console.error('Registration error:', error);\n    if (error instanceof z.ZodError) {\n      return { status: 'invalid_data' };\n    }\n    return { status: 'failed' };\n  }\n};", "startLine": 61, "endLine": 117, "type": "util", "symbols": ["register"], "score": 1, "context": "This function handles user registration, including validation, checking for existing users, creating a new user, signing in, and sending a welcome email. It is a key part of the authentication flow.", "includesImports": false}, {"filePath": "src/app/(auth)/actions.ts", "content": "export const requestPasswordReset = async (\n  _: ResetPasswordRequestState,\n  formData: FormData,\n): Promise<ResetPasswordRequestState> => {\n  try {\n    const validatedData = resetPasswordRequestSchema.parse({\n      email: formData.get('email'),\n    });\n\n    // Check rate limit before proceeding\n    const rateLimitAllowed = await checkPasswordResetRateLimit(validatedData.email);\n    if (!rateLimitAllowed) {\n      return { status: 'rate_limited' };\n    }\n\n    const users = await getUser(validatedData.email);\n    if (users.length === 0) {\n      return { status: 'user_not_found' };\n    }\n\n    const user = users[0];\n    const token = crypto.randomUUID();\n    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours\n\n    await createPasswordResetToken({\n      userId: user.id,\n      token,\n      expiresAt,\n    });\n\n    const resetLink = `${process.env.NEXT_PUBLIC_APP_URL}/reset-password/${token}`;\n\n    try {\n      await sendPasswordResetEmail({\n        email: user.email,\n        resetLink,\n      });\n    } catch (error) {\n      console.error('Failed to send reset email:', error);\n      // Delete the token if email fails\n      await deletePasswordResetToken(token);\n      return { status: 'failed' };\n    }\n\n    return { status: 'success' };\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { status: 'invalid_data' };\n    }\n    return { status: 'failed' };\n  }\n};", "startLine": 136, "endLine": 187, "type": "util", "symbols": ["requestPasswordReset"], "score": 1, "context": "This function handles the password reset request process, including validation, rate limiting, token creation, and sending the reset email. It is part of the authentication workflow.", "includesImports": false}, {"filePath": "src/app/(auth)/actions.ts", "content": "export const resetPassword = async (\n  _: ResetPasswordState,\n  formData: FormData,\n): Promise<ResetPasswordState> => {\n  try {\n    const validatedData = resetPasswordSchema.parse({\n      token: formData.get('token'),\n      password: formData.get('password'),\n    });\n\n    const tokens = await getPasswordResetToken(validatedData.token);\n    if (tokens.length === 0) {\n      return { status: 'invalid_token' };\n    }\n\n    const token = tokens[0];\n    if (token.expiresAt < new Date()) {\n      return { status: 'token_expired' };\n    }\n\n    const salt = genSaltSync(10);\n    const hashedPassword = hashSync(validatedData.password, salt);\n    await updateUserPassword({\n      userId: token.userId,\n      hashedPassword,\n    });\n\n    await deletePasswordResetToken(token.token);\n\n    return { status: 'success' };\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { status: 'invalid_data' };\n    }\n    return { status: 'failed' };\n  }\n};", "startLine": 189, "endLine": 225, "type": "util", "symbols": ["resetPassword"], "score": 1, "context": "This function handles the password reset process by validating the token and new password, updating the user's password, and deleting the reset token. It is essential for authentication management.", "includesImports": false}, {"filePath": "src/hooks/use-auth.ts", "content": "export function useAuth() {\n    const { data: session, status } = useSession();\n    const router = useRouter();\n\n    const isAuthenticated = status === 'authenticated';\n    const isLoading = status === 'loading';\n\n    const handleRedirect = () => {\n        if (isAuthenticated) {\n            router.push('/applications');\n        } else {\n            router.push('/login');\n        }\n    };\n\n    return {\n        isAuthenticated,\n        isLoading,\n        handleRedirect,\n        session,\n    };\n}", "startLine": 6, "endLine": 27, "type": "hook", "symbols": ["useAuth"], "score": 0.8, "context": "This React hook manages authentication state using next-auth's useSession hook and provides redirect logic based on authentication status. It is relevant for client-side auth state management.", "includesImports": false}], "additionalFiles": []}}