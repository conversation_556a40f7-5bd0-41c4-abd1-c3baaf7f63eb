{"timestamp": "2025-06-14T17:33:53.666Z", "query": "Find the authentication system and how user sessions are managed across the application", "executionTime": 14292, "snippetsCount": 6, "additionalFilesCount": 0, "totalLines": 297, "snippets": [{"filePath": "src/app/(auth)/auth.ts", "type": "config", "context": "This snippet contains the main authentication system configuration using NextAuth, including providers (Google and Credentials), user authorization logic, JWT and session callbacks that manage user sessions and token data.", "score": 1, "lines": 127, "startLine": 1, "endLine": 127, "symbols": ["NextAuth configuration with providers and callbacks"], "preview": "import NextAuth, { type User, type Session } from 'next-auth';\nimport { JWT } from '@auth/core/jwt';\nimport Google from 'next-auth/providers/google';\nimport Credentials from 'next-auth/providers/credentials';\nimport { compare } from 'bcrypt-ts';\n..."}, {"filePath": "src/app/(auth)/actions.ts", "type": "util", "context": "This function handles user login by validating credentials and calling signIn from the auth system, directly related to authentication flow.", "score": 0.9, "lines": 25, "startLine": 25, "endLine": 49, "symbols": ["login"], "preview": "export const login = async (\n  _: LoginActionState,\n  formData: FormData,\n): Promise<LoginActionState> => {\n  try {\n..."}, {"filePath": "src/app/(auth)/actions.ts", "type": "util", "context": "This function handles user registration, creating users and signing them in, which is part of the authentication system and session initiation.", "score": 0.9, "lines": 57, "startLine": 61, "endLine": 117, "symbols": ["register"], "preview": "export const register = async (\n  _: RegisterActionState,\n  formData: FormData,\n): Promise<RegisterActionState> => {\n  try {\n..."}, {"filePath": "src/hooks/use-auth.ts", "type": "hook", "context": "This React hook uses next-auth's useSession to manage user session state and provides redirect logic based on authentication status, relevant for session management in the app.", "score": 0.8, "lines": 22, "startLine": 6, "endLine": 27, "symbols": ["useAuth"], "preview": "export function useAuth() {\n    const { data: session, status } = useSession();\n    const router = useRouter();\n\n    const isAuthenticated = status === 'authenticated';\n..."}, {"filePath": "src/app/(auth)/auth.config.ts", "type": "config", "context": "This config defines auth pages, protected routes, and authorization callback logic, which controls access and session authorization across the app.", "score": 0.7, "lines": 57, "startLine": 3, "endLine": 59, "symbols": ["authConfig"], "preview": "export const authConfig: NextAuthConfig = {\n    pages: {\n        signIn: '/login',\n        newUser: '/',\n    },\n..."}, {"filePath": "middleware.ts", "type": "unknown", "context": "Exports NextAuth middleware with authConfig, relevant for session management and authentication enforcement on routes.", "score": 0.6, "lines": 9, "startLine": 1, "endLine": 9, "symbols": ["NextAuth middleware export"], "preview": "import NextAuth from 'next-auth';\n\nimport { authConfig } from '@/app/(auth)/auth.config';\n\nexport default NextAuth(authConfig).auth;\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/app/(auth)/auth.ts", "content": "import NextAuth, { type User, type Session } from 'next-auth';\nimport { JWT } from '@auth/core/jwt';\nimport Google from 'next-auth/providers/google';\nimport Credentials from 'next-auth/providers/credentials';\nimport { compare } from 'bcrypt-ts';\n\nimport {createUser, getUser} from '@/lib/db/queries';\nimport { authConfig } from './auth.config';\nimport { sendWelcomeEmail } from '@/lib/email';\n\nexport const {\n  handlers: { GET, POST },\n  auth,\n  signIn,\n  signOut,\n} = NextAuth({\n  ...authConfig,\n  providers: [\n    Google({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n      authorization: {\n        params: {\n          prompt: \"select_account\"\n        }\n      }\n    }),\n    Credentials({\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize({ email, password }: any) {\n        const users = await getUser(email);\n        if (users.length === 0) return null;\n\n        const passwordsMatch = await compare(password, users[0].password!);\n        if (!passwordsMatch) return null;\n\n        // Return user without password\n        const { password: _, ...userWithoutPassword } = users[0];\n        return userWithoutPassword as User;\n      },\n    })\n  ],\n  callbacks: {\n    async jwt({ token, user, account }: { token: any, user: User | null, account: any }) {\n      if (user) {\n        if (account?.provider === 'google') {\n          try {\n            // Check if user exists\n            const existingUsers = await getUser(user.email!);\n            let userId: string;\n            let isNewUser = false;\n\n            if (existingUsers.length === 0) {\n              // Create new user for Google OAuth\n              const userName = user.name || user.email!.split('@')[0]; // Use name or email prefix as name\n              const createdUser = await createUser(\n                user.email!,\n                '', // No password for Google OAuth\n                userName,\n                'google' // Set provider as google\n              );\n              userId = createdUser.id;\n              isNewUser = true;\n\n              // Send welcome email for new Google OAuth users\n              try {\n                await sendWelcomeEmail({\n                  email: user.email!,\n                  name: userName,\n                });\n              } catch (emailError) {\n                // Log the error but don't fail the authentication process\n                console.error('Failed to send welcome email for Google OAuth user:', emailError);\n              }\n            } else {\n              // User exists, treat as login\n              userId = existingUsers[0].id;\n            }\n\n            // Set token properties for Google user\n            token.id = userId;\n            token.isNewUser = isNewUser;\n            token.email = user.email!;\n            token.name = user.name;\n            token.picture = user.image;\n            token.provider = account.provider;\n          } catch (error) {\n            console.error('Error in Google OAuth flow:', error);\n            throw error;\n          }\n        } else {\n          // Credentials provider sign in\n          token.id = user.id as string;\n          token.email = user.email!;\n          token.name = user.name;\n          token.provider = account?.provider || 'credentials';\n          token.isNewUser = false;\n        }\n      }\n      return token;\n    },\n    // @ts-ignore - Bypassing type check for build\n    async session({\n      session,\n      token,\n    }) {\n      if (session.user) {\n        // @ts-ignore\n        session.user.id = token.id;\n        // @ts-ignore\n        session.user.email = token.email;\n        // @ts-ignore\n        session.user.name = token.name;\n        // @ts-ignore\n        session.user.image = token.picture;\n        // @ts-ignore\n        session.user.provider = token.provider;\n        // @ts-ignore\n        session.user.isNewUser = token.isNewUser;\n      }\n      return session;\n    },\n  },\n});", "startLine": 1, "endLine": 127, "type": "config", "symbols": ["NextAuth configuration with providers and callbacks"], "score": 1, "context": "This snippet contains the main authentication system configuration using NextAuth, including providers (Google and Credentials), user authorization logic, JWT and session callbacks that manage user sessions and token data.", "includesImports": false}, {"filePath": "src/app/(auth)/actions.ts", "content": "export const login = async (\n  _: LoginActionState,\n  formData: FormData,\n): Promise<LoginActionState> => {\n  try {\n    const validatedData = authFormSchema.parse({\n      email: formData.get('email'),\n      password: formData.get('password'),\n    });\n\n    await signIn('credentials', {\n      email: validatedData.email,\n      password: validatedData.password,\n      redirect: false,\n    });\n\n    return { status: 'success' };\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { status: 'invalid_data' };\n    }\n\n    return { status: 'failed' };\n  }\n};", "startLine": 25, "endLine": 49, "type": "util", "symbols": ["login"], "score": 0.9, "context": "This function handles user login by validating credentials and calling signIn from the auth system, directly related to authentication flow.", "includesImports": false}, {"filePath": "src/app/(auth)/actions.ts", "content": "export const register = async (\n  _: RegisterActionState,\n  formData: FormData,\n): Promise<RegisterActionState> => {\n  try {\n    const validatedData = signupFormSchema.parse({\n      email: formData.get('email'),\n      password: formData.get('password'),\n      name: formData.get(\"name\")\n    });\n\n    const [existingUser] = await getUser(validatedData.email);\n\n    if (existingUser) {\n      return { status: 'user_exists' };\n    }\n\n    // Create the user first\n    const newUser = await createUser(\n      validatedData.email,\n      validatedData.password,\n      validatedData.name,\n        'credentials'\n    );\n\n    // Then sign them in\n    const signInResult = await signIn('credentials', {\n      email: validatedData.email,\n      password: validatedData.password,\n      redirect: false,\n    });\n\n    if (signInResult?.error) {\n      console.error('Failed to sign in after registration:', signInResult.error);\n      return { status: 'failed' };\n    }\n\n    // Send welcome email\n    try {\n      await sendWelcomeEmail({\n        email: validatedData.email,\n        name: validatedData.name,\n      });\n    } catch (emailError) {\n      // Log the error but don't fail the registration process\n      console.error('Failed to send welcome email:', emailError);\n    }\n\n    return { status: 'success' };\n  } catch (error) {\n    console.error('Registration error:', error);\n    if (error instanceof z.ZodError) {\n      return { status: 'invalid_data' };\n    }\n    return { status: 'failed' };\n  }\n};", "startLine": 61, "endLine": 117, "type": "util", "symbols": ["register"], "score": 0.9, "context": "This function handles user registration, creating users and signing them in, which is part of the authentication system and session initiation.", "includesImports": false}, {"filePath": "src/hooks/use-auth.ts", "content": "export function useAuth() {\n    const { data: session, status } = useSession();\n    const router = useRouter();\n\n    const isAuthenticated = status === 'authenticated';\n    const isLoading = status === 'loading';\n\n    const handleRedirect = () => {\n        if (isAuthenticated) {\n            router.push('/applications');\n        } else {\n            router.push('/login');\n        }\n    };\n\n    return {\n        isAuthenticated,\n        isLoading,\n        handleRedirect,\n        session,\n    };\n}", "startLine": 6, "endLine": 27, "type": "hook", "symbols": ["useAuth"], "score": 0.8, "context": "This React hook uses next-auth's useSession to manage user session state and provides redirect logic based on authentication status, relevant for session management in the app.", "includesImports": false}, {"filePath": "src/app/(auth)/auth.config.ts", "content": "export const authConfig: NextAuthConfig = {\n    pages: {\n        signIn: '/login',\n        newUser: '/',\n    },\n    providers: [\n        // added later in auth.ts since it requires bcrypt which is only compatible with Node.js\n        // while this file is also used in non-Node.js environments\n    ],\n    callbacks: {\n        authorized({auth, request: {nextUrl}}) {\n            const isLoggedIn = !!auth?.user;\n            \n            // Check if this is a media API route - these should be public\n            if (nextUrl.pathname.startsWith('/api/media')) {\n                return true; // Always allow access to media API routes\n            }\n\n            // Protected Routes that require auth\n            const protectedPaths = [\n                '/dashboard'\n                // '/generator' removed to allow anonymous access\n            ];\n\n            // Auth Routes (login/register)\n            const authPaths = [\n                '/login',\n                '/register'\n            ];\n\n            const isProtectedRoute = protectedPaths.some(path =>\n                nextUrl.pathname.startsWith(path)\n            );\n            const isAuthRoute = authPaths.some(path =>\n                nextUrl.pathname.startsWith(path)\n            );\n\n            // Redirect logged-in users away from auth pages\n            if (isLoggedIn && isAuthRoute) {\n                return Response.redirect(new URL('/', nextUrl));\n            }\n\n            // Allow access to auth pages if not logged in\n            if (isAuthRoute) {\n                return true;\n            }\n\n            // Protect routes that require auth\n            if (isProtectedRoute) {\n                return isLoggedIn;\n            }\n\n            // Allow public access to all other routes (including landing page)\n            return true;\n        },\n    },\n} satisfies NextAuthConfig;", "startLine": 3, "endLine": 59, "type": "config", "symbols": ["authConfig"], "score": 0.7, "context": "This config defines auth pages, protected routes, and authorization callback logic, which controls access and session authorization across the app.", "includesImports": false}, {"filePath": "middleware.ts", "content": "import NextAuth from 'next-auth';\n\nimport { authConfig } from '@/app/(auth)/auth.config';\n\nexport default NextAuth(authConfig).auth;\n\nexport const config = {\n  matcher: ['/', '/:id', '/api/:path*', '/login', '/register'],\n};", "startLine": 1, "endLine": 9, "type": "unknown", "symbols": ["NextAuth middleware export"], "score": 0.6, "context": "Exports NextAuth middleware with authConfig, relevant for session management and authentication enforcement on routes.", "includesImports": false}], "additionalFiles": []}}