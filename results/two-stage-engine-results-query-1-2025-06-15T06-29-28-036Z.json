{"timestamp": "2025-06-15T06:29:28.036Z", "query": "How does the payment work in the app? Where can we change to fix subscription renewal bug?", "executionTime": 13868, "snippetsCount": 3, "additionalFilesCount": 0, "totalLines": 75, "snippets": [{"filePath": "src/lib/db/schema.ts", "type": "unknown", "context": "This schema defines the 'subscriptions' table which stores subscription details including status, plan, provider IDs, renewal-related fields like 'stripeCurrentPeriodEnd', and metadata. This is directly relevant to understanding payment and subscription renewal.", "score": 1, "lines": 37, "startLine": 378, "endLine": 414, "symbols": ["subscriptions"], "preview": "export const subscriptions = pgTable('Subscription', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  userId: varchar('userId', { length: 255 }).notNull(),\n  status: varchar('status', { length: 50 }).notNull().default('inactive'),\n  planId: varchar('planId', { length: 50 }).notNull().default('free'),\n..."}, {"filePath": "src/lib/db/queries.ts", "type": "util", "context": "Function to fetch subscription details by ID from the database, relevant for payment and subscription renewal logic.", "score": 0.9, "lines": 9, "startLine": 1529, "endLine": 1537, "symbols": ["getSubscriptionById"], "preview": "export async function getSubscriptionById(id: string): Promise<typeof subscriptionsSchema.$inferSelect | undefined> {\n  try {\n    const result = await db.select().from(subscriptionsSchema).where(eq(subscriptionsSchema.id, id)).limit(1);\n    return result[0];\n  } catch (error) {\n..."}, {"filePath": "src/lib/db/queries.ts", "type": "util", "context": "Function to update subscription status on cancellation, including updating 'isActive' and 'metadata'. Relevant for fixing subscription renewal bugs related to cancellation handling.", "score": 0.9, "lines": 29, "startLine": 1539, "endLine": 1567, "symbols": ["updateSubscriptionOnCancel"], "preview": "export async function updateSubscriptionOnCancel(\n  id: string,\n  isActive: boolean,\n  newMetadata: Record<string, any>,\n  currentMetadata?: Record<string, any> | null\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/db/schema.ts", "content": "export const subscriptions = pgTable('Subscription', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  userId: varchar('userId', { length: 255 }).notNull(),\n  status: varchar('status', { length: 50 }).notNull().default('inactive'),\n  planId: varchar('planId', { length: 50 }).notNull().default('free'),\n  subscriptionId: varchar('subscriptionId', { length: 255 }).unique(),\n  credits: integer('credits').notNull().default(50), // Default to free tier credits\n  creditsUsed: integer('creditsUsed').notNull().default(0),\n  isActive: boolean('isActive').default(false),\n  // LemonSqueezy fields\n  lemonSqueezyCustomerId: varchar('lemonSqueezyCustomerId'),\n  lemonSqueezySubscriptionId: varchar('lemonSqueezySubscriptionId'),\n  lemonSqueezyOrderId: varchar('lemonSqueezyOrderId'),\n  lemonSqueezyVariantId: varchar('lemonSqueezyVariantId'),\n  provider: varchar('provider'),\n  // Stripe fields (for compatibility)\n  stripeCustomerId: varchar('stripeCustomerId'),\n  stripeSubscriptionId: varchar('stripeSubscriptionId'),\n  stripePriceId: varchar('stripePriceId'),\n  stripeCurrentPeriodEnd: timestamp('stripeCurrentPeriodEnd'),\n  stripeVariantId: timestamp('stripeVariantId'),\n  // Billing cycle\n  resetDate: timestamp('resetDate'),\n  // Flags\n  isDowngraded: boolean('isDowngraded').default(false),\n  // Additional metadata (cancellation feedback, etc.)\n  metadata: json('metadata').$type<Record<string, any>>(),\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n}, (table) => {\n  return {\n    userIdIdx: index('subscription_userId_idx').on(table.userId),\n    statusIdx: index('subscription_status_idx').on(table.status),\n    userIdUpdatedAtIdx: index('subscription_userId_updatedAt_idx').on(table.userId, table.updatedAt),\n    isActiveIdx: index('subscription_isActive_idx').on(table.isActive)\n  };\n});", "startLine": 378, "endLine": 414, "type": "unknown", "symbols": ["subscriptions"], "score": 1, "context": "This schema defines the 'subscriptions' table which stores subscription details including status, plan, provider IDs, renewal-related fields like 'stripeCurrentPeriodEnd', and metadata. This is directly relevant to understanding payment and subscription renewal.", "includesImports": false}, {"filePath": "src/lib/db/queries.ts", "content": "export async function getSubscriptionById(id: string): Promise<typeof subscriptionsSchema.$inferSelect | undefined> {\n  try {\n    const result = await db.select().from(subscriptionsSchema).where(eq(subscriptionsSchema.id, id)).limit(1);\n    return result[0];\n  } catch (error) {\n    console.error('Failed to get subscription by LemonSqueezy ID from database:', error);\n    throw error;\n  }\n}", "startLine": 1529, "endLine": 1537, "type": "util", "symbols": ["getSubscriptionById"], "score": 0.9, "context": "Function to fetch subscription details by ID from the database, relevant for payment and subscription renewal logic.", "includesImports": false}, {"filePath": "src/lib/db/queries.ts", "content": "export async function updateSubscriptionOnCancel(\n  id: string,\n  isActive: boolean,\n  newMetadata: Record<string, any>,\n  currentMetadata?: Record<string, any> | null\n) {\n  try {\n    const combinedMetadata = { ...currentMetadata, ...newMetadata };\n    const endsAtFromMeta = newMetadata.endsAt;\n    const updates = {\n      status: 'cancelled',\n      isActive: isActive,\n      metadata: combinedMetadata,\n      updatedAt: new Date(),\n    }\n    const resetDateValue = endsAtFromMeta;\n    if (resetDateValue) {\n      updates[resetDate] = resetDateValue;\n    }\n\n    await db\n      .update(subscriptionsSchema)\n      .set(updates as any)\n      .where(eq(subscriptionsSchema.id, id));\n  } catch (error) {\n    console.error('Failed to update subscription on cancel in database:', error);\n    throw error;\n  }\n}", "startLine": 1539, "endLine": 1567, "type": "util", "symbols": ["updateSubscriptionOnCancel"], "score": 0.9, "context": "Function to update subscription status on cancellation, including updating 'isActive' and 'metadata'. Relevant for fixing subscription renewal bugs related to cancellation handling.", "includesImports": false}], "additionalFiles": []}}