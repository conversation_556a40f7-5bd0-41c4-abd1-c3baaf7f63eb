{"timestamp": "2025-06-15T13:48:43.271Z", "query": "Show me how the two-stage context engine works and how it selects relevant files for queries", "executionTime": 12259, "snippetsCount": 3, "additionalFilesCount": 0, "totalLines": 1426, "snippets": [{"filePath": "src/lib/services/two-stage-context-engine.ts", "type": "unknown", "context": "This class implements the two-stage context engine, including methods for selecting relevant files (first stage) and identifying relevant snippets within those files (second stage). It orchestrates the two-stage process and is the core of the architecture.", "score": 1, "lines": 680, "startLine": 27, "endLine": 706, "symbols": ["TwoStageLLMContextEngine"], "preview": "export class TwoStageLLMContextEngine {\n  private files: FileItem[] = [];\n  private fileIndex: Map<string, FileItem> = new Map();\n\n  /**\n..."}, {"filePath": "src/lib/services/context-engine.ts", "type": "unknown", "context": "This class uses the TwoStageLLMContextEngine to extract relevant code snippets and manages the overall context querying process, showing how the two-stage engine is integrated and used to select relevant files and snippets.", "score": 0.9, "lines": 585, "startLine": 106, "endLine": 690, "symbols": ["ContextEngine"], "preview": "export class ContextEngine {\n    private files: FileItem[] = [];\n    private fileIndex: Map<string, FileItem> = new Map();\n    private dependencyGraph: Map<string, Set<string>> = new Map();\n    private fileMetadata: Map<string, FileMetadata> = new Map();\n..."}, {"filePath": "src/lib/chat/tools/query-codebase.ts", "type": "util", "context": "This tool function initializes the ContextEngine and calls its query method to get relevant files and snippets for a given query, showing how the two-stage context engine is used in the chat tool to select relevant files.", "score": 0.8, "lines": 161, "startLine": 23, "endLine": 183, "symbols": ["queryCodebase"], "preview": "export const queryCodebase = ({\n                                  projectId,\n                                  files,\n                                  fileManager,\n                                  messageId = '',\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/services/two-stage-context-engine.ts", "content": "export class TwoStageLLMContextEngine {\n  private files: FileItem[] = [];\n  private fileIndex: Map<string, FileItem> = new Map();\n\n  /**\n   * Initialize with project files\n   */\n  constructor(files: FileItem[]) {\n    this.files = files;\n    this.buildIndex();\n  }\n\n  /**\n   * Build an index of files for quick lookup\n   */\n  private buildIndex(): void {\n    for (const file of this.files) {\n      this.fileIndex.set(file.name, file);\n    }\n  }\n\n  /**\n   * Get a structured representation of the codebase optimized for cheap LLM understanding\n   * Groups files logically and provides clear context for React Native + Supabase projects\n   */\n  private getCodebaseStructure(): string {\n    // Build import relationships\n    const importMap = new Map<string, Set<string>>();\n\n    // Extract imports and build relationships\n    this.files.forEach(file => {\n      const imports = this.extractImports(file.content || \"\");\n      imports.forEach(importPath => {\n        const resolvedPath = this.resolveImportPath(importPath, file.name);\n        if (resolvedPath) {\n          if (!importMap.has(resolvedPath)) {\n            importMap.set(resolvedPath, new Set());\n          }\n          importMap.get(resolvedPath)?.add(file.name);\n        }\n      });\n    });\n\n    // Group files by logical categories for better LLM understanding\n    const fileGroups = this.groupFilesByCategory();\n\n    // Build structured output\n    const sections: string[] = [];\n\n    // Add header\n    sections.push(\"PROJECT STRUCTURE:\");\n    sections.push(\"================\");\n\n    // Process each category\n    Object.entries(fileGroups).forEach(([category, files]) => {\n      if (files.length > 0) {\n        sections.push(`\\n${category}:`);\n\n        files.forEach(file => {\n          const fileType = this.determineFileType(file.name, file.content || \"\");\n          const exports = this.extractExports(file.content || \"\");\n          const usageCount = importMap.get(file.name)?.size || 0;\n          const usedByFiles = Array.from(importMap.get(file.name) || []).slice(0, 2); // Limit to 2 for space\n\n          // Create clean, structured line\n          let line = `  ${file.name}`;\n\n          // Add type indicator\n          if (fileType !== 'unknown') {\n            line += ` [${fileType}]`;\n          }\n\n          // Add key exports (limit to 3 most important)\n          const keyExports = this.getKeyExports(exports, fileType);\n          if (keyExports.length > 0) {\n            line += ` → ${keyExports.slice(0, 3).join(', ')}`;\n            if (keyExports.length > 3) {\n              line += ` +${keyExports.length - 3}more`;\n            }\n          }\n\n          // Add usage information (count + specific files)\n          if (usageCount > 0) {\n            line += ` (${usageCount} refs`;\n            if (usedByFiles.length > 0) {\n              // Show just the file names without full paths for brevity\n              const shortNames = usedByFiles.map(f => f.split('/').pop()).slice(0, 2);\n              line += `: ${shortNames.join(', ')}`;\n              if (usageCount > 2) {\n                line += ` +${usageCount - 2}more`;\n              }\n            }\n            line += ')';\n          }\n\n          sections.push(line);\n        });\n      }\n    });\n\n    return sections.join('\\n');\n  }\n\n  /**\n   * Group files into logical categories for better LLM understanding\n   */\n  private groupFilesByCategory(): Record<string, typeof this.files> {\n    const groups: Record<string, typeof this.files> = {\n      'App Entry': [],\n      'Screens': [],\n      'Components': [],\n      'Navigation': [],\n      'State/Stores': [],\n      'Services': [],\n      'Database/Supabase': [],\n      'Utils/Helpers': [],\n      'Types': [],\n      'Config': [],\n      'Other': []\n    };\n\n    this.files.forEach(file => {\n      const path = file.name.toLowerCase();\n      const name = file.name.toLowerCase();\n\n      // App entry points\n      if (name.includes('app.') || name.includes('index.') || name === 'app.tsx' || name === 'app.ts') {\n        groups['App Entry'].push(file);\n      }\n      // Screens\n      else if (path.includes('screen') || path.includes('page') || name.endsWith('screen.tsx')) {\n        groups['Screens'].push(file);\n      }\n      // Navigation\n      else if (path.includes('navigation') || path.includes('navigator') || name.includes('navigation')) {\n        groups['Navigation'].push(file);\n      }\n      // Components\n      else if (path.includes('component') || name.endsWith('.tsx') && !path.includes('screen')) {\n        groups['Components'].push(file);\n      }\n      // State management\n      else if (path.includes('store') || path.includes('context') || name.includes('store') || name.includes('context')) {\n        groups['State/Stores'].push(file);\n      }\n      // Services\n      else if (path.includes('service') || path.includes('api') || name.includes('service')) {\n        groups['Services'].push(file);\n      }\n      // Database/Supabase\n      else if (path.includes('supabase') || path.includes('database') || path.includes('db') || name.includes('supabase')) {\n        groups['Database/Supabase'].push(file);\n      }\n      // Types\n      else if (name.includes('type') || name.includes('.d.ts') || path.includes('types')) {\n        groups['Types'].push(file);\n      }\n      // Utils\n      else if (path.includes('util') || path.includes('helper') || name.includes('util') || name.includes('helper')) {\n        groups['Utils/Helpers'].push(file);\n      }\n      // Config\n      else if (name.includes('config') || name.includes('.config.') || path.includes('config')) {\n        groups['Config'].push(file);\n      }\n      // Everything else\n      else {\n        groups['Other'].push(file);\n      }\n    });\n\n    // Sort files within each group by importance (usage and name)\n    Object.keys(groups).forEach(category => {\n      groups[category].sort((a, b) => {\n        // Prioritize index files and main entry points\n        if (a.name.includes('index.') && !b.name.includes('index.')) return -1;\n        if (!a.name.includes('index.') && b.name.includes('index.')) return 1;\n\n        // Then by name\n        return a.name.localeCompare(b.name);\n      });\n    });\n\n    return groups;\n  }\n\n  /**\n   * Extract key exports, filtering noise for better LLM focus\n   */\n  private getKeyExports(exports: string[], fileType: string): string[] {\n    // Filter out common noise\n    const filtered = exports.filter(exp =>\n      exp !== 'default' &&\n      exp.length > 2 && // Skip very short names\n      !exp.toLowerCase().includes('props') &&\n      !exp.toLowerCase().includes('style')\n    );\n\n    // For components, prioritize component names\n    if (fileType === 'component' || fileType === 'screen') {\n      return filtered.filter(exp =>\n        !exp.toLowerCase().startsWith('use') || exp.length > 6 // Keep longer hooks\n      );\n    }\n\n    return filtered.slice(0, 5); // Limit to 5 key exports\n  }\n\n  /**\n   * First stage: Find relevant files for a query\n   */\n  async findRelevantFiles(query: string, reason: string, excludedFiles: string[] = []): Promise<{files: string[], reasoning: string }> {\n    console.time('find-relevant-files');\n\n    // Get a compact representation of the codebase structure\n    const codebaseStructure = this.getCodebaseStructure();\n\n    // console.log('codebaseStructure', codebaseStructure)\n\n    // Use LLM to identify relevant files\n    const result = await generateObject({\n      model: customModel('openai/gpt-4.1-nano'), // Using a smaller model to reduce costs\n      temperature: 0.1,\n      schema: z.object({\n        files: z.array(z.string()),\n        reasoning: z.string().describe(\"Explanation of why these files were selected\")\n      }),\n      system: `You are a code analysis expert. Your task is to identify which files in a React Native Expo project are most relevant to a specific query.\n\nYou will be given:\n1. A query about the codebase\n2. A structured representation of files organized by category\n\nHOW TO READ THE FILE FORMAT:\n- Files are grouped by category (App Entry, Screens, Components, Services, etc.)\n- Each file line shows: filename [type] → exports (usage_info)\n- [type] indicates file purpose: [component], [hook], [service], [context], [config], etc.\n- → exports shows key functions/classes exported by the file\n- (N refs: file1, file2 +Nmore) shows which files import/use this file\n- Higher ref counts indicate more important/central files\n- Files with many references are often core infrastructure\n- Understand the semantic use of the file and its dependencies, sometimes the pure file name may not be sufficient\n- Understand related concepts across the entire stack needed to answer the query, look for relationships of the data flow\n\nSELECTION STRATEGY:\n1. For architectural queries: Focus on files with high ref counts and core types\n2. For specific features: Look for files in relevant categories with matching exports\n3. For debugging: Include both the problematic area AND its dependencies (ref relationships)\n4. Always include some high-ref-count files for context, even if not directly related\n\nReturn a JSON object with:\n1. An array of the most relevant file paths (maximum 20)\n2. Your reasoning for selecting these files\n\nChoose files that would be most helpful for understanding or implementing the query.`,\n      prompt: `Query: ${query}\n      \nReason: ${reason}\n\nFiles in the project:\n${codebaseStructure}\n\n${excludedFiles.length > 0 ? `Note: The following files have been excluded from analysis: ${excludedFiles.join(', ')}\\n\\n` : ''}\nReturn the most relevant file paths (maximum 20) and your reasoning:`,\n    });\n\n    console.timeEnd('find-relevant-files');\n    console.log(`Selected files reasoning: ${result.object.reasoning}`);\n\n    console.log('result.object.files', result.object.files)\n    // Filter out any files that don't exist in our index\n    return {files: result.object.files.filter(file => this.fileIndex.has(file)), reasoning: result.object.reasoning};\n  }\n\n  /**\n   * Second stage: Identify relevant snippets within files\n   */\n  async identifyRelevantSnippets(query: string, originalReason: string, relevantFiles: string[], previousStageReason: string): Promise<SnippetIdentification[]> {\n    console.time('identify-snippets');\n\n\n    // Prepare file contents with line numbers\n    const fileContents = relevantFiles.map(fileName => {\n      const file = this.fileIndex.get(fileName);\n      const content = file?.content || \"\";\n\n      // Add line numbers to help the LLM identify specific ranges\n      // Format with consistent padding to make line numbers stand out\n      const lines = content.split(\"\\n\");\n      const maxLineNumberWidth = String(lines.length).length;\n      const numberedContent = lines.map((line, i) => {\n        const lineNumber = i + 1;\n        const paddedLineNumber = String(lineNumber).padStart(maxLineNumberWidth, ' ');\n        return `${paddedLineNumber}: ${line}`;\n      }).join(\"\\n\");\n\n      return {\n        name: fileName,\n        content: numberedContent,\n        lineCount: lines.length\n      };\n    });\n\n    // Use LLM to identify relevant snippets within these files\n    const result = await generateObject({\n      model: customModel('openai/gpt-4.1-mini'), // Using a more capable model for this task\n      temperature: 0.1,\n      schema: z.object({\n        snippets: z.array(z.object({\n          fileName: z.string(),\n          startLine: z.number(),\n          endLine: z.number(),\n          snippetType: z.string(),\n          snippetName: z.string(),\n          relevanceScore: z.number().min(0).max(1),\n          reasoning: z.string()\n        }))\n      }),\n      system: `You are a code analysis expert. Your task is to identify the MINIMAL relevant code snippets within files for a specific query.\n\nReturn the exact line numbers for each snippet, along with metadata about the snippet.\n\nGuidelines for MINIMAL context extraction:\n1. Extract ONLY the specific lines directly relevant to the query - avoid including entire files or anything extra at all\n2. For imports, include ONLY the imports directly related to the query and is needed to reliable editing\n2. For style, include ONLY the styles directly related to the query and is needed to reliable editing\n3. For components, include ONLY the component declaration, props, and relevant JSX elements - NOT the entire implementation\n4. For functions, include ONLY the signature and critical logic - NOT entire function bodies\n5. Assign a relevance score from 0.0 to 1.0 based on how directly the snippet addresses the query. \n6. Keep snippets as SHORT as possible while maintaining necessary context\n7. Pay close attention to the line numbers at the beginning of each line (formatted as \"NUMBER: code\")\n8. For React errors, focus on component declarations, imports/exports, and JSX return statements\n9. NEVER include style definitions unless they are directly relevant to the query\n10. NEVER include helper functions unless they are directly relevant to the query\n11. When specific line numbers are requested, return only those line numbers\n\nToken efficiency guidelines:\n1. Maximum 30 lines per snippet unless absolutely necessary\n4. Omit implementation details of methods unless directly relevant\n5. For UI issues, include only the relevant JSX elements, not entire render methods\n6. When multiple similar components exist, include only one representative example\\``,\n      prompt: `Query: ${query}\n\nI need to identify the MINIMAL relevant code snippets in these files. For each relevant code block, provide:\n1. The file name\n2. Start and end line numbers (inclusive) - use the exact line numbers shown at the beginning of each line\n3. Type of snippet (function, component, hook, type, etc.)\n4. Name of the snippet (function name, component name, etc.)\n5. Relevance score (0.0 to 1.0)\n6. Brief reasoning for why this snippet is relevant\n\nReliable editing:\n- Please include import and styles if they need are needed to add imports\n- Include just enough context for clear understanding and editing \n\nCRITICAL TOKEN EFFICIENCY GUIDELINES:\n- Extract ONLY the specific lines directly relevant to the query\n- Each line in the files is prefixed with its line number (e.g., \"42: const foo = bar;\"). Use these exact line numbers.\n- For imports, include ONLY those directly related to the query\n- For components, include ONLY the declaration, props, and relevant JSX - NOT entire implementations\n- NEVER include style definitions unless directly relevant to the query\n- Keep snippets to a MAXIMUM of 30 lines when possible\n- For React errors, focus ONLY on component declarations, imports/exports, and JSX return statements\n- AVOID including entire component implementations - be extremely selective\n\nReasoning from the user/llm issuing the original query on why they want the result. This gets fed to the first engine:\n${originalReason}\n\nReasoning from the first engine on why each file was selected. Use the reasoning to understand the whole flow and to select snippets accordingly:\n${previousStageReason}\n\nFiles to analyze:\n${fileContents.map(file => `\n=== ${file.name} (${file.lineCount} lines) ===\n${file.content}\n`).join(\"\\n\\n\")}\n\nReturn an array of the most relevant code snippets with their exact line numbers and metadata:`,\n    });\n\n    console.timeEnd('identify-snippets');\n    return result.object.snippets;\n  }\n\n  /**\n   * Smart truncation to fit within line budget while preserving understanding context\n   */\n  smartTruncateSnippets(snippets: CodeSnippet[], maxLines: number = 600): {truncatedSnippets: CodeSnippet[], additionalFromTruncation: {fileName: string, reason: string, suggestedQuery: string}[]} {\n    // Categorize snippets by type for strategic selection\n    const implementation = snippets.filter(s => (s.score || 0) >= 0.9);\n    const usage = snippets.filter(s => (s.score || 0) >= 0.7 && (s.score || 0) < 0.9);\n    const context = snippets.filter(s => (s.score || 0) < 0.7);\n\n    let currentLines = 0;\n    const truncatedSnippets: CodeSnippet[] = [];\n    const additionalFromTruncation: {fileName: string, reason: string, suggestedQuery: string}[] = [];\n\n    // Strategy: Always include implementation, then usage, then context within budget\n    const prioritizedSnippets = [\n      ...orderBy(implementation, ['score'], ['desc']).slice(0, 2), // Max 2 implementation snippets\n      ...orderBy(usage, ['score'], ['desc']).slice(0, 2), // Max 2 usage examples\n      ...orderBy(context, ['score'], ['desc']).slice(0, 1)  // Max 1 context snippet\n    ];\n\n    for (const snippet of prioritizedSnippets) {\n      const snippetLines = snippet.content.split('\\n').length;\n\n      if (currentLines + snippetLines <= maxLines) {\n        truncatedSnippets.push(snippet);\n        currentLines += snippetLines;\n      } else {\n        // Add to additional files instead of truncating content\n        additionalFromTruncation.push({\n          fileName: snippet.filePath,\n          reason: `Relevant but truncated due to 1000-line budget (relevance: ${(snippet.score || 0).toFixed(2)}, ${snippetLines} lines)`,\n          suggestedQuery: `Show ${snippet.symbols.join(', ')} in ${snippet.filePath}`\n        });\n      }\n    }\n\n    // Add any remaining snippets to additional files\n    const remainingSnippets = snippets.filter(s => !truncatedSnippets.includes(s) && !additionalFromTruncation.some(a => a.fileName === s.filePath));\n    for (const snippet of remainingSnippets) {\n      additionalFromTruncation.push({\n        fileName: snippet.filePath,\n        reason: `Additional context available (relevance: ${(snippet.score || 0).toFixed(2)})`,\n        suggestedQuery: `Show ${snippet.symbols.join(', ')} in ${snippet.filePath}`\n      });\n    }\n\n    console.log(`Smart truncation: kept ${truncatedSnippets.length}/${snippets.length} snippets within ${maxLines} line budget (${currentLines} lines total)`);\n    console.log(`  - Implementation: ${implementation.length} found, ${truncatedSnippets.filter(s => (s.score || 0) >= 0.9).length} kept`);\n    console.log(`  - Usage: ${usage.length} found, ${truncatedSnippets.filter(s => (s.score || 0) >= 0.7 && (s.score || 0) < 0.9).length} kept`);\n    console.log(`  - Context: ${context.length} found, ${truncatedSnippets.filter(s => (s.score || 0) < 0.7).length} kept`);\n\n    return {truncatedSnippets, additionalFromTruncation};\n  }\n\n  /**\n   * Extract actual code snippets based on line numbers\n   */\n  extractSnippets(snippetIdentifications: SnippetIdentification[]): CodeSnippet[] {\n    // Group snippets by file to avoid duplicate processing\n    const snippetsByFile = new Map<string, SnippetIdentification[]>();\n\n    for (const snippet of snippetIdentifications) {\n      if (!snippetsByFile.has(snippet.fileName)) {\n        snippetsByFile.set(snippet.fileName, []);\n      }\n      snippetsByFile.get(snippet.fileName)?.push(snippet);\n    }\n\n    const results: CodeSnippet[] = [];\n\n    // Process each file's snippets\n    for (const [fileName, fileSnippets] of snippetsByFile.entries()) {\n      const file = this.fileIndex.get(fileName);\n      if (!file || !file.content) continue;\n\n      const lines = file.content.split(\"\\n\");\n\n      // Find import statements (usually at the top of the file)\n      const importEndLine = this.findImportEndLine(lines);\n      const hasImports = importEndLine > 0;\n\n      // Process each snippet in the file\n      for (const identification of fileSnippets) {\n        // Ensure line numbers are within bounds\n        const startLine = Math.max(1, Math.min(identification.startLine, lines.length));\n        const endLine = Math.max(startLine, Math.min(identification.endLine, lines.length));\n\n        // Removing as this is causing the llm issues to understand\n        const shouldIncludeImports = false;\n\n        // Determine if we should include imports\n        // const shouldIncludeImports = hasImports &&\n        //     identification.snippetType.toLowerCase() !== 'import' &&\n        //     startLine > importEndLine;\n\n        // Extract the snippet content with imports if needed\n        let snippetLines: string[];\n        let actualStartLine: number;\n\n        if (shouldIncludeImports) {\n          // Include imports and the actual snippet\n          const importLines = lines.slice(0, importEndLine);\n          const codeLines = lines.slice(startLine - 1, endLine);\n\n          // Add a separator between imports and code\n          snippetLines = [...importLines, '', '// Main snippet', ...codeLines];\n          actualStartLine = 1; // Starting from the beginning of the file\n        } else {\n          // Just include the snippet itself\n          snippetLines = lines.slice(startLine - 1, endLine);\n          actualStartLine = startLine;\n        }\n\n        const content = snippetLines.join(\"\\n\");\n\n        // Log the extraction for debugging\n        console.log(`Extracting snippet from ${identification.fileName}:`);\n        console.log(`  Lines ${startLine}-${endLine} (${snippetLines.length} lines)`);\n        console.log(`  Type: ${identification.snippetType}, Name: ${identification.snippetName}`);\n        console.log(`  Relevance: ${identification.relevanceScore.toFixed(2)}`);\n        if (shouldIncludeImports) {\n          console.log(`  Including imports from lines 1-${importEndLine}`);\n        }\n\n        results.push({\n          filePath: identification.fileName,\n          content,\n          startLine: actualStartLine,\n          endLine: shouldIncludeImports ? endLine + (importEndLine + 2) : endLine, // Adjust for added separator lines\n          type: this.mapSnippetType(identification.snippetType),\n          symbols: [identification.snippetName],\n          score: identification.relevanceScore,\n          context: identification.reasoning,\n          includesImports: shouldIncludeImports\n        } as CodeSnippet);\n      }\n    }\n\n    console.log('Total lines', results.reduce((acc, a) => {\n      return acc + ((a.endLine - a.startLine)) + 1;\n    }, 0));\n\n    return orderBy(results, ['score'], ['desc']);\n  }\n\n  /**\n   * Find the line where imports end in a file\n   */\n  private findImportEndLine(lines: string[]): number {\n    let lastImportLine = 0;\n\n    for (let i = 0; i < Math.min(lines.length, 50); i++) { // Only check first 50 lines\n      const line = lines[i].trim();\n      if (line.startsWith('import ')) {\n        lastImportLine = i + 1; // +1 because line numbers are 1-based\n      }\n    }\n\n    return lastImportLine;\n  }\n\n  /**\n   * Main method to get relevant snippets for a query\n   */\n  async getRelevantSnippets(query: string, reason: string, excludedFiles: string[] = []): Promise<CodeSnippet[]> {\n    // Stage 1: Find relevant files\n    const {files: relevantFiles, reasoning} = await this.findRelevantFiles(query, reason, excludedFiles);\n\n    if (relevantFiles.length === 0) {\n      console.log(\"No relevant files found\");\n      return [];\n    }\n\n    // Stage 2: Identify relevant snippets within those files\n    const snippetIdentifications = await this.identifyRelevantSnippets(query, reason, relevantFiles, reasoning);\n\n    // Stage 3: Extract the actual snippets\n    return this.extractSnippets(snippetIdentifications);\n  }\n\n  /**\n   * Helper methods\n   */\n  private isCodeFile(fileName: string): boolean {\n    const ext = path.extname(fileName).toLowerCase();\n    return ['.ts', '.tsx', '.js', '.jsx'].includes(ext);\n  }\n\n  private determineFileType(fileName: string, content: string): string {\n    const name = fileName.toLowerCase();\n\n    if (name.includes('screen') || name.includes('page')) {\n      return 'screen';\n    }\n\n    if (name.includes('context')) {\n      return 'context';\n    }\n\n    if (name.includes('hook') || (content && content.includes('use') && content.includes('return {'))) {\n      return 'hook';\n    }\n\n    if (name.includes('util') || name.includes('helper')) {\n      return 'util';\n    }\n\n    if (name.includes('type') || name.includes('interface') || name.includes('.d.ts')) {\n      return 'type';\n    }\n\n    if (name.includes('config') || name.includes('setup')) {\n      return 'config';\n    }\n\n    // Default to component for TSX/JSX files\n    if (name.endsWith('.tsx') || name.endsWith('.jsx')) {\n      return 'component';\n    }\n\n    return 'unknown';\n  }\n\n  private extractExports(content: string): string[] {\n    const exports: string[] = [];\n    const exportRegex = /export\\s+(const|function|class|interface|type|default)\\s+(\\w+)/g;\n\n    let match;\n    while ((match = exportRegex.exec(content)) !== null) {\n      exports.push(match[2]);\n    }\n\n    return exports;\n  }\n\n  private extractImports(content: string): string[] {\n    const imports: string[] = [];\n    // Fixed regex to handle all import patterns including relative paths, scoped packages, and complex paths\n    const importRegex = /import\\s+(?:{[^}]*}|\\*\\s+as\\s+\\w+|\\w+)\\s+from\\s+['\"]([^'\"]+)['\"];?/g;\n\n    let match;\n    while ((match = importRegex.exec(content)) !== null) {\n      const importPath = match[1];\n      imports.push(importPath);\n    }\n\n    return imports;\n  }\n\n  /**\n   * Resolve import path to actual file name in the project\n   */\n  private resolveImportPath(importPath: string, currentFile: string): string | null {\n    // Skip node_modules and external packages (anything that doesn't start with ./ or ../)\n    if (!importPath.startsWith('./') && !importPath.startsWith('../')) {\n      return null;\n    }\n\n    // Handle relative paths only\n    const currentDir = path.dirname(currentFile);\n    let resolvedPath = path.join(currentDir, importPath);\n    // Normalize path separators and remove leading ./\n    resolvedPath = resolvedPath.replace(/\\\\/g, '/').replace(/^\\.\\//, '');\n\n    // Try to find the actual file with common extensions\n    const possibleExtensions = ['.ts', '.tsx', '.js', '.jsx', '/index.ts', '/index.tsx', '/index.js', '/index.jsx'];\n\n    for (const ext of possibleExtensions) {\n      const candidatePath = resolvedPath + ext;\n      if (this.fileIndex.has(candidatePath)) {\n        return candidatePath;\n      }\n    }\n\n    // If no extension worked, try exact match\n    if (this.fileIndex.has(resolvedPath)) {\n      return resolvedPath;\n    }\n\n    return null;\n  }\n\n  private mapSnippetType(type: string): 'component' | 'hook' | 'screen' | 'util' | 'type' | 'context' | 'config' | 'unknown' {\n    const normalizedType = type.toLowerCase();\n\n    if (normalizedType.includes('component')) return 'component';\n    if (normalizedType.includes('hook')) return 'hook';\n    if (normalizedType.includes('screen')) return 'screen';\n    if (normalizedType.includes('util') || normalizedType.includes('function')) return 'util';\n    if (normalizedType.includes('type') || normalizedType.includes('interface')) return 'type';\n    if (normalizedType.includes('context') || normalizedType.includes('provider')) return 'context';\n    if (normalizedType.includes('config')) return 'config';\n\n    return 'unknown';\n  }\n}", "startLine": 27, "endLine": 706, "type": "unknown", "symbols": ["TwoStageLLMContextEngine"], "score": 1, "context": "This class implements the two-stage context engine, including methods for selecting relevant files (first stage) and identifying relevant snippets within those files (second stage). It orchestrates the two-stage process and is the core of the architecture.", "includesImports": false}, {"filePath": "src/lib/services/context-engine.ts", "content": "export class ContextEngine {\n    private files: FileItem[] = [];\n    private fileIndex: Map<string, FileItem> = new Map();\n    private dependencyGraph: Map<string, Set<string>> = new Map();\n    private fileMetadata: Map<string, FileMetadata> = new Map();\n    private codebaseEmbedding: any = null;\n    private supabaseSchema: SupabaseSchema | null = null;\n    private project: Project | null = null;\n    private actionPlan: z.infer<typeof ActionPlanSchema> | null = null;\n    private snippetCache: Map<string, CodeSnippet[]> = new Map(); // Cache for extracted snippets\n    private astCache: Map<string, any> = new Map(); // Cache for AST data\n\n    /**\n     * Initialize the context engine with the project files\n     * @param files Array of file items from the project\n     * @param project Optional project information for Supabase integration\n     */\n    constructor(files: FileItem[], project?: Project) {\n        this.files = files;\n        this.project = project || null;\n        this.buildIndex();\n        this.analyzeDependencies();\n        this.extractMetadata();\n    }\n\n    /**\n     * Build an index of files for quick lookup\n     */\n    private buildIndex(): void {\n        for (const file of this.files) {\n            this.fileIndex.set(file.name, file);\n        }\n    }\n\n    /**\n     * Check if a file should be excluded based on the excludedFiles patterns\n     * @param filePath Path of the file to check\n     * @param excludedFiles Array of file paths to exclude\n     * @returns True if the file should be excluded\n     */\n    private shouldExcludeFile(filePath: string, excludedFiles: string[]): boolean {\n        if (!excludedFiles || excludedFiles.length === 0) {\n            return false;\n        }\n\n        // Normalize the file path (remove leading slashes, etc.)\n        const normalizedPath = filePath.replace(/^\\/+/, '');\n        const fileName = path.basename(normalizedPath);\n\n        for (const pattern of excludedFiles) {\n            // Normalize the pattern\n            const normalizedPattern = pattern.replace(/^\\/+/, '');\n\n            // Case 1: Exact match (absolute path)\n            if (normalizedPath === normalizedPattern) {\n                return true;\n            }\n\n            // Case 2: Filename match (for unique filenames)\n            if (fileName === path.basename(normalizedPattern)) {\n                return true;\n            }\n\n            // Case 3: Pattern is contained in the path\n            if (normalizedPath.includes(normalizedPattern)) {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Analyze dependencies between files\n     * Builds a graph of which files import/depend on other files\n     */\n    private analyzeDependencies(): void {\n        const importRegex = /import\\s+(?:{[^}]*}|\\*\\s+as\\s+\\w+|\\w+)\\s+from\\s+['\"]([^'\"]+)['\"]/g;\n\n        for (const file of this.files) {\n            if (!file.content || !isCodeFile(file.name)) continue;\n\n            const dependencies = new Set<string>();\n            this.dependencyGraph.set(file.name, dependencies);\n\n            let match;\n            while ((match = importRegex.exec(file.content)) !== null) {\n                const importPath = match[1];\n\n                // Handle relative imports\n                if (importPath.startsWith('.')) {\n                    const resolvedPath = resolveRelativePath(file.name, importPath);\n                    if (this.fileIndex.has(resolvedPath)) {\n                        dependencies.add(resolvedPath);\n                    }\n                }\n\n                // Handle absolute imports (e.g., @/components/...)\n                if (importPath.startsWith('@/')) {\n                    const absolutePath = importPath.replace('@/', '');\n                    const possibleFiles = this.files\n                        .map(f => f.name)\n                        .filter(name => name.includes(absolutePath));\n\n                    if (possibleFiles.length > 0) {\n                        dependencies.add(possibleFiles[0]);\n                    }\n                }\n            }\n        }\n    }\n\n    /**\n     * Extract metadata from files (components, hooks, contexts, etc.)\n     */\n    private extractMetadata(): void {\n        for (const file of this.files) {\n            if (!file.content || !isCodeFile(file.name)) continue;\n\n            const metadata: FileMetadata = {\n                type: determineFileType(file.name, file.content),\n                exports: extractExports(file.content),\n                imports: extractImports(file.content),\n                lineCount: file.content.split('\\n').length,\n                hasJSX: file.content.includes('return (') || file.content.includes('return <'),\n                hasHooks: file.content.includes('useState') || file.content.includes('useEffect'),\n                hasContext: file.content.includes('createContext'),\n                hasStyles: file.content.includes('StyleSheet.create'),\n            };\n\n            this.fileMetadata.set(file.name, metadata);\n        }\n    }\n\n    /**\n     * Query the context engine for information about the codebase\n     * @param query Natural language query about the codebase\n     * @param reason\n     * @param excludedFiles Optional array of file paths to exclude from analysis\n     * @returns Comprehensive information about the relevant parts of the codebase\n     */\n    async query(query: string, reason: string, excludedFiles?: string[]): Promise<ContextResult> {\n        console.time('context-engine-query');\n        console.log(`Context engine query started for: \"${query}\"`, `Excluded file: ${(excludedFiles || []).join(',')}`);\n\n        // First, determine which files are most relevant to the query\n        // console.time('find-relevant-files');\n        // const relevantFiles = await this.findRelevantFiles(query, excludedFiles || []);\n        // console.timeEnd('find-relevant-files');\n\n        // Start parallel operations\n        const parallelOperations: any[] = [];\n\n        // 1. Process file contents\n        // const fileContentsPromise = Promise.resolve().then(() => {\n        //     console.time('process-file-contents');\n        //     // Get the content and metadata for these files\n        //     const result = relevantFiles.map(fileName => {\n        //         const file = this.fileIndex.get(fileName);\n        //         const metadata = this.fileMetadata.get(fileName);\n        //\n        //         return {\n        //             name: fileName,\n        //             content: file?.content || '',\n        //             // metadata: metadata || {type: 'unknown'},\n        //             // dependencies: Array.from(this.dependencyGraph.get(fileName) || []),\n        //             // dependents: this.findDependents(fileName),\n        //         };\n        //     });\n        //     console.timeEnd('process-file-contents');\n        //     return result;\n        // });\n        // parallelOperations.push(fileContentsPromise);\n\n        // 2. Get the overall codebase structure\n        // const codebaseStructurePromise = Promise.resolve().then(() => {\n        //     console.time('get-codebase-structure');\n        //     const structure = this.getCodebaseStructure();\n        //     console.timeEnd('get-codebase-structure');\n        //     return structure;\n        // });\n        // parallelOperations.push(codebaseStructurePromise);\n\n        // 3. Get file relationships (prepare the promise but don't execute yet)\n        // const relationshipsPromise = Promise.resolve().then(() => {\n        //     console.time('get-file-relationships');\n        //     const relationships = this.getFileRelationships(relevantFiles);\n        //     console.timeEnd('get-file-relationships');\n        //     return relationships;\n        // });\n        // parallelOperations.push(relationshipsPromise);\n\n        // 4. Extract code snippets using the two-stage approach\n        const snippetsPromise = Promise.resolve().then(async () => {\n            console.time('extract-code-snippets');\n\n            // Check if we have cached snippets for this query\n            const cachedSnippets = this.snippetCache.get(query);\n            if (cachedSnippets) {\n                console.log('Using cached snippets for query');\n                console.timeEnd('extract-code-snippets');\n                return cachedSnippets;\n            }\n\n            try {\n                // Use the TwoStageLLMContextEngine for better snippet extraction\n                const twoStageEngine = new TwoStageLLMContextEngine(this.files);\n                const snippets = await twoStageEngine.getRelevantSnippets(query, reason, excludedFiles || []);\n\n                if (snippets && snippets.length > 0) {\n                    // Cache the snippets for future use\n                    this.snippetCache.set(query, snippets);\n                    console.log(`Found ${snippets.length} snippets using two-stage approach`);\n                    console.timeEnd('extract-code-snippets');\n                    return snippets;\n                }\n            } catch (error) {\n                console.error('Error using TwoStageLLMContextEngine for snippets:', error);\n                // Fall through to the original implementation if the new approach fails\n            }\n\n            // Fallback to original regex-based extraction\n            // console.log('Falling back to regex-based snippet extraction');\n            // const allSnippets: CodeSnippet[] = [];\n            // for (const fileName of relevantFiles) {\n            //     const fileSnippets = this.extractCodeSnippets(fileName, query);\n            //     allSnippets.push(...fileSnippets);\n            // }\n\n            // Sort snippets by score and take top 10\n            // const topSnippets = allSnippets\n            //     .sort((a, b) => (b.score || 0) - (a.score || 0))\n            //     .slice(0, 10);\n\n            // Cache the snippets for future use\n            // this.snippetCache.set(query, topSnippets);\n\n            // console.timeEnd('extract-code-snippets');\n            // return topSnippets;\n        });\n        parallelOperations.push(snippetsPromise);\n\n        // Wait for all parallel operations to complete\n        console.time('parallel-operations');\n        const [snippets] = await Promise.all(parallelOperations);\n        console.timeEnd('parallel-operations');\n\n        // Performance metrics for debugging\n        console.timeEnd('context-engine-query');\n        console.log(`Context engine query completed for: \"${query}\"`);\n        // console.log(`- Found ${fileContents?.length} relevant files`);\n        console.log(`- Found ${snippets?.length} relevant code snippets`);\n\n        return {\n            query,\n            // relevantFiles: fileContents,\n            snippets,\n            // codebaseStructure,\n            // relationships,\n            // supabaseSchema,\n            // actionPlan,\n        } as ContextResult;\n    }\n\n    /**\n     * Get Supabase schema information if available\n     * @returns Supabase schema information\n     */\n    async getSupabaseSchema(): Promise<SupabaseSchema | null> {\n        if (!this.project || !this.project.supabaseProjectId) {\n            return null;\n        }\n\n        // If we already have the schema cached, return it\n        if (this.supabaseSchema) {\n            return this.supabaseSchema;\n        }\n\n        try {\n            const supabaseProvider = new SupabaseIntegrationProvider();\n            const result = await supabaseProvider.getLatestInstructionsForChat({project: this.project});\n\n            const {schema, functions, secrets, dbFunctions, triggers, rlsPolicies, storageBuckets} = result as any;\n            // console.log('result', result)\n            // Parse the instructions to extract schema information\n\n\n            // Create a structured representation of the schema\n            const parsedSchema: SupabaseSchema = {\n                tables: schema,\n                functions,\n                secrets,\n                dbFunctions,\n                triggers,\n                rlsPolicies,\n                storageBuckets\n            };\n\n            console.log('parsedSchema', parsedSchema)\n\n            this.supabaseSchema = parsedSchema;\n            return parsedSchema;\n        } catch (error) {\n            console.error('Error fetching Supabase schema:', error);\n            return null;\n        }\n    }\n\n    /**\n     * Generate an action plan based on the query and context\n     * @param query The user's query\n     * @param relevantFiles The relevant files for the query\n     * @param supabaseSchema The Supabase schema if available\n     * @returns An action plan with steps to implement the requested changes\n     */\n    async generateActionPlan(query: string, relevantFiles: any[], supabaseSchema: SupabaseSchema | null): Promise<z.infer<typeof ActionPlanSchema>> {\n        try {\n            // Define the schema for the action plan\n\n            // Prepare context for the AI\n            const fileContext = relevantFiles.map(file => {\n                return `File: ${file.name}\\nType: ${file.metadata.type}\\nExports: ${file.metadata.exports?.join(', ') || 'None'}\\nImports: ${file.metadata.imports?.slice(0, 5)?.join(', ') || 'None'}${file.metadata.imports?.length > 5 ? '...' : ''}\\n`;\n            }).join('\\n');\n\n            // Prepare Supabase context if available\n            let supabaseContext = '';\n            if (supabaseSchema) {\n                // Add tables information\n                if (supabaseSchema.tables && supabaseSchema.tables.length > 0) {\n                    supabaseContext += 'Supabase Tables:\\n' +\n                        supabaseSchema.tables.map(table => {\n                            return `Table: ${table.table_name}\\nColumns: ${table.columns.map((col: any) =>\n                                `${col.column_name} (${col.data_type})`).join(', ')}\\n`;\n                        }).join('\\n');\n                }\n\n                // Add Edge Functions information\n                if (supabaseSchema.functions && supabaseSchema.functions.length > 0) {\n                    supabaseContext += '\\nSupabase Edge Functions:\\n' +\n                        supabaseSchema.functions.map((func: any) =>\n                            `- ${func.name || func.slug || 'Unknown function'}${func.entrypoint ? ` (Entrypoint: ${func.entrypoint})` : ''}`\n                        ).join('\\n');\n                }\n\n                // Add Database Functions information\n                if (supabaseSchema.dbFunctions && supabaseSchema.dbFunctions.length > 0) {\n                    supabaseContext += '\\nSupabase Database Functions:\\n' +\n                        supabaseSchema.dbFunctions.map((func: any) =>\n                            `- ${func.name || 'Unknown function'}${func.schema ? ` (Schema: ${func.schema})` : ''}`\n                        ).join('\\n');\n                }\n\n                // Add RLS Policies information\n                if (supabaseSchema.rlsPolicies && supabaseSchema.rlsPolicies.length > 0) {\n                    supabaseContext += '\\nSupabase RLS Policies:\\n' +\n                        supabaseSchema.rlsPolicies.map((policy: any) =>\n                            `- ${policy.name || 'Unknown policy'} on ${policy.table || 'Unknown table'} (${policy.action || 'Unknown action'})`\n                        ).join('\\n');\n                }\n\n                // Add Triggers information\n                if (supabaseSchema.triggers && supabaseSchema.triggers.length > 0) {\n                    supabaseContext += '\\nSupabase Triggers:\\n' +\n                        supabaseSchema.triggers.map((trigger: any) =>\n                            `- ${trigger.name || 'Unknown trigger'} on ${trigger.table || 'Unknown table'}`\n                        ).join('\\n');\n                }\n\n                // Add Storage Buckets information\n                if (supabaseSchema.storageBuckets && supabaseSchema.storageBuckets.length > 0) {\n                    supabaseContext += '\\nSupabase Storage Buckets:\\n' +\n                        supabaseSchema.storageBuckets.map((bucket: any) =>\n                            `- ${bucket.name || 'Unknown bucket'} (Public: ${bucket.public ? 'Yes' : 'No'})${bucket.file_size_limit ? ` (Size Limit: ${bucket.file_size_limit} bytes)` : ''}`\n                        ).join('\\n');\n                }\n            }\n\n            // Generate the action plan using AI\n            const result = await generateObject({\n                model: customModel('openai/gpt-4.1'),\n                temperature: 0.1,\n                schema: ActionPlanSchema,\n                system: `You are an expert software architect and developer specializing in React Native Expo applications with Supabase integration.\n      Your task is to create a detailed action plan for implementing the requested changes based on the provided codebase context.\n      Focus on creating a practical, step-by-step plan that addresses all aspects of the request.\n      Be specific about which files need to be changed and why.\n\n      If Supabase integration is involved:\n      1. Consider all available Supabase resources (tables, edge functions, database functions, RLS policies, triggers, storage buckets)\n      2. Recommend appropriate changes to database schema when needed\n      3. Suggest Edge Functions for complex server-side operations\n      4. Include necessary RLS policy updates for proper security\n      5. Utilize Storage buckets for file uploads when appropriate\n      6. Consider database triggers for automated operations\n\n      Provide a comprehensive plan that leverages all available resources efficiently.`,\n                prompt: `Based on the following query and codebase context, create a detailed action plan for implementing the requested changes.\n\nQuery: ${query}\n\nCodebase Context:\n${fileContext}\n\n${supabaseContext ? supabaseContext : 'No Supabase integration detected.'}\n\nPlease provide a comprehensive action plan with specific steps, file changes, and considerations.`\n            });\n\n            return result.object;\n        } catch (error) {\n            console.error('Error generating action plan:', error);\n            // Return a basic action plan if generation fails\n            return {\n                summary: `Implement changes for: ${query}`,\n                complexity: \"moderate\",\n                steps: [{\n                    description: \"Analyze and implement the requested changes\",\n                    fileChanges: relevantFiles.slice(0, 3).map(file => ({\n                        path: file.name,\n                        action: \"modify\",\n                        purpose: \"Implement requested changes\",\n                        priority: \"high\"\n                    }))\n                }],\n                considerations: [\"Consider the existing codebase structure\", \"Ensure compatibility with current implementation\"],\n                supabaseChanges: supabaseSchema ? [\n                    {\n                        type: \"table\",\n                        description: \"May need database schema changes to support the new feature\",\n                        priority: \"medium\"\n                    },\n                    supabaseSchema.functions?.length > 0 ? {\n                        type: \"edge_function\",\n                        description: \"May need to update Edge Functions to support the new feature\",\n                        priority: \"medium\"\n                    } : null,\n                    supabaseSchema.storageBuckets?.length > 0 ? {\n                        type: \"storage_bucket\",\n                        description: \"May need to configure Storage for file uploads\",\n                        priority: \"medium\"\n                    } : null,\n                    supabaseSchema.rlsPolicies?.length > 0 ? {\n                        type: \"policy\",\n                        description: \"May need to update RLS policies for proper access control\",\n                        priority: \"high\"\n                    } : null\n                ].filter(Boolean) as any : []\n            };\n        }\n    }\n\n\n    /**\n     * Find files that are relevant to the given query\n     * @param query Natural language query\n     * @param excludedFiles Array of file paths to exclude from analysis\n     * @returns Array of file names that are relevant to the query\n     */\n    private async findRelevantFiles(query: string, excludedFiles: string[]): Promise<string[]> {\n        // Get the list of files that are not excluded\n        const availableFiles = this.files.filter(file => !this.shouldExcludeFile(file.name, excludedFiles));\n\n        // Use AI to determine which files are most relevant to the query\n        const result = await generateObject({\n            model: customModel('openai/gpt-4.1'),\n            temperature: 0.1,\n            schema: z.object({files: z.array(z.string())}),\n            system: `You are a code analysis expert. Your task is to identify which files in a React Native Expo project are most relevant to a specific query.\n\nYou will be given:\n1. A query about the codebase\n2. A list of files in the project with their types\n\nReturn ONLY a JSON array of the most relevant file paths, with a maximum of 5 files. Choose files that would be most helpful for understanding or implementing the query.`,\n            prompt: `Query: ${query}\n\nFiles in the project:\n${availableFiles.map(file => `${file.name} - ${this.fileMetadata.get(file.name)?.type || 'unknown'}`).join('\\n')}\n\n${excludedFiles.length > 0 ? `Note: The following files have been excluded from analysis: ${excludedFiles.join(', ')}\\n\\n` : ''}\nReturn ONLY a JSON array of the most relevant file paths (maximum 5):`,\n        });\n\n        try {\n            // console.log('result', result.object)\n            // Parse the result as a JSON array\n            const fileList = result.object.files;\n            if (Array.isArray(fileList) && fileList.length > 0) {\n                return fileList.filter(file => this.fileIndex.has(file));\n            }\n        } catch (error) {\n            console.error('Error parsing relevant files:', error);\n        }\n\n        // Fallback: return the most common file types if AI parsing fails\n        return this.files\n            .filter(file => isCodeFile(file.name) && !this.shouldExcludeFile(file.name, excludedFiles))\n            .map(file => file.name)\n            .slice(0, 5);\n    }\n\n    /**\n     * Find all files that depend on the given file\n     * @param fileName Name of the file\n     * @returns Array of file names that depend on the given file\n     */\n    private findDependents(fileName: string): string[] {\n        const dependents: string[] = [];\n\n        for (const [file, dependencies] of this.dependencyGraph.entries()) {\n            if (dependencies.has(fileName)) {\n                dependents.push(file);\n            }\n        }\n\n        return dependents;\n    }\n\n    /**\n     * Get the overall structure of the codebase\n     * @returns Object representing the codebase structure\n     */\n    private getCodebaseStructure(): CodebaseStructure {\n        const structure: CodebaseStructure = {\n            components: [],\n            screens: [],\n            contexts: [],\n            hooks: [],\n            utils: [],\n            types: [],\n            configs: [],\n        };\n\n        for (const [fileName, metadata] of this.fileMetadata.entries()) {\n            switch (metadata.type) {\n                case 'component':\n                    structure.components.push(fileName);\n                    break;\n                case 'screen':\n                    structure.screens.push(fileName);\n                    break;\n                case 'context':\n                    structure.contexts.push(fileName);\n                    break;\n                case 'hook':\n                    structure.hooks.push(fileName);\n                    break;\n                case 'util':\n                    structure.utils.push(fileName);\n                    break;\n                case 'type':\n                    structure.types.push(fileName);\n                    break;\n                case 'config':\n                    structure.configs.push(fileName);\n                    break;\n            }\n        }\n\n        return structure;\n    }\n\n    /**\n     * Get relationships between files\n     * @param fileNames Array of file names\n     * @returns Object representing relationships between files\n     */\n    private getFileRelationships(fileNames: string[]): FileRelationships {\n        const relationships: FileRelationships = {\n            imports: {},\n            exports: {},\n            dependencies: {},\n            dependents: {},\n        };\n\n        for (const fileName of fileNames) {\n            relationships.imports[fileName] = this.fileMetadata.get(fileName)?.imports || [];\n            relationships.exports[fileName] = this.fileMetadata.get(fileName)?.exports || [];\n            relationships.dependencies[fileName] = Array.from(this.dependencyGraph.get(fileName) || []);\n            relationships.dependents[fileName] = this.findDependents(fileName);\n        }\n\n        return relationships;\n    }\n}", "startLine": 106, "endLine": 690, "type": "unknown", "symbols": ["ContextEngine"], "score": 0.9, "context": "This class uses the TwoStageLLMContextEngine to extract relevant code snippets and manages the overall context querying process, showing how the two-stage engine is integrated and used to select relevant files and snippets.", "includesImports": false}, {"filePath": "src/lib/chat/tools/query-codebase.ts", "content": "export const queryCodebase = ({\n                                  projectId,\n                                  files,\n                                  fileManager,\n                                  messageId = '',\n                                  userMessage = ''\n                              }: { projectId: string, files: FileItem[], fileManager: FileLineManager, messageId?: string, userMessage?: string }) => {\n    return tool({\n        description: 'Query the codebase to understand its structure, relationships, and relevant files for a specific task. ' +\n            'Use this instead of requesting individual files. ' +\n            'You can exclude specific files from the analysis by providing an array of file paths in the excludedFiles parameter. ' +\n            'Use filePath parameter to retrieve a SINGLE file in full ONLY when absolutely necessary. You are LIMITED to ONE file per request.',\n        parameters: QueryCodebaseSchema,\n        execute: async ({query, excludedFiles, reason}: z.infer<typeof QueryCodebaseSchema>) => {\n            console.log('Query:', query)\n            console.log('Reason:', reason)\n            console.log('Excluded files:', excludedFiles?.join())\n\n            if(!query) {\n                return {\n                    result: null,\n                    message: \"One of query or filePath is required. To look at the holistic view, use query and to look at a specific file, use filePath.\"\n                }\n            }\n            // Check if this is a \"continue\" message\n            const isContinueMessage = userMessage.toLowerCase().trim().startsWith('continue');\n\n            // Get the tool tracker instance\n            const toolTracker = ToolCountTracker.getInstance();\n\n            console.log('Tool call allowed (queryCodebase):', toolTracker.shouldAllowToolCall(messageId, 'queryCodebase'))\n\n            // Check if we should allow this tool call\n            if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'queryCodebase')) {\n                return {\n                    result: null,\n                    message: \"⚠️ Tool call limit reached. You've already made multiple codebase queries. Please implement or summarize with the information you already have.\",\n                };\n            }\n\n            // Increment the tool count if we have a chat ID\n            if (messageId) {\n                toolTracker.incrementToolCount(messageId, 'queryCodebase');\n            }\n            try {\n                if (!files || files.length === 0) {\n                    throw new Error('No files available for context engine');\n                }\n\n                // Get project information if available\n                let project: Project | null = null;\n                if (projectId) {\n                    try {\n                        project = await getProjectById({id: projectId});\n                    } catch (error) {\n                        console.warn('Could not fetch project information:', error);\n                        // Continue without project info\n                    }\n                }\n\n                if (!project) {\n                    throw new Error('No project information available');\n                }\n\n                // Check if we're requesting a specific file in full\n                // Note: filePath takes precedence over semantic query when both are provided\n                // if (filePath) {\n                //     console.log('Specific file requested:', filePath);\n                //\n                //     // Find the requested file\n                //     const fileItem = fileManager.getFileItems().find(file => file.name === filePath.trim());\n                //\n                //     if (!fileItem) {\n                //         throw new Error(`The requested file was not found: ${filePath}`);\n                //     }\n                //\n                //     const fileItems = [fileItem];\n                //\n                //     // Create snippets for each file\n                //     const snippets = fileItems.map(fileItem => ({\n                //         filePath: fileItem.name,\n                //         content: fileItem.content || '',\n                //         startLine: 1,\n                //         endLine: (fileItem.content || '').split('\\n').length,\n                //         type: 'unknown',\n                //         symbols: [],\n                //         score: 1.0\n                //     }));\n                //\n                //     // Create a simplified result with just the full file contents\n                //     const result = {\n                //         query,\n                //         snippets,\n                //         codebaseStructure: { components: [], screens: [], contexts: [], hooks: [], utils: [], types: [], configs: [] },\n                //         relationships: { imports: {}, exports: {}, dependencies: {}, dependents: {} }\n                //     };\n                //\n                //     let message = `Showing full content of the requested file: ${fileItems[0].name}`;\n                //\n                //     return {\n                //         result,\n                //         message,\n                //         toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')\n                //     };\n                // }\n                \n                // Initialize the context engine with the current files and project\n                const contextEngine = new ContextEngine(fileManager.getFileItems(), project);\n\n                // Default excluded files for Expo Snack projects if not provided\n                const defaultExcludedFiles = [\n                    // Large asset files\n                    'assets/',\n                    '.mp3',\n                    '.mp4',\n                    '.jpg',\n                    '.png',\n                    '.gif',\n                    // Generated type files\n                    'types/generated',\n                    // These don't exist in Expo Snack but kept as fallbacks\n                    'node_modules',\n                    'dist'\n                ];\n\n                // Combine user-provided excludedFiles with defaults\n                const combinedExcludedFiles = excludedFiles ?\n                    [...excludedFiles, ...defaultExcludedFiles] :\n                    defaultExcludedFiles;\n\n                // Query the context engine\n                const result = await contextEngine.query(query, reason, combinedExcludedFiles);\n\n                // Prepare a more informative response\n                let message = `Found ${result.snippets?.length} snippets for your query.`;\n\n                // Add information about excluded files if any\n                if (excludedFiles && excludedFiles.length > 0) {\n                    message += `\\n${excludedFiles.length} files were excluded from the analysis.`;\n                }\n\n                if (result.actionPlan) {\n                    message += `\\n\\nAction Plan: ${result.actionPlan.summary}\\nComplexity: ${result.actionPlan.complexity}\\nSteps: ${result.actionPlan.steps.length}`;\n                }\n\n                // if (result.supabaseSchema) {\n                //     message += `\\n\\nSupabase integration detected with ${result.supabaseSchema.tables.length} tables.`;\n                // }\n\n                return {\n                    result,\n                    message,\n                    toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')\n                };\n            } catch (error) {\n                console.error('Context engine tool error:', error);\n                throw new Error(`Failed to query context engine: ${error instanceof Error ? error.message : 'Unknown error'}`);\n            }\n        },\n    });\n}", "startLine": 23, "endLine": 183, "type": "util", "symbols": ["queryCodebase"], "score": 0.8, "context": "This tool function initializes the ContextEngine and calls its query method to get relevant files and snippets for a given query, showing how the two-stage context engine is used in the chat tool to select relevant files.", "includesImports": false}], "additionalFiles": []}}