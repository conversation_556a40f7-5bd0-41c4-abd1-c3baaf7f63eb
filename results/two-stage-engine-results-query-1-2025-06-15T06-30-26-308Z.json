{"timestamp": "2025-06-15T06:30:26.308Z", "query": "How does the payment work in the app? Where can we change to fix subscription renewals?", "executionTime": 7098, "snippetsCount": 2, "additionalFilesCount": 0, "totalLines": 710, "snippets": [{"filePath": "src/components/subscription/subscription-management.tsx", "type": "component", "context": "This component manages the subscription status, displays subscription details, and handles subscription upgrades and renewals. It includes the logic for renewing subscriptions and upgrading plans, which is directly relevant to how payment and subscription renewals work in the app.", "score": 1, "lines": 368, "startLine": 41, "endLine": 408, "symbols": ["SubscriptionManagement"], "preview": "export function SubscriptionManagement() {\n  const router = useRouter();\n  const [showExitInterview, setShowExitInterview] = useState(false);\n  const [isUpgrading, setIsUpgrading] = useState(false);\n\n..."}, {"filePath": "src/components/subscription/exit-interview.tsx", "type": "component", "context": "This component handles the subscription cancellation process including user feedback and API call to cancel the subscription. It is relevant for fixing subscription renewals as it manages cancellation and related user interactions.", "score": 0.9, "lines": 342, "startLine": 36, "endLine": 377, "symbols": ["ExitInterview"], "preview": "export function ExitInterview({\n  open,\n  onOpenChange,\n  subscriptionId,\n  onCancellationComplete\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/components/subscription/subscription-management.tsx", "content": "export function SubscriptionManagement() {\n  const router = useRouter();\n  const [showExitInterview, setShowExitInterview] = useState(false);\n  const [isUpgrading, setIsUpgrading] = useState(false);\n\n  const { data: status, error, isLoading, mutate } = useSWR<SubscriptionStatus>(\n    '/api/subscription/status',\n    fetcher,\n    {\n      refreshInterval: 60000, // Refresh every minute\n      revalidateOnFocus: true,\n    }\n  );\n\n  // Format large numbers with k suffix\n  const formatNumber = (num: number) => {\n    return num >= 1000 ? `${(num / 1000).toFixed(1)}k` : num.toString();\n  };\n\n  const getPlanIcon = (plan: PlanTier) => {\n    switch (plan) {\n      case 'prime':\n        return <Sparkles className=\"h-5 w-5 text-yellow-400\" />;\n      case 'plus':\n        return <Sparkles className=\"h-5 w-5 text-yellow-400\" />;\n      case 'pro':\n        return <Zap className=\"h-5 w-5 text-purple-400\" />;\n      case 'starter':\n        return <Rocket className=\"h-5 w-5 text-blue-400\" />;\n      case 'free':\n        return <CreditCardIcon className=\"h-5 w-5 text-gray-400\" />;\n      default:\n        return <CreditCardIcon className=\"h-5 w-5 text-gray-400\" />;\n    }\n  };\n\n  const getPlanColor = (plan: PlanTier) => {\n    switch (plan) {\n      case 'prime':\n        return 'bg-yellow-400';\n      case 'plus':\n        return 'bg-yellow-400';\n      case 'pro':\n        return 'bg-purple-400';\n      case 'starter':\n        return 'bg-blue-400';\n      default:\n        return 'bg-gray-400';\n    }\n  };\n\n  const handleViewPlans = () => {\n    router.push('/pricing');\n  };\n\n  const handleUpgrade = async (planId: string) => {\n    try {\n      setIsUpgrading(true);\n\n      // Get plan details for tracking\n      const planDetails = PLANS.find(plan => plan.id === planId);\n\n      // Track upgrade initiation\n      trackSubscriptionEvent('UPGRADE_INITIATED', {\n        current_plan: status?.planTier || 'unknown',\n        plan_type: planDetails?.tier || planId,\n        price: planDetails?.price || 0,\n        currency: 'USD',\n        entry_point: 'subscription_management'\n      });\n\n      const response = await fetch('/api/checkout', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          returnUrl: '/subscription',\n          planId\n        })\n      });\n\n      const data = await response.json();\n\n      if (data.error) {\n        // Track upgrade failure\n        trackSubscriptionEvent('UPGRADE_FAILED', {\n          current_plan: status?.planTier || 'unknown',\n          plan_type: planDetails?.tier || planId,\n          // Using any to bypass type checking for custom properties\n          ...(data.error ? { error_message: data.error } : {})\n        });\n\n        throw new Error(data.error);\n      }\n\n      // Track upgrade initiated (instead of checkout initiated which isn't in the allowed event types)\n      trackSubscriptionEvent('UPGRADE_INITIATED', {\n        current_plan: status?.planTier || 'unknown',\n        plan_type: planDetails?.tier || planId,\n        entry_point: 'subscription_management'\n      });\n\n      // Redirect to checkout URL\n      if (data.url) {\n        window.location.href = data.url;\n      } else {\n        throw new Error('No checkout URL returned');\n      }\n    } catch (error) {\n      console.error('Error upgrading plan:', error);\n    } finally {\n      setIsUpgrading(false);\n    }\n  };\n\n  // Format date to readable format\n  const formatDate = (dateString?: string) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center py-10\">\n        <div className=\"animate-pulse flex flex-col items-center gap-4\">\n          <div className=\"h-8 w-40 bg-muted rounded\"></div>\n          <div className=\"h-40 w-full max-w-md bg-muted rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !status) {\n    return (\n      <Alert variant=\"destructive\" className=\"max-w-3xl mx-auto my-8\">\n        <AlertCircle className=\"h-4 w-4\" />\n        <AlertTitle>Error</AlertTitle>\n        <AlertDescription>\n          Unable to load subscription information. Please try refreshing the page.\n        </AlertDescription>\n      </Alert>\n    );\n  }\n\n  const usagePercentage = Math.min(100, Math.round((status.credits.used / status.credits.total) * 100));\n  const planDetails = PLANS.find(plan => plan.tier === status.planTier);\n\n  // Get color based on usage percentage\n  const getUsageColor = (percentage: number) => {\n    if (percentage >= 90) return 'bg-red-500';\n    if (percentage >= 75) return 'bg-amber-500';\n    return getPlanColor(status.planTier);\n  };\n\n  return (\n    <div className=\"space-y-8 max-w-4xl mx-auto\">\n      <div className=\"space-y-2\">\n        <h1 className=\"text-3xl font-bold\">Subscription Management</h1>\n        <p className=\"text-muted-foreground\">Manage your subscription and usage details</p>\n      </div>\n\n      {/* Current Plan Card */}\n      <Card className=\"overflow-hidden\">\n        <CardHeader className=\"bg-muted/50\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-2\">\n              {getPlanIcon(status.planTier)}\n              <CardTitle className=\"capitalize\">{status.planName} Plan</CardTitle>\n            </div>\n            <Badge\n              variant={status.status === 'active' ? 'default' :\n                      status.status === 'cancelled' ? 'destructive' :\n                      status.status === 'past_due' ? 'destructive' : 'outline'}\n            >\n              {status.status === 'active' ? 'Active' :\n               status.status === 'cancelled' ? 'Cancelled' :\n               status.status === 'past_due' ? 'Past Due' : 'Inactive'}\n            </Badge>\n          </div>\n        </CardHeader>\n        <CardContent className=\"pt-6 space-y-6\">\n          {/* Subscription Status Information */}\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            <div className=\"space-y-4\">\n              <div>\n                <h3 className=\"text-sm font-medium text-muted-foreground mb-1\">Subscription Details</h3>\n                <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                  <div>Plan:</div>\n                  <div className=\"font-medium capitalize\">{status.planName}</div>\n\n                  <div>Status:</div>\n                  <div className=\"font-medium capitalize\">{status.status || 'Unknown'}</div>\n\n                  <div>Price:</div>\n                  <div className=\"font-medium\">${planDetails?.price || 0}/month</div>\n\n                  {status.currentPeriodEnd && (\n                    <>\n                      <div>Current Period Ends:</div>\n                      <div className=\"font-medium\">{formatDate(status.currentPeriodEnd)}</div>\n                    </>\n                  )}\n\n                  {status.status === 'cancelled' && status.cancelAt && (\n                    <>\n                      <div>Access Until:</div>\n                      <div className=\"font-medium\">{formatDate(status.cancelAt)}</div>\n                    </>\n                  )}\n                </div>\n              </div>\n\n              {status.status === 'cancelled' && (\n                <Alert>\n                  <CalendarClock className=\"h-4 w-4\" />\n                  <AlertTitle>Subscription Cancelled</AlertTitle>\n                  <AlertDescription>\n                    Your subscription has been cancelled but you still have access until {formatDate(status.cancelAt || status.currentPeriodEnd)}.\n                  </AlertDescription>\n                </Alert>\n              )}\n            </div>\n\n            {/* Usage Information */}\n            <div className=\"space-y-4\">\n              <div>\n                <h3 className=\"text-sm font-medium text-muted-foreground mb-1\">Usage</h3>\n                <div className=\"flex items-center justify-between mb-1\">\n                  <span className=\"text-sm\">Messages</span>\n                  <div>\n                    <span className=\"text-sm font-medium\">{formatNumber(status.credits.remaining)}</span>\n                    <span className=\"text-xs text-muted-foreground\"> / {formatNumber(status.credits.total)}</span>\n                  </div>\n                </div>\n                <Progress value={usagePercentage} className=\"h-2\">\n                  <div className={`h-full ${getUsageColor(usagePercentage)}`} style={{ width: `${usagePercentage}%` }} />\n                </Progress>\n                {usagePercentage >= 90 && (\n                  <p className=\"text-xs text-red-500 mt-1\">\n                    Very low on messages. Consider upgrading your plan.\n                  </p>\n                )}\n                {usagePercentage >= 75 && usagePercentage < 90 && (\n                  <p className=\"text-xs text-amber-500 mt-1\">\n                    Running low on messages.\n                  </p>\n                )}\n              </div>\n\n              {status.dailyLimit && status.dailyLimit > 0 && (\n                <div>\n                  <div className=\"flex items-center justify-between mb-1\">\n                    <span className=\"text-sm\">Daily Limit</span>\n                    <div>\n                      <span className=\"text-sm font-medium\">{status.dailyRemaining}</span>\n                      <span className=\"text-xs text-muted-foreground\"> / {status.dailyLimit}</span>\n                    </div>\n                  </div>\n                  <Progress\n                    value={Math.min(100, Math.round(((status.dailyLimit - (status.dailyRemaining || 0)) / status.dailyLimit) * 100))}\n                    className=\"h-2\"\n                  >\n                    <div\n                      className={`h-full ${getPlanColor(status.planTier)}`}\n                      style={{ width: `${Math.min(100, Math.round(((status.dailyLimit - (status.dailyRemaining || 0)) / status.dailyLimit) * 100))}%` }}\n                    />\n                  </Progress>\n                </div>\n              )}\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Plan Features */}\n          <div>\n            <h3 className=\"text-sm font-medium text-muted-foreground mb-2\">Plan Features</h3>\n            <ul className=\"grid grid-cols-1 md:grid-cols-2 gap-2\">\n              {planDetails?.features.map((feature, index) => (\n                <li key={index} className=\"flex items-start gap-2\">\n                  <CheckCircle className=\"h-4 w-4 text-green-500 mt-0.5\" />\n                  <span className=\"text-sm\">{feature}</span>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </CardContent>\n        <CardFooter className=\"flex justify-between items-center bg-muted/30 border-t\">\n          <div className=\"flex flex-col sm:flex-row gap-3 w-full justify-end\">\n            {status.status === 'active' && (\n              <>\n                {/* Upgrade button with better styling */}\n                <Button \n                  variant=\"default\" \n                  size=\"sm\"\n                  className=\"sm:w-auto px-6 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white\"\n                  onClick={handleViewPlans}\n                >\n                  <Zap className=\"mr-2 h-4 w-4\" />\n                  Upgrade Plan\n                </Button>\n                \n                {/* Cancel subscription button with subtle styling */}\n                <Button \n                  variant=\"ghost\" \n                  size=\"sm\"\n                  className=\"sm:w-auto px-6 text-gray-500 hover:text-gray-700 hover:bg-gray-100/50\"\n                  onClick={() => setShowExitInterview(true)}\n                  disabled={!status.subscriptionId}\n                >\n                  Cancel Plan\n                </Button>\n              </>\n            )}\n            \n            {status.status === 'cancelled' && (\n              <Button \n                variant=\"default\" \n                size=\"sm\"\n                className=\"sm:w-auto px-6 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white\"\n                onClick={handleViewPlans}\n              >\n                <CreditCard className=\"mr-2 h-4 w-4\" />\n                Resubscribe\n              </Button>\n            )}\n            \n            {(status.status === 'expired' || status.status === 'past_due') && (\n              <Button \n                variant=\"default\" \n                size=\"sm\"\n                className=\"sm:w-auto px-6 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white\"\n                onClick={handleViewPlans}\n              >\n                <CreditCard className=\"mr-2 h-4 w-4\" />\n                Renew Subscription\n              </Button>\n            )}\n          </div>\n        </CardFooter>\n      </Card>\n\n      {/* Exit Interview Modal with improved integration */}\n      {status.subscriptionId && (\n        <ExitInterview\n          open={showExitInterview}\n          onOpenChange={(open) => {\n            setShowExitInterview(open);\n            if (!open) {\n              // Refresh data when modal is closed\n              setTimeout(() => mutate(), 500);\n            }\n          }}\n          subscriptionId={status.subscriptionId}\n          onCancellationComplete={() => {\n            // Refresh data after cancellation\n            mutate();\n          }}\n        />\n      )}\n    </div>\n  );\n}", "startLine": 41, "endLine": 408, "type": "component", "symbols": ["SubscriptionManagement"], "score": 1, "context": "This component manages the subscription status, displays subscription details, and handles subscription upgrades and renewals. It includes the logic for renewing subscriptions and upgrading plans, which is directly relevant to how payment and subscription renewals work in the app.", "includesImports": false}, {"filePath": "src/components/subscription/exit-interview.tsx", "content": "export function ExitInterview({\n  open,\n  onOpenChange,\n  subscriptionId,\n  onCancellationComplete\n}: ExitInterviewProps) {\n  const router = useRouter();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [currentStep, setCurrentStep] = useState(0);\n  const [feedback, setFeedback] = useState<CancellationFeedback>({\n    signupGoal: '',\n    stopReason: '',\n    recommendScore: 5,\n    recommendReason: ''\n  });\n  \n  const [wantsFeedbackCall, setWantsFeedbackCall] = useState(false);\n  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n\n  const steps = [\n    {\n      title: \"What was your main goal when you signed up?\",\n      key: \"signupGoal\",\n      options: [\n        { value: \"build_mvp\", label: \"Build an MVP to test my idea\" },\n        { value: \"create_production_app\", label: \"Create a production app for my business\" },\n        { value: \"learn_no_code\", label: \"Learn if no-code could work for me\" },\n        { value: \"replace_current_process\", label: \"Replace my current app development process\" },\n        { value: \"other\", label: \"Other\" }\n      ]\n    },\n    {\n      title: \"What stopped you from achieving that goal?\",\n      key: \"stopReason\",\n      options: [\n        { value: \"too_complex\", label: \"App building process was too complex\" },\n        { value: \"didnt_work\", label: \"Generated app didn't work as expected\" },\n        { value: \"missing_features\", label: \"Missing features I needed\" },\n        { value: \"too_expensive\", label: \"Too expensive for the value\" },\n        { value: \"better_alternative\", label: \"Found a better alternative\" },\n        { value: \"other\", label: \"Other\" }\n      ]\n    },\n    {\n      title: \"How likely are you to recommend Magically?\",\n      key: \"recommendScore\",\n      subKey: \"recommendReason\",\n      placeholder: \"What would make you more likely to recommend us?\"\n    },\n    {\n      title: \"Would you like to schedule a feedback call?\",\n      key: \"feedbackCall\",\n      description: \"Schedule a quick call to provide detailed feedback and get a refund (up to $15 for Magically Starter) after the call. This helps us improve the overall experience.\"\n    }\n  ];\n\n  const isCurrentStepValid = useCallback(() => {\n    const currentStepData = steps[currentStep];\n    if (currentStepData.key === 'signupGoal' || currentStepData.key === 'stopReason') {\n      return !!feedback[currentStepData.key as keyof CancellationFeedback];\n    }\n    return true;\n  }, [currentStep, feedback, steps]);\n\n  const handleNext = useCallback(() => {\n    if (!isCurrentStepValid()) {\n      // Optionally, add user feedback here (e.g., toast, shake animation)\n      return;\n    }\n    if (currentStep < steps.length - 1) {\n      setCurrentStep(currentStep + 1);\n    }\n  }, [currentStep, steps, isCurrentStepValid, setCurrentStep]);\n\n  const handlePrevious = useCallback(() => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1);\n    }\n  }, [currentStep, setCurrentStep]);\n\n  // Effect for cleaning up the timeout if the component unmounts\n  useEffect(() => {\n    return () => {\n      if (debounceTimeoutRef.current) {\n        clearTimeout(debounceTimeoutRef.current);\n      }\n    };\n  }, []);\n\n  const handleSubmit = useCallback(async () => {\n    if (isSubmitting) {\n      return; // Prevent multiple submissions if already processing\n    }\n    setIsSubmitting(true); // Set loading state immediately for UI feedback\n\n    // Clear any existing debounce timeout\n    if (debounceTimeoutRef.current) {\n      clearTimeout(debounceTimeoutRef.current);\n    }\n\n    // Set a new debounce timeout\n    debounceTimeoutRef.current = setTimeout(async () => {\n      try {\n        const response = await fetch('/api/subscription/cancel', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          subscriptionId,\n          feedback: {\n            ...feedback,\n            // Include feedback call preference in the metadata\n            requestedFeedbackCall: wantsFeedbackCall\n          }\n        }),\n      });\n\n      if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.message || 'Failed to cancel subscription');\n      }\n\n      const result = await response.json();\n\n      if (onCancellationComplete) {\n        onCancellationComplete();\n      }\n\n      toast.success('Subscription cancelled successfully.');\n\n      onOpenChange(false); // Close the dialog\n      router.refresh();    // Refresh page data\n\n      // Reset internal component state\n      setCurrentStep(0);\n      setFeedback({ signupGoal: '', stopReason: '', recommendScore: 5, recommendReason: '' });\n      setWantsFeedbackCall(false);\n\n      } catch (error) {\n        console.error('Error during API call:', error);\n        // Ensure alert is shown for API call errors within the timeout\n        toast.error('An unexpected error occurred while cancelling. Please try again.');\n      } finally {\n        setIsSubmitting(false); // Reset loading state after API call attempt\n      }\n    }, 1000); // 1-second debounce period\n  }, [isSubmitting, subscriptionId, feedback, wantsFeedbackCall, onOpenChange, onCancellationComplete, router, setCurrentStep, setFeedback, setWantsFeedbackCall]);\n\n  // Success state\n  if (currentStep === steps.length) {\n    return (\n      <Dialog open={open} onOpenChange={onOpenChange}>\n        <DialogContent className=\"sm:max-w-[400px] p-0 overflow-hidden\">\n          <div className=\"flex flex-col items-center justify-center text-center p-8 space-y-4\">\n            <div className=\"rounded-full bg-green-50 p-3\">\n              <CheckCircle2 className=\"h-8 w-8 text-green-500\" />\n            </div>\n            <DialogTitle className=\"text-xl font-medium\">Subscription Cancelled</DialogTitle>\n            <DialogDescription>\n              You'll have access until the end of your billing period.\n            </DialogDescription>\n          </div>\n        </DialogContent>\n      </Dialog>\n    );\n  }\n\n  // Error state\n  if (currentStep === steps.length + 1) {\n    return (\n      <Dialog open={open} onOpenChange={onOpenChange}>\n        <DialogContent className=\"sm:max-w-[400px] p-0 overflow-hidden\">\n          <div className=\"flex flex-col items-center justify-center text-center p-8 space-y-4\">\n            <div className=\"rounded-full bg-red-50 p-3\">\n              <AlertCircle className=\"h-8 w-8 text-red-500\" />\n            </div>\n            <DialogTitle className=\"text-xl font-medium\">Something went wrong</DialogTitle>\n            <DialogDescription>\n              We couldn't cancel your subscription. Please try again.\n            </DialogDescription>\n            <Button \n              variant=\"outline\" \n              onClick={() => setCurrentStep(0)}\n              className=\"mt-2\"\n            >\n              Try Again\n            </Button>\n          </div>\n        </DialogContent>\n      </Dialog>\n    );\n  }\n\n  const step = steps[currentStep];\n  \n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"sm:max-w-[450px] p-0 overflow-hidden\">\n        <DialogHeader className=\"p-6 pb-3 border-b\">\n          <DialogTitle className=\"text-lg font-medium\">Cancel Subscription</DialogTitle>\n          <DialogDescription className=\"text-sm\">\n            We're sorry to see you go. Your feedback helps us improve.\n          </DialogDescription>\n        </DialogHeader>\n        \n        <div className=\"p-6\">\n          <h3 className=\"text-base font-medium mb-4\">{step.title}</h3>\n          \n          {(step.key === 'signupGoal' || step.key === 'stopReason') && (\n            <RadioGroup \n              value={feedback[step.key]}\n              onValueChange={(value) => setFeedback({...feedback, [step.key]: value})}\n              className=\"space-y-2.5\"\n            >\n              {step.options?.map((option) => (\n                <div \n                  key={option.value} \n                  className=\"flex items-center space-x-2 rounded-md border p-3 hover:bg-muted/20 transition-colors\"\n                >\n                  <RadioGroupItem value={option.value} id={`${step.key}-${option.value}`} />\n                  <Label htmlFor={`${step.key}-${option.value}`} className=\"flex-grow cursor-pointer\">\n                    {option.label}\n                  </Label>\n                </div>\n              ))}\n            </RadioGroup>\n          )}\n          \n          {step.key === 'recommendScore' && (\n            <div className=\"space-y-6\">\n              <div className=\"space-y-4\">\n                <div className=\"flex justify-between text-sm text-muted-foreground\">\n                  <span>Not likely</span>\n                  <span>Very likely</span>\n                </div>\n                <div className=\"px-1\">\n                  <Slider\n                    value={[feedback.recommendScore]}\n                    min={0}\n                    max={10}\n                    step={1}\n                    onValueChange={(value) => setFeedback({...feedback, recommendScore: value[0]})}\n                    className=\"my-4\"\n                  />\n                </div>\n                <div className=\"flex justify-between px-1\">\n                  {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (\n                    <span \n                      key={num} \n                      className={`w-6 h-6 flex items-center justify-center text-xs rounded-full \n                        ${feedback.recommendScore === num ? 'bg-primary text-primary-foreground' : 'text-muted-foreground'}`}\n                    >\n                      {num}\n                    </span>\n                  ))}\n                </div>\n              </div>\n              \n              <div className=\"pt-4\">\n                <Label htmlFor=\"recommend-reason\" className=\"text-sm font-medium mb-2 block\">\n                  Why did you give this score?\n                </Label>\n                <Textarea\n                  id=\"recommend-reason\"\n                  placeholder={step.placeholder}\n                  value={feedback.recommendReason || ''}\n                  onChange={(e) => setFeedback({...feedback, recommendReason: e.target.value})}\n                  className=\"resize-none h-24\"\n                />\n              </div>\n            </div>\n          )}\n          \n          {step.key === 'feedbackCall' && (\n            <div className=\"space-y-6\">\n              <p className=\"text-sm text-muted-foreground\">{step.description}</p>\n              \n              <div className=\"space-y-4\">\n                <RadioGroup \n                  value={wantsFeedbackCall ? 'yes' : 'no'}\n                  onValueChange={(value) => setWantsFeedbackCall(value === 'yes')}\n                  className=\"space-y-3\"\n                >\n                  <div className=\"flex items-center space-x-2 rounded-md border p-3 hover:bg-muted/20 transition-colors\">\n                    <RadioGroupItem value=\"yes\" id=\"feedback-call-yes\" />\n                    <Label htmlFor=\"feedback-call-yes\" className=\"flex-grow cursor-pointer\">\n                      Yes, I'd like to schedule a call and get a refund\n                    </Label>\n                  </div>\n                  <div className=\"flex items-center space-x-2 rounded-md border p-3 hover:bg-muted/20 transition-colors\">\n                    <RadioGroupItem value=\"no\" id=\"feedback-call-no\" />\n                    <Label htmlFor=\"feedback-call-no\" className=\"flex-grow cursor-pointer\">\n                      No, I'll skip the call\n                    </Label>\n                  </div>\n                </RadioGroup>\n                \n                {wantsFeedbackCall && (\n                  <div className=\"mt-4 p-4 bg-muted/20 rounded-md\">\n                    <h4 className=\"font-medium mb-2\">Schedule a Feedback Call</h4>\n                    <p className=\"text-sm mb-3\">After clicking the link below, you'll be able to choose a time that works for you.</p>\n                    <a \n                      href=\"https://calendly.com/magically-feedback/exit-interview\"\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full\"\n                    >\n                      Schedule Call & Get Refund\n                    </a>\n                    <p className=\"text-xs text-muted-foreground mt-2\">Your refund (up to $15 for Magically Starter) will be processed after the call.</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n        \n        <DialogFooter className=\"flex justify-between p-6 border-t bg-muted/10\">\n          <Button \n            variant=\"ghost\" \n            onClick={currentStep === 0 ? () => onOpenChange(false) : handlePrevious}\n            disabled={isSubmitting}\n          >\n            {currentStep === 0 ? 'Cancel' : 'Back'}\n          </Button>\n          \n          <Button \n            variant={currentStep === steps.length - 1 ? \"destructive\" : \"default\"}\n            onClick={currentStep === steps.length - 1 ? handleSubmit : handleNext}\n            disabled={isSubmitting || (currentStep === steps.length - 1 ? !isCurrentStepValid() : false)}\n          >\n            {currentStep === steps.length - 1 ? \n              (isSubmitting ? \"Processing...\" : \"Confirm Cancellation\") : \n              \"Continue\"\n            }\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  );\n}", "startLine": 36, "endLine": 377, "type": "component", "symbols": ["ExitInterview"], "score": 0.9, "context": "This component handles the subscription cancellation process including user feedback and API call to cancel the subscription. It is relevant for fixing subscription renewals as it manages cancellation and related user interactions.", "includesImports": false}], "additionalFiles": []}}