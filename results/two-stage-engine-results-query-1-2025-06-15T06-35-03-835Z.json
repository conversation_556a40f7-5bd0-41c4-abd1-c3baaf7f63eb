{"timestamp": "2025-06-15T06:35:03.835Z", "query": "How does the payment work in the app? Where can we change to fix subscription renewals?", "executionTime": 17542, "snippetsCount": 6, "additionalFilesCount": 0, "totalLines": 1430, "snippets": [{"filePath": "src/lib/subscription.ts", "type": "util", "context": "This function checks the subscription status of a user, including active subscriptions, plan tiers, and credits used, which is core to understanding how payment and subscription renewals are managed.", "score": 1, "lines": 83, "startLine": 19, "endLine": 101, "symbols": ["checkSubscriptionStatus"], "preview": "export async function checkSubscriptionStatus(userId: string, isAnonymous?: boolean): Promise<SubscriptionStatus> {\n  // Get appropriate plan based on user type\n  const freePlan = PLANS.find(plan => plan.tier === \"free\");\n  const anonymousPlan = PLANS.find(plan => plan.tier === \"anonymous\");\n  \n..."}, {"filePath": "src/components/subscription/subscription-management.tsx", "type": "component", "context": "This React component manages subscription UI including upgrade, cancellation, and renewal buttons, showing how users interact with payment and subscription renewals in the app.", "score": 1, "lines": 368, "startLine": 41, "endLine": 408, "symbols": ["SubscriptionManagement"], "preview": "export function SubscriptionManagement() {\n  const router = useRouter();\n  const [showExitInterview, setShowExitInterview] = useState(false);\n  const [isUpgrading, setIsUpgrading] = useState(false);\n\n..."}, {"filePath": "src/lib/lemonsqueezy.ts", "type": "unknown", "context": "This module contains functions to create checkouts, upgrade, cancel, and retrieve subscriptions via LemonSqueezy API, directly handling payment and subscription renewal logic.", "score": 1, "lines": 156, "startLine": 1, "endLine": 156, "symbols": ["lemonsqueezy integration functions"], "preview": "import {createCheckout as LmCheckout, updateSubscription, retrieveSubscription,} from 'lemonsqueezy.ts';\nimport {CheckoutOptions, UpdateSubscriptionOptions} from '@/types/lemonsqueezy';\n\n// Define CancelSubscriptionOptions interface\nexport interface CancelSubscriptionOptions {\n..."}, {"filePath": "src/lib/subscription.ts", "type": "util", "context": "This function uses subscription status to enforce message limits and credits, relevant to how subscription usage and renewals affect payment and access.", "score": 0.9, "lines": 70, "startLine": 103, "endLine": 172, "symbols": ["checkMessageLimit"], "preview": "export async function checkMessageLimit(userId: string, isAnonymous: boolean) {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n\n  // Get subscription status first (which already has creditsUsed for paid plans)\n..."}, {"filePath": "src/app/(admin)/admin/subscriptions/page.tsx", "type": "component", "context": "Admin page component for managing subscriptions, including viewing subscription status and details, useful for fixing subscription renewals from admin perspective.", "score": 0.7, "lines": 341, "startLine": 58, "endLine": 398, "symbols": ["SubscriptionsPage"], "preview": "export default function SubscriptionsPage() {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const { data: session } = useSession();\n  \n..."}, {"filePath": "src/app/(admin)/admin/subscriptions/[id]/page.tsx", "type": "component", "context": "Admin subscription detail page showing subscription info and usage, relevant for understanding and fixing subscription renewals and payment issues.", "score": 0.7, "lines": 412, "startLine": 95, "endLine": 506, "symbols": ["SubscriptionDetailPage"], "preview": "export default function SubscriptionDetailPage() {\n  const router = useRouter();\n  const params = useParams() || {};\n  const { data: session } = useSession();\n  const subscriptionId = params.id as string;\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/subscription.ts", "content": "export async function checkSubscriptionStatus(userId: string, isAnonymous?: boolean): Promise<SubscriptionStatus> {\n  // Get appropriate plan based on user type\n  const freePlan = PLANS.find(plan => plan.tier === \"free\");\n  const anonymousPlan = PLANS.find(plan => plan.tier === \"anonymous\");\n  \n  // Skip subscription check for anonymous users\n  if (isAnonymous) {\n    // For anonymous users, count messages from token consumption\n    const result = await db\n      .select({\n        messageCount: sql<number>`COUNT(*)`\n      })\n      .from(tokenConsumption)\n      .where(\n        eq(tokenConsumption.userId, userId)\n      );\n\n    // Ensure we have a number by explicitly converting\n    const messagesUsed = Number(result[0]?.messageCount || 0);\n    const anonMessageLimit = anonymousPlan?.operations || 100;\n    \n    return {\n      isActive: false,\n      status: 'active',\n      plan: 'anonymous',\n      expiresAt: null,\n      credits: anonMessageLimit,  // Now represents message limit\n      creditsUsed: messagesUsed,  // Now represents messages used\n      creditsRemaining: Math.max(0, anonMessageLimit - messagesUsed)\n    };\n  }\n  \n  // For authenticated users, check if they have an active subscription\n  const userSubscription = await db\n    .select()\n    .from(subscriptions)\n    .where(and(\n        eq(subscriptions.userId, userId),\n        eq(subscriptions.isActive, true)\n    ))\n    .orderBy(desc(subscriptions.updatedAt))\n    .limit(1)\n    .then(results => results[0]);\n\n  if (!userSubscription || !userSubscription.isActive) {\n    // For free tier users, count messages from token consumption\n    const result = await db\n      .select({\n        messageCount: sql<number>`COUNT(*)`\n      })\n      .from(tokenConsumption)\n      .where(\n        eq(tokenConsumption.userId, userId)\n      );\n\n    // Ensure we have a number by explicitly converting\n    const messagesUsed = Number(result[0]?.messageCount || 0);\n    const freeMessageLimit = freePlan?.operations || 50;\n\n    return {\n      isActive: false,\n      plan: 'free',\n      status: 'active',\n      expiresAt: null,\n      credits: freeMessageLimit, // Free tier message limit\n      creditsUsed: messagesUsed, // Messages used\n      creditsRemaining: Math.max(0, freeMessageLimit - messagesUsed)\n    };\n  }\n\n  // User has an active subscription - use subscription.creditsUsed as source of truth for message count\n  return {\n    expiryDate: userSubscription.resetDate ? userSubscription.resetDate.toISOString() : undefined,\n    isActive: !!userSubscription.isActive,\n    subscriptionId: userSubscription.id,\n    status: userSubscription.status as any,\n    plan: userSubscription.planId as PlanTier,\n    expiresAt: userSubscription.resetDate, // Use resetDate as the expiration date\n    credits: userSubscription.credits,      // Now represents message limit\n    creditsUsed: userSubscription.creditsUsed || 0, // Now represents messages used\n    creditsRemaining: Math.max(0, userSubscription.credits - (userSubscription.creditsUsed || 0))\n  };\n}", "startLine": 19, "endLine": 101, "type": "util", "symbols": ["checkSubscriptionStatus"], "score": 1, "context": "This function checks the subscription status of a user, including active subscriptions, plan tiers, and credits used, which is core to understanding how payment and subscription renewals are managed.", "includesImports": false}, {"filePath": "src/components/subscription/subscription-management.tsx", "content": "export function SubscriptionManagement() {\n  const router = useRouter();\n  const [showExitInterview, setShowExitInterview] = useState(false);\n  const [isUpgrading, setIsUpgrading] = useState(false);\n\n  const { data: status, error, isLoading, mutate } = useSWR<SubscriptionStatus>(\n    '/api/subscription/status',\n    fetcher,\n    {\n      refreshInterval: 60000, // Refresh every minute\n      revalidateOnFocus: true,\n    }\n  );\n\n  // Format large numbers with k suffix\n  const formatNumber = (num: number) => {\n    return num >= 1000 ? `${(num / 1000).toFixed(1)}k` : num.toString();\n  };\n\n  const getPlanIcon = (plan: PlanTier) => {\n    switch (plan) {\n      case 'prime':\n        return <Sparkles className=\"h-5 w-5 text-yellow-400\" />;\n      case 'plus':\n        return <Sparkles className=\"h-5 w-5 text-yellow-400\" />;\n      case 'pro':\n        return <Zap className=\"h-5 w-5 text-purple-400\" />;\n      case 'starter':\n        return <Rocket className=\"h-5 w-5 text-blue-400\" />;\n      case 'free':\n        return <CreditCardIcon className=\"h-5 w-5 text-gray-400\" />;\n      default:\n        return <CreditCardIcon className=\"h-5 w-5 text-gray-400\" />;\n    }\n  };\n\n  const getPlanColor = (plan: PlanTier) => {\n    switch (plan) {\n      case 'prime':\n        return 'bg-yellow-400';\n      case 'plus':\n        return 'bg-yellow-400';\n      case 'pro':\n        return 'bg-purple-400';\n      case 'starter':\n        return 'bg-blue-400';\n      default:\n        return 'bg-gray-400';\n    }\n  };\n\n  const handleViewPlans = () => {\n    router.push('/pricing');\n  };\n\n  const handleUpgrade = async (planId: string) => {\n    try {\n      setIsUpgrading(true);\n\n      // Get plan details for tracking\n      const planDetails = PLANS.find(plan => plan.id === planId);\n\n      // Track upgrade initiation\n      trackSubscriptionEvent('UPGRADE_INITIATED', {\n        current_plan: status?.planTier || 'unknown',\n        plan_type: planDetails?.tier || planId,\n        price: planDetails?.price || 0,\n        currency: 'USD',\n        entry_point: 'subscription_management'\n      });\n\n      const response = await fetch('/api/checkout', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          returnUrl: '/subscription',\n          planId\n        })\n      });\n\n      const data = await response.json();\n\n      if (data.error) {\n        // Track upgrade failure\n        trackSubscriptionEvent('UPGRADE_FAILED', {\n          current_plan: status?.planTier || 'unknown',\n          plan_type: planDetails?.tier || planId,\n          // Using any to bypass type checking for custom properties\n          ...(data.error ? { error_message: data.error } : {})\n        });\n\n        throw new Error(data.error);\n      }\n\n      // Track upgrade initiated (instead of checkout initiated which isn't in the allowed event types)\n      trackSubscriptionEvent('UPGRADE_INITIATED', {\n        current_plan: status?.planTier || 'unknown',\n        plan_type: planDetails?.tier || planId,\n        entry_point: 'subscription_management'\n      });\n\n      // Redirect to checkout URL\n      if (data.url) {\n        window.location.href = data.url;\n      } else {\n        throw new Error('No checkout URL returned');\n      }\n    } catch (error) {\n      console.error('Error upgrading plan:', error);\n    } finally {\n      setIsUpgrading(false);\n    }\n  };\n\n  // Format date to readable format\n  const formatDate = (dateString?: string) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center py-10\">\n        <div className=\"animate-pulse flex flex-col items-center gap-4\">\n          <div className=\"h-8 w-40 bg-muted rounded\"></div>\n          <div className=\"h-40 w-full max-w-md bg-muted rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !status) {\n    return (\n      <Alert variant=\"destructive\" className=\"max-w-3xl mx-auto my-8\">\n        <AlertCircle className=\"h-4 w-4\" />\n        <AlertTitle>Error</AlertTitle>\n        <AlertDescription>\n          Unable to load subscription information. Please try refreshing the page.\n        </AlertDescription>\n      </Alert>\n    );\n  }\n\n  const usagePercentage = Math.min(100, Math.round((status.credits.used / status.credits.total) * 100));\n  const planDetails = PLANS.find(plan => plan.tier === status.planTier);\n\n  // Get color based on usage percentage\n  const getUsageColor = (percentage: number) => {\n    if (percentage >= 90) return 'bg-red-500';\n    if (percentage >= 75) return 'bg-amber-500';\n    return getPlanColor(status.planTier);\n  };\n\n  return (\n    <div className=\"space-y-8 max-w-4xl mx-auto\">\n      <div className=\"space-y-2\">\n        <h1 className=\"text-3xl font-bold\">Subscription Management</h1>\n        <p className=\"text-muted-foreground\">Manage your subscription and usage details</p>\n      </div>\n\n      {/* Current Plan Card */}\n      <Card className=\"overflow-hidden\">\n        <CardHeader className=\"bg-muted/50\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-2\">\n              {getPlanIcon(status.planTier)}\n              <CardTitle className=\"capitalize\">{status.planName} Plan</CardTitle>\n            </div>\n            <Badge\n              variant={status.status === 'active' ? 'default' :\n                      status.status === 'cancelled' ? 'destructive' :\n                      status.status === 'past_due' ? 'destructive' : 'outline'}\n            >\n              {status.status === 'active' ? 'Active' :\n               status.status === 'cancelled' ? 'Cancelled' :\n               status.status === 'past_due' ? 'Past Due' : 'Inactive'}\n            </Badge>\n          </div>\n        </CardHeader>\n        <CardContent className=\"pt-6 space-y-6\">\n          {/* Subscription Status Information */}\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            <div className=\"space-y-4\">\n              <div>\n                <h3 className=\"text-sm font-medium text-muted-foreground mb-1\">Subscription Details</h3>\n                <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                  <div>Plan:</div>\n                  <div className=\"font-medium capitalize\">{status.planName}</div>\n\n                  <div>Status:</div>\n                  <div className=\"font-medium capitalize\">{status.status || 'Unknown'}</div>\n\n                  <div>Price:</div>\n                  <div className=\"font-medium\">${planDetails?.price || 0}/month</div>\n\n                  {status.currentPeriodEnd && (\n                    <>\n                      <div>Current Period Ends:</div>\n                      <div className=\"font-medium\">{formatDate(status.currentPeriodEnd)}</div>\n                    </>\n                  )}\n\n                  {status.status === 'cancelled' && status.cancelAt && (\n                    <>\n                      <div>Access Until:</div>\n                      <div className=\"font-medium\">{formatDate(status.cancelAt)}</div>\n                    </>\n                  )}\n                </div>\n              </div>\n\n              {status.status === 'cancelled' && (\n                <Alert>\n                  <CalendarClock className=\"h-4 w-4\" />\n                  <AlertTitle>Subscription Cancelled</AlertTitle>\n                  <AlertDescription>\n                    Your subscription has been cancelled but you still have access until {formatDate(status.cancelAt || status.currentPeriodEnd)}.\n                  </AlertDescription>\n                </Alert>\n              )}\n            </div>\n\n            {/* Usage Information */}\n            <div className=\"space-y-4\">\n              <div>\n                <h3 className=\"text-sm font-medium text-muted-foreground mb-1\">Usage</h3>\n                <div className=\"flex items-center justify-between mb-1\">\n                  <span className=\"text-sm\">Messages</span>\n                  <div>\n                    <span className=\"text-sm font-medium\">{formatNumber(status.credits.remaining)}</span>\n                    <span className=\"text-xs text-muted-foreground\"> / {formatNumber(status.credits.total)}</span>\n                  </div>\n                </div>\n                <Progress value={usagePercentage} className=\"h-2\">\n                  <div className={`h-full ${getUsageColor(usagePercentage)}`} style={{ width: `${usagePercentage}%` }} />\n                </Progress>\n                {usagePercentage >= 90 && (\n                  <p className=\"text-xs text-red-500 mt-1\">\n                    Very low on messages. Consider upgrading your plan.\n                  </p>\n                )}\n                {usagePercentage >= 75 && usagePercentage < 90 && (\n                  <p className=\"text-xs text-amber-500 mt-1\">\n                    Running low on messages.\n                  </p>\n                )}\n              </div>\n\n              {status.dailyLimit && status.dailyLimit > 0 && (\n                <div>\n                  <div className=\"flex items-center justify-between mb-1\">\n                    <span className=\"text-sm\">Daily Limit</span>\n                    <div>\n                      <span className=\"text-sm font-medium\">{status.dailyRemaining}</span>\n                      <span className=\"text-xs text-muted-foreground\"> / {status.dailyLimit}</span>\n                    </div>\n                  </div>\n                  <Progress\n                    value={Math.min(100, Math.round(((status.dailyLimit - (status.dailyRemaining || 0)) / status.dailyLimit) * 100))}\n                    className=\"h-2\"\n                  >\n                    <div\n                      className={`h-full ${getPlanColor(status.planTier)}`}\n                      style={{ width: `${Math.min(100, Math.round(((status.dailyLimit - (status.dailyRemaining || 0)) / status.dailyLimit) * 100))}%` }}\n                    />\n                  </Progress>\n                </div>\n              )}\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Plan Features */}\n          <div>\n            <h3 className=\"text-sm font-medium text-muted-foreground mb-2\">Plan Features</h3>\n            <ul className=\"grid grid-cols-1 md:grid-cols-2 gap-2\">\n              {planDetails?.features.map((feature, index) => (\n                <li key={index} className=\"flex items-start gap-2\">\n                  <CheckCircle className=\"h-4 w-4 text-green-500 mt-0.5\" />\n                  <span className=\"text-sm\">{feature}</span>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </CardContent>\n        <CardFooter className=\"flex justify-between items-center bg-muted/30 border-t\">\n          <div className=\"flex flex-col sm:flex-row gap-3 w-full justify-end\">\n            {status.status === 'active' && (\n              <>\n                {/* Upgrade button with better styling */}\n                <Button \n                  variant=\"default\" \n                  size=\"sm\"\n                  className=\"sm:w-auto px-6 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white\"\n                  onClick={handleViewPlans}\n                >\n                  <Zap className=\"mr-2 h-4 w-4\" />\n                  Upgrade Plan\n                </Button>\n                \n                {/* Cancel subscription button with subtle styling */}\n                <Button \n                  variant=\"ghost\" \n                  size=\"sm\"\n                  className=\"sm:w-auto px-6 text-gray-500 hover:text-gray-700 hover:bg-gray-100/50\"\n                  onClick={() => setShowExitInterview(true)}\n                  disabled={!status.subscriptionId}\n                >\n                  Cancel Plan\n                </Button>\n              </>\n            )}\n            \n            {status.status === 'cancelled' && (\n              <Button \n                variant=\"default\" \n                size=\"sm\"\n                className=\"sm:w-auto px-6 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white\"\n                onClick={handleViewPlans}\n              >\n                <CreditCard className=\"mr-2 h-4 w-4\" />\n                Resubscribe\n              </Button>\n            )}\n            \n            {(status.status === 'expired' || status.status === 'past_due') && (\n              <Button \n                variant=\"default\" \n                size=\"sm\"\n                className=\"sm:w-auto px-6 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white\"\n                onClick={handleViewPlans}\n              >\n                <CreditCard className=\"mr-2 h-4 w-4\" />\n                Renew Subscription\n              </Button>\n            )}\n          </div>\n        </CardFooter>\n      </Card>\n\n      {/* Exit Interview Modal with improved integration */}\n      {status.subscriptionId && (\n        <ExitInterview\n          open={showExitInterview}\n          onOpenChange={(open) => {\n            setShowExitInterview(open);\n            if (!open) {\n              // Refresh data when modal is closed\n              setTimeout(() => mutate(), 500);\n            }\n          }}\n          subscriptionId={status.subscriptionId}\n          onCancellationComplete={() => {\n            // Refresh data after cancellation\n            mutate();\n          }}\n        />\n      )}\n    </div>\n  );\n}", "startLine": 41, "endLine": 408, "type": "component", "symbols": ["SubscriptionManagement"], "score": 1, "context": "This React component manages subscription UI including upgrade, cancellation, and renewal buttons, showing how users interact with payment and subscription renewals in the app.", "includesImports": false}, {"filePath": "src/lib/lemonsqueezy.ts", "content": "import {createCheckout as LmCheckout, updateSubscription, retrieveSubscription,} from 'lemonsqueezy.ts';\nimport {CheckoutOptions, UpdateSubscriptionOptions} from '@/types/lemonsqueezy';\n\n// Define CancelSubscriptionOptions interface\nexport interface CancelSubscriptionOptions {\n    subscriptionId: string;\n    variantId: number;\n    productId: number\n}\n\nif (!process.env.LEMON_SQUEEZY_API_KEY) {\n    throw new Error('LEMON_SQUEEZY_API_KEY is not set');\n}\n\nexport const STORE_ID = process.env.LEMON_SQUEEZY_STORE_ID;\nexport const VARIANT_IDS = {\n    starter: process.env.LEMON_SQUEEZY_STARTER_VARIANT_ID,\n    pro: process.env.LEMON_SQUEEZY_PRO_VARIANT_ID,\n    plus: process.env.LEMON_SQUEEZY_PLUS_VARIANT_ID,\n    prime: process.env.LEMON_SQUEEZY_PRIME_VARIANT_ID\n} as const;\n\nexport const PRODUCT_IDS = {\n    starter: process.env.LEMON_SQUEEZY_STARTER_PRODUCT_ID,\n    pro: process.env.LEMON_SQUEEZY_PRO_PRODUCT_ID,\n    plus: process.env.LEMON_SQUEEZY_PLUS_PRODUCT_ID,\n    prime: process.env.LEMON_SQUEEZY_PRIME_PRODUCT_ID\n} as const;\n\nexport type PlanVariant = keyof typeof VARIANT_IDS;\n\nexport async function createCheckout(options: CheckoutOptions) {\n    // console.log('options', options)\n    try {\n        const checkout = await LmCheckout({\n            // ...(options as any),\n            checkout_data: {\n                name: options.checkout_data?.name,\n                email: options.checkout_data?.email,\n                custom: options?.checkout_data?.custom_data\n            },\n            product_options: {\n                redirect_url: options.product_options?.redirect_url,\n                name: options.product_options?.name || \"magically Builder Plan\",\n                description: options.product_options?.description || \"Subscribe to your magically Builder plan\",\n            },\n            store: options.store_id + \"\",\n            variant: options.variant_id + \"\",\n            apiKey: process.env.LEMON_SQUEEZY_API_KEY as string,\n            checkout_options: {\n                dark: true,\n                embed: false,\n                subscription_preview: true,\n                media: false,\n                desc: true,\n            }\n        });\n        console.log('checkout', checkout.data.attributes)\n        return checkout;\n    } catch (error) {\n        console.error('Error creating checkout:', error);\n        throw error;\n    }\n}\n\nexport async function verifyWebhookSignature(\n    payload: string,\n    signature: string\n) {\n    // Implement webhook signature verification\n    const secret = process.env.LEMON_SQUEEZY_WEBHOOK_SECRET;\n    if (!secret) throw new Error('LEMON_SQUEEZY_WEBHOOK_SECRET is not set');\n\n    const crypto = require('crypto');\n    const hmac = crypto.createHmac('sha256', secret);\n    const digest = hmac.update(payload).digest('hex');\n\n    return signature === digest;\n}\n\n/**\n * Update an existing subscription to a new plan\n * @param options Options for updating the subscription\n * @returns The updated subscription data\n */\nexport async function upgradeSubscription(options: UpdateSubscriptionOptions) {\n    try {\n        const {subscriptionId, variantId, invoiceImmediately = true, disableProrations = false, productId} = options;\n\n        if (!process.env.LEMON_SQUEEZY_API_KEY) {\n            throw new Error('LEMON_SQUEEZY_API_KEY is not set');\n        }\n\n        return await updateSubscription({\n            id: subscriptionId,\n            variantId: variantId + \"\",\n            productId: productId + \"\",\n            disableProrations: disableProrations,\n            invoiceImmediately: invoiceImmediately,\n            apiKey: process.env.LEMON_SQUEEZY_API_KEY as string,\n        });\n    } catch (error) {\n        console.error('Error updating subscription:', JSON.stringify(error, null, 2));\n        throw error;\n    }\n}\n\n// getSubscription function is already defined below\n\n/**\n * Cancel an existing subscription\n * @param options Options for cancelling the subscription\n * @returns The cancelled subscription data\n */\nexport async function cancelSubscription(options: CancelSubscriptionOptions) {\n    try {\n        const {subscriptionId, variantId, productId} = options;\n\n        if (!process.env.LEMON_SQUEEZY_API_KEY) {\n            throw new Error('LEMON_SQUEEZY_API_KEY is not set');\n        }\n\n        return updateSubscription({\n            apiKey: process.env.LEMON_SQUEEZY_API_KEY as string,\n            cancelled: true,\n            id: subscriptionId,\n            variantId: variantId + \"\",\n            productId: productId + \"\",\n        })\n    } catch (error) {\n        console.error('Error cancelling subscription:', error);\n        throw error;\n    }\n}\n\n/**\n * Get a subscription by ID\n * @param subscriptionId The ID of the subscription to retrieve\n * @returns The subscription data\n */\nexport async function getSubscription(subscriptionId: string) {\n    try {\n        if (!process.env.LEMON_SQUEEZY_API_KEY) {\n            throw new Error('LEMON_SQUEEZY_API_KEY is not set');\n        }\n\n        return retrieveSubscription({\n            apiKey: process.env.LEMON_SQUEEZY_API_KEY as string,\n            id: subscriptionId,\n        })\n    } catch (error) {\n        console.error('Error getting subscription:', error);\n        throw error;\n    }\n}\n", "startLine": 1, "endLine": 156, "type": "unknown", "symbols": ["lemonsqueezy integration functions"], "score": 1, "context": "This module contains functions to create checkouts, upgrade, cancel, and retrieve subscriptions via LemonSqueezy API, directly handling payment and subscription renewal logic.", "includesImports": false}, {"filePath": "src/lib/subscription.ts", "content": "export async function checkMessageLimit(userId: string, isAnonymous: boolean) {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n\n  // Get subscription status first (which already has creditsUsed for paid plans)\n  const subscription = await checkSubscriptionStatus(userId, isAnonymous);\n  \n  // For consistency, use the creditsUsed from subscription status (now represents messages)\n  const messagesUsed = subscription.creditsUsed;\n\n  // Get plan details\n  const plan = getPlanByTier(subscription.plan);\n  \n  // Get daily limit from plan\n  const dailyLimit = plan.dailyLimit;\n  \n  // Check if plan has a daily limit (not -1)\n  if (dailyLimit > 0) {\n    // Count messages used today\n    const todayResult = await db\n      .select({\n        todayMessageCount: sql<number>`COUNT(*)`\n      })\n      .from(tokenConsumption)\n      .where(\n        and(\n          eq(tokenConsumption.userId, userId),\n          gte(tokenConsumption.createdAt, today)\n        )\n      );\n    \n    // Ensure we have a number by explicitly converting\n    const todayMessagesUsed = Number(todayResult[0]?.todayMessageCount || 0);\n    const dailyRemaining = Math.max(0, dailyLimit - todayMessagesUsed);\n    \n    // Check both daily and monthly message limits\n    return {\n      canSendMessage: todayMessagesUsed < dailyLimit && messagesUsed < subscription.credits,\n      remaining: Math.min(\n        dailyRemaining,\n        Math.max(0, subscription.credits - messagesUsed)\n      ),\n      limit: subscription.credits,\n      dailyLimit: dailyLimit,\n      dailyRemaining: dailyRemaining,\n      isPro: subscription.plan !== 'free' && subscription.plan !== 'anonymous',\n      isAnonymous: subscription.plan === 'anonymous',\n      planTier: subscription.plan,\n      totalCredits: subscription.credits,      // Now represents total message limit\n      creditsUsed: messagesUsed,               // Now represents messages used\n      creditsRemaining: Math.max(0, subscription.credits - messagesUsed) // Messages remaining\n    };\n  }\n\n  // Plans without daily limit (dailyLimit = -1)\n  return {\n    canSendMessage: subscription.creditsRemaining > 0,\n    remaining: subscription.creditsRemaining,\n    limit: subscription.credits,\n    isPro: subscription.plan !== 'free' && subscription.plan !== 'anonymous',\n    isAnonymous: subscription.plan === 'anonymous',\n    planTier: subscription.plan,\n    totalCredits: subscription.credits,      // Now represents total message limit\n    creditsUsed: subscription.creditsUsed,   // Now represents messages used\n    creditsRemaining: subscription.creditsRemaining, // Messages remaining\n    // Include dailyLimit as -1 to indicate unlimited\n    dailyLimit: -1,\n    dailyRemaining: -1\n  };\n}", "startLine": 103, "endLine": 172, "type": "util", "symbols": ["checkMessageLimit"], "score": 0.9, "context": "This function uses subscription status to enforce message limits and credits, relevant to how subscription usage and renewals affect payment and access.", "includesImports": false}, {"filePath": "src/app/(admin)/admin/subscriptions/page.tsx", "content": "export default function SubscriptionsPage() {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const { data: session } = useSession();\n  \n  // State\n  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);\n  const [pagination, setPagination] = useState<PaginationInfo>({\n    page: 1,\n    pageSize: 10,\n    pageCount: 1,\n    totalCount: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortField, setSortField] = useState<string>('createdAt');\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');\n  const [filters, setFilters] = useState<Record<string, string[]>>({});\n  \n  // Get current page from URL or default to 1\n  const currentPage = Number(searchParams?.get('page') || 1);\n\n  // Fetch subscriptions data\n  const fetchSubscriptions = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch(`/api/admin/subscriptions?page=${currentPage}&pageSize=10&sortField=${sortField}&sortDirection=${sortDirection}&searchTerm=${searchTerm}${\n        Object.keys(filters).length > 0 \n          ? `&filters=${encodeURIComponent(JSON.stringify(filters))}` \n          : ''\n      }`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch subscriptions');\n      }\n      \n      const data = await response.json();\n      setSubscriptions(data.data);\n      setPagination(data.pagination);\n    } catch (error) {\n      console.error('Error fetching subscriptions:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  \n  // Initial fetch\n  useEffect(() => {\n    fetchSubscriptions();\n  }, [currentPage, sortField, sortDirection, searchTerm, filters]);\n\n  // Handle search\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    router.push(`/admin/subscriptions?page=1`);\n  };\n  \n  // Handle sort\n  const handleSort = (field: string) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('desc');\n    }\n  };\n  \n  // Handle pagination\n  const goToPage = (page: number) => {\n    router.push(`/admin/subscriptions?page=${page}`);\n  };\n  \n  // Handle filter change\n  const handleFilterChange = (key: string, value: string) => {\n    // If the value is one of our \"all\" options, remove the filter\n    if (value === 'all_status' || value === 'all_plans') {\n      const newFilters = { ...filters };\n      delete newFilters[key];\n      setFilters(newFilters);\n    } else {\n      setFilters(prev => ({\n        ...prev,\n        [key]: [value]\n      }));\n    }\n  };\n  \n  // View token consumption for a subscription\n  const viewTokenConsumption = (subscriptionId: string) => {\n    router.push(`/admin/subscriptions/${subscriptionId}`);\n  };\n  \n  // Render status badge\n  const renderStatusBadge = (status: string) => {\n    let variant = 'default';\n    \n    switch (status) {\n      case 'active':\n        variant = 'success';\n        break;\n      case 'inactive':\n        variant = 'secondary';\n        break;\n      case 'cancelled':\n        variant = 'destructive';\n        break;\n      case 'pending':\n        variant = 'warning';\n        break;\n      default:\n        variant = 'default';\n    }\n    \n    return (\n      <Badge variant={variant as any}>{status}</Badge>\n    );\n  };\n  \n  // Loading skeleton\n  if (loading && subscriptions.length === 0) {\n    return (\n      <div className=\"space-y-4\">\n        <Card>\n          <CardHeader>\n            <Skeleton className=\"h-8 w-1/3\" />\n            <Skeleton className=\"h-4 w-1/2\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-2\">\n              {Array.from({ length: 5 }).map((_, i) => (\n                <Skeleton key={i} className=\"h-12 w-full\" />\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n  \n  return (\n    <div className=\"space-y-4\">\n      <Card>\n        <CardHeader>\n          <CardTitle>Subscriptions</CardTitle>\n          <CardDescription>\n            Manage and view all user subscriptions\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {/* Search and filters */}\n          <div className=\"flex items-center justify-between mb-4\">\n            <form onSubmit={handleSearch} className=\"flex items-center space-x-2\">\n              <Input\n                placeholder=\"Search by name, email, or plan...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-64\"\n              />\n              <Button type=\"submit\" size=\"sm\" variant=\"secondary\">\n                <Search className=\"h-4 w-4 mr-1\" /> Search\n              </Button>\n            </form>\n            \n            <div className=\"flex items-center space-x-2\">\n              <Select\n                value={filters.status?.[0] || 'all_status'}\n                onValueChange={(value) => handleFilterChange('status', value)}\n              >\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue placeholder=\"Status\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all_status\">All Status</SelectItem>\n                  <SelectItem value=\"active\">Active</SelectItem>\n                  <SelectItem value=\"inactive\">Inactive</SelectItem>\n                  <SelectItem value=\"cancelled\">Cancelled</SelectItem>\n                  <SelectItem value=\"pending\">Pending</SelectItem>\n                </SelectContent>\n              </Select>\n              \n              <Select\n                value={filters.planId?.[0] || 'all_plans'}\n                onValueChange={(value) => handleFilterChange('planId', value)}\n              >\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue placeholder=\"Plan\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all_plans\">All Plans</SelectItem>\n                  <SelectItem value=\"free\">Free</SelectItem>\n                  <SelectItem value=\"pro\">Pro</SelectItem>\n                  <SelectItem value=\"team\">Team</SelectItem>\n                </SelectContent>\n              </Select>\n              \n              <Button onClick={fetchSubscriptions} size=\"sm\" variant=\"outline\">\n                <RefreshCw className=\"h-4 w-4 mr-1\" /> Refresh\n              </Button>\n            </div>\n          </div>\n          \n          {/* Subscriptions table */}\n          <div className=\"rounded-md border\">\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead \n                    className=\"cursor-pointer\"\n                    onClick={() => handleSort('userName')}\n                  >\n                    User\n                  </TableHead>\n                  <TableHead \n                    className=\"cursor-pointer\"\n                    onClick={() => handleSort('planId')}\n                  >\n                    Plan\n                  </TableHead>\n                  <TableHead \n                    className=\"cursor-pointer\"\n                    onClick={() => handleSort('status')}\n                  >\n                    Status\n                  </TableHead>\n                  <TableHead \n                    className=\"cursor-pointer\"\n                    onClick={() => handleSort('credits')}\n                  >\n                    Credits\n                  </TableHead>\n                  <TableHead \n                    className=\"cursor-pointer\"\n                    onClick={() => handleSort('createdAt')}\n                  >\n                    Created\n                  </TableHead>\n                  <TableHead \n                    className=\"cursor-pointer\"\n                    onClick={() => handleSort('resetDate')}\n                  >\n                    Reset Date\n                  </TableHead>\n                  <TableHead>Actions</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {subscriptions.length === 0 ? (\n                  <TableRow>\n                    <TableCell colSpan={7} className=\"text-center py-4\">\n                      No subscriptions found\n                    </TableCell>\n                  </TableRow>\n                ) : (\n                  subscriptions.map((subscription) => (\n                    <TableRow key={subscription.id}>\n                      <TableCell>\n                        <div>\n                          <div className=\"font-medium\">\n                            {subscription.userName || 'Unknown'}\n                            {subscription.userName && subscription.userEmail && (\n                              <span className=\"ml-1 text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded-full\">\n                                User\n                              </span>\n                            )}\n                          </div>\n                          <div className=\"text-sm text-muted-foreground\">{subscription.userEmail}</div>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <Badge variant=\"outline\">{subscription.planId}</Badge>\n                      </TableCell>\n                      <TableCell>\n                        {renderStatusBadge(subscription.status)}\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"space-y-1\">\n                          <div className=\"text-sm\">\n                            {subscription.creditsUsed} / {subscription.credits}\n                          </div>\n                          <Progress value={subscription.percentUsed} className=\"h-2\" />\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        {subscription.createdAt \n                          ? format(new Date(subscription.createdAt), 'MMM d, yyyy') \n                          : 'N/A'}\n                      </TableCell>\n                      <TableCell>\n                        {subscription.resetDate \n                          ? format(new Date(subscription.resetDate), 'MMM d, yyyy') \n                          : 'N/A'}\n                      </TableCell>\n                      <TableCell>\n                        <Button \n                          size=\"sm\" \n                          onClick={() => viewTokenConsumption(subscription.id)}\n                        >\n                          View Usage\n                        </Button>\n                      </TableCell>\n                    </TableRow>\n                  ))\n                )}\n              </TableBody>\n            </Table>\n          </div>\n          \n          {/* Pagination */}\n          {pagination.pageCount > 1 && (\n            <div className=\"flex items-center justify-between mt-4\">\n              <div className=\"text-sm text-muted-foreground\">\n                Showing {((pagination.page - 1) * pagination.pageSize) + 1} to {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} of {pagination.totalCount} subscriptions\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => goToPage(pagination.page - 1)}\n                  disabled={pagination.page === 1}\n                >\n                  <ChevronLeft className=\"h-4 w-4\" />\n                </Button>\n                <div className=\"text-sm\">\n                  Page {pagination.page} of {pagination.pageCount}\n                </div>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => goToPage(pagination.page + 1)}\n                  disabled={pagination.page === pagination.pageCount}\n                >\n                  <ChevronRight className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n}", "startLine": 58, "endLine": 398, "type": "component", "symbols": ["SubscriptionsPage"], "score": 0.7, "context": "Admin page component for managing subscriptions, including viewing subscription status and details, useful for fixing subscription renewals from admin perspective.", "includesImports": false}, {"filePath": "src/app/(admin)/admin/subscriptions/[id]/page.tsx", "content": "export default function SubscriptionDetailPage() {\n  const router = useRouter();\n  const params = useParams() || {};\n  const { data: session } = useSession();\n  const subscriptionId = params.id as string;\n  \n  // State\n  const [subscription, setSubscription] = useState<SubscriptionDetails | null>(null);\n  const [tokenConsumption, setTokenConsumption] = useState<TokenConsumption[]>([]);\n  const [summary, setSummary] = useState<ConsumptionSummary | null>(null);\n  const [cachingBreakdown, setCachingBreakdown] = useState<CachingBreakdown[]>([]);\n  const [pagination, setPagination] = useState<PaginationInfo>({\n    page: 1,\n    pageSize: 10,\n    pageCount: 1,\n    totalCount: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [sortField, setSortField] = useState<string>('createdAt');\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');\n  const [dateRange, setDateRange] = useState({\n    from: subDays(new Date(), 30),\n    to: new Date(),\n  });\n  \n  // Fetch subscription details\n  const fetchSubscriptionDetails = async () => {\n    try {\n      const response = await fetch(`/api/admin/subscriptions/${subscriptionId}`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch subscription details');\n      }\n      \n      const data = await response.json();\n      setSubscription(data);\n    } catch (error) {\n      console.error('Error fetching subscription details:', error);\n    }\n  };\n  \n  // Fetch token consumption data\n  const fetchTokenConsumption = async (page = 1) => {\n    setLoading(true);\n    try {\n      // Prepare query parameters\n      const params = new URLSearchParams();\n      params.append('page', page.toString());\n      params.append('pageSize', pagination.pageSize.toString());\n      params.append('sortField', sortField);\n      params.append('sortDirection', sortDirection);\n      \n      if (dateRange.from) {\n        params.append('startDate', dateRange.from.toISOString());\n      }\n      \n      if (dateRange.to) {\n        params.append('endDate', dateRange.to.toISOString());\n      }\n      \n      const response = await fetch(`/api/admin/subscriptions/${subscriptionId}/consumption?${params.toString()}`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch token consumption');\n      }\n      \n      const data = await response.json();\n      setTokenConsumption(data.data);\n      setSummary(data.summary);\n      setCachingBreakdown(data.cachingBreakdown);\n      setPagination(data.pagination);\n    } catch (error) {\n      console.error('Error fetching token consumption:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  \n  // Initial data fetch\n  useEffect(() => {\n    fetchSubscriptionDetails();\n    fetchTokenConsumption();\n  }, [subscriptionId]);\n  \n  // Handle sort\n  const handleSort = (field: string) => {\n    if (field === sortField) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('desc');\n    }\n    fetchTokenConsumption(1);\n  };\n  \n  // Handle pagination\n  const goToPage = (page: number) => {\n    fetchTokenConsumption(page);\n  };\n  \n  // Go back to subscriptions list\n  const goBack = () => {\n    router.push('/admin/subscriptions');\n  };\n  \n  // Format date\n  const formatDate = (dateString: string) => {\n    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');\n  };\n  \n  // Format number with commas\n  const formatNumber = (num: number) => {\n    return new Intl.NumberFormat().format(num);\n  };\n  \n  // Format cost as currency\n  const formatCost = (cost: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 4\n    }).format(cost);\n  };\n  \n  // Handle date range selection\n  const handleDateRangeSelect = (days: number) => {\n    const to = new Date();\n    const from = subDays(to, days);\n    setDateRange({ from, to });\n    fetchTokenConsumption(1);\n  };\n  \n  return (\n    <div className=\"container max-w-7xl mx-auto py-4 space-y-4\">\n      {/* Subscription Header Card */}\n      <Card className=\"shadow-sm\">\n        <CardHeader className=\"pb-3 flex flex-row items-center justify-between\">\n          <div>\n            <Button \n              variant=\"ghost\" \n              size=\"sm\" \n              onClick={goBack} \n              className=\"mb-2\"\n            >\n              <ArrowLeft className=\"mr-2 h-4 w-4\" />\n              Back to Subscriptions\n            </Button>\n            <CardTitle className=\"text-xl flex items-center gap-2\">\n              {subscription ? (\n                <>\n                  <span>{subscription.userName || 'User'}</span>\n                  {subscription.isActive ? (\n                    <Badge className=\"ml-2\">Active</Badge>\n                  ) : (\n                    <Badge variant=\"outline\" className=\"ml-2\">Inactive</Badge>\n                  )}\n                </>\n              ) : (\n                <Skeleton className=\"h-6 w-32\" />\n              )}\n            </CardTitle>\n            {subscription ? (\n              <CardDescription className=\"text-xs flex flex-col sm:flex-row sm:gap-4 mt-1\">\n                <span><strong>Email:</strong> {subscription.userEmail}</span>\n                <span><strong>Plan:</strong> {subscription.planId}</span>\n                <span><strong>Provider:</strong> {subscription.provider}</span>\n                <span><strong>Created:</strong> {formatDate(subscription.createdAt)}</span>\n              </CardDescription>\n            ) : (\n              <Skeleton className=\"h-4 w-64 mt-2\" />\n            )}\n          </div>\n          \n          <div className=\"flex items-center gap-2\">\n            <Button\n              size=\"sm\"\n              variant=\"outline\"\n              onClick={() => fetchTokenConsumption(pagination.page)}\n            >\n              <RefreshCw className=\"mr-2 h-3 w-3\" />\n              Refresh\n            </Button>\n          </div>\n        </CardHeader>\n        \n        {/* Date Range Selector */}\n        <CardContent className=\"pb-3 pt-0\">\n          <div className=\"flex flex-wrap items-center gap-2 text-xs\">\n            <span className=\"font-medium\">Date Range:</span>\n            <Button \n              variant={dateRange.from && dateRange.to && (dateRange.to.getTime() - dateRange.from.getTime()) === 7 * 24 * 60 * 60 * 1000 ? \"default\" : \"outline\"} \n              size=\"sm\"\n              onClick={() => handleDateRangeSelect(7)}\n              className=\"h-7 text-xs\"\n            >\n              7 Days\n            </Button>\n            <Button \n              variant={dateRange.from && dateRange.to && (dateRange.to.getTime() - dateRange.from.getTime()) === 30 * 24 * 60 * 60 * 1000 ? \"default\" : \"outline\"} \n              size=\"sm\"\n              onClick={() => handleDateRangeSelect(30)}\n              className=\"h-7 text-xs\"\n            >\n              30 Days\n            </Button>\n            <Button \n              variant={dateRange.from && dateRange.to && (dateRange.to.getTime() - dateRange.from.getTime()) === 90 * 24 * 60 * 60 * 1000 ? \"default\" : \"outline\"} \n              size=\"sm\"\n              onClick={() => handleDateRangeSelect(90)}\n              className=\"h-7 text-xs\"\n            >\n              90 Days\n            </Button>\n            \n            <div className=\"ml-auto flex items-center gap-2\">\n              <Select\n                value={pagination.pageSize.toString()}\n                onValueChange={(value) => {\n                  setPagination(prev => ({\n                    ...prev,\n                    pageSize: parseInt(value),\n                    page: 1\n                  }));\n                  fetchTokenConsumption(1);\n                }}\n              >\n                <SelectTrigger className=\"h-7 w-[70px] text-xs\">\n                  <SelectValue placeholder=\"10\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"10\">10</SelectItem>\n                  <SelectItem value=\"25\">25</SelectItem>\n                  <SelectItem value=\"50\">50</SelectItem>\n                  <SelectItem value=\"100\">100</SelectItem>\n                </SelectContent>\n              </Select>\n              <span className=\"text-xs text-muted-foreground\">per page</span>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n      \n      {/* Financial Summary */}\n      {summary && (\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2\">\n          <FinancialSummaryCard\n            title=\"Total Requests\"\n            value={formatNumber(summary.requestCount)}\n          />\n          <FinancialSummaryCard\n            title=\"Total Tokens\"\n            value={formatNumber(summary.totalTokensSum)}\n            subValue={`Input: ${formatNumber(summary.totalPromptTokens)} | Output: ${formatNumber(summary.totalCompletionTokens)}`}\n          />\n          <FinancialSummaryCard\n            title=\"Credits Used\"\n            value={formatNumber(summary.totalCreditsConsumed)}\n          />\n          <FinancialSummaryCard\n            title=\"Credits Saved\"\n            value={formatNumber(summary.totalDiscountedCredits)}\n            subValue={summary.totalDiscountedCredits > 0 ? \n              `${Math.round((summary.totalDiscountedCredits / (summary.totalCreditsConsumed + summary.totalDiscountedCredits)) * 100)}% savings ratio` : undefined}\n            subValueColor=\"text-red-500 text-xs\"\n            isNegative={true}\n          />\n          <FinancialSummaryCard\n            title=\"Cache Savings\"\n            value={formatCost(summary.totalCachingDiscount)}\n            isNegative={true}\n          />\n          <FinancialSummaryCard\n            title=\"Total Cost\"\n            value={formatCost(summary.totalCostSum)}\n          />\n        </div>\n      )}\n      \n      {/* Main Content Tabs */}\n      <Card>\n        <CardContent className=\"p-0\">\n          <Tabs defaultValue=\"history\" className=\"w-full\">\n            <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center px-4 pt-4 pb-2 border-b\">\n              <TabsList className=\"mb-2 sm:mb-0\">\n                <TabsTrigger value=\"history\">Usage History</TabsTrigger>\n                <TabsTrigger value=\"caching\">Caching Analysis</TabsTrigger>\n              </TabsList>\n              \n              <div className=\"flex items-center gap-2\">\n                <Select\n                  value={sortField}\n                  onValueChange={(value) => {\n                    setSortField(value);\n                    setSortDirection('desc');\n                    fetchTokenConsumption(1);\n                  }}\n                >\n                  <SelectTrigger className=\"h-8 w-[110px] text-xs\">\n                    <SelectValue placeholder=\"Sort by\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"createdAt\">Date</SelectItem>\n                    <SelectItem value=\"totalTokens\">Tokens</SelectItem>\n                    <SelectItem value=\"totalCost\">Cost</SelectItem>\n                    <SelectItem value=\"cacheDiscountPercent\">Cache %</SelectItem>\n                  </SelectContent>\n                </Select>\n                \n                <Select\n                  value={sortDirection}\n                  onValueChange={(value) => {\n                    setSortDirection(value as 'asc' | 'desc');\n                    fetchTokenConsumption(1);\n                  }}\n                >\n                  <SelectTrigger className=\"h-8 w-[80px] text-xs\">\n                    <SelectValue placeholder=\"Order\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"asc\">Ascending</SelectItem>\n                    <SelectItem value=\"desc\">Descending</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n            \n            <TabsContent value=\"history\" className=\"p-0 sm:p-4\">\n              {loading ? (\n                <div className=\"p-4\">\n                  <Skeleton className=\"h-64 w-full\" />\n                </div>\n              ) : (\n                <>\n                  <TokenConsumptionTable \n                    data={tokenConsumption} \n                    onSort={handleSort}\n                    sortField={sortField}\n                    sortDirection={sortDirection}\n                  />\n                  \n                  {/* Pagination */}\n                  {pagination.pageCount > 1 && (\n                    <div className=\"flex items-center justify-between p-4 text-xs\">\n                      <div className=\"text-muted-foreground\">\n                        Showing {((pagination.page - 1) * pagination.pageSize) + 1} to {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} of {pagination.totalCount} records\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => goToPage(1)}\n                          disabled={pagination.page === 1}\n                          className=\"h-7 w-7 p-0\"\n                        >\n                          <span className=\"sr-only\">First page</span>\n                          <ChevronLeft className=\"h-4 w-4\" />\n                          <ChevronLeft className=\"h-4 w-4 -ml-2\" />\n                        </Button>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => goToPage(pagination.page - 1)}\n                          disabled={pagination.page === 1}\n                          className=\"h-7 w-7 p-0\"\n                        >\n                          <span className=\"sr-only\">Previous page</span>\n                          <ChevronLeft className=\"h-4 w-4\" />\n                        </Button>\n                        <span className=\"text-xs\">\n                          Page {pagination.page} of {pagination.pageCount}\n                        </span>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => goToPage(pagination.page + 1)}\n                          disabled={pagination.page === pagination.pageCount}\n                          className=\"h-7 w-7 p-0\"\n                        >\n                          <span className=\"sr-only\">Next page</span>\n                          <ChevronRight className=\"h-4 w-4\" />\n                        </Button>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => goToPage(pagination.pageCount)}\n                          disabled={pagination.page === pagination.pageCount}\n                          className=\"h-7 w-7 p-0\"\n                        >\n                          <span className=\"sr-only\">Last page</span>\n                          <ChevronRight className=\"h-4 w-4\" />\n                          <ChevronRight className=\"h-4 w-4 -ml-2\" />\n                        </Button>\n                      </div>\n                    </div>\n                  )}\n                </>\n              )}\n            </TabsContent>\n            \n            <TabsContent value=\"caching\" className=\"p-4\">\n              <CachingBreakdown \n                data={cachingBreakdown} \n                loading={loading} \n              />\n            </TabsContent>\n          </Tabs>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}", "startLine": 95, "endLine": 506, "type": "component", "symbols": ["SubscriptionDetailPage"], "score": 0.7, "context": "Admin subscription detail page showing subscription info and usage, relevant for understanding and fixing subscription renewals and payment issues.", "includesImports": false}], "additionalFiles": []}}