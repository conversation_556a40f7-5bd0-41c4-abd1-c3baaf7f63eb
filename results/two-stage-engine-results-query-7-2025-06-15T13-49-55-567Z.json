{"timestamp": "2025-06-15T13:49:55.567Z", "query": "Show me how the file system and project management works, including file operations and state management", "executionTime": 9830, "snippetsCount": 4, "additionalFilesCount": 0, "totalLines": 182, "snippets": [{"filePath": "src/lib/services/file-storage.ts", "type": "unknown", "context": "This object handles file operations related to saving, retrieving, and clearing files in localStorage, which is core to file system management in the project.", "score": 1, "lines": 63, "startLine": 1, "endLine": 63, "symbols": ["fileStorage"], "preview": "import { FileItem } from '@/types/file';\n\nconst STORAGE_KEY = 'magically_files';\n\ninterface StoredFileData {\n..."}, {"filePath": "src/lib/db/project-queries.ts", "type": "util", "context": "These functions manage project state and metadata in the database, including fetching projects, updating metadata, checking ownership, and listing user projects, which are essential for project management and state handling.", "score": 1, "lines": 94, "startLine": 8, "endLine": 101, "symbols": ["project database query functions"], "preview": "export async function getProjectById({id}: {id: string}): Promise<Project> {\n  try {\n    const [project] = await db.select().from(projects).where(eq(projects.id, id));\n    return project as Project;\n  } catch (error) {\n..."}, {"filePath": "src/lib/db/migrations/0003_melodic_star_brand.sql", "type": "unknown", "context": "This migration defines the FileState table which stores file state information linked to chats and messages, relevant for understanding file state management in the database schema.", "score": 0.9, "lines": 10, "startLine": 1, "endLine": 10, "symbols": ["FileState table creation"], "preview": "CREATE TABLE \"FileState\" (\n\t\"id\" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,\n\t\"chatId\" uuid NOT NULL,\n\t\"messageId\" uuid,\n\t\"files\" json NOT NULL,\n..."}, {"filePath": "src/lib/db/migrations/0009_massive_millenium_guard.sql", "type": "unknown", "context": "This migration defines the Project table and its foreign key constraints, which is critical for understanding project data structure and relationships in the database.", "score": 0.9, "lines": 15, "startLine": 12, "endLine": 26, "symbols": ["Project table creation and constraints"], "preview": "CREATE TABLE \"Project\" (\n\t\"id\" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,\n\t\"title\" text,\n\t\"chatId\" uuid,\n\t\"userId\" uuid NOT NULL,\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/services/file-storage.ts", "content": "import { FileItem } from '@/types/file';\n\nconst STORAGE_KEY = 'magically_files';\n\ninterface StoredFileData {\n  files: FileItem[];\n  timestamp: number;\n  groupId: string;\n}\n\nexport const fileStorage = {\n  saveFiles: (files: FileItem[], groupId: string) => {\n    try {\n      if (typeof window === 'undefined') return;\n      \n      const data: StoredFileData = {\n        files,\n        timestamp: Date.now(),\n        groupId\n      };\n      \n      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));\n    } catch (error) {\n      console.error('Error saving files to localStorage:', error);\n    }\n  },\n\n  getFiles: (groupId: string): FileItem[] | null => {\n    try {\n      if (typeof window === 'undefined') return null;\n      \n      const storedData = localStorage.getItem(STORAGE_KEY);\n      if (!storedData) return null;\n\n      const data: StoredFileData = JSON.parse(storedData);\n      \n      // Check if data is stale (older than 1 hour)\n      if (Date.now() - data.timestamp > 60 * 60 * 1000) {\n        localStorage.removeItem(STORAGE_KEY);\n        return null;\n      }\n\n      // Check if group ID matches\n      if (data.groupId !== groupId) {\n        return null;\n      }\n\n      return data.files;\n    } catch (error) {\n      console.error('Error reading files from localStorage:', error);\n      return null;\n    }\n  },\n\n  clearFiles: () => {\n    try {\n      if (typeof window === 'undefined') return;\n      localStorage.removeItem(STORAGE_KEY);\n    } catch (error) {\n      console.error('Error clearing files from localStorage:', error);\n    }\n  }\n};", "startLine": 1, "endLine": 63, "type": "unknown", "symbols": ["fileStorage"], "score": 1, "context": "This object handles file operations related to saving, retrieving, and clearing files in localStorage, which is core to file system management in the project.", "includesImports": false}, {"filePath": "src/lib/db/project-queries.ts", "content": "export async function getProjectById({id}: {id: string}): Promise<Project> {\n  try {\n    const [project] = await db.select().from(projects).where(eq(projects.id, id));\n    return project as Project;\n  } catch (error) {\n    console.error('Failed to get project by id from database');\n    throw error;\n  }\n\n\n}/**\n * Get a project by its ID\n */\nexport async function getSanitizedProjectById({id}: {id: string}): Promise<Project> {\n  try {\n    const [project] = await db.select().from(projects).where(eq(projects.id, id));\n      const {supabaseAnonKey, supabaseServiceKey, ...rest} = project;\n      return rest as Project;\n  } catch (error) {\n    console.error('Failed to get project by id from database');\n    throw error;\n  }\n}\n\n/**\n * Update project metadata\n */\nexport async function updateProjectMetadata(\n  id: string,\n  metadata: {\n    slug?: string;\n    appName?: string;\n    description?: string;\n    primaryColor?: string;\n    bundleIdentifier?: string;\n    packageName?: string;\n    appIdentifier?: string;\n    icon?: string;\n    splashImage?: string;\n    isMigratedv1?: boolean;\n  }\n) {\n  try {\n    const updateData = {\n      ...metadata,\n      updatedAt: new Date()\n    };\n\n    const [updatedProject] = await db\n      .update(projects)\n      .set(updateData)\n      .where(eq(projects.id, id))\n      .returning();\n    \n    return updatedProject;\n  } catch (error) {\n    console.error('Failed to update project metadata in database');\n    throw error;\n  }\n}\n\n/**\n * Check if user owns a project\n */\nexport async function isProjectOwner(projectId: string, userId: string) {\n  try {\n    const project = await getProjectById({id: projectId});\n    return project && project.userId === userId;\n  } catch (error) {\n    console.error('Failed to check project ownership');\n    throw error;\n  }\n}\n\n/**\n * Get all projects for a user\n */\nexport async function getUserProjects(userId: string) {\n  try {\n    const userProjects = await db\n      .select()\n      .from(projects)\n      .where(and(\n          eq(projects.userId, userId),\n          ne(projects.visibility, 'hidden')\n      ))\n      .orderBy(desc(projects.updatedAt));\n    \n    return userProjects;\n  } catch (error) {\n    console.error('Failed to get user projects from database');\n    throw error;\n  }\n}", "startLine": 8, "endLine": 101, "type": "util", "symbols": ["project database query functions"], "score": 1, "context": "These functions manage project state and metadata in the database, including fetching projects, updating metadata, checking ownership, and listing user projects, which are essential for project management and state handling.", "includesImports": false}, {"filePath": "src/lib/db/migrations/0003_melodic_star_brand.sql", "content": "CREATE TABLE \"FileState\" (\n\t\"id\" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,\n\t\"chatId\" uuid NOT NULL,\n\t\"messageId\" uuid,\n\t\"files\" json NOT NULL,\n\t\"createdAt\" timestamp DEFAULT now() NOT NULL\n);\n--> statement-breakpoint\nALTER TABLE \"FileState\" ADD CONSTRAINT \"FileState_chatId_Chat_id_fk\" FOREIGN KEY (\"chatId\") REFERENCES \"public\".\"Chat\"(\"id\") ON DELETE no action ON UPDATE no action;--> statement-breakpoint\nALTER TABLE \"FileState\" ADD CONSTRAINT \"FileState_messageId_Message_id_fk\" FOREIGN KEY (\"messageId\") REFERENCES \"public\".\"Message\"(\"id\") ON DELETE no action ON UPDATE no action;", "startLine": 1, "endLine": 10, "type": "unknown", "symbols": ["FileState table creation"], "score": 0.9, "context": "This migration defines the FileState table which stores file state information linked to chats and messages, relevant for understanding file state management in the database schema.", "includesImports": false}, {"filePath": "src/lib/db/migrations/0009_massive_millenium_guard.sql", "content": "CREATE TABLE \"Project\" (\n\t\"id\" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,\n\t\"title\" text,\n\t\"chatId\" uuid,\n\t\"userId\" uuid NOT NULL,\n\t\"connectionId\" uuid NOT NULL,\n\t\"visibility\" varchar DEFAULT 'private' NOT NULL,\n\t\"createdAt\" timestamp DEFAULT now() NOT NULL,\n\t\"updatedAt\" timestamp DEFAULT now() NOT NULL\n);\n--> statement-breakpoint\nALTER TABLE \"ProjectConnection\" ADD CONSTRAINT \"ProjectConnection_projectId_Project_id_fk\" FOREIGN KEY (\"projectId\") REFERENCES \"public\".\"Project\"(\"id\") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint\nALTER TABLE \"Project\" ADD CONSTRAINT \"Project_chatId_Chat_id_fk\" FOREIGN KEY (\"chatId\") REFERENCES \"public\".\"Chat\"(\"id\") ON DELETE no action ON UPDATE no action;--> statement-breakpoint\nALTER TABLE \"Project\" ADD CONSTRAINT \"Project_userId_User_id_fk\" FOREIGN KEY (\"userId\") REFERENCES \"public\".\"User\"(\"id\") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint\nALTER TABLE \"Project\" ADD CONSTRAINT \"Project_connectionId_Connection_id_fk\" FOREIGN KEY (\"connectionId\") REFERENCES \"public\".\"Connection\"(\"id\") ON DELETE cascade ON UPDATE no action;", "startLine": 12, "endLine": 26, "type": "unknown", "symbols": ["Project table creation and constraints"], "score": 0.9, "context": "This migration defines the Project table and its foreign key constraints, which is critical for understanding project data structure and relationships in the database.", "includesImports": false}], "additionalFiles": []}}