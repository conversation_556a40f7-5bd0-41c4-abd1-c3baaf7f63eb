{"timestamp": "2025-06-14T17:35:05.063Z", "query": "Find all the chat tools and how they integrate with the streaming system. Show me the tool execution flow.", "executionTime": 29221, "snippetsCount": 17, "additionalFilesCount": 0, "totalLines": 3947, "snippets": [{"filePath": "src/lib/chat/tools/multi-perspective-analysis.tool.ts", "type": "util", "context": "This snippet defines the multiPerspectiveAnalysis tool which streams multi-persona analysis results via dataStream, showing detailed tool execution flow and integration with streaming.", "score": 1, "lines": 261, "startLine": 55, "endLine": 315, "symbols": ["multiPerspectiveAnalysis"], "preview": "export const multiPerspectiveAnalysis = ({\n  dataStream,\n}: {\n  dataStream: DataStreamWriter;\n}) => {\n..."}, {"filePath": "src/lib/chat/handlers/message.handler.ts", "type": "unknown", "context": "This class handles message processing including filtering, preparing messages, managing tool calls, and integrating with streaming and tool execution flow, critical for understanding how chat tools are executed.", "score": 1, "lines": 632, "startLine": 34, "endLine": 665, "symbols": ["MessageHandler"], "preview": "export class MessageHandler {\n    private messagesWithTurns: Array<Message> = [];\n    private coreMessages: CoreMessage[] = [];\n    private userMessage: CoreMessage | null = null;\n    private filteredMessages: CoreMessage[] = [];\n..."}, {"filePath": "src/lib/services/stream-service.ts", "type": "unknown", "context": "This class manages the streaming of AI text including tool call streaming, chunk processing, tool call repair, and integration with the dataStream, essential for understanding the streaming system and tool execution flow.", "score": 1, "lines": 273, "startLine": 110, "endLine": 382, "symbols": ["StreamService"], "preview": "export class StreamService {\n    private config: StreamServiceConfig;\n    private toolCallCosts: ToolCallCost[] = [];\n\n    constructor(config: StreamServiceConfig) {\n..."}, {"filePath": "src/components/base/data-stream-handler.tsx", "type": "component", "context": "This React component handles the data stream from the chat, processes different delta types including tool calls and file operations, and updates UI state, showing the integration of chat tools with the streaming system.", "score": 1, "lines": 262, "startLine": 73, "endLine": 334, "symbols": ["DataStreamHandler"], "preview": "export function DataStreamHandler({id, operationChange, onStreamingComplete, onFileOpen, onContinuationFlagChange, onUpdateUserMessage}: {\n    id: string,\n    operationChange: (codeBlock: CodeBlock) => any;\n    onFileOpen: (fileName: string) => any;\n    onContinuationFlagChange?: (needsContinuation: boolean) => void;\n..."}, {"filePath": "src/components/base/chat.tsx", "type": "component", "context": "This React component implements the chat UI, manages chat state, handles tool calls via onToolCall, processes streaming responses, and integrates multiple chat tools with the streaming system, showing the full tool execution flow.", "score": 1, "lines": 794, "startLine": 41, "endLine": 834, "symbols": ["Cha<PERSON>"], "preview": "const Chat = observer(({\n                           id,\n                           initialMessages,\n                           isReadonly,\n                           initialPrompt,\n..."}, {"filePath": "src/lib/chat/tools/add-ai-memory.ts", "type": "util", "context": "This snippet defines the addAiMemory tool which integrates with the streaming system via dataStream and creditUsageTracker, showing how it executes and updates project memory, relevant for tool execution flow.", "score": 0.9, "lines": 72, "startLine": 12, "endLine": 83, "symbols": ["addAiMemory"], "preview": "export const addAiMemory = ({\n                                dataStream,\n                                creditUsageTracker,\n                                projectId\n                            }: {\n..."}, {"filePath": "src/lib/chat/tools/query-codebase.ts", "type": "util", "context": "This snippet defines the queryCodebase tool which queries the codebase and integrates with the streaming system, including tool call limits and context engine usage, relevant for tool execution flow.", "score": 0.9, "lines": 161, "startLine": 23, "endLine": 183, "symbols": ["queryCodebase"], "preview": "export const queryCodebase = ({\n                                  projectId,\n                                  files,\n                                  fileManager,\n                                  messageId = '',\n..."}, {"filePath": "src/lib/chat/tools/query-supabase-context.ts", "type": "util", "context": "This snippet defines the querySupabaseContext tool which queries Supabase resources or executes SQL, integrates with streaming and tool call limits, showing tool execution flow.", "score": 0.9, "lines": 203, "startLine": 23, "endLine": 225, "symbols": ["querySupabaseContext"], "preview": "export const querySupabaseContext = ({\n    projectId,\n    messageId = '',\n}: {\n    projectId: string, \n..."}, {"filePath": "src/lib/chat/tools/manage-supabase-auth.ts", "type": "util", "context": "This snippet defines the manageSupabaseAuth tool which manages Supabase auth configuration, integrates with streaming and credit usage tracking, relevant for tool execution flow.", "score": 0.9, "lines": 78, "startLine": 13, "endLine": 90, "symbols": ["manageSupabaseAuth"], "preview": "export const manageSupabaseAuth = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n..."}, {"filePath": "src/lib/chat/tools/get-client-logs.ts", "type": "util", "context": "This snippet defines the getClientLogs tool which fetches client-side logs, integrates with streaming and tool call limits, relevant for tool execution flow.", "score": 0.9, "lines": 105, "startLine": 13, "endLine": 117, "symbols": ["getClientLogs"], "preview": "export const getClientLogs = ({\n  dataStream,\n  creditUsageTracker,\n  logs,\n  messageId\n..."}, {"filePath": "src/lib/chat/tools/get-file-contents.ts", "type": "util", "context": "This snippet defines the getFileContents tool which retrieves file contents or directory listings, integrates with streaming and credit usage tracking, relevant for tool execution flow.", "score": 0.9, "lines": 236, "startLine": 7, "endLine": 242, "symbols": ["getFileContents"], "preview": "export const getFileContents = ({\n                                    fileManager,\n                                    dataStream,\n                                    creditUsageTracker\n                                }: {\n..."}, {"filePath": "src/lib/chat/tools/get-supabase-logs.ts", "type": "util", "context": "This snippet defines the getSupabaseLogs tool which fetches Supabase logs, integrates with streaming and credit usage tracking, relevant for tool execution flow.", "score": 0.9, "lines": 111, "startLine": 14, "endLine": 124, "symbols": ["getSupabaseLogs"], "preview": "export const getSupabaseLogs = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n..."}, {"filePath": "src/lib/chat/tools/edit-file.tool.ts", "type": "util", "context": "This snippet defines the editFileTool which edits files and integrates with streaming, credit usage tracking, and Supabase SQL execution, relevant for tool execution flow.", "score": 0.9, "lines": 239, "startLine": 25, "endLine": 263, "symbols": ["editFileTool"], "preview": "export const editFileTool = ({\n                                 fileManager,\n                                 contentManager,\n                                 processImagePlaceholders,\n                                 processVideoPlaceholders,\n..."}, {"filePath": "src/components/base/message.tsx", "type": "component", "context": "This React component renders chat messages including tool call results for various chat tools, showing how tool execution results are integrated into the chat UI and streaming system.", "score": 0.9, "lines": 422, "startLine": 50, "endLine": 471, "symbols": ["PurePreviewMessage"], "preview": "const PurePreviewMessage = ({\n                                projectId,\n                                chatId,\n                                message,\n                                vote,\n..."}, {"filePath": "src/lib/chat/tools/client-testing.tool.ts", "type": "util", "context": "This snippet defines the clientTestingTool chat tool which integrates with the streaming system via the dataStream parameter and uses a human-in-the-loop pattern without an execute function, relevant for understanding tool execution flow.", "score": 0.8, "lines": 18, "startLine": 10, "endLine": 27, "symbols": ["clientTestingTool"], "preview": "export const clientTestingTool = ({\n                                      dataStream,\n                                      creditUsageTracker\n                                  }: {\n    dataStream: DataStreamWriter;\n..."}, {"filePath": "src/lib/chat/tools/get-supabase-instructions.ts", "type": "util", "context": "This snippet defines the getSupabaseInstructions tool which fetches Supabase project instructions, integrates with streaming and credit usage tracking, relevant for tool execution flow.", "score": 0.8, "lines": 38, "startLine": 12, "endLine": 49, "symbols": ["getSupabaseInstructions"], "preview": "export const getSupabaseInstructions = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n..."}, {"filePath": "src/lib/chat/tools/create-file.tool.ts", "type": "util", "context": "This snippet defines the createFileTool which creates new files and integrates with streaming and placeholder processing, relevant for tool execution flow.", "score": 0.8, "lines": 42, "startLine": 5, "endLine": 46, "symbols": ["createFileTool"], "preview": "export const createFileTool = ({\n                            fileManager,\n                            processImagePlaceholders,\n                            processVideoPlaceholders,\n                            dataStream\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/chat/tools/multi-perspective-analysis.tool.ts", "content": "export const multiPerspectiveAnalysis = ({\n  dataStream,\n}: {\n  dataStream: DataStreamWriter;\n}) => {\n  return tool({\n    description: 'Analyze a topic from multiple perspectives using different AI personas to reach a more comprehensive and balanced understanding',\n    parameters: multiPerspectiveAnalysisSchema,\n    execute: async ({ topic, context = '', focusAreas = [], requiredPersonas = [], analysisDepth = 'standard', discussionRounds = 3 }) => {\n      try {\n        // Stream the initial setup information\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `# Multi-Perspective Analysis: ${topic}\\n\\nInitiating analysis with multiple perspectives...\\n\\n`\n        });\n\n        // Select which personas to use\n        const selectedPersonaNames = requiredPersonas.length > 0 ? requiredPersonas : personaList;\n        \n        if (selectedPersonaNames.length === 0) {\n          throw new Error('No valid personas selected for analysis');\n        }\n\n        // Determine token allocation based on analysis depth\n        let maxTokensPerPersona;\n        switch (analysisDepth) {\n          case 'brief':\n            maxTokensPerPersona = 500;\n            break;\n          case 'comprehensive':\n            maxTokensPerPersona = 1500;\n            break;\n          case 'standard':\n          default:\n            maxTokensPerPersona = 1000;\n        }\n\n        // Track all contributions to the discussion\n        type Contribution = {\n          persona: PersonaName;\n          content: string;\n          round: number;\n          replyTo?: PersonaName[];\n        };\n\n        const contributions: Contribution[] = [];\n        \n        // Initialize the discussion with the topic introduction\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `## Multi-Perspective Discussion\\n\\n`\n        });\n\n        // PHASE 1: Initial perspectives from each persona\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `### Round 1: Initial Perspectives\\n\\n`\n        });\n\n        // Generate initial perspectives from each persona\n        for (const personaName of selectedPersonaNames) {\n          dataStream.writeData({\n            type: 'multi-perspective-update',\n            content: `#### ${personaName}:\\n\\n`\n          });\n\n          // Create the prompt for this persona\n          const personaPrompt = `\nYou are the \"${personaName}\" persona with the following characteristics:\n${personaDescriptions[personaName]}\n\nAnalyze the following topic from your unique perspective:\nTopic: ${topic}\n${context ? `Context: ${context}` : ''}\n${focusAreas.length > 0 ? `Focus Areas: ${focusAreas.join(', ')}` : ''}\n\nProvide your analysis in a clear, concise manner that reflects your perspective. Be insightful but concise.\n`;\n\n          // Generate the persona's perspective\n          const result = streamText({\n            model: customModel(\"anthropic/claude-sonnet-4\"),\n            temperature: 0.7,\n            messages: [\n              {\n                role: 'system',\n                content: personaPrompt\n              }\n            ]\n          });\n          \n          let perspective = '';\n          for await (const chunk of result.textStream) {\n            perspective += chunk;\n          }\n\n          // Add this perspective to the contributions\n          contributions.push({\n            persona: personaName,\n            content: perspective,\n            round: 1\n          });\n\n          dataStream.writeData({\n            type: 'multi-perspective-update',\n            content: `${perspective}\\n\\n`\n          });\n        }\n\n        // PHASE 2: Interactive discussion rounds\n        // Each round, personas respond to previous contributions\n        for (let round = 2; round <= discussionRounds; round++) {\n          dataStream.writeData({\n            type: 'multi-perspective-update',\n            content: `### Round ${round}: Developing the Discussion\\n\\n`\n          });\n\n          // Get all contributions from the previous round\n          const previousRoundContributions = contributions.filter(c => c.round === round - 1);\n          \n          // Each persona responds to the previous round\n          for (const personaName of selectedPersonaNames) {\n            // Get all previous contributions except this persona's own contribution\n            const relevantContributions = previousRoundContributions\n              .filter(c => c.persona !== personaName);\n              \n            if (relevantContributions.length === 0) continue;\n            \n            dataStream.writeData({\n              type: 'multi-perspective-update',\n              content: `#### ${personaName}:\\n\\n`\n            });\n\n            // Format the previous contributions for the prompt\n            const previousContributionsText = relevantContributions\n              .map(c => `${c.persona}: ${c.content}`)\n              .join('\\n\\n');\n\n            // Create a prompt that encourages building on previous ideas\n            const dialoguePrompt = `\nYou are the \"${personaName}\" persona with the following characteristics:\n${personaDescriptions[personaName]}\n\nYou're participating in round ${round} of a discussion on \"${topic}\".\n\nHere are the most recent contributions from other participants:\n\n${previousContributionsText}\n\nBased on your unique perspective and the discussion so far:\n1. Respond to at least 2 specific points made by others\n2. Build upon or challenge ideas in a constructive way\n3. Introduce a new insight or question that advances the discussion\n4. Maintain your distinct perspective while seeking common ground\n\nBe concise but insightful. Your goal is to deepen the collective understanding.\n`;\n\n            // Generate the persona's response\n            const result = streamText({\n              model: customModel(\"anthropic/claude-sonnet-4\"),\n              temperature: 0.7,\n              messages: [\n                {\n                  role: 'system',\n                  content: dialoguePrompt\n                }\n              ]\n            });\n            \n            let response = '';\n            for await (const chunk of result.textStream) {\n              response += chunk;\n            }\n\n            // Add this response to the contributions\n            contributions.push({\n              persona: personaName,\n              content: response,\n              round: round,\n              replyTo: relevantContributions.map(c => c.persona)\n            });\n\n            dataStream.writeData({\n              type: 'multi-perspective-update',\n              content: `${response}\\n\\n`\n            });\n          }\n        }\n\n        // PHASE 3: Generate synthesis and consensus\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: `## Synthesis and Consensus\\n\\n`\n        });\n\n        // Compile all contributions for synthesis\n        const allContent = contributions\n          .map(c => `Round ${c.round} - ${c.persona}: ${c.content}`)\n          .join('\\n\\n');\n\n        // Create the synthesis prompt\n        const synthesisPrompt = `\nYou are a neutral synthesizer tasked with finding common ground and key insights from multiple perspectives on the topic: \"${topic}\"\n\nHere are all the perspectives and dialogue exchanges:\n\n${allContent}\n\nPlease provide:\n1. A summary of key points of agreement across perspectives\n2. Important points of disagreement or tension\n3. Unique insights contributed by each perspective\n4. A balanced synthesis that incorporates the strongest elements from each viewpoint\n5. Remaining questions or areas for further exploration\n\nYour synthesis should be fair to all perspectives while highlighting the most valuable insights from each.\n`;\n\n        // Generate the synthesis\n        const synthesisResult = streamText({\n          model: customModel(\"anthropic/claude-sonnet-4\"),\n          temperature: 0.7,\n          messages: [\n            {\n              role: 'system',\n              content: synthesisPrompt\n            }\n          ]\n        });\n        \n        let synthesis = '';\n        for await (const chunk of synthesisResult.textStream) {\n          synthesis += chunk;\n        }\n\n        // Synthesis is built in the streaming loop above\n\n        dataStream.writeData({\n          type: 'multi-perspective-update',\n          content: synthesis\n        });\n\n        // Return the complete analysis results\n        return {\n          topic,\n          contributions,\n          synthesis,\n          result: 'success'\n        };\n      } catch (error) {\n        console.error('Error in multi-perspective analysis:', error);\n        dataStream.writeData({\n          type: 'error',\n          error: error instanceof Error ? error.message : 'Failed to complete multi-perspective analysis'\n        });\n        throw error;\n      }\n    }\n  });\n};", "startLine": 55, "endLine": 315, "type": "util", "symbols": ["multiPerspectiveAnalysis"], "score": 1, "context": "This snippet defines the multiPerspectiveAnalysis tool which streams multi-persona analysis results via dataStream, showing detailed tool execution flow and integration with streaming.", "includesImports": false}, {"filePath": "src/lib/chat/handlers/message.handler.ts", "content": "export class MessageHandler {\n    private messagesWithTurns: Array<Message> = [];\n    private coreMessages: CoreMessage[] = [];\n    private userMessage: CoreMessage | null = null;\n    private filteredMessages: CoreMessage[] = [];\n    private systemPrompts: CoreSystemMessage[] = []\n    private fileMessage: CoreMessage | null = null;\n    private componentContexts: {componentName: string;\n    element: string;\n    sourceFile: string;\n    lineNumber: number;\n    imageUrl?: string;}[] = []\n    private extractor: Extractor = new Extractor();\n\n    /**\n     * Initialize the handler with messages\n     * This sets up the internal state for processing\n     */\n    public async initialize(messages: Array<Message>, minMessages: number = 5, options: {\n        projectId?: string,\n        backendEnabled?: boolean,\n        componentContexts?: ComponentContext[],\n        isFirstUserMessage?: boolean,\n        agentModeEnabled?: boolean,\n        userId: string,\n        isDiscussion?: boolean,\n        discussionType?: 'error-fix' | 'code-review' | 'general-discussion'}): Promise<void> {\n        let system;\n        const matchedFlagPayload = await posthogClient.getFeatureFlagPayload('design-preview-feature', options.userId)\n\n        // Handle discussion mode first (highest priority)\n        if (options?.isDiscussion) {\n            switch (options.discussionType) {\n                case 'error-fix':\n                    system = DISCUSS_MODE_ERROR_FIX_PROMPT;\n                    break;\n                case 'code-review':\n                    system = DISCUSS_MODE_CODE_REVIEW_PROMPT;\n                    break;\n                default:\n                    system = DISCUSS_MODE_PROMPT;\n                    break;\n            }\n        } else if (options?.isFirstUserMessage) {\n            system = STREAMLINED_V1_IMPLEMENTATION_PROMPT;\n        } else {\n            if (options?.agentModeEnabled) {\n                system = STREAMLINED_AGENT_PROMPT\n            } else {\n                system = systemPrompt;\n            }\n        }\n\n            // .replace(/{{BACKEND_PROMPT}}/g, options.backendEnabled ? backendPrompt : \"There is no backend configured. NO MATTER WHAT, don't write backend code even if the user asks for it.\");\n\n        if(options.backendEnabled) {\n            system = system.replace(/{{BACKEND_URL}}/g, `https://${process.env.VERCEL_URL || process.env.BACKEND_BASE_URL}/api/dynamic-backend/${options.projectId}/latest`);\n        }\n\n        // console.log('Replacing backend prompt, enabled:', options.backendEnabled)\n\n        this.systemPrompts = [\n            {\n                role: 'system',\n                content: system\n            },\n    //         {\n    //             role: 'system',\n    //             content: ` <extended_system_prompt>\n    //     <title>From the system to you:</title>\n    //     <extended_system_prompt_reminder>Remember, you are building a mobile app and the user needs to get a wow factor. Use spacing as a design element and keep it minimal and clean.</extended_system_prompt_reminder>\n    //     <reminders>\n    //         <reminder id=\"1\">Read the system prompt very carefully.</reminder>\n    //         <reminder id=\"2\">[ULTRA IMPORTANT]: Always follow the MO_FILE response formats. Use MO_DIFF whenever you can to save costs. </reminder>\n    //         <reminder id=\"3\">[ULTRA IMPORTANT]: NEVER change the existing design/layout/functionality of a screen when making edits unless explicitly specified.</reminder>\n    //         <reminder id=\"4\">[ULTRA IMPORTANT]: Return complete implementations of every file, do not leave it for the user to implement. The user does not have access to the code.</reminder>\n    //         <reminder id=\"5\">ALWAYS adhere to the \"NEVER ASSUME THAT THE USER WILL MAKE CHANGES TO A FILE\" section no matter what</reminder>\n    //         <reminder id=\"6\">ALWAYS provide complete implementations and never partial updates to files</reminder>\n    //\n    // </reminders>\n    // </extended_system_prompt>`\n    //         }\n        ]\n        this.messagesWithTurns = this.getMessagesWithCompleteTurns(messages, minMessages);\n        this.coreMessages = convertToCoreMessages(this.messagesWithTurns);\n        this.userMessage = this.getMostRecentUserMessage(this.coreMessages);\n        this.filteredMessages = this.filterCoreMessages(this.coreMessages);\n        if(options.componentContexts) {\n            this.componentContexts = options.componentContexts;\n        }\n        this.appendMessageCountSystemMessage();\n    }\n\n\n    appendToSystemPrompt(content: string) {\n        this.systemPrompts.push({\n            role: 'system',\n            content: content\n        })\n    }\n\n    private appendMessageCountSystemMessage() {\n        // Count how many user messages we've had\n        const userMessageCount = this.messagesWithTurns.filter(m => m.role === 'assistant').length;\n\n        // if (userMessageCount <= 3) {\n        //     this.systemPrompts.push({\n        //         role: \"system\",\n        //         content: `${onboardingPrompt}\n        //     <CurrentPhase>Message: ${userMessageCount}</CurrentPhase>`\n        //     })\n        // }\n    }\n\n    /**\n     * Replace MO_FILE and MO_DIFF tags with a summary message\n     * @param text The text to process\n     * @returns The text with tags replaced\n     */\n    public replaceMoFileTags(text: string) {\n        // Create proper RegExp objects with correct character classes to match any character including newlines\n        const mofileregex = new RegExp(`<MO_FILE\\\\s+path=\"([^\"]+)\"[^>]*?>([\\\\s\\\\S]*?)</MO_FILE>`, 'g');\n        const modiffregex = new RegExp(`<MO_DIFF\\\\s+path=\"([^\"]+)\"[^>]*?>([\\\\s\\\\S]*?)</MO_DIFF>`, 'g');\n\n        // For MO_FILE tags, extract the path and replace with a summary\n        const fileReplacementContent = (match: string, path: string, content: string) => {\n            // Create a summary of what was changed\n            return `[Created/Modified file: ${path}]`;\n        };\n\n        // For MO_DIFF tags, extract the path and replace with a summary\n        const diffReplacementContent = (match: string, path: string, content: string) => {\n            // Create a summary of what was changed\n            return `[Modified file: ${path}]`;\n        };\n\n        // Replace both MO_FILE and MO_DIFF tags with summaries\n        let replaced = text.replace(mofileregex, fileReplacementContent);\n        return replaced.replace(modiffregex, diffReplacementContent);\n    }\n\n    private removeCode(message: CoreMessage) {\n        if (typeof message.content === \"string\") {\n            message.content = this.replaceMoFileTags(message.content);\n        } else {\n            message.content = message.content.map(cont => {\n                if (cont.type === \"text\") {\n                    cont.text = this.replaceMoFileTags(cont.text);\n                }\n                return cont;\n            }) as any\n        }\n        return message;\n    }\n    \n    /**\n     * Truncate tool message content to save context window space\n     * @param message The tool message to truncate\n     * @param maxLength Maximum allowed content length (default: 3000)\n     * @returns The message with truncated content\n     */\n    private truncateToolContent(message: CoreMessage, maxLength: number = 3000): CoreMessage {\n        if (!message) return message;\n        \n        if (typeof message.content === \"string\") {\n            if (message.content.length > maxLength) {\n                message.content = message.content.slice(0, maxLength) + \n                    '\\n// Tool response truncated to save context window size. Focus on the available information.';\n            }\n        } else if (Array.isArray(message.content)) {\n            message.content = message.content.map(cont => {\n                // Handle text type content\n                if (cont.type === \"text\" && cont.text.length > maxLength) {\n                    return {\n                        ...cont,\n                        text: cont.text.slice(0, maxLength) + \n                            '\\n// Tool response truncated to save context window size. Focus on the available information.'\n                    };\n                }\n                \n                // Handle tool-result type content\n                if (cont.type === \"tool-result\" && cont.result && typeof cont.result === 'string' && cont.result.length > maxLength) {\n                    return {\n                        ...cont,\n                        result: cont.result.slice(0, maxLength) + \n                            '\\n// Tool response truncated to save context window size. Focus on the available information.'\n                    };\n                }\n                \n                return cont;\n            }) as any;\n        }\n        \n        return message;\n    }\n\n\n    public getMessagesForRequest({agentModeEnabled, applyCaching}: { agentModeEnabled: boolean, applyCaching?: boolean }) {\n        let messages: CoreMessage[] = [\n            ...this.systemPrompts\n        ]\n\n        if (this.fileMessage) {\n            messages.push(this.fileMessage)\n        }\n\n        const latestUserMessageIndex = this.getMostRecentUserMessageIndex(this.filteredMessages);\n        const clonedFilteredMessages = [...this.filteredMessages.toSpliced(latestUserMessageIndex, 1)];\n        if (latestUserMessageIndex !== -1 && this.userMessage) {\n            clonedFilteredMessages[latestUserMessageIndex] = this.userMessage;\n        }\n\n        messages = messages.concat(clonedFilteredMessages)\n        if(applyCaching) {\n            messages = this.applyCaching(messages);\n        }\n        messages = messages.filter(m => !!m);\n\n        if (agentModeEnabled) {\n            // In agent mode, remove MO tags from all messages\n            messages = messages.map(message => {\n                if(message.role === \"assistant\") {\n                    message = this.removeCode(message);\n                }\n                if(message.role === \"tool\") {\n                    message = this.truncateToolContent(message);\n                }\n                return message;\n            });\n        } else {\n            // Find all assistant messages with MO_FILE tags\n            const assistantMessagesWithMOFILE = messages.map((message, index) => {\n                const content: string = (message.content?.[0] as TextPart)?.text || message.content as string;\n                return {\n                    index,\n                    hasMOFILE: message.role === \"assistant\" && content.includes(\"MO_FILE\")\n                };\n            }).filter(item => item.hasMOFILE);\n\n            // Keep the last 2 messages with MO_FILE tags intact\n            const keepLastN = 2;\n            const messagesToKeep = assistantMessagesWithMOFILE.slice(-keepLastN).map(item => item.index);\n\n            messages = messages.map((message, index) => {\n                return messagesToKeep.includes(index) ? message : this.removeCode(message);\n            });\n        }\n\n        return messages\n    }\n\n    private applyCaching(messages: CoreMessage[]) {\n        const lastSystemMessageIndex = messages.findIndex(m => m.role === 'system');\n        if (lastSystemMessageIndex !== -1) {\n            console.log('Adding cache to last system message')\n            messages[lastSystemMessageIndex] = this.appendCacheTag(messages[lastSystemMessageIndex]);\n        }\n\n        // const fileMessageIndex = messages.findIndex((message: any) => {\n        //     if (message.role === \"system\") {\n        //         return Array.isArray(message.content) ?\n        //             message.content.some(\n        //                 (text: any) => {\n        //                     return text.text?.includes(\"<FILE_MESSAGE>\")\n        //                 }\n        //             ) : message.content.includes(\"<FILE_MESSAGE>\")\n        //     }\n        //     return false;\n        // });\n        // if (fileMessageIndex !== -1) {\n        //     console.log('Adding cache to file message')\n        //     messages[fileMessageIndex] = this.appendCacheTag(messages[fileMessageIndex]);\n        // }\n\n        // Find first user message\n        const firstAssistantResponseIndex = messages.findIndex((message: any) => message?.role === \"assistant\");\n        if (firstAssistantResponseIndex !== -1) {\n            console.log('Adding cache first assistant response')\n            messages[firstAssistantResponseIndex] = this.appendCacheTag(messages[firstAssistantResponseIndex]);\n        }\n\n        const lastAssistantResponseIndex = messages.findLastIndex((message: any) => message?.role === \"assistant\");\n        if (lastAssistantResponseIndex !== -1) {\n            console.log('Adding cache to last assistant response')\n            messages[lastAssistantResponseIndex] = this.appendCacheTag(messages[lastAssistantResponseIndex]);\n        }\n        return messages;\n    }\n\n    private appendCacheTag(message: CoreMessage) {\n        if (typeof message.content === \"string\") {\n            message =  {\n                ...message,\n                providerOptions: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n                experimental_providerMetadata: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n                // @ts-ignore\n                providerMetadata: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n            }\n        } else {\n            message.content[message.content.length - 1] = {\n                ...message.content[message.content.length - 1],\n                // @ts-ignore\n                providerOptions: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n                experimental_providerMetadata: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n                // @ts-ignore\n                providerMetadata: {\n                    openrouter: {\n                        // cache_control also works\n                        // cache_control: { type: 'ephemeral' }\n                        cacheControl: { type: 'ephemeral' },\n                    },\n                },\n            }\n        }\n\n        return message;\n    }\n\n    /**\n     * Get the current user message\n     */\n    public getCurrentUserMessage(): CoreMessage | null {\n        return this.userMessage;\n    }\n\n    public getCurrentUserMessageForUI() {\n        const messages =  convertToUIMessages([this.userMessage || {} as any])\n        return messages[0];\n    }\n\n    /**\n     * Set the current user message\n     * Useful when the message has been enhanced with additional context\n     */\n    public setCurrentUserMessage(message: CoreMessage): void {\n        this.userMessage = message;\n    }\n\n    /**\n     * Get messages with complete conversation turns\n     * Ensures we have complete context by including the most recent messages\n     * plus any older messages that form complete conversation turns\n     */\n    public getMessagesWithCompleteTurns(messages: Array<Message>, minMessages: number = 10): Array<Message> {\n        if (messages.length <= minMessages) {\n            return messages;\n        }\n\n        const recentMessages = messages.slice(-minMessages);\n        const remainingMessages = messages.slice(0, -minMessages);\n        const oldestUserMessageIndex = remainingMessages.map(m => m.role).lastIndexOf('user');\n\n        if (oldestUserMessageIndex === -1) {\n            return recentMessages;\n        }\n\n        const additionalMessages = remainingMessages.slice(oldestUserMessageIndex);\n        return [...additionalMessages, ...recentMessages];\n    }\n\n    /**\n     * Get the most recent user message from a list of messages\n     */\n    public getMostRecentUserMessage(messages: CoreMessage[]): CoreMessage | null {\n        for (let i = messages.length - 1; i >= 0; i--) {\n            if (messages[i].role === 'user') {\n                return messages[i];\n            }\n        }\n        return null;\n    }\n\n    public getMostRecentUserMessageIndex(messages: CoreMessage[]): number {\n        for (let i = messages.length - 1; i >= 0; i--) {\n            if (messages[i].role === 'user') {\n                return i;\n            }\n        }\n        return -1;\n    }\n\n    /**\n     * Filter core messages to optimize context window usage\n     * - Keeps all user messages\n     * - Keeps assistant messages without tool calls\n     * - For assistant messages with tool calls, keeps the last 4 and removes tool-call content\n     * - Removes all tool messages\n     */\n    public filterCoreMessages(messages: CoreMessage[]): CoreMessage[] {\n        // Find last 4 assistant messages that have tool-call content (increased from 2)\n        const findSecondLastUserMessageIndexArray: number[] = messages\n            .map((message, index) => message.role === \"user\" ? index : undefined)\n            .filter(m => typeof m !== \"undefined\");\n        const secondLastUserMessageIndex = findSecondLastUserMessageIndexArray.slice(-3)[0];\n        const assistantMessagesWithTools = messages\n            .filter((msg, index) => msg.role === 'assistant' &&\n                index > secondLastUserMessageIndex)\n             // Increased from 2 to 4 to provide more context\n\n        const toolCallIDs = assistantMessagesWithTools.reduce((acc, message) => {\n            if (Array.isArray(message.content)) {\n                const ids = message.content.map(cont => {\n                    return cont.type === \"tool-call\" ? cont.toolCallId : null;\n                }).filter(id => !!id);\n                acc = acc.concat(ids);\n            }\n            return acc;\n        }, [] as string[])\n\n\n        return messages.filter(msg => {\n            // Keep user messages\n            if (msg.role === 'user') return true;\n\n            // For assistant messages\n            if (msg.role === 'assistant') {\n                // If it has tool calls, only keep last 2 and remove tool-call content\n                if (Array.isArray(msg.content) && msg.content.some(c => c.type === 'tool-call')) {\n                    if (assistantMessagesWithTools.includes(msg)) {\n                        // Keep only text content\n                        // msg.content = msg.content.filter(c => c.type === 'text');\n                        return true;\n                    }\n                    return false;\n                }\n                return true; // Keep assistant messages without tool calls\n            }\n\n            // Remove tool messages not in the whitelisted ids\n            if (msg.role === 'tool') {\n                const allowed = Array.isArray(msg.content) && msg.content.some(cont => toolCallIDs.includes(cont.toolCallId));\n                return allowed;\n            }\n\n            return false;\n        });\n    }\n\n    /**\n     * Prepare user message for LLM processing\n     * - Handles both array and string content formats\n     * - Ensures image parts have correct type information\n     */\n    public prepareUserMessage(userMessage: CoreMessage): CoreMessage {\n        if (!Array.isArray(userMessage.content)) {\n            return userMessage;\n        }\n\n        // Process array content\n        const processedContent = userMessage.content.map(content => {\n            if (content.type === \"image\") {\n                return {\n                    type: 'image',\n                    mimeType: \"image/png\",\n                    image: content.image\n                } as ImagePart;\n            }\n            return content;\n        }) as UserContent;\n\n        return {\n            ...userMessage,\n            content: processedContent\n        } as CoreMessage;\n    }\n\n    /**\n     * Extracts import statements from message content\n     * Useful for analyzing code snippets and understanding dependencies\n     * @param content Array of messages to analyze\n     * @returns Array of extracted import statements\n     */\n    public extractImportsFromContent(content: string): string[] {\n        const importStatements: string[] = [];\n        const importRegex = /import\\s+(?:{[^}]*}|\\*\\s+as\\s+[^;]+|[^;{]*)\\s+from\\s+['\"][^'\"]+['\"];?|import\\s+['\"][^'\"]+['\"];?/g;\n\n        const matches = content.match(importRegex)\n        if (matches) {\n            importStatements.push(...matches);\n        }\n\n        // Remove duplicates and return\n        return [...new Set(importStatements)];\n    }\n\n    /**\n     * Append additional context to user message\n     * This can be used to add system instructions or other context\n     */\n    public appendToUserMessage(userMessage: CoreMessage, additionalContext: string): CoreMessage {\n        if (Array.isArray(userMessage.content)) {\n            return {\n                ...userMessage,\n                content: userMessage.content.map(content => {\n                    if (content.type === \"text\") {\n                        return {\n                            ...content,\n                            text: content.text + additionalContext\n                        } as TextPart;\n                    }\n                    return content;\n                }) as UserContent\n            } as CoreMessage;\n        } else {\n            return {\n                ...userMessage,\n                content: userMessage.content + additionalContext\n            } as CoreMessage;\n        }\n    }\n\n    /**\n     * Create a file message from the provided files\n     * This formats the files in a way that can be included in the message context\n     */\n    public createFileMessage(files: any[], fileManager: FileLineManager, useCache: boolean = false, agentModeEnabled = false) {\n        const textContent = `<FILE_MESSAGE>\nYou have access to only these files and you cannot create any assets like mp3 or fonts or lottie. Remember, the project is running in expo snack.\nFiles like app.json, package.json, babel.config, metro.config or any other configuration files are automatically taken care of.\n\nGiven the following files in a Expo React Native project:\n\n${files.map((file) => {\n    const fileCount = this.extractor.getFileLineCount(file.content);\n    const warnings: string[] = [];\n\n    if(fileCount.warning) {\n        warnings.push(fileCount.warning);\n    }\n                        return `\n\n---- File: ------------\nPath: ${file.name}\nFileType: ${this.extractor.getFileType(file.name)}\nNumber of lines: ${fileCount.count}\nWarnings to solve: ${warnings.join(',')}\nFile Contents\n---------\n    ${\n       agentModeEnabled ?\n       this.extractor.extractMinimalFileStructure(file.content) :\n       file.content\n    }\n    `;\n                    }).join('')}\n${agentModeEnabled ? this.extractor.generateMinimalDependencyGraph(files) : ''}\nAnswer the user's question only to be able write code and nothing else.\n                    `;\n\n        const messageContent: TextPart = {\n            type: \"text\",\n            text: textContent\n        };\n\n        // // Only add cache_control if this is a base version\n        // if (useCache) {\n        //     messageContent.cache_control = { type: \"ephemeral\" };\n        // }\n\n        this.fileMessage = {\n            role: \"user\",\n            content: textContent\n        };\n    }\n\n    /**\n     * Enhance user message with additional context\n     * - Adds Supabase prompt if available\n     * - Can be extended to add other context as needed\n     */\n    public enhanceUserMessage(supabasePrompt?: string) {\n        if (!supabasePrompt || !this.userMessage) {\n            return;\n        }\n        this.userMessage = this.appendToUserMessage(this.userMessage, `\\n${supabasePrompt}`);\n\n    }\n\n    /**\n     * Create a message object ready for saving to the database\n     * - Formats the message with all required fields\n     * - Handles proper processing of content\n     */\n    public createMessageForSaving(\n        message: CoreMessage,\n        messageId: string,\n        chatId: string,\n        userId: string,\n        autoFixed: boolean,\n    ): any {\n        return {\n            ...this.prepareUserMessage(message),\n            id: messageId,\n            createdAt: new Date(),\n            chatId: chatId,\n            userId: userId,\n            componentContexts: this.componentContexts,\n            autoFixed,\n            hidden: autoFixed\n        };\n    }\n}", "startLine": 34, "endLine": 665, "type": "unknown", "symbols": ["MessageHandler"], "score": 1, "context": "This class handles message processing including filtering, preparing messages, managing tool calls, and integrating with streaming and tool execution flow, critical for understanding how chat tools are executed.", "includesImports": false}, {"filePath": "src/lib/services/stream-service.ts", "content": "export class StreamService {\n    private config: StreamServiceConfig;\n    private toolCallCosts: ToolCallCost[] = [];\n\n    constructor(config: StreamServiceConfig) {\n        this.config = config;\n    }\n\n    /**\n     * Start the text streaming process\n     * @returns The result of the streamText call\n     */\n    public startStream() {\n        const {\n            messages,\n            parser,\n            diffParser,\n            dataStream,\n            userMessageId,\n            abortSignal,\n            agentModeEnabled = false,\n            isFirstMessage = false,\n            onFinish,\n            tools = {},\n            temperature = 0,\n            maxSteps = 10,\n            generateMessageId = () => crypto.randomUUID(),\n            isSupabaseConnected = false,\n            enabledTools = [],\n            enableValidation,\n            transformChunking = \"line\",\n            modelId,\n            isConnectingSupabase\n        } = this.config;\n        let streamModelId = modelId\n        if(!modelId) {\n            streamModelId = \"anthropic/claude-sonnet-4\"\n            if (!isFirstMessage) {\n                if(agentModeEnabled) {\n                    // streamModelId = 'anthropic/claude-3.7-sonnet';\n                    streamModelId = 'anthropic/claude-sonnet-4';\n                    // streamModelId = 'openai/gpt-4.1';\n                } else {\n                    streamModelId = \"anthropic/claude-sonnet-4\"\n                }\n            }\n            if(isConnectingSupabase) {\n                streamModelId = \"anthropic/claude-sonnet-4\"\n            }\n        }\n\n        return streamText({\n            model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),\n            maxSteps,\n            experimental_activeTools: enabledTools,\n            experimental_toolCallStreaming: true,\n            toolCallStreaming: true,\n            temperature,\n            messages: messages.filter(m => !!m),\n            experimental_transform: smoothStream({chunking: transformChunking}),\n            maxRetries: 3,\n            abortSignal,\n            experimental_continueSteps: true,\n            onError: (error) => {\n                console.log('Error', error);\n                throw error;\n            },\n            onStepFinish: async (stepResult) => {\n                // console.log('stepResult', stepResult)\n                const body = await fetchFromOpenRouter(stepResult.response.id);\n                // console.log('Step result body', body)\n                \n                // Track tool call cost if data is available\n                if (body && stepResult.response.id) {\n                    const toolCallCost: ToolCallCost = {\n                        id: stepResult.response.id,\n                        model: body.data.model || streamModelId,\n                        tokens_prompt: body.data.tokens_prompt || 0,\n                        tokens_completion: body.data.tokens_completion || 0,\n                        native_tokens_prompt: body.data.native_tokens_prompt,\n                        native_tokens_completion: body.data.native_tokens_completion,\n                        total_cost: body.data.total_cost || 0,\n                        cache_discount: body.data.cache_discount,\n                        provider_name: body.data.provider_name,\n                        timestamp: new Date()\n                    };\n                    console.log('toolCallCost', toolCallCost)\n                    this.toolCallCosts.push(toolCallCost);\n                }\n            },\n            onChunk: ({chunk}) => {\n                if (chunk.type === \"text-delta\") {\n                    try {\n                        // First run the file parser\n                        const afterFileParser = parser.parse(userMessageId, chunk.textDelta);\n\n                        // Then run the diff parser on the output of the file parser\n                        diffParser.parse(userMessageId, afterFileParser);\n                    } catch (e) {\n                        console.error('Error in chunk processing', e);\n                    }\n                } else if (chunk.type === \"tool-call\") {\n                    // Send tool call information to the client\n                    dataStream.writeData({\n                        type: 'tool-call',\n                        content: {\n                            toolCallId: chunk.toolCallId,\n                            toolName: chunk.toolName,\n                            args: chunk.args\n                        }\n                    });\n                } else if (chunk.type === \"tool-result\") {\n                    // Send tool result information to the client\n                    dataStream.writeData({\n                        type: 'tool-result',\n                        content: {\n                            toolCallId: chunk.toolCallId,\n                            toolName: chunk.toolName,\n                            result: chunk.result\n                        }\n                    });\n                }\n            },\n            experimental_generateMessageId: generateMessageId,\n            experimental_repairToolCall: async ({\n                                                    toolCall,\n                                                    tools,\n                                                    error,\n                                                    messages,\n                                                    system,\n                                                }) => {\n                console.log('In repair', error);\n                const result = await generateText({\n                    model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),\n                    system,\n                    messages: [\n                        ...messages,\n                        {\n                            role: 'assistant',\n                            content: [\n                                {\n                                    type: 'tool-call',\n                                    toolCallId: toolCall.toolCallId,\n                                    toolName: toolCall.toolName,\n                                    args: toolCall.args,\n                                },\n                            ],\n                        },\n                        {\n                            role: 'tool' as const,\n                            content: [\n                                {\n                                    type: 'tool-result',\n                                    toolCallId: toolCall.toolCallId,\n                                    toolName: toolCall.toolName,\n                                    result: error.message,\n                                },\n                            ],\n                        },\n                    ],\n                    tools,\n                });\n\n                const newToolCall = result.toolCalls.find(\n                    newToolCall => newToolCall.toolName === toolCall.toolName,\n                );\n\n                return newToolCall != null\n                    ? {\n                        toolCallType: 'function' as const,\n                        toolCallId: toolCall.toolCallId,\n                        toolName: toolCall.toolName,\n                        args: JSON.stringify(newToolCall.args),\n                    }\n                    : null;\n            },\n            tools,\n            onFinish: async (params) => {\n                if (onFinish) {\n                    await onFinish({...params, enableValidation});\n                }\n            },\n        });\n    }\n\n    /**\n     * Sanitize response messages by removing incomplete tool calls\n     * @param messages The messages to sanitize\n     * @returns Sanitized messages\n     */\n    public static sanitizeResponseMessages(messages: AIMessage[]): AIMessage[] {\n        return messages.filter(message => {\n            // Filter out messages that are just incomplete tool calls\n            if (message.role === 'assistant' && Array.isArray(message.content)) {\n                const hasOnlyToolCalls = message.content.every(\n                    part => part.type === 'tool-call'\n                );\n\n                if (hasOnlyToolCalls) {\n                    return false;\n                }\n            }\n            return true;\n        });\n    }\n\n    /**\n     * Get the aggregated cost and token usage for all tool calls\n     * @returns Summary of token usage and costs\n     */\n    public getToolCallCostSummary() {\n        if (this.toolCallCosts.length === 0) {\n            return {\n                count: 0,\n                total_tokens_prompt: 0,\n                total_tokens_completion: 0,\n                total_cost: 0,\n                total_cache_discount: 0,\n                net_cost: 0,\n                tool_calls: []\n            };\n        }\n        \n        const total_tokens_prompt = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_prompt || 0), 0);\n        const total_tokens_completion = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_completion || 0), 0);\n        const total_cost = this.toolCallCosts.reduce((sum, call) => sum + (call.total_cost || 0), 0);\n        const total_cache_discount = this.toolCallCosts.reduce((sum, call) => sum + (call.cache_discount || 0), 0);\n        \n        return {\n            count: this.toolCallCosts.length,\n            total_tokens_prompt: round(total_tokens_prompt, 0),\n            total_tokens_completion: round(total_tokens_completion, 0),\n            total_tokens: round(total_tokens_prompt + total_tokens_completion, 0),\n            total_cost: round(total_cost, 6),\n            total_cache_discount: round(total_cache_discount, 6),\n            net_cost: round(total_cost + total_cache_discount, 6),\n            tool_calls: this.toolCallCosts\n        };\n    }\n    \n    /**\n     * Get a simple formatted string representation of the tool call costs\n     * @returns Formatted string with cost summary\n     */\n    public getToolCallCostReport(): string {\n        const summary = this.getToolCallCostSummary();\n        \n        if (summary.count === 0) {\n            return \"No tool calls recorded.\";\n        }\n        \n        return `Tool Call Summary:\n- Total Calls: ${summary.count}\n- Prompt Tokens: ${summary.total_tokens_prompt}\n- Completion Tokens: ${summary.total_tokens_completion}\n- Total Tokens: ${summary.total_tokens}\n- Total Cost: $${summary.total_cost}\n- Cache Discount: $${summary.total_cache_discount}\n- Net Cost: $${summary.net_cost}`;\n    }\n    \n    /**\n     * Create a new instance with updated configuration\n     * @param updatedConfig The updated configuration\n     * @returns A new StreamService instance\n     */\n    public withConfig(updatedConfig: Partial<StreamServiceConfig>): StreamService {\n        return new StreamService({\n            ...this.config,\n            ...updatedConfig,\n        });\n    }\n}", "startLine": 110, "endLine": 382, "type": "unknown", "symbols": ["StreamService"], "score": 1, "context": "This class manages the streaming of AI text including tool call streaming, chunk processing, tool call repair, and integration with the dataStream, essential for understanding the streaming system and tool execution flow.", "includesImports": false}, {"filePath": "src/components/base/data-stream-handler.tsx", "content": "export function DataStreamHandler({id, operationChange, onStreamingComplete, onFileOpen, onContinuationFlagChange, onUpdateUserMessage}: {\n    id: string,\n    operationChange: (codeBlock: CodeBlock) => any;\n    onFileOpen: (fileName: string) => any;\n    onContinuationFlagChange?: (needsContinuation: boolean) => void;\n    onUpdateUserMessage?: (message: string) => void;\n    onStreamingComplete?: () => void;\n}) {\n    const {data: dataStream, messages, setMessages} = useChat({id, streamProtocol: \"text\"});\n    const {setUserMessageIdFromServer} = useUserMessageId();\n    const {setAIMessageIdFromServer} = useAIMessageId();\n    const {setBlock} = useBlock();\n    const lastProcessedIndex = useRef(-1);\n    const [currentOperation, setCurrentOperation] = useState<CodeBlock>({type: null, fileName: '', content: ''});\n\n    useEffect(() => {\n        if (!dataStream?.length) return;\n\n        const newDeltas = dataStream.slice(lastProcessedIndex.current + 1);\n        lastProcessedIndex.current = dataStream.length - 1;\n\n        (newDeltas as DataStreamDelta[]).forEach((delta: DataStreamDelta) => {\n            if (delta.type === 'user-message-id') {\n                setUserMessageIdFromServer(delta.content as string);\n                return;\n            }\n            if (delta.type === 'ai-message-id') {\n                console.log('AI message id', delta.content);\n                onStreamingComplete && onStreamingComplete();\n                setAIMessageIdFromServer(delta.content as string);\n                // setMessages(messages => {\n                //     messages[messages.length - 1].id = delta.content as string;\n                //     return [...messages];\n                // })\n                return;\n            }\n            if (delta.type === \"open-file\") {\n                onFileOpen((delta.content as FileOpenBlock).filename)\n                return;\n            }\n            \n            if (delta.type === \"file-move\") {\n                const moveOp = delta.content as FileMoveBlock;\n                toast.success(\n                    `File moved from ${moveOp.sourcePath} to ${moveOp.targetPath}`,\n                    {\n                        duration: 4000,\n                        position: \"top-right\",\n                        icon: \"📂\",\n                    }\n                );\n                return;\n            }\n\n            if (delta.type === \"messages-update\") {\n                console.log('messages-update', delta.content, typeof delta.content)\n                // setMessages(messages => {\n                //     (delta.content as MessagesUpdate).messages.reverse().forEach((m, index) => {\n                //         // if (messages[messages.length - 1 - index].id.includes(\"msg-\")) {\n                //         if(messages[messages.length - 1 - index]) {\n                //             messages[messages.length - 1 - index].id = m?.id;\n                //             // @ts-ignore\n                //             messages[messages.length - 1 - index].parentUserMessageId = m?.parentUserMessageId;\n                //             // @ts-ignore\n                //             messages[messages.length - 1 - index].parentAssistantMessageId = m?.parentAssistantMessageId;\n                //             // @ts-ignore\n                //             messages[messages.length - 1 - index].isAssistantGroupHead = m?.isAssistantGroupHead;\n                //         }\n                //         // }\n                //     })\n                //\n                //     // messages = messages.map(message => {\n                //     //     // if (message.id.includes(\"msg-\")) {\n                //     //         // find the corresponding user message\n                //     //         if (message.role === \"user\") {\n                //     //             (delta.content as MessagesUpdate).messages.find(m => m.role === \"user\");\n                //     //         }\n                //     //     // }\n                //     //     return message;\n                //     // })\n                //     return messages;\n                // })\n                return;\n            }\n            \n            if (delta.type === \"credit-usage-event\") {\n                const creditEvent = delta.content as CreditUsageEvent;\n                if (creditEvent.type === \"error-fix\") {\n                    toast.success(\"Error fixing doesn't consume credits! Keep your app running smoothly.\", {\n                        duration: 4000,\n                        position: \"top-right\",\n                        className: \"bg-gradient-to-r from-green-400 to-green-600\"\n                    });\n                } else if (creditEvent.type === \"auto-fix\") {\n                    toast.success(\"I automatically fixed a small issue for you!\", {\n                        description: \"Your app should now work correctly.\",\n                        duration: 4000,\n                        position: \"top-right\",\n                        className: \"bg-gradient-to-r from-green-400 to-green-600\"\n                    });\n                }\n                return;\n            }\n            \n            if (delta.type === \"continuation-flag\") {\n                console.log('delta', JSON.stringify(delta))\n                const continuationEvent = delta.content as ContinuationFlagEvent;\n                console.log(`[DataStreamHandler] Continuation flag: ${continuationEvent?.needsContinuation}`);\n                if (onContinuationFlagChange) {\n                    onContinuationFlagChange(!!continuationEvent?.needsContinuation);\n                }\n                return;\n            }\n\n            if (delta.type === \"update-user-message\") {\n                if(onUpdateUserMessage) {\n                    onUpdateUserMessage((delta.content as UpdateUserMessage)?.userMessage)\n                }\n                return;\n            }\n            \n            // Handle validation status messages\n            if (delta.type === \"validating-output\" || delta.type === \"validated-output\") {\n                console.log('delta.type', delta.type)\n                const validationMessage = delta.content as ValidationMessage;\n                \n                // Create a special validation message with a distinct visual style\n                const validationContent = delta.type === \"validating-output\" ?\n                    `🔍 ${validationMessage.message}` :\n                    `✅ ${validationMessage.message}`;\n                \n                // Add the validation message to the chat as a UIMessage (from 'ai' package)\n                const newMessage: UIMessage = {\n                    id: `validation-${Date.now()}`,\n                    role: 'system',\n                    content: validationContent\n                };\n                \n                // Update messages with the new validation message\n                setMessages([...messages, newMessage]);\n                return;\n            }\n\n            if (delta.type === 'file-operation') {\n                const op: CodeBlock = delta.content as CodeBlock;\n                setCurrentOperation({\n                    type: op.type,\n                    fileName: op.fileName || op.absolutePath || '',\n                    absolutePath: op.absolutePath || op.fileName || '',\n                    content: op.content || ''\n                });\n            }\n\n\n            // const content = delta.content as string;\n\n            // // Detect file operations\n            // if (content.includes('<create_file name=\"')) {\n            //     const fileName = content.match(/name=\"([^\"]+)\"/)?.[1];\n            //     setCurrentOperation({\n            //         type: 'create',\n            //         fileName: fileName || '',\n            //         content: ''\n            //     });\n            // } else if (content.includes('<edit_file name=\"')) {\n            //     const fileName = content.match(/name=\"([^\"]+)\"/)?.[1];\n            //     setCurrentOperation({\n            //         type: 'edit',\n            //         fileName: fileName || '',\n            //         content: ''\n            //     });\n            // } else if (content.includes('</create_file>') || content.includes('</edit_file>')) {\n            //     setCurrentOperation({type: null, fileName: '', content: ''});\n            // } else if (currentOperation.type) {\n            //     // Accumulate content\n            //     setCurrentOperation(prev => ({\n            //         ...prev,\n            //         content: prev.content + content\n            //     }));\n            // }\n\n            setBlock((draftBlock) => {\n                if (!draftBlock) {\n                    return {...initialBlockData, status: 'streaming'};\n                }\n\n                switch (delta.type) {\n                    case 'id':\n                        return {\n                            ...draftBlock,\n                            documentId: delta.content as string,\n                            status: 'streaming',\n                        };\n\n                    case 'title':\n                        return {\n                            ...draftBlock,\n                            title: delta.content as string,\n                            status: 'streaming',\n                        };\n\n                    case 'kind':\n                        return {\n                            ...draftBlock,\n                            kind: delta.content as BlockKind,\n                            status: 'streaming',\n                        };\n\n                    case 'text-delta':\n                        return {\n                            ...draftBlock,\n                            content: draftBlock.content + (delta.content as string),\n                            isVisible:\n                                draftBlock.status === 'streaming' &&\n                                draftBlock.content.length > 400 &&\n                                draftBlock.content.length < 450\n                                    ? true\n                                    : draftBlock.isVisible,\n                            status: 'streaming',\n                        };\n\n                    case 'code-delta':\n                        return {\n                            ...draftBlock,\n                            content: delta.content as string,\n                            isVisible:\n                                draftBlock.status === 'streaming' &&\n                                draftBlock.content.length > 300 &&\n                                draftBlock.content.length < 310\n                                    ? true\n                                    : draftBlock.isVisible,\n                            status: 'streaming',\n                        };\n\n                    case 'clear':\n                        return {\n                            ...draftBlock,\n                            content: '',\n                            status: 'streaming',\n                        };\n\n                    case 'finish':\n                        return {\n                            ...draftBlock,\n                            status: 'idle',\n                        };\n\n                    default:\n                        return draftBlock;\n                }\n            });\n        });\n    }, [dataStream, setBlock, setUserMessageIdFromServer, setAIMessageIdFromServer]);\n\n    useEffect(() => {\n        if (operationChange) {\n            operationChange(currentOperation);\n        }\n    }, [currentOperation]);\n\n    return null;\n}", "startLine": 73, "endLine": 334, "type": "component", "symbols": ["DataStreamHandler"], "score": 1, "context": "This React component handles the data stream from the chat, processes different delta types including tool calls and file operations, and updates UI state, showing the integration of chat tools with the streaming system.", "includesImports": false}, {"filePath": "src/components/base/chat.tsx", "content": "const Chat = observer(({\n                           id,\n                           initialMessages,\n                           isReadonly,\n                           initialPrompt,\n                           onLoadingChange,\n                           projectId,\n                           runLastUserMessage,\n                           onInitialRunInitialized,\n                           hasMoreMessages,\n                           totalUserMessages\n                       }: {\n    id: string;\n    initialMessages: Array<UIMessage>;\n    initialPrompt?: string;\n    isReadonly: boolean;\n    onLoadingChange?: (isLoading: boolean) => any;\n    projectId: string;\n    runLastUserMessage: boolean;\n    onInitialRunInitialized: () => any;\n    hasMoreMessages?: boolean;\n    totalUserMessages?: number;\n}) => {\n    const {mutate} = useSWRConfig();\n\n    const {anonymousId, hasReachedLimit, incrementChatCount, storeChat, getStoredChat} = useAnonymousSession();\n    const {status: authStatus} = useSession();\n\n    const {generatorStore, integrationStore, logStore, snackStore} = useStores();\n    const session = generatorStore.getActiveSession(id);\n    const [integrationOpen, setIntegrationOpen] = useState(false);\n    const [isSecretOpen, setIsSecretOpen] = useState(false);\n    const [secretNames, setSecretNames] = useState<string[] | null>(null);\n    const [droppedFiles, setDroppedFiles]= useState<File[]>();\n    const [selectMode, setSelectMode] = useState(false);\n    const [sqlDialogOpen, setSqlDialogOpen] = useState(false);\n    // Define a proper type for SQL queries\n    type SqlQuery = {\n        query: string;\n        source: string;\n        table: string;\n    };\n    const [pendingSqlQueries, setPendingSqlQueries] = useState<SqlQuery[]>([]);\n    const [analysisResult, setAnalysisResult] = useState<{ analysis: CodeChangeAnalysis, message: string } | null>(null);\n    const [isAnalysisDialogOpen, setIsAnalysisDialogOpen] = useState(false);\n    const [isLoadingPrevious, setIsLoadingPrevious] = useState(false);\n    const [isLoadAllDialogOpen, setIsLoadAllDialogOpen] = useState(false);\n    const [hasMoreLocalOverride, setHasMoreLocalOverride] = useState<boolean | undefined>(undefined);\n    if (!session) {\n        return;\n    }\n\n    const {agentModeEnabled} = useAgentModeSettings();\n\n    // Track when a chat is started or resumed\n    useEffect(() => {\n        const isNewChat = initialMessages.length <= 1; // Only system message or empty\n\n        if (isNewChat) {\n            trackMessageEvent('CHAT_STARTED', {\n                chat_id: id,\n                project_id: projectId\n            });\n        } else {\n            trackMessageEvent('CHAT_RESUMED', {\n                chat_id: id,\n                time_since_last_activity: 0, // We don't have this info yet, but could add it\n                session_message_count: initialMessages.length\n            });\n        }\n    }, [id, initialMessages.length, projectId]);\n\n    const {\n        messages,\n        setMessages,\n        handleSubmit,\n        input,\n        setInput,\n        append,\n        isLoading,\n        stop,\n        reload,\n        data,\n        status,\n        experimental_resume,\n        addToolResult\n    } = useChat({\n        id,\n        keepLastMessageOnError: false,\n        experimental_prepareRequestBody: (options: {\n            id: string;\n            messages: UIMessage[];\n            requestData?: JSONValue;\n            requestBody?: object;\n        }) => {\n            options.messages = options.messages.map(message => {\n                message.parts = message.parts.filter(part => {\n                    if(part.type === \"tool-invocation\") {\n                        return part.toolInvocation.state !== \"partial-call\"\n                    }\n                    return true\n                })\n                return message\n            }).slice(-20);\n            return {\n                files: session.fileTree.length ? session.fileTree : DEFAULT_CODE,\n                activeFile: session.activeFile,\n                dependencies: session.dependencies,\n                linkSupabaseProjectId: integrationStore.currentSelectedProjectId,\n                linkSupabaseConnection: integrationStore.getConnection(\"supabase\")?.id,\n                projectId: session.projectId,\n                logs: logStore.getLogs(id),\n                agentModeEnabled: agentModeEnabled,\n                messages: options.messages,\n                id,\n                ...options.requestBody\n            }\n        },\n        headers: anonymousId ? {\n            'x-anonymous-id': anonymousId,\n            'x-chat-count': hasReachedLimit ? '2' : '1',\n        } : {},\n        initialMessages,\n        generateId: () => {\n            return generateUUID();\n        },\n        sendExtraMessageFields: true,\n        streamProtocol: \"data\",\n        onToolCall: async ({toolCall}) => {\n            if(toolCall.toolName === \"clientTesting\") {\n                console.log('[Chat] Client testing tool call received:', toolCall);\n                if (session) {\n                    // Start client testing with the tool call parameters\n                    session.startClientTesting(\n                        toolCall.toolCallId,\n                        (toolCall.args as any)?.featuresToTest || '',\n                        (toolCall.args as any)?.expectations || '',\n                        (toolCall.args as any)?.reason || ''\n                    );\n\n                    console.log('[Chat] Client testing started, waiting for user interaction');\n                    // For human-in-the-loop tools, we don't return anything here\n                    // The tool result will be added via addToolResult when user completes testing\n                    // This prevents the connection from closing prematurely\n                }\n                // Don't return anything for human-in-the-loop tools\n                // The result will be provided later via addToolResult\n            }\n        },\n        onResponse: () => {\n            session.setNeedsContinuation(false);\n            setSelectMode(false);\n            // Track when AI begins streaming a response\n            trackMessageEvent('AI_RESPONSE_STREAMING', {\n                chat_id: id\n            });\n        },\n        onFinish: async (message) => {\n            // Track when a message is finished\n            // Use a valid event type from the available options\n            trackMessageEvent('RECEIVED', {\n                chat_id: id,\n                message_id: message.id\n                // Remove properties that don't exist in MessageEventProperties\n            });\n\n            // Check if the message contains SQL queries\n            if (typeof message.content === 'string' && message.content.includes('<MO_DATABASE_QUERY')) {\n                // Extract SQL queries using regex\n                const regex = /<MO_DATABASE_QUERY[^>]*source=[\"']([^\"']*)[\"'][^>]*(?:table=[\"']([^\"']*)[\"'])?[^>]*>([\\s\\S]*?)<\\/MO_DATABASE_QUERY>/g;\n                const queries: SqlQuery[] = [];\n                let match;\n\n                while ((match = regex.exec(message.content)) !== null) {\n                    const source = match[1];\n                    const table = match[2] || '';\n                    const query = match[3].trim();\n\n                    if (query) {\n                        // Add typed query to the array\n                        const sqlQuery: SqlQuery = {\n                            query,\n                            source,\n                            table\n                        };\n                        queries.push(sqlQuery);\n                    }\n                }\n\n                if (queries.length > 0) {\n                    setPendingSqlQueries(queries);\n                    setSqlDialogOpen(true);\n                }\n            }\n\n            session.markStreamingComplete();\n            console.log('Generated message ID', message.id)\n            // Track when AI completes a response\n            trackMessageEvent('AI_RESPONSE_RECEIVED', {\n                chat_id: id,\n                message_content_length: message.content.length\n            });\n\n            // try {\n            //     delay(() => {\n            //         session.performAnalysis(message, messages);\n            //     }, 500);\n            // } catch (e) {\n            //     console.log('Error performing analysis', e)\n            // }\n\n            if (anonymousId) {\n                // Store messages and file state for anonymous users\n                storeChat(id, [...initialMessages, message], session.fileTree);\n                if (!hasReachedLimit) {\n                    incrementChatCount();\n                }\n            } else {\n                // Update both history and subscription status for authenticated users\n                mutate('/api/history');\n                mutate('/api/subscription/status');\n            }\n        },\n        onError: (error) => {\n            session.markAsFailed();\n            Sentry.withScope(function (scope) {\n                scope.setTag(\"action\", \"chat\");\n                scope.setLevel(\"error\");\n                Sentry.captureException(error);\n            });\n\n            // Track message error\n            trackMessageEvent('ERROR', {\n                chat_id: id,\n                error_type: error.message?.includes(\"limit reached\") ? 'rate_limit' : 'server_error',\n                error_message: error.message || 'Unknown error'\n            });\n\n            try {\n                let draft = '';\n                // const lastUserMessageIndex = messages.findLastIndex(message => message.role === \"user\");\n                // setMessages(messages => {\n                //     draft = messages[lastUserMessageIndex].content;\n                //     messages = messages.slice(0, lastUserMessageIndex);\n                //     return messages;\n                // });\n                toast.error(\"There was an error with our LLM provider while processing your last message. You will not be charged for it.\");\n                setInput(draft)\n                const errorData = JSON.parse(error.message);\n                // Regardless of the limit reached, we want to first show the upgrade dialog to anonymous users and upgrade dialog once they are logged in\n                if (errorData.error?.includes(\"limit reached\")) {\n                    if (errorData.isAnonymous) {\n                        generatorStore.toggleLoginDialog(true);\n                    } else {\n                        generatorStore.setUsageLimit(\n                            errorData.limit,\n                            errorData.remaining\n                        );\n                        generatorStore.toggleUpgradeDialog(true);\n                    }\n                } else {\n                    toast.error('An error occurred while processing your message');\n                }\n            } catch (e) {\n                console.error('Failed to parse error:', error);\n                toast.error('An error occurred while processing your message');\n            }\n        }\n    });\n\n    const {data: votes} = useSWR<Array<Vote>>(\n        `/api/vote?chatId=${id}`,\n        fetcher,\n    );\n\n    const [attachments, setAttachments] = useState<Array<Attachment>>([]);\n\n    const isBlockVisible = useBlockSelector((state) => state.isVisible);\n    const [initialPromptAdded, setInitialPromptAdded] = useState(false);\n    const [lastRunInitialized, setLastRunInitialized] = useState(false);\n\n    const appendInitialPrompt = useCallback(() => {\n        if (initialPrompt && append && !initialPromptAdded) {\n            console.log('Append');\n            setInitialPromptAdded(true)\n            append({\n                id: id,\n                role: \"user\",\n                content: initialPrompt\n            }).then().catch(err => {\n                console.log('Error appending initial prompt added for user', err);\n                setInput(initialPrompt);\n                handleSubmit();\n            });\n            console.log('Can fire now');\n            // window.history.replaceState({}, '', `/generator`);\n        }\n    }, [initialPrompt, id, append]);\n\n    useEffect(() => {\n        appendInitialPrompt();\n    }, [appendInitialPrompt]);\n\n    useEffect(() => {\n        if(session.updatedUserMessage) {\n            // Let's change the latest user message and clear the updatedUserMessage prop\n            setMessages(messages => {\n                const updateMessages = [...messages];\n                const lastUserMessageIndex = updateMessages.findLastIndex(message => message.role === \"user\");\n                if(lastUserMessageIndex !== null) {\n                    updateMessages[lastUserMessageIndex].content = session.updatedUserMessage;\n                }\n                return updateMessages;\n            })\n            session.onUserMessageUpdate('');\n        }\n    }, [session.updatedUserMessage])\n\n    // useEffect(() => {\n    //     if(session?.needsContinuation && [\"ready\"].includes(status)) {\n    //         append({\n    //             content: \"Please continue. First start by printing the plan of what is done and what is left with clear indicators. Make sure to be on track and don't drift doing things not asked to do. Batch the edits in a single call. Please be careful and make changes that are holistic and coherent. Pay close attention to the logs and use the correct tools judiciously.\",\n    //             role: \"user\"\n    //         })\n    //     }\n    // }, [session?.needsContinuation])\n\n\n    const hasRunRef = useRef(false);\n\n    const initiateLatestMessageAutoRun = useCallback(() => {\n        if (runLastUserMessage && !lastRunInitialized && !hasRunRef.current) {\n            onInitialRunInitialized();\n            hasRunRef.current = true;\n            setLastRunInitialized(true);\n            reload({\n                body: {\n                    isReload: true,\n                    isInitial: true\n                }\n            })\n        }\n    }, [runLastUserMessage, reload, lastRunInitialized, setLastRunInitialized])\n\n    const onVisualSelectionClicked = () => {\n        snackStore.sendMessageToSnack(id, 'TOGGLE_INSPECTION_MODE');\n        setSelectMode(selectMode => !selectMode);\n    }\n\n\n    useEffect(() => {\n        initiateLatestMessageAutoRun()\n    }, [initiateLatestMessageAutoRun])\n\n\n    useEffect(() => {\n        if (integrationStore.currentSelectedProjectId && integrationStore.shouldSendMessage) {\n            append({\n                content: `Please connect my supabase project ${integrationStore.currentSelectedProjectId} with this app`,\n                role: \"user\"\n            })\n                .then(() => {\n                    toast.success('Your app is now linked to supabase');\n                })\n                .catch(err => {\n                    toast.success('Failed to link app to supabase project');\n                })\n                .finally(() => {\n                    integrationStore.setShouldSendMessage(false);\n                    integrationStore.resetCurrentSelectedProjectId();\n                })\n        }\n    }, [integrationStore.currentSelectedProjectId, integrationStore.shouldSendMessage]);\n\n    useEffect(() => {\n        // Update session status based on loading state\n        if (isLoading) {\n            window.history.replaceState({}, '', `/projects/${session.projectId}/chats/${id}`);\n            session.setStatus('streaming');\n        } else {\n            session.setStatus('idle');\n        }\n\n        // Notify parent component if needed\n        if (onLoadingChange) {\n            onLoadingChange(isLoading);\n        }\n    }, [isLoading]);\n\n    // Watch for error fix requests\n    useEffect(() => {\n        if (session.currentMessage) {\n            setInput(session.currentMessage);\n            session.setCurrentMessage(''); // Clear after sending\n        }\n    }, [session.currentMessage]);\n\n    // Create local state to track component contexts\n    const [localComponentContexts, setLocalComponentContexts] = useState<any[]>([]);\n\n    // Watch for component context changes\n    useEffect(() => {\n        // Make a shallow copy of the observable array to trigger React's change detection\n        setLocalComponentContexts([...session.componentContexts]);\n    }, [session.componentContexts, session.componentContexts.length]);\n\n    useEffect(() => {\n        if(session.currentFile) {\n            setDroppedFiles([session.currentFile]);\n        }\n    }, [session.currentFile])\n\n    const selectionChange = (model: string) => {\n        // setModelId(model);\n    }\n\n    const onVersionClick = (messageId: string) => {\n        const messageIndex = messages.findIndex((message) => message.id === messageId);\n        // Find the next previous user message before the current message\n        const previousUserMessage = messages.find((message, index) => message.role === 'user' && index < messageIndex);\n\n        if (previousUserMessage) {\n            session.loadHistoricalVersion(projectId, id, previousUserMessage.id, messageId);\n        } else {\n            toast.warning('No previous state message found');\n        }\n    }\n\n    const onActionClick = (action: ActionMeta) => {\n        console.log('action.type', action.type, action.type.includes(\"supabase_integration\"))\n        const {type, content, tool, link, secretName, allSecretNames} = action;\n        if (status !== 'ready' && status !== 'error') {\n            toast.warning(\"Please wait for the previous response to be completed before using this action.\")\n            return;\n        }\n        // Handle button click based on type\n        if (type === 'tool' && tool) {\n            console.log(`Tool action: ${tool}`);\n            // Here you would trigger the tool\n            switch (tool) {\n                case 'supabase_integration':\n                    if (authStatus !== \"authenticated\") {\n                        generatorStore.toggleLoginDialog(true);\n                        return;\n                    }\n                    setIntegrationOpen(true);\n                    break;\n\n                case 'secrets_form':\n                    if (allSecretNames?.length) {\n                        if (authStatus !== \"authenticated\") {\n                            generatorStore.toggleLoginDialog(true);\n                            return;\n                        }\n                        setSecretNames(allSecretNames);\n                        setIsSecretOpen(true);\n                    } else {\n                        append({\n                            content: `The secret names are not set in the action. Please give an actions with the correct secretName for me to enter.`,\n                            role: 'user'\n                        })\n                    }\n                    break;\n            }\n        } else if (type === \"feature\" || type === \"code\") {\n            append({\n                content: content,\n                role: 'user'\n            })\n        } else {\n            console.log(`Action clicked: ${type} - ${content}`);\n        }\n    }\n\n    const onSecretSubmit = async (secretValues: Record<string, string>): Promise<void> => {\n        const result = await session.saveSecrets(secretValues, projectId, append as any);\n        if (result) {\n            setIsSecretOpen(false);\n        }\n        setSecretNames(null);\n    }\n\n    // Function to load all messages in the background\n    const loadAllMessages = async () => {\n        if (isLoadingPrevious || !hasMoreMessages) return;\n\n        try {\n            // Close the dialog immediately\n            setIsLoadAllDialogOpen(false);\n\n            // Set loading state\n            setIsLoadingPrevious(true);\n            toast.info('Loading all messages in the background...');\n\n            // Fetch all messages for this chat\n            const response = await fetch(`/api/chats/${id}/messages?all=true`);\n\n            if (!response.ok) {\n                throw new Error(`Failed to fetch all messages: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.error) {\n                throw new Error(data.error);\n            }\n\n            // Convert DB messages to UI messages\n            const allMessages = convertToUIMessages(data.messages);\n\n            // Replace current messages with all messages\n            if (allMessages.length > 0) {\n                setMessages(allMessages);\n                toast.success(`Loaded all ${allMessages.length} messages`);\n\n                // Hide the load more button by setting hasMoreMessages to false\n                if (totalUserMessages && allMessages.length >= totalUserMessages) {\n                    // If we've loaded all messages, update the state to hide the button\n                    if (hasMoreMessages) {\n                        // Only update if needed to avoid unnecessary re-renders\n                        // This is a local state update since we can't modify the prop directly\n                        setHasMoreLocalOverride(false);\n                    }\n                }\n            } else {\n                toast.info('No messages found');\n            }\n        } catch (error) {\n            console.error('Error loading all messages:', error);\n            toast.error('Failed to load all messages');\n        } finally {\n            setIsLoadingPrevious(false);\n        }\n    };\n\n    // Function to show confirmation dialog\n    const showLoadAllConfirmation = useCallback(() => {\n        setIsLoadAllDialogOpen(true);\n    }, [setIsLoadAllDialogOpen]);\n\n    const integrationClicked = (open: boolean) => {\n        setIntegrationOpen(open)\n    }\n\n    const setSnackError = (error: any) => {\n        session.setSnackError(error, 'supabase');\n    }\n\n    // Function to execute SQL queries\n    const executeSqlQuery = async (query: SQLStatus): Promise<string | null> => {\n        try {\n            const response = await fetch(`/api/project/${projectId}/sql`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify({ query: query.query }),\n            });\n\n            const data = await response.json();\n            if (!response.ok) {\n                return data.error || data.message || 'Failed to execute query';\n            }\n\n            return null; // No error\n        } catch (err: any) {\n            return err.message || 'An unexpected error occurred';\n        }\n    };\n\n    // Handle dialog close\n    const handleSqlDialogComplete = () => {\n        setSqlDialogOpen(false);\n        setPendingSqlQueries([]);\n    };\n\n    // useAutoResume({\n    //     autoResume: true,\n    //     initialMessages,\n    //     experimental_resume,\n    //     data,\n    //     setMessages,\n    // });\n\n    // Wrap reload and append from useChat in stable references\n    const stableReload = useCallback((opts?: any) => reload(opts), [reload]);\n    const stableAppend = useCallback((msg: Message | CreateMessage, opts?: any) => append(msg, opts), [append]);\n\n    // Stable wrapper for version and action clicks\n    const stableOnVersionClick = useCallback((messageId: string) => onVersionClick(messageId), [onVersionClick]);\n    const stableOnActionClick = useCallback((action: ActionMeta) => onActionClick(action), [onActionClick]);\n\n    // Stable snack error setter\n    const stableSetSnackError = useCallback((error: any) => setSnackError(error), [setSnackError]);\n\n    return (\n        <>\n            <div className=\"flex flex-col h-full bg-background overflow-hidden\">\n                {/* Main scrollable area */}\n                <div className=\"flex-1 overflow-y-auto pb-safe relative\">\n                    <div className=\"flex flex-col h-full min-h-0\">\n                        <Messages\n                            votes={votes}\n                            chatId={id}\n                            projectId={projectId}\n                            isLoading={isLoading}\n                            messages={messages}\n                            setMessages={setMessages}\n                            reload={stableReload}\n                            append={stableAppend}\n                            isReadonly={isReadonly}\n                            isBlockVisible={isBlockVisible}\n                            setInput={setInput}\n                            setAttachments={setAttachments}\n                            onVersionClick={stableOnVersionClick}\n                            lastActiveVersionId={''}\n                            onActionClick={stableOnActionClick}\n                            setSnackError={stableSetSnackError}\n                            hasMoreMessages={hasMoreLocalOverride !== undefined ? hasMoreLocalOverride : hasMoreMessages}\n                            totalUserMessages={totalUserMessages}\n                            onLoadPrevious={showLoadAllConfirmation}\n                            isLoadingPrevious={isLoadingPrevious}\n                            removeActions={false}\n                            status={status}\n                            addToolResult={addToolResult}\n                        />\n                    </div>\n                </div>\n\n                {/* Input area - with safe area padding */}\n                {!isReadonly && (\n                    <div\n                        className=\"flex-shrink-0 border-t border-border/50 bg-background backdrop-blur bg-opacity-45 pb-safe pt-2\">\n                        <div className=\"px-4 py-2 mx-auto\">\n                            <MultimodalInput\n                                chatId={id}\n                                input={input}\n                                setInput={setInput}\n                                handleSubmit={handleSubmit}\n                                isLoading={isLoading}\n                                stop={stop}\n                                inDesignMode={false}\n                                attachments={attachments}\n                                setAttachments={setAttachments}\n                                messages={messages}\n                                setMessages={setMessages}\n                                append={append}\n                                projectId={projectId}\n                                needsContinuation={session.needsContinuation}\n                                droppedFiles={droppedFiles}\n                                onVisualSelectionClicked={onVisualSelectionClicked}\n                                componentContexts={localComponentContexts}\n                                onRemoveComponentContext={(id) => session.removeComponentContext(id)}\n                                onClearComponentContexts={() => session.clearComponentContexts()}\n                                selectMode={selectMode}\n                            />\n                        </div>\n                    </div>\n                )}\n            </div>\n\n            {/*<Block*/}\n            {/*    chatId={id}*/}\n            {/*    input={input}*/}\n            {/*    setInput={setInput}*/}\n            {/*    handleSubmit={handleSubmit}*/}\n            {/*    isLoading={isLoading}*/}\n            {/*    stop={stop}*/}\n            {/*    attachments={attachments}*/}\n            {/*    setAttachments={setAttachments}*/}\n            {/*    append={append}*/}\n            {/*    messages={messages}*/}\n            {/*    setMessages={setMessages}*/}\n            {/*    reload={reload}*/}\n            {/*    votes={votes}*/}\n            {/*    isReadonly={isReadonly}*/}\n            {/*/>*/}\n\n            <IntegrationDialog\n                providerId={'supabase'}\n                open={integrationOpen}\n                chatId={id}\n                projectId={projectId}\n                onOpenChange={integrationClicked}\n            />\n\n            <SecretInput\n                isOpen={isSecretOpen && !!secretNames}\n                onClose={() => setIsSecretOpen(false)}\n                onSubmit={onSecretSubmit}\n                secretNames={secretNames as string[]}\n            />\n\n            {/* SQL Execution Dialog */}\n            <SQLExecutionDialog\n                queries={pendingSqlQueries.map((q: SqlQuery) => ({\n                    type: 'up',\n                    source: q.source,\n                    table: q.table || 'database',\n                    description: `Setting up ${q.table || 'database'} for ${q.source}`,\n                    state: 'pending',\n                    query: q.query\n                }))}\n                messageId=\"\"\n                isOpen={sqlDialogOpen}\n                onExecute={executeSqlQuery}\n                onComplete={handleSqlDialogComplete}\n                setInput={setInput}\n            />\n\n            {/* Code Analysis Dialog */}\n            {/* Confirmation dialog for loading all messages */}\n            <ConfirmationDialog\n                title=\"Load All Messages\"\n                description=\"Loading all messages may impact performance, especially for large conversations. Are you sure you want to load all messages?\"\n                confirmText=\"Load All Messages\"\n                cancelText=\"Cancel\"\n                open={isLoadAllDialogOpen}\n                onOpenChange={setIsLoadAllDialogOpen}\n                onConfirm={loadAllMessages}\n                hideTriggerButton\n            />\n\n            <AlertDialog open={isAnalysisDialogOpen} onOpenChange={setIsAnalysisDialogOpen}>\n                <AlertDialogContent className=\"max-w-3xl\">\n                    <AlertDialogHeader>\n                        <AlertDialogTitle className=\"flex items-center gap-2\">\n                            {analysisResult?.analysis.recommendedAction === 'continue' && (\n                                <CheckCircle className=\"h-5 w-5 text-green-500\" />\n                            )}\n                            {analysisResult?.analysis.recommendedAction === 'fix' && (\n                                <AlertTriangle className=\"h-5 w-5 text-amber-500\" />\n                            )}\n                            {analysisResult?.analysis.recommendedAction === 'redo' && (\n                                <AlertCircle className=\"h-5 w-5 text-red-500\" />\n                            )}\n                            Code Change Analysis\n                        </AlertDialogTitle>\n                        <AlertDialogDescription className=\"prose max-w-none\">\n                            {analysisResult?.message && (\n                                <div dangerouslySetInnerHTML={{ __html: marked.parse(analysisResult.message) }} />\n                            )}\n                        </AlertDialogDescription>\n                    </AlertDialogHeader>\n                    <AlertDialogFooter className=\"flex gap-2\">\n                        {analysisResult?.analysis.recommendedAction === 'continue' && (\n                            <AlertDialogAction asChild>\n                                <Button className=\"bg-green-600 hover:bg-green-700\">\n                                    Continue Building\n                                </Button>\n                            </AlertDialogAction>\n                        )}\n                        {analysisResult?.analysis.recommendedAction === 'fix' && (\n                            <>\n                                <AlertDialogAction asChild>\n                                    <Button variant=\"outline\">Continue Anyway</Button>\n                                </AlertDialogAction>\n                                <AlertDialogAction asChild>\n                                    <Button className=\"bg-amber-600 hover:bg-amber-700\"\n                                        onClick={() => {\n                                            setInput(`Fix the following issues: ${analysisResult.analysis.potentialIssues\n                                                .map(issue => issue.description)\n                                                .join(', ')}`);\n                                        }}>\n                                        Fix Issues\n                                    </Button>\n                                </AlertDialogAction>\n                            </>\n                        )}\n                        {analysisResult?.analysis.recommendedAction === 'redo' && (\n                            <>\n                                <AlertDialogAction asChild>\n                                    <Button variant=\"outline\">Continue Anyway</Button>\n                                </AlertDialogAction>\n                                <AlertDialogAction asChild>\n                                    <Button className=\"bg-red-600 hover:bg-red-700\"\n                                        onClick={() => {\n                                            setInput(`Please redo the implementation. The previous attempt had these issues: ${analysisResult.analysis.potentialIssues\n                                                .map(issue => issue.description)\n                                                .join(', ')}`);\n                                        }}>\n                                        Redo Implementation\n                                    </Button>\n                                </AlertDialogAction>\n                            </>\n                        )}\n                    </AlertDialogFooter>\n                </AlertDialogContent>\n            </AlertDialog>\n        </>\n    );\n})\n\nexport default Chat;", "startLine": 41, "endLine": 834, "type": "component", "symbols": ["Cha<PERSON>"], "score": 1, "context": "This React component implements the chat UI, manages chat state, handles tool calls via onToolCall, processes streaming responses, and integrates multiple chat tools with the streaming system, showing the full tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/add-ai-memory.ts", "content": "export const addAiMemory = ({\n                                dataStream,\n                                creditUsageTracker,\n                                projectId\n                            }: {\n    dataStream: DataStreamWriter,\n    creditUsageTracker: CreditUsageTracker,\n    projectId: string\n}) => {\n    return tool({\n        description: `Use the addAiMemory tool to store critical information for future interactions.\n        WHEN TO USE:\n        - After completing part of a multi-step task to track remaining steps\n        - To record user preferences, project requirements, or design decisions\n        - To capture technical constraints or architectural decisions\n        - To maintain context across multiple interactions\n\n        HOW TO USE:\n        - Use clear prefixes for different types of memories:\n          * USER PREFERENCE: For user style/feature preferences\n          * REQUIREMENT: For project requirements\n          * COMPLETED/NEXT: For task progress tracking\n          * DECISION: For design/technical decisions\n          * CONSTRAINT: For technical limitations\n        - Be specific and concise (1-2 sentences per memory)\n        - Always use at the END of your response\n        `,\n        parameters: z.object({\n            knowledge: z.string().describe(`Concise, specific information to remember for future interactions. Use prefixes like USER PREFERENCE:, REQUIREMENT:, COMPLETED:/NEXT:, DECISION:, or CONSTRAINT: to categorize the memory. Be specific and actionable - this information will be available in future interactions.`)\n        }),\n        execute: async ({knowledge}: { knowledge: string }) => {\n            try {\n                const project = await getProjectById({id: projectId})\n                if (!project) {\n                    return \"Project not found\"\n                }\n\n                // Constants for memory management\n                const MAX_MEMORY_CHARS = 8000; // About 2000 tokens\n                const MAX_CATEGORY_ENTRIES = 10; // Maximum entries per category before summarization\n\n                // Process the new knowledge entry\n                const timestamp = dayjs().toISOString();\n                const newEntry = {\n                    timestamp,\n                    content: knowledge.trim(),\n                    category: extractCategory(knowledge)\n                };\n\n                // Parse existing memory into structured format\n                const existingMemories = parseMemories(project.aiGeneratedMemory || '');\n\n                // Add new entry\n                existingMemories.push(newEntry);\n\n                // Manage memory size through categorization and summarization\n                const managedMemories = manageMemorySize(existingMemories, MAX_MEMORY_CHARS, MAX_CATEGORY_ENTRIES);\n\n                // Convert back to string format\n                const updatedMemory = memoriesToString(managedMemories);\n\n                // Update the project with the new memory\n                creditUsageTracker.trackOperation(\"add_ai_memory\", 1);\n                await updateProject({id: projectId, aiGeneratedMemory: updatedMemory});\n                return \"Memory saved and optimized\"\n            } catch (e: any) {\n                console.log('Error while saving memory', e);\n                return `Failed to save memory: ${e.message}`\n            }\n        }\n    });\n};", "startLine": 12, "endLine": 83, "type": "util", "symbols": ["addAiMemory"], "score": 0.9, "context": "This snippet defines the addAiMemory tool which integrates with the streaming system via dataStream and creditUsageTracker, showing how it executes and updates project memory, relevant for tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/query-codebase.ts", "content": "export const queryCodebase = ({\n                                  projectId,\n                                  files,\n                                  fileManager,\n                                  messageId = '',\n                                  userMessage = ''\n                              }: { projectId: string, files: FileItem[], fileManager: FileLineManager, messageId?: string, userMessage?: string }) => {\n    return tool({\n        description: 'Query the codebase to understand its structure, relationships, and relevant files for a specific task. ' +\n            'Use this instead of requesting individual files. ' +\n            'You can exclude specific files from the analysis by providing an array of file paths in the excludedFiles parameter. ' +\n            'Use filePath parameter to retrieve a SINGLE file in full ONLY when absolutely necessary. You are LIMITED to ONE file per request.',\n        parameters: QueryCodebaseSchema,\n        execute: async ({query, excludedFiles, reason}: z.infer<typeof QueryCodebaseSchema>) => {\n            console.log('Query:', query)\n            console.log('Reason:', reason)\n            console.log('Excluded files:', excludedFiles?.join())\n\n            if(!query) {\n                return {\n                    result: null,\n                    message: \"One of query or filePath is required. To look at the holistic view, use query and to look at a specific file, use filePath.\"\n                }\n            }\n            // Check if this is a \"continue\" message\n            const isContinueMessage = userMessage.toLowerCase().trim().startsWith('continue');\n\n            // Get the tool tracker instance\n            const toolTracker = ToolCountTracker.getInstance();\n\n            console.log('Tool call allowed (queryCodebase):', toolTracker.shouldAllowToolCall(messageId, 'queryCodebase'))\n\n            // Check if we should allow this tool call\n            if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'queryCodebase')) {\n                return {\n                    result: null,\n                    message: \"⚠️ Tool call limit reached. You've already made multiple codebase queries. Please implement or summarize with the information you already have.\",\n                };\n            }\n\n            // Increment the tool count if we have a chat ID\n            if (messageId) {\n                toolTracker.incrementToolCount(messageId, 'queryCodebase');\n            }\n            try {\n                if (!files || files.length === 0) {\n                    throw new Error('No files available for context engine');\n                }\n\n                // Get project information if available\n                let project: Project | null = null;\n                if (projectId) {\n                    try {\n                        project = await getProjectById({id: projectId});\n                    } catch (error) {\n                        console.warn('Could not fetch project information:', error);\n                        // Continue without project info\n                    }\n                }\n\n                if (!project) {\n                    throw new Error('No project information available');\n                }\n\n                // Check if we're requesting a specific file in full\n                // Note: filePath takes precedence over semantic query when both are provided\n                // if (filePath) {\n                //     console.log('Specific file requested:', filePath);\n                //\n                //     // Find the requested file\n                //     const fileItem = fileManager.getFileItems().find(file => file.name === filePath.trim());\n                //\n                //     if (!fileItem) {\n                //         throw new Error(`The requested file was not found: ${filePath}`);\n                //     }\n                //\n                //     const fileItems = [fileItem];\n                //\n                //     // Create snippets for each file\n                //     const snippets = fileItems.map(fileItem => ({\n                //         filePath: fileItem.name,\n                //         content: fileItem.content || '',\n                //         startLine: 1,\n                //         endLine: (fileItem.content || '').split('\\n').length,\n                //         type: 'unknown',\n                //         symbols: [],\n                //         score: 1.0\n                //     }));\n                //\n                //     // Create a simplified result with just the full file contents\n                //     const result = {\n                //         query,\n                //         snippets,\n                //         codebaseStructure: { components: [], screens: [], contexts: [], hooks: [], utils: [], types: [], configs: [] },\n                //         relationships: { imports: {}, exports: {}, dependencies: {}, dependents: {} }\n                //     };\n                //\n                //     let message = `Showing full content of the requested file: ${fileItems[0].name}`;\n                //\n                //     return {\n                //         result,\n                //         message,\n                //         toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')\n                //     };\n                // }\n                \n                // Initialize the context engine with the current files and project\n                const contextEngine = new ContextEngine(fileManager.getFileItems(), project);\n\n                // Default excluded files for Expo Snack projects if not provided\n                const defaultExcludedFiles = [\n                    // Large asset files\n                    'assets/',\n                    '.mp3',\n                    '.mp4',\n                    '.jpg',\n                    '.png',\n                    '.gif',\n                    // Generated type files\n                    'types/generated',\n                    // These don't exist in Expo Snack but kept as fallbacks\n                    'node_modules',\n                    'dist'\n                ];\n\n                // Combine user-provided excludedFiles with defaults\n                const combinedExcludedFiles = excludedFiles ?\n                    [...excludedFiles, ...defaultExcludedFiles] :\n                    defaultExcludedFiles;\n\n                // Query the context engine\n                const result = await contextEngine.query(query, reason, combinedExcludedFiles);\n\n                // Prepare a more informative response\n                let message = `Found ${result.snippets?.length} snippets for your query.`;\n\n                // Add information about excluded files if any\n                if (excludedFiles && excludedFiles.length > 0) {\n                    message += `\\n${excludedFiles.length} files were excluded from the analysis.`;\n                }\n\n                if (result.actionPlan) {\n                    message += `\\n\\nAction Plan: ${result.actionPlan.summary}\\nComplexity: ${result.actionPlan.complexity}\\nSteps: ${result.actionPlan.steps.length}`;\n                }\n\n                // if (result.supabaseSchema) {\n                //     message += `\\n\\nSupabase integration detected with ${result.supabaseSchema.tables.length} tables.`;\n                // }\n\n                return {\n                    result,\n                    message,\n                    toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'queryCodebase')\n                };\n            } catch (error) {\n                console.error('Context engine tool error:', error);\n                throw new Error(`Failed to query context engine: ${error instanceof Error ? error.message : 'Unknown error'}`);\n            }\n        },\n    });\n}", "startLine": 23, "endLine": 183, "type": "util", "symbols": ["queryCodebase"], "score": 0.9, "context": "This snippet defines the queryCodebase tool which queries the codebase and integrates with the streaming system, including tool call limits and context engine usage, relevant for tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/query-supabase-context.ts", "content": "export const querySupabaseContext = ({\n    projectId,\n    messageId = '',\n}: {\n    projectId: string, \n    messageId?: string, \n}) => {\n    return tool({\n        description: 'Query Supabase resources or execute SQL (either one at a time) to understand the project tables, dbFunctions, triggers, functions, RLS policies, storage buckets, and secrets. ' +\n            'Use this instead of requesting full Supabase instructions. Issue queries that are as specific as possible. ' +\n            'You can exclude specific resources using the excludedResources parameter in the format \"resourceType.resourceName\" or use wildcards like \"table.*\". ' +\n            'This tool uses a two-stage approach to efficiently retrieve only the relevant Supabase resources. ' +\n            'If the initial query returns incomplete information, the tool may indicate mustRetrieveAdditionalResources=true with guidance on what additional resources to request in a follow-up query. ' +\n            'You can also execute SELECT SQL queries directly against the database using the executeSQL parameter. Only SELECT queries are allowed for security reasons. When executing a SQL query, code exploration will not happen.',\n        parameters: QuerySupabaseContextSchema,\n        execute: async ({query, excludedResources, executeSQL, reason}: z.infer<typeof QuerySupabaseContextSchema>) => {\n            try {\n                console.log('Supabase Query:', query);\n                console.log('Excluded Resources:', excludedResources || []);\n                console.log('SQL Query:', executeSQL || 'None');\n                console.log('Reason:', reason);\n\n                // Validate inputs\n                if (!query && !executeSQL) {\n                    return {\n                        result: null,\n                        message: \"Either a natural language query or SQL query is required.\"\n                    };\n                }\n\n                // Get the tool tracker instance\n                const toolTracker = ToolCountTracker.getInstance();\n\n                console.log('Tool call allowed (querySupabaseContext):', toolTracker.shouldAllowToolCall(messageId, 'querySupabaseContext'));\n\n                // Check if we should allow this tool call\n                if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'querySupabaseContext')) {\n                    return {\n                        result: null,\n                        message: \"⚠️ Tool call limit reached. You've already made multiple Supabase context queries. Please implement or summarize with the information you already have.\",\n                    };\n                }\n\n                // Increment the tool count if we have a chat ID\n                if (messageId) {\n                    toolTracker.incrementToolCount(messageId, 'querySupabaseContext');\n                }\n\n                // Get project information\n                let project: Project | null = null;\n                try {\n                    if (!projectId) {\n                        return {\n                            result: null,\n                            message: \"Project ID is required\"\n                        };\n                    }\n                    \n                    project = await getProjectById({id: projectId});\n                    if (!project) {\n                        return {\n                            result: null,\n                            message: `Project not found: ${projectId}`\n                        };\n                    }\n                    \n                    // Check if project is connected to Supabase\n                    if (!project.connectionId || !project.supabaseProjectId) {\n                        return {\n                            result: null,\n                            message: 'This project is not connected to Supabase'\n                        };\n                    }\n                } catch (error) {\n                    console.error('Error fetching project:', error);\n                    return {\n                        result: null,\n                        message: `Failed to fetch project information: ${error instanceof Error ? error.message : 'Unknown error'}`\n                    };\n                }\n                \n                // Handle SQL execution if provided\n                if (executeSQL) {\n                    console.log('Executing SQL query:', executeSQL);\n                    \n                    // Validate that it's a SELECT query only\n                    const sqlLower = executeSQL.trim().toLowerCase();\n                    if (!sqlLower.startsWith('select ')) {\n                        return {\n                            result: null,\n                            message: '⚠️ Only SELECT queries are allowed for security reasons. Your query was rejected.'\n                        };\n                    }\n                    \n                    // Check for potentially harmful statements\n                    const disallowedKeywords = ['insert', 'update', 'delete', 'drop', 'alter', 'create', 'truncate'];\n                    if (disallowedKeywords.some(keyword => sqlLower.includes(keyword))) {\n                        return {\n                            result: null,\n                            message: '⚠️ SQL query contains disallowed keywords. Only pure SELECT queries are permitted. Your query was rejected.'\n                        };\n                    }\n                    \n                    try {\n                        // Execute the SQL query\n                        const supabaseProvider = new SupabaseIntegrationProvider();\n                        const result = await supabaseProvider.executeSQL({\n                            query: executeSQL,\n                            projectId\n                        });\n                        \n                        // Truncate the result if it's too large\n                        const resultStr = JSON.stringify(result || {}, null, 2);\n                        const MAX_LENGTH = 3000;\n                        \n                        let sqlResult;\n                        let message = '📊 SQL query executed successfully.';\n                        \n                        if (resultStr.length > MAX_LENGTH) {\n                            sqlResult = {\n                                data: result?.data,\n                                truncated: true,\n                                originalLength: resultStr.length,\n                                message: `SQL result was truncated from ${resultStr.length} to ${MAX_LENGTH} characters`\n                            };\n                            message += ` Results were truncated (${sqlResult.message}).`;\n                        } else {\n                            sqlResult = result;\n                        }\n                        \n                        return {\n                            result: { sqlResult },\n                            message,\n                            toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'querySupabaseContext')\n                        };\n                    } catch (error) {\n                        console.error('SQL execution error:', error);\n                        return {\n                            result: null,\n                            message: `⚠️ Failed to execute SQL query: ${error instanceof Error ? error.message : 'Unknown error'}`\n                        };\n                    }\n                } else {\n                    // Only query the context engine if no SQL was provided\n                    try {\n                        // Initialize the Supabase context engine\n                        const contextEngine = new SupabaseContextEngine(project);\n\n                        // Query the Supabase context engine\n                        const result = await contextEngine.getRelevantSupabaseResources(query, excludedResources);\n\n                        // Prepare a more informative response\n                        const resourceCount = result.resources?.length || 0;\n                        let message = `Found ${resourceCount} relevant Supabase resources for your query.`;\n\n                        // Add information about resource types\n                        const resourceTypes = result.resources?.map(r => r.resourceType) || [];\n                        const uniqueTypes = [...new Set(resourceTypes)];\n                        \n                        if (uniqueTypes.length > 0) {\n                            message += ` Types: ${uniqueTypes.join(', ')}.`;\n                        }\n                        \n                        // Add information about excluded resources if any\n                        if (excludedResources && excludedResources.length > 0) {\n                            message += `\\n${excludedResources.length} resources were excluded from the analysis.`;\n                        }\n                        \n                        // Add information about additional resources if needed\n                        if (result.hasAdditional) {\n                            message += `\\nThere are additional resources that may be relevant but weren't included due to the result limit.`;\n                            \n                            if (result.mustRetrieveAdditionalResources) {\n                                message += `\\n\\n⚠️ IMPORTANT: You MUST make another query to retrieve critical additional resources. ${result.additionalResourcesSummary}`;\n                            } else if (result.additionalResourcesSummary) {\n                                message += `\\n\\nConsider querying for: ${result.additionalResourcesSummary}`;\n                            }\n                        }\n                        \n                        return {\n                            result,\n                            message,\n                            toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'querySupabaseContext')\n                        };\n                    } catch (error) {\n                        console.error('Supabase context engine error:', error);\n                        return {\n                            result: null,\n                            message: `⚠️ Failed to query Supabase context engine: ${error instanceof Error ? error.message : 'Unknown error'}`\n                        };\n                    }\n                }\n            } catch (error) {\n                // Global error handler - ensures we never throw errors\n                console.error('Unexpected error in querySupabaseContext:', error);\n                return {\n                    result: null,\n                    message: `⚠️ An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`\n                };\n            }\n        },\n    });\n};", "startLine": 23, "endLine": 225, "type": "util", "symbols": ["querySupabaseContext"], "score": 0.9, "context": "This snippet defines the querySupabaseContext tool which queries Supabase resources or executes SQL, integrates with streaming and tool call limits, showing tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/manage-supabase-auth.ts", "content": "export const manageSupabaseAuth = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n  project: Project;\n}) => {\n  return tool({\n    description: 'Manages Supabase auth configuration. Use this tool to view current auth settings, update auth configuration, or retrieve SSO provider information. This helps with configuring authentication options like email templates, redirect URLs, and external OAuth providers.',\n    parameters: z.object({\n      action: z.enum([\n        'getAuthConfig',\n        'updateAuthConfig',\n        'getSSOProviders'\n      ]).describe('The action to perform on the auth configuration'),\n      authConfig: z.record(z.any()).optional().describe('The auth configuration to update (only required for updateAuthConfig action). Same format as the response from getAuthConfig. Pass only the fields to update.'),\n      reason: z.string().describe(\"Describe why you need to access or modify the auth configuration\")\n    }),\n    execute: async ({ action, authConfig, reason }: { \n      action: 'getAuthConfig' | 'updateAuthConfig' | 'getSSOProviders',\n      authConfig?: UpdateProjectAuthConfigRequestBody,\n      reason: string \n    }) => {\n      try {\n        console.log(`Performing Supabase auth action: ${action}. Reason: ${reason}.`);\n        if(authConfig) {\n          console.log('Updating', authConfig)\n        }\n        \n        if (!project.connectionId || !project.supabaseProjectId) {\n          throw new Error('Project is not linked to a Supabase project');\n        }\n        \n        // Create a new instance of the SupabaseIntegrationProvider\n        const supabaseIntegrationProvider = new SupabaseIntegrationProvider();\n        \n        let result;\n        \n        // Perform the requested action\n        switch (action) {\n          case 'getAuthConfig':\n            result = await supabaseIntegrationProvider.getAuthConfig(project.id);\n            break;\n          case 'updateAuthConfig':\n            if (!authConfig) {\n              throw new Error('Auth configuration is required for updateAuthConfig action');\n            }\n            result = await supabaseIntegrationProvider.updateAuthConfig(project.id, authConfig);\n            break;\n          case 'getSSOProviders':\n            result = await supabaseIntegrationProvider.getSSOProviders(project.id);\n            break;\n        }\n        \n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n        \n        // Format the response\n        return JSON.stringify({\n          action,\n          result,\n          timestamp: new Date().toISOString(),\n          comment: getActionComment(action, result)\n        });\n      } catch (e: any) {\n        console.error(`Error while performing Supabase auth action: ${action}`, e);\n        return JSON.stringify({\n          error: e.message,\n          action,\n          timestamp: new Date().toISOString(),\n          comment: `Error while performing Supabase auth action ${action}: ${e.message}. Please try again or check your Supabase connection.`\n        });\n      }\n    }\n  });\n};", "startLine": 13, "endLine": 90, "type": "util", "symbols": ["manageSupabaseAuth"], "score": 0.9, "context": "This snippet defines the manageSupabaseAuth tool which manages Supabase auth configuration, integrates with streaming and credit usage tracking, relevant for tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/get-client-logs.ts", "content": "export const getClientLogs = ({\n  dataStream,\n  creditUsageTracker,\n  logs,\n  messageId\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n  logs: LogEntry[];\n  messageId: string;\n}) => {\n  return tool({\n    description: 'Gets client-side logs to help debug problems with the application. This will return the most recent logs (maximum 20) from the current session. You can filter by log type and source. [CRITICAL INSTRUCTION]: DO NOT CALL THIS TOOL MULTIPLE TIMES IN A ROW. The logs will be EXACTLY THE SAME each time you call this tool until the user interacts with the app again. Calling this tool multiple times is useless and wasteful. WAIT for the user to tell you they have tried your suggestion before calling this tool again. If you have already called this tool once, DO NOT call it again until the user confirms they have interacted with the app.',\n    parameters: z.object({\n      type: z.enum(['error', 'warn', 'info', 'log', 'debug', 'all']).default('all')\n        .describe('The type of logs to fetch. Use \"all\" to get logs of all types.'),\n      source: z.enum(['console', 'network', 'snack', 'all']).default('all')\n        .describe('The source of logs to fetch. Use \"all\" to get logs from all sources.'),\n      limit: z.number().min(1).max(20).default(10)\n        .describe('Maximum number of log entries to return (1-20)'),\n      reason: z.string()\n        .describe(\"Describe why you need these logs for debugging\")\n    }),\n    execute: async ({ type, source, limit, reason }: { \n      type: 'error' | 'warn' | 'info' | 'log' | 'debug' | 'all',\n      source: 'console' | 'network' | 'snack' | 'all',\n      limit: number,\n      reason: string \n    }) => {\n      try {\n\n        const toolTracker = ToolCountTracker.getInstance();\n        console.log('Tool call allowed (getClientLogs):', toolTracker.shouldAllowToolCall(messageId, 'getClientLogs'))\n\n        // Check if we should allow this tool call\n        if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'getClientLogs')) {\n          return {\n            result: null,\n            message: `⚠️ Tool call limit reached. \n            Client logs do not refresh while you are working on the code. You need to stop and ask the user to interact with the app in the preview to get new logs.\n            DO NOT CLAIM THAT YOU HAVE SUCCESSFULLY updated/fixed/created a task until you get a confirmation using logs. ASK the user to test the app and them tell them to confirm if the feature worked or not. \n            THEY are NON technical, they can't see the logs or give you the logs. HELP THEM, DO NOT CONFUSE or MISGUIDE THEM.\n            `,\n          };\n        }\n        console.log(`Fetching client logs. Type: ${type}, Source: ${source}, Limit: ${limit}, Reason: ${reason}`);\n\n        // Increment the tool count if we have a chat ID\n        if (messageId) {\n          toolTracker.incrementToolCount(messageId, 'getClientLogs');\n        }\n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n\n        // Filter logs based on parameters\n        let filteredLogs = [...logs];\n        \n        // Filter by type if not 'all'\n        if (type !== 'all') {\n          filteredLogs = filteredLogs.filter(log => log.type.toLowerCase() === type.toLowerCase());\n        }\n        \n        // Filter by source if not 'all'\n        if (source !== 'all') {\n          filteredLogs = filteredLogs.filter(log => log.source === source);\n        }\n        \n        // Sort by timestamp (newest first)\n        filteredLogs.sort((a, b) => b.timestamp - a.timestamp);\n        \n        // Limit the number of logs\n        filteredLogs = filteredLogs.slice(0, Math.min(limit, 20));\n        \n        // Format logs for readability and truncate large messages\n        const formattedLogs = filteredLogs.map(log => ({\n          timestamp: dayjs(log.timestamp).toISOString(),\n          type: log.type,\n          source: log.source,\n          message: truncateLogMessage(log.message)\n        }));\n        \n        // Check for critical issues in the logs\n        const criticalIssues = detectCriticalIssues(logs);\n        \n        return JSON.stringify({\n          logs: formattedLogs,\n          count: formattedLogs.length,\n          totalAvailable: logs.length,\n          criticalIssues: criticalIssues.length > 0 ? criticalIssues : undefined,\n          timestamp: new Date().toISOString(),\n          comment: formattedLogs.length > 0 \n            ? `These are the ${formattedLogs.length} most recent logs matching your criteria. Use them to diagnose any issues you're experiencing.`\n            : `No logs found matching your criteria. Try broadening your search or check if the issue might be elsewhere.`\n        });\n      } catch (e: any) {\n        console.error(`Error while fetching client logs`, e);\n        return JSON.stringify({\n          error: e.message,\n          timestamp: new Date().toISOString(),\n          comment: `Error while fetching client logs: ${e.message}. Please try again with different parameters.`\n        });\n      }\n    }\n  });\n};", "startLine": 13, "endLine": 117, "type": "util", "symbols": ["getClientLogs"], "score": 0.9, "context": "This snippet defines the getClientLogs tool which fetches client-side logs, integrates with streaming and tool call limits, relevant for tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/get-file-contents.ts", "content": "export const getFileContents = ({\n                                    fileManager,\n                                    dataStream,\n                                    creditUsageTracker\n                                }: {\n    fileManager: FileLineManager,\n    dataStream: DataStreamWriter,\n    creditUsageTracker: CreditUsageTracker\n}) => {\n    return tool({\n        description: 'Get complete file content or list directory contents when queryCodebase snippets are insufficient. Use sparingly - only when you need to see the full file structure, all imports/exports, or complete implementation details that snippets miss.',\n        parameters: z.object({\n            path: z.string().describe(\"File or directory path to retrieve. For files: must match exactly a file from the project. For directories: use folder path like 'components' or 'screens' to list contents. Use '*' to see all top-level directories and files.\"),\n            lineRange: z.array(z.number()).length(2).optional().describe(\"Optional [start, end] line range (1-based, inclusive). Use [start, -1] for start to end of file. Only applicable for files.\"),\n            searchPattern: z.string().optional().describe(\"Optional regex pattern to search within the file. Shows matching lines with context. Only applicable for files.\"),\n            reason: z.string().describe(\"Why you need the full file content or directory listing instead of using queryCodebase snippets.\")\n        }),\n        execute: async ({path, lineRange, searchPattern, reason}: {\n            path: string,\n            lineRange?: [number, number],\n            searchPattern?: string,\n            reason: string\n        }) => {\n            try {\n                const files = fileManager.getFileItems();\n                // Normalize path - handle both /libs and libs formats\n                const normalizedPath = path.startsWith('/') ? path.slice(1) : path;\n\n                // Check if this is a directory listing request\n                const isDirectoryRequest = !normalizedPath.includes('.') || normalizedPath.endsWith('/') || normalizedPath === '*';\n\n                if (isDirectoryRequest) {\n                    // Directory listing\n                    const dirPath = normalizedPath === '*' ? '' : normalizedPath.replace(/\\/$/, ''); // Remove trailing slash\n\n                    // Find all files that start with this directory path\n                    const matchingFiles = files.filter(f => {\n                        const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                        if (dirPath === '') {\n                            // For root or * pattern, include all files\n                            return true;\n                        }\n                        return fileName.startsWith(dirPath + '/');\n                    });\n\n                    if (matchingFiles.length === 0) {\n                        // Show available top-level directories\n                        const topLevelDirs = new Set<string>();\n                        files.forEach(f => {\n                            const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                            const firstSlash = fileName.indexOf('/');\n                            if (firstSlash > 0) {\n                                topLevelDirs.add(fileName.substring(0, firstSlash));\n                            }\n                        });\n\n                        return `Directory ${path} not found. Available directories: ${Array.from(topLevelDirs).join(', ')}`;\n                    }\n\n                    // Group files by subdirectory with depth 2 support\n                    const structure = new Map<string, { files: string[], subdirs: Map<string, string[]> }>();\n\n                    matchingFiles.forEach(f => {\n                        const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;\n                        const relativePath = fileName.substring(dirPath.length + (dirPath ? 1 : 0));\n                        const pathParts = relativePath.split('/');\n\n                        if (pathParts.length === 1) {\n                            // Direct file in this directory\n                            if (!structure.has('.')) structure.set('.', { files: [], subdirs: new Map() });\n                            structure.get('.')!.files.push(pathParts[0]);\n                        } else if (pathParts.length === 2) {\n                            // File in immediate subdirectory (depth 1)\n                            const subDir = pathParts[0];\n                            if (!structure.has(subDir)) structure.set(subDir, { files: [], subdirs: new Map() });\n                            structure.get(subDir)!.files.push(pathParts[1]);\n                        } else if (pathParts.length >= 3) {\n                            // File in nested subdirectory (depth 2+)\n                            const subDir = pathParts[0];\n                            const nestedSubDir = pathParts[1];\n                            if (!structure.has(subDir)) structure.set(subDir, { files: [], subdirs: new Map() });\n\n                            const subDirData = structure.get(subDir)!;\n                            if (!subDirData.subdirs.has(nestedSubDir)) {\n                                subDirData.subdirs.set(nestedSubDir, []);\n                            }\n                            subDirData.subdirs.get(nestedSubDir)!.push(pathParts.slice(2).join('/'));\n                        }\n                    });\n\n                    // Format directory listing\n                    const displayPath = path === '*' ? 'Project Root' : path;\n                    let listing = `Directory: ${displayPath} (${matchingFiles.length} items)\\n\\n`;\n\n                    // Show subdirectories first\n                    const sortedKeys = Array.from(structure.keys()).sort((a, b) => {\n                        if (a === '.') return 1; // Files last\n                        if (b === '.') return -1;\n                        return a.localeCompare(b);\n                    });\n\n                    sortedKeys.forEach(key => {\n                        if (key === '.') {\n                            // Direct files\n                            const directFiles = structure.get(key)!.files.sort();\n                            directFiles.forEach(file => {\n                                const fullPath = dirPath ? `${dirPath}/${file}` : file;\n                                listing += `📄 ${file} (${fullPath})\\n`;\n                            });\n                        } else {\n                            // Subdirectory\n                            const subDirData = structure.get(key)!;\n                            const subDirPath = dirPath ? `${dirPath}/${key}` : key;\n                            const totalFiles = subDirData.files.length +\n                                Array.from(subDirData.subdirs.values()).reduce((sum, files) => sum + files.length, 0);\n\n                            listing += `📁 ${key}/ (${totalFiles} files) - ${subDirPath}/\\n`;\n\n                            // Show files in this subdirectory\n                            subDirData.files.sort().forEach(file => {\n                                listing += `  📄 ${file} (${subDirPath}/${file})\\n`;\n                            });\n\n                            // Show nested subdirectories (depth 2)\n                            const sortedSubDirs = Array.from(subDirData.subdirs.keys()).sort();\n                            sortedSubDirs.forEach(nestedDir => {\n                                const nestedFiles = subDirData.subdirs.get(nestedDir)!;\n                                const nestedPath = `${subDirPath}/${nestedDir}`;\n                                listing += `  📁 ${nestedDir}/ (${nestedFiles.length} files) - ${nestedPath}/\\n`;\n\n                                // Show first few files in nested directory\n                                const filesToShow = nestedFiles.slice(0, 3);\n                                filesToShow.forEach(file => {\n                                    listing += `    📄 ${file} (${nestedPath}/${file})\\n`;\n                                });\n\n                                if (nestedFiles.length > 3) {\n                                    listing += `    ... and ${nestedFiles.length - 3} more files\\n`;\n                                }\n                            });\n                        }\n                    });\n\n                    return listing;\n                }\n\n                // File content request\n                const fileItem = files.find(f => f.name === normalizedPath || f.name === `/${normalizedPath}`);\n\n                if (!fileItem) {\n                    return `File ${path} not found. Available files: ${files.slice(0, 10).map(f => f.name).join(', ')}${files.length > 10 ? '...' : ''}`;\n                }\n\n                let content = fileItem.content;\n                const lines = content.split('\\n');\n                const totalLines = lines.length;\n\n                // Apply search pattern if specified\n                if (searchPattern) {\n                    try {\n                        const regex = new RegExp(searchPattern, 'gi');\n                        const matchingLines: string[] = [];\n                        const contextBefore = 3;\n                        const contextAfter = 3;\n\n                        let lastIncludedLine = -1;\n\n                        lines.forEach((line, index) => {\n                            if (regex.test(line)) {\n                                const startContext = Math.max(0, index - contextBefore);\n                                const endContext = Math.min(lines.length - 1, index + contextAfter);\n\n                                // Add gap indicator if there's a gap\n                                if (startContext > lastIncludedLine + 1) {\n                                    matchingLines.push('...');\n                                }\n\n                                // Add context and matching line\n                                for (let i = startContext; i <= endContext; i++) {\n                                    if (i > lastIncludedLine) {\n                                        const lineNumber = i + 1;\n                                        const prefix = i === index ? '>' : ' ';\n                                        matchingLines.push(`${prefix}${lineNumber.toString().padStart(4)}: ${lines[i]}`);\n                                        lastIncludedLine = i;\n                                    }\n                                }\n                            }\n                        });\n\n                        content = matchingLines.join('\\n');\n                    } catch (regexError) {\n                        return `Invalid regex pattern: ${searchPattern}`;\n                    }\n                } else if (lineRange) {\n                    // Apply line range if specified\n                    const [start, end] = lineRange;\n                    const startLine = Math.max(1, start) - 1; // Convert to 0-based\n                    const endLine = end === -1 ? lines.length : Math.min(end, lines.length);\n\n                    const selectedLines = lines.slice(startLine, endLine);\n                    content = selectedLines.map((line, index) => {\n                        const lineNumber = startLine + index + 1;\n                        return `${lineNumber.toString().padStart(4)}: ${line}`;\n                    }).join('\\n');\n                } else {\n                    // Return full file with line numbers\n                    content = lines.map((line, index) => {\n                        const lineNumber = index + 1;\n                        return `${lineNumber.toString().padStart(4)}: ${line}`;\n                    }).join('\\n');\n                }\n\n                creditUsageTracker.trackOperation('tool_call');\n\n                const resultLines = content.split('\\n').length;\n                const truncated = resultLines > 500;\n\n                if (truncated) {\n                    const truncatedLines = content.split('\\n').slice(0, 500);\n                    content = truncatedLines.join('\\n') + '\\n\\n<response clipped - file too long>';\n                }\n\n                return `File: ${path} (${totalLines} lines total, showing ${truncated ? '500' : resultLines} lines)\nReason: ${reason}\n${lineRange ? `Line range: ${lineRange[0]}-${lineRange[1] === -1 ? 'end' : lineRange[1]}` : ''}\n${searchPattern ? `Search pattern: ${searchPattern}` : ''}\n\n${content}`;\n\n            } catch (error: any) {\n                console.error('Error in getFileContents:', error);\n                return `Error retrieving file content: ${error.message}`;\n            }\n        }\n    });\n};", "startLine": 7, "endLine": 242, "type": "util", "symbols": ["getFileContents"], "score": 0.9, "context": "This snippet defines the getFileContents tool which retrieves file contents or directory listings, integrates with streaming and credit usage tracking, relevant for tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/get-supabase-logs.ts", "content": "export const getSupabaseLogs = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n  project: Project;\n}) => {\n  return tool({\n    description: 'Gets logs for a Supabase project by service type. Use this to help debug problems with your app. There are two ways to fetch edge function logs:\\n\\n1. By function name: Use `functionName: \"your-function-name\"` to fetch logs by the function name (easier for users to understand)\\n2. By function ID: Use `functionId: \"ce62b3db-daf3-44ca-b935-957570435829\"` if you know the specific ID\\n\\nThis will return logs from the last hour. If no logs are found, ask the user to test the functionality again to generate new logs, as only the user can trigger new function executions.',\n    parameters: z.object({\n      service: z.enum([\n        'api',\n        'branch-action',\n        'postgres',\n        'edge-function',\n        'auth',\n        'storage',\n        'realtime',\n      ]).describe('The service to fetch logs for'),\n      limit: z.number().optional().default(100).describe('Maximum number of log entries to return'),\n      functionId: z.string().optional().describe('Specific function ID to filter logs for a single edge function (only applicable when service is \"edge-function\"). If you know the ID, provide it directly.'),\n      functionName: z.string().optional().describe('Name of the function to fetch logs for (only applicable when service is \"edge-function\"). The tool will automatically find the matching function ID.'),\n      reason: z.string().describe(\"Describe why you need these logs for debugging\")\n    }),\n    execute: async ({ service, limit, functionId, functionName, reason }: { \n      service: 'api' | 'branch-action' | 'postgres' | 'edge-function' | 'auth' | 'storage' | 'realtime',\n      limit?: number,\n      functionId?: string,\n      functionName?: string,\n      reason: string \n    }) => {\n      try {\n        console.log(`Fetching Supabase ${service} logs. Reason: ${reason}. Limit: ${limit || 100}. ${functionId ? `Function ID: ${functionId}` : ''}${functionName ? `Function Name: ${functionName}` : ''}`);\n        \n        if (!project.connectionId || !project.supabaseProjectId) {\n          throw new Error('Project is not linked to a Supabase project');\n        }\n        \n        // Create a new instance of the SupabaseIntegrationProvider\n        const supabaseIntegrationProvider = new SupabaseIntegrationProvider();\n        \n        let resolvedFunctionId = functionId;\n        \n        // If functionName is provided but not functionId, try to find the function ID\n        if (service === 'edge-function' && functionName && !functionId) {\n          try {\n            // Get the functions list\n            const functionsResult = await supabaseIntegrationProvider.getProjectResources({\n              projectId: project.id,\n              resourceType: 'functions'\n            });\n            \n            // Find the function with the matching name\n            const functions = functionsResult?.functions || [];\n            const matchingFunction = functions.find(\n              (func: any) => func.name?.toLowerCase() === functionName.toLowerCase()\n            );\n            \n            if (matchingFunction) {\n              resolvedFunctionId = matchingFunction.id;\n              console.log(`Found function ID ${resolvedFunctionId} for function name ${functionName}`);\n            } else {\n              console.log(`Could not find function ID for function name ${functionName}`);\n            }\n          } catch (error) {\n            console.error('Error finding function ID from name:', error);\n          }\n        }\n        \n        // Use the getLogs method from SupabaseIntegrationProvider which uses SupabaseDebuggingTools\n        const logsData = await supabaseIntegrationProvider.getLogs({\n          projectId: project.id,\n          service,\n          limit: limit || 100,\n          functionId: resolvedFunctionId\n        });\n        \n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n\n        // For debugging\n        console.log('service', service);\n        console.log('logsData', logsData);\n        \n        // Format the response using the actual logs data from SupabaseIntegrationProvider\n        return JSON.stringify({\n          logs: logsData,\n          service,\n          ...(resolvedFunctionId && { functionId: resolvedFunctionId }),\n          ...(functionName && { functionName }),\n          timestamp: new Date().toISOString(),\n          comment: resolvedFunctionId\n            ? `These are the most recent logs for function ${functionName || ''} (ID: ${resolvedFunctionId}) from your Supabase project. Use them to diagnose any issues you're experiencing.`\n            : `These are the most recent ${service} logs from your Supabase project. Use them to diagnose any issues you're experiencing.`\n        });\n      } catch (e: any) {\n        console.error(`Error while fetching Supabase ${service} logs`, e);\n        return JSON.stringify({\n          error: e.message,\n          service,\n          ...(functionId && { functionId }),\n          ...(functionName && { functionName }),\n          timestamp: new Date().toISOString(),\n          comment: `Error while fetching Supabase ${service} logs: ${e.message}. Please try again or check your Supabase connection.`\n        });\n      }\n    }\n  });\n};", "startLine": 14, "endLine": 124, "type": "util", "symbols": ["getSupabaseLogs"], "score": 0.9, "context": "This snippet defines the getSupabaseLogs tool which fetches Supabase logs, integrates with streaming and credit usage tracking, relevant for tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/edit-file.tool.ts", "content": "export const editFileTool = ({\n                                 fileManager,\n                                 contentManager,\n                                 processImagePlaceholders,\n                                 processVideoPlaceholders,\n                                 dataStream,\n                                 creditUsageTracker,\n                                 projectId\n                             }: {\n    fileManager: FileLineManager,\n    contentManager: FileContentManager,\n    dataStream: DataStreamWriter,\n    processImagePlaceholders: (text: string) => Promise<string>,\n    processVideoPlaceholders: (text: string) => Promise<string>,\n    creditUsageTracker?: any,\n    projectId: string\n}) => {\n    return tool({\n        description: 'Edit an existing file using search and replace operations, similar to MO_DIFF. This eliminates line shift issues and is more reliable. IMPORTANT: When making multiple edits to the same file, batch them together in a single call to avoid conflicts. Always validate your edits with the TargetedValidator after making changes.',\n        parameters: fileEditSchema,\n        execute: async ({\n                            absolutePath,\n                            edits\n                        }: any) => {\n            try {\n                console.log('Editing file:', absolutePath);\n                console.log('Number of edits:', edits.length);\n\n                // Add checks to detect if the llm is giving a sql file or a invalid file not supported,\n                // we will return immediately without applying to course correct\n                const errors: string[] = VALIDATORS.reduce((acc, validator) => {\n                    if (validator.check(absolutePath, fileManager.hasFile(absolutePath) ? fileManager.getFinalContent(absolutePath): '')) {\n                        acc.push(validator.error);\n                    }\n                    return acc;\n                }, [] as string[]);\n\n                if (errors.length) {\n                    console.log('Errors found. Rejecting file edit', errors.join(','))\n                    return errors.join(',')\n                }\n\n                // Check if file exists in the file manager\n                let fileExists = true;\n                let currentContent: string | null = \"\";\n                try {\n                    currentContent = fileManager.getFinalContent(absolutePath);\n                } catch (e) {\n                    fileExists = false;\n                }\n\n                // Prevent editing existing SQL files - SQL migrations should never be edited\n                const isSqlMigration = absolutePath.toLowerCase().endsWith('.sql');\n                if (isSqlMigration && fileExists) {\n                    return `Cannot edit existing SQL migration ${absolutePath}. Please create a new migration file instead.`;\n                }\n\n                // If file doesn't exist, create it with empty content\n                if (!fileExists) {\n                    fileManager.addFiles(absolutePath, \"\");\n                    contentManager.setFileContent(absolutePath, \"\");\n                    console.log(`Created new file ${absolutePath} as it didn't exist`);\n                } else {\n                    // Ensure the content manager has the current content\n                    contentManager.setFileContent(absolutePath, currentContent);\n                }\n\n                // Create a diff meta object to use with the DiffApplicationService\n                const diffMeta: DiffMeta = {\n                    path: absolutePath,\n                    lang: path.extname(absolutePath).substring(1) || 'text',\n                    searches: [],\n                    replacements: [],\n                    currentPair: 0,\n                    lineCount: 0\n                };\n\n                // Process each edit as a search/replace pair\n                for (const edit of edits) {\n                    // Get the current content again as it might have changed\n                    currentContent = contentManager.getFileContent(absolutePath);\n\n                    // For new files with no content, handle differently\n                    if (!currentContent && edit.searchPattern === '') {\n                        diffMeta.searches.push('');\n                        diffMeta.replacements.push(edit.replacementContent);\n                        continue;\n                    }\n\n                    // Add the search/replace pair to the diff meta\n                    diffMeta.searches.push(edit.searchPattern);\n\n                    // If isAppend is true, we're inserting before the search pattern\n                    if (edit.isAppend) {\n                        diffMeta.replacements.push(edit.replacementContent + edit.searchPattern);\n                    } else {\n                        diffMeta.replacements.push(edit.replacementContent);\n                    }\n\n                    // Log the edit for debugging\n                    console.log(`Edit ${diffMeta.searches.length}: ${edit.description || 'No description'}`);\n                }\n\n                // Apply the diff using the content manager\n                console.log(`Applying ${diffMeta.searches.length} search/replace pairs to ${absolutePath}`);\n                const result = await contentManager.applyDiff(diffMeta, {bestEffort: true, filePath: absolutePath});\n\n                if (!result.success) {\n                    return `Error applying changes to ${absolutePath}: ${result.message}`;\n                }\n\n                // Get the updated content\n                let updatedContent: string | null = contentManager.getFileContent(absolutePath);\n\n                if (!updatedContent) {\n                    return `Error applying changes to ${absolutePath}: Something wrong with the replacement. Please ask the user <NAME_EMAIL> to get it fixed immediately.`;\n                }\n\n                // Update the file manager to keep it in sync\n                fileManager.replaceFile(absolutePath, updatedContent);\n\n                // Process placeholders\n                updatedContent = await processImagePlaceholders(updatedContent);\n                updatedContent = await processVideoPlaceholders(updatedContent);\n\n                // Update content after processing placeholders\n                contentManager.setFileContent(absolutePath, updatedContent);\n                fileManager.replaceFile(absolutePath, updatedContent);\n\n                // Validate the file for syntax errors\n                const fileName = path.basename(absolutePath);\n                const validationResult = TargetedValidator.validateFiles([{\n                    name: fileName,\n                    content: updatedContent\n                }]);\n\n                // Send validation results to the client\n                dataStream.writeData({\n                    type: 'validation-result',\n                    content: {\n                        path: absolutePath,\n                        isValid: validationResult.isValid,\n                        summary: validationResult.summary\n                    }\n                });\n\n                // Track credit usage if available\n                if (creditUsageTracker) {\n                    creditUsageTracker.trackOperation('file_change');\n                }\n\n                // Check if this is a SQL file and run the migration if it is\n                const isSqlFile = absolutePath.toLowerCase().endsWith('.sql');\n                let sqlResult = '';\n                let migrationStatus: 'success' | 'failed' = 'failed';\n                \n                // Save original content for potential rollback\n                const originalContent = fileExists ? fileManager.getFinalContent(absolutePath) : '';\n                \n                if (isSqlFile) {\n                    try {\n                        // Use the project ID passed to the tool\n                        if (projectId) {\n                            // Execute the SQL migration\n                            const supabase = new SupabaseIntegrationProvider();\n                            const result = await supabase.executeSQL({\n                                projectId,\n                                query: updatedContent,\n                            });\n\n                            sqlResult = `SQL migration executed successfully for ${absolutePath}. Result: ${JSON.stringify(result)}`;\n                            migrationStatus = 'success';\n                            console.log(sqlResult);\n                        } else {\n                            sqlResult = `Could not execute SQL migration: No project ID provided`;\n                            console.warn(sqlResult);\n                            migrationStatus = 'failed';\n                            \n                            // Rollback the file if migration failed due to missing project ID\n                            if (fileExists) {\n                                fileManager.rollbackFile(absolutePath, originalContent);\n                                contentManager.setFileContent(absolutePath, originalContent);\n                            } else {\n                                // For new files, just remove them\n                                fileManager.rollbackFile(absolutePath);\n                                contentManager.setFileContent(absolutePath, '');\n                            }\n                            \n                            return `SQL migration failed: ${sqlResult}. Changes to ${absolutePath} were not applied and the file has been rolled back.`;\n                        }\n                    } catch (error: any) {\n                        sqlResult = `Error executing SQL migration: ${error.message}`;\n                        migrationStatus = 'failed';\n                        console.error(sqlResult);\n                        \n                        // Rollback the file if migration failed due to an error\n                        if (fileExists) {\n                            fileManager.rollbackFile(absolutePath, originalContent);\n                            contentManager.setFileContent(absolutePath, originalContent);\n                        } else {\n                            // For new files, just remove them\n                            fileManager.rollbackFile(absolutePath);\n                            contentManager.setFileContent(absolutePath, '');\n                        }\n                        \n                        return `SQL migration failed: ${sqlResult}. Changes to ${absolutePath} were not applied and the file has been rolled back.`;\n                    }\n                }\n\n                // Send the file operation to the client\n                dataStream.writeData({\n                    type: 'file-operation',\n                    content: {\n                        type: fileExists ? 'edit' : 'create',\n                        absolutePath,\n                        content: updatedContent\n                    }\n                });\n\n                if (!validationResult.isValid) {\n                    return `Updated file ${absolutePath}, but found validation issues: ${validationResult.summary}`;\n                }\n\n                // Add SQL-specific success message\n                if (absolutePath.toLowerCase().endsWith('.sql')) {\n                    if (migrationStatus === 'failed') {\n                        return `File ${fileExists ? 'Updated' : 'Created'} SQL file ${absolutePath} successfully. Migration failed: ${sqlResult}`;\n                    }\n                    return `${fileExists ? 'Updated' : 'Created'} SQL file ${absolutePath} successfully and executed the migration. Migration ran successfully.`;\n                }\n\n                return `${fileExists ? 'Updated' : 'Created'} file ${absolutePath} successfully using search and replace!`;\n            } catch (e: any) {\n                console.log('Error while applying edits', e);\n                return `Error while applying changes. Please rethink and make the changes again. Error: ${e.message}`\n            }\n        }\n    })\n}", "startLine": 25, "endLine": 263, "type": "util", "symbols": ["editFileTool"], "score": 0.9, "context": "This snippet defines the editFileTool which edits files and integrates with streaming, credit usage tracking, and Supabase SQL execution, relevant for tool execution flow.", "includesImports": false}, {"filePath": "src/components/base/message.tsx", "content": "const PurePreviewMessage = ({\n                                projectId,\n                                chatId,\n                                message,\n                                vote,\n                                isLoading,\n                                setMessages,\n                                reload,\n                                setInput,\n                                isReadonly,\n                                isLastMessage = false,\n                                isLastUserMessage = false,\n                                status,\n                                setAttachments,\n                                append,\n                                onVersionClick,\n                                isVersionActive,\n                                onActionClick,\n                                setSnackError,\n                                removeActions,\n                                addToolResult\n                            }: {\n    projectId: string;\n    chatId: string;\n    message: Message;\n    vote: Vote | undefined;\n    isLoading: boolean;\n    setMessages: (\n        messages: Message[] | ((messages: Message[]) => Message[]),\n    ) => void;\n    setInput: (value: string) => void;\n    reload: (\n        chatRequestOptions?: ChatRequestOptions,\n    ) => Promise<string | null | undefined>;\n    isReadonly: boolean;\n    isLastMessage: boolean;\n    isLastUserMessage: boolean;\n    status: 'submitted' | 'streaming' | 'ready' | 'error';\n    append: (\n        message: Message | CreateMessage,\n        chatRequestOptions?: ChatRequestOptions,\n    ) => Promise<string | null | undefined>;\n    setAttachments?: (attachments: Attachment[]) => void;\n    onVersionClick: (messageId: string) => void;\n    isVersionActive?: boolean;\n    onActionClick: (action: ActionMeta) => void;\n    setSnackError: any;\n    removeActions: boolean\n    addToolResult?: (params: { toolCallId: string; result: any }) => void;\n}) => {\n    const {\n        fileStatuses,\n        sqlStatuses,\n        diffErrors,\n        thinkingStatus,\n        actionsStatus,\n        hiddenContentStatus,\n        cleanContent,\n        executeQuery\n    } = useContentParser(message, projectId, isLastMessage);\n    \n    // Filter out duplicate content from message.content that appears in message.parts\n    const filteredContent = useMemo(() => {\n        if(message.role === \"user\") {\n            return message.content;\n        }\n        if (!message.parts || message.parts.length === 0) {\n            return message.content;\n        }\n        \n        // Get text parts only\n        const textParts = message.parts\n            .filter(part => part.type === 'text' && 'text' in part)\n            .map(part => (part as { text: string }).text);\n        \n        if (textParts.length === 0) {\n            return message.content;\n        }\n        \n        // Sort parts by length (descending) to handle overlapping matches\n        textParts.sort((a, b) => b.length - a.length);\n        \n        // Create a filtered version of the content\n        let processedContent = message.content;\n        \n        // Remove each part from the content\n        for (const part of textParts) {\n            processedContent = processedContent.replace(part, '');\n        }\n        \n        // Clean up any double spaces and trim\n        return processedContent.replace(/\\s+/g, ' ').trim();\n    }, [message.content, message.parts]);\n\n    // Check if this is a validation message\n    // const isValidationMessage = message.role === 'system' && (\n    //     message.content?.includes('🔍') || message.content?.includes('✅')\n    // );\n\n    // Extract validation message type and content\n    // const validationType = message.content?.includes('🔍') ? 'validating' : 'validated';\n    // const validationContent = message.content?.replace(/^[🔍✅]\\s*/, '');\n    const { generatorStore } = useStores();\n    const [mode, setMode] = useState<'view' | 'edit'>('view');\n\n    const handleCtaSubmit = useCallback((prompt: string) => {\n        if (setInput) {\n            setInput(prompt);\n        }\n        // setMessages(messages => [\n        //   ...messages,\n        //   {\n        //     id: Math.random().toString(),\n        //     content: prompt,\n        //     role: 'user',\n        //   }\n        // ]);\n    }, [setInput]);\n\n\n    // If this is a validation message, render the ValidationMessage component\n    // if (isValidationMessage) {\n    //     return <ValidationMessage content={validationContent} type={validationType} />;\n    // }\n\n    // const isIntermediateToolMessage = !message.annotations?.length && message.toolInvocations?.length;\n\n    return (\n        <AnimatePresence>\n            <motion.div\n                className={`w-[98%] mx-auto rounded-xl px-8 group/message text-xs ${message.role === 'assistant' ? 'bg-gradient-to-br from-accent/5 to-accent/3 py-4' : ''}`}\n                initial={{y: 5, opacity: 0}}\n                animate={{y: 0, opacity: 1}}\n                data-role={message.role}\n            >\n                {message.role === 'assistant' && (\n                    <div className=\"flex items-center space-x-2 justify-start mb-2\">\n                        <div\n                            className=\"size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border bg-background\">\n                            <div className=\"translate-y-px\">\n                                <MagicallyLogo iconOnly logoWidthAction={20}/>\n                            </div>\n\n                        </div>\n                        <span className=\"text-md font-brand font-bold ml-2\">magically</span>\n                    </div>\n\n                )}\n                <div\n                    className={cn(\n                        'flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-full',\n                        {\n                            'w-full': mode === 'edit',\n                            'group-data-[role=user]/message:w-fit': mode !== 'edit',\n                        },\n                    )}\n                >\n\n\n                    <div className=\"flex flex-col gap-2 min-w-0 flex-1\">\n                        {message.experimental_attachments && (\n                            <div className=\"flex flex-row justify-end gap-2 flex-wrap\">\n                                {message.experimental_attachments.map((attachment) => (\n                                    <PreviewAttachment\n                                        key={attachment.url}\n                                        attachment={attachment}\n                                    />\n                                ))}\n                            </div>\n                        )}\n\n                        <ThinkingDisplay thinkingStatus={thinkingStatus}/>\n                        {message.content && mode === 'view' && (\n                            <div className=\"flex flex-row gap-2 items-start w-full\">\n                                {/*{message.role === 'user' && !isReadonly && isLastUserMessage && (*/}\n                                {/*    <Button*/}\n                                {/*        variant=\"ghost\"*/}\n                                {/*        className=\"px-2 h-fit rounded-full text-muted-foreground shrink-0\"*/}\n                                {/*        onClick={() => {*/}\n                                {/*            setMode('edit');*/}\n                                {/*        }}*/}\n                                {/*    >*/}\n                                {/*        <EditIcon/>*/}\n                                {/*    </Button>*/}\n                                {/*)}*/}\n\n                                <div\n                                    className={cn('flex flex-col gap-4 break-words w-full', {\n                                        'dark:bg-primary-foreground dark:text-black px-3 py-2 rounded-sm':\n                                            message.role === 'user',\n                                    })}\n                                >\n                                    {typeof message.content === 'string' && (\n                                        <ParsedContent\n                                            content={filteredContent}\n                                            message={message}\n                                            projectId={projectId}\n                                            isLastMessage={isLastMessage}\n                                            role={message.role as 'user' | 'assistant' | 'system'}\n                                        />\n                                    )}\n                                </div>\n                            </div>\n                        )}\n\n                        {/* Display tool calls from either message.parts or extracted tool calls */}\n                        {(message.parts && message.parts.length > 0) && (\n                            message.parts.map((part, index) => {\n                                switch (part.type) {\n\n\n                                    case \"reasoning\":\n                                        return (\n                                            <>\n                                                <pre className=\"text-wrap\">{JSON.stringify(part, null, 2)}</pre>\n                                                <ThinkingDisplay thinkingStatus={{ isActive: true, isComplete: true, content: part.reasoning, sections: [] } }/>\n                                            </>\n                                        )\n                                    case \"text\":\n                                        if(message.role === \"user\") {\n                                            return;\n                                        }\n                                        // if(message.content.includes(part.text)) {\n                                        //     return;\n                                        // }\n                                        return (\n                                            <div\n                                                className={cn('flex flex-col gap-4 break-words w-full')}\n                                            >\n                                                {/*<pre>{JSON.stringify(actionsStatus)}</pre>*/}\n                                                {/*<pre className=\"text-wrap\">{JSON.stringify(part, null, 2)}</pre>*/}\n                                                <ParsedContent\n                                                    content={part.text}\n                                                    message={{...message, id: message.id, content: part.text}}\n                                                    projectId={projectId}\n                                                    isLastMessage={isLastMessage}\n                                                    role={message.role as 'user' | 'assistant' | 'system'}\n                                                />\n                                            </div>\n                                        )\n\n                                    case \"tool-invocation\":\n\n                                        const {toolInvocation} = part;\n                                        const {state, toolCallId, toolName, args} = toolInvocation;\n\n                                        if (state === 'result') {\n                                            const {result} = toolInvocation;\n\n                                            return (\n                                                <div key={`${message.id}_${index}_${toolCallId}`}>\n                                                    {\n                                                        (toolName === 'getFileContents') ? (\n                                                            <GetFileContentsToolResult\n                                                                path={args.path || 'unknown'}\n                                                                reason={args.reason || 'File content requested'}\n                                                                isDirectory={!args.path?.includes('.') || args.path?.endsWith('/')}\n                                                            />\n                                                        ) : toolName === 'queryCodebase' ? (\n                                                            <QueryCodebaseToolResult query={args.query} excludedFiles={args.excludedFiles} result={args?.result} state={state}/>\n                                                        ) : toolName === 'editFile' ? (\n                                                            <EditFileToolResult absolutePath={args.absolutePath} result={args} state={state}/>\n                                                        ) : toolName === 'getSupabaseInstructions' ? (\n                                                            <GetSupabaseInstructionsToolResult reason={args.reason || 'Fetching Supabase schema'} state={state}/>\n                                                        ) : toolName === 'getSupabaseLogs' ? (\n                                                            <GetSupabaseLogsToolResult reason={args.reason || 'Fetching Supabase logs'} service={args.service} state={'complete'} result={result}/>\n                                                         ) : toolName === 'querySupabaseContext' ? (\n                                                            <QuerySupabaseContextToolResult query={args?.query || 'Fetching Supabase resources'} state={'result'} result={result}/>\n                                                         ) : toolName === 'searchWeb' ? (\n                                                             <SearchWebToolResult query={args?.query || ''} reason={args?.reason || 'Searching the web'} state={'complete'} result={result}/>\n                                                         ) : toolName === 'generateDesign' ? (\n                                                             <DesignToolResult projectId={projectId} chatId={chatId} reason={args?.prompt || 'Design app screens'} state={'complete'} result={result}/>\n                                                          ) : toolName === 'getClientLogs' ? (\n                                                             <GetClientLogsToolResult reason={args.reason || 'Fetching client logs'} type={args.type} source={args.source} state={'complete'} result={result}/>\n                                                          ) : toolName === 'displayMultiPerspectiveAnalysis' ? (\n                                                             <MultiPerspectiveAnalysisToolResult projectId={projectId} chatId={chatId} reason={args?.reason || 'Multi-perspective analysis'} state={'complete'} result={result}/>\n                                                          ) : toolName === 'manageSupabaseAuth' ? (\n                                                             <ManageSupabaseAuthToolResult reason={args.reason || 'Managing Supabase auth'} action={args.action} state={'complete'} result={result}/>\n                                                          ) : toolName === 'clientTesting' ? (\n                                                             <InlineClientTestingToolResult\n                                                                 reason={args.reason || 'Testing application'}\n                                                                 state={'complete'}\n                                                                 chatId={chatId}\n                                                                 expectations={args.expectations}\n                                                                 featuresToTest={args.featuresToTest}\n                                                                 toolCallId={toolCallId}\n                                                                 addToolResult={addToolResult}\n                                                             />\n                                                          ) : (\n                                                            <></>\n                                                            // <pre>{JSON.stringify(result, null, 2)}</pre>\n                                                        )}\n                                                </div>\n                                            );\n                                        }\n                                        return (\n                                            <div\n                                                key={toolCallId}\n                                                className={cx({\n                                                    skeleton: ['getWeather', 'getFileContents', 'queryCodebase', 'editFile', 'getSupabaseInstructions', 'getSupabaseLogs', 'getClientLogs', 'manageSupabaseAuth'].includes(toolName),\n                                                })}\n                                            >\n                                                {toolName === 'getFileContents' ? (\n                                                    <GetFileContentsToolResult\n                                                        path={args?.path || 'unknown'}\n                                                        reason={args?.reason || 'File content requested'}\n                                                        isDirectory={!args?.path?.includes('.') || args?.path?.endsWith('/')}\n                                                    />\n                                                ) : toolName === 'queryCodebase' ? (\n                                                    <QueryCodebaseToolResult query={args?.query} excludedFiles={args?.excludedFiles} result={args?.result} state={state}/>\n                                                ) : toolName === 'editFile' ? (\n                                                    <EditFileToolResult absolutePath={args?.absolutePath} result={args} state={state}/>\n                                                ) : toolName === 'getSupabaseInstructions' ? (\n                                                    <GetSupabaseInstructionsToolResult reason={args?.reason || 'Fetching Supabase schema'} state={state}/>\n                                                ) : toolName === 'getSupabaseLogs' ? (\n                                                    <GetSupabaseLogsToolResult reason={args?.reason || 'Fetching Supabase logs'} service={args?.service} functionId={args?.functionId} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'}/>\n                                                ) : toolName === 'querySupabaseContext' ? (\n                                                    <QuerySupabaseContextToolResult query={args?.query || 'Fetching Supabase resources'} state={'call'} result={''}/>\n                                                ): toolName === 'searchWeb' ? (\n                                                     <SearchWebToolResult query={args?.query || ''} reason={args?.reason || 'Searching the web'} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>\n                                                 ) : toolName === 'generateDesign' ? (\n                                                     <DesignToolResult projectId={projectId} chatId={chatId} reason={args?.prompt || 'Design app screens'} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>\n                                                 ) : toolName === 'getClientLogs' ? (\n                                                    <GetClientLogsToolResult reason={args?.reason || 'Fetching client logs'} type={args?.type} source={args?.source} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>\n                                                ) : toolName === 'displayMultiPerspectiveAnalysis' ? (\n                                                    <MultiPerspectiveAnalysisToolResult projectId={projectId} chatId={chatId} reason={args?.reason || 'Multi-perspective analysis'} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>\n                                                ) : toolName === 'manageSupabaseAuth' ? (\n                                                    <ManageSupabaseAuthToolResult reason={args?.reason || 'Managing Supabase auth'} action={args?.action} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>\n                                                ):  toolName === 'clientTesting' ? (\n                                                    <ClientTestingToolResult\n                                                        reason={args?.reason || 'Testing application'}\n                                                        state={'loading'}\n                                                        chatId={chatId}\n                                                        expectations={args?.expectations}\n                                                        featuresToTest={args?.featuresToTest}\n                                                        toolCallId={toolCallId}\n                                                        addToolResult={addToolResult}\n                                                    />\n                                                ): null}\n                                            </div>\n                                        );\n                                        break;\n                                }\n                            })\n                        )}\n\n                        <FileStatusList\n                            files={fileStatuses}\n                            messageId={message.id}\n                            onFileClick={onVersionClick}\n                            isVersionActive={isVersionActive}\n                        />\n                        <SQLStatusList\n                            queries={sqlStatuses}\n                            onExecute={executeQuery}\n                            onError={(error) => setSnackError(error)}\n                            status={status}\n                        />\n\n                        {message.content && mode === 'edit' && (\n                            <div className=\"flex flex-row gap-2 items-start\">\n                                <div className=\"size-8\"/>\n\n                                <MessageEditor\n                                    key={message.id}\n                                    message={message}\n                                    setMode={setMode}\n                                    setInput={setInput}\n                                    setMessages={setMessages}\n                                    reload={reload}\n                                    chatId={chatId}\n                                />\n                            </div>\n                        )}\n\n\n\n\n                        {actionsStatus?.isActive && message.role === 'assistant' && (\n                            <ActionsDisplay actionsStatus={actionsStatus} append={append} status={status} onActionClick={onActionClick} />\n                        )}\n\n                        {\n                            !isLoading  ? <p className=\"text-[8px] mt-0\">{dayjs(message.createdAt).format('DD MMM YYYY, HH:mm a')}</p> : null\n                        }\n\n                        {!isReadonly && !removeActions  && (\n                            <MessageActions\n                                key={`action-${message.id}`}\n                                chatId={chatId}\n                                message={message}\n                                vote={vote}\n                                reload={reload}\n                                isLoading={isLoading}\n                                isLastMessage={isLastMessage}\n                                isLastUserMessage={isLastUserMessage}\n                                setMessages={setMessages}\n                                setMode={setMode}\n                                status={status}\n                                setInput={setInput}\n                                setAttachments={setAttachments}\n                                onVersionClick={onVersionClick}\n                            />\n                        )}\n\n                        {/* Integration Callout - show only for last assistant message when not loading and Supabase not connected */}\n                        {message.role === 'assistant' && isLastMessage && !isLoading && !isReadonly && (\n                            <IntegrationCallout\n                                projectId={projectId}\n                                chatId={chatId}\n                            />\n                        )}\n                        {/*<p>{(message as any)?.restorationId || message.id}</p>*/}\n\n\n\n                    </div>\n                </div>\n            </motion.div>\n        </AnimatePresence>\n    );\n};", "startLine": 50, "endLine": 471, "type": "component", "symbols": ["PurePreviewMessage"], "score": 0.9, "context": "This React component renders chat messages including tool call results for various chat tools, showing how tool execution results are integrated into the chat UI and streaming system.", "includesImports": false}, {"filePath": "src/lib/chat/tools/client-testing.tool.ts", "content": "export const clientTestingTool = ({\n                                      dataStream,\n                                      creditUsageTracker\n                                  }: {\n    dataStream: DataStreamWriter;\n    creditUsageTracker: CreditUsageTracker;\n}) => {\n    return tool({\n        description: 'After every complex step, ask the user to test the functionality.',\n        parameters: z.object({\n            featuresToTest: z.string().describe(\"What should the user test? Be concise, noon-technical and focussed\"),\n            expectations: z.string().describe(\"What is the expected outcome?\"),\n            reason: z.string().describe(\"Why do you need the user to test the app right now?\")\n        })\n        // No execute function - this enables human-in-the-loop pattern\n        // The tool result will be added via addToolResult when user completes testing\n    });\n};", "startLine": 10, "endLine": 27, "type": "util", "symbols": ["clientTestingTool"], "score": 0.8, "context": "This snippet defines the clientTestingTool chat tool which integrates with the streaming system via the dataStream parameter and uses a human-in-the-loop pattern without an execute function, relevant for understanding tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/get-supabase-instructions.ts", "content": "export const getSupabaseInstructions = ({\n  dataStream,\n  creditUsageTracker,\n  project\n}: {\n  dataStream: DataStreamWriter;\n  creditUsageTracker: CreditUsageTracker;\n  project: Project;\n}) => {\n  return tool({\n    description: 'Get Supabase project instructions. This provides you with the supabase specific guides and instructions to understand and plan how to integrate supabase into the project.',\n    parameters: z.object({\n      reason: z.string().describe(\"Describe why you need the Supabase instructions\")\n    }),\n    execute: async ({ reason }: { reason: string }) => {\n      try {\n        console.log(`Fetching Supabase instructions. Reason: ${reason}`);\n        \n        // Create a new instance of the SupabaseIntegrationProvider\n        const supabaseIntegrationProvider = new SupabaseIntegrationProvider();\n        \n        // Get the latest instructions for the chat\n        const result =  supabaseIntegrationProvider.getSupabaseInitialInstructions();\n        \n        // Track the operation for credit usage\n        creditUsageTracker.trackOperation('tool_call');\n        \n        return JSON.stringify({\n          instructions: result,\n          comment: 'Use this Supabase instruction to implement supabase features correctly. Call querySupabaseContext tool to fetch schema structure and proper RLS policies, secrets, functions, database functions. triggers.'\n        });\n      } catch (e: any) {\n        console.error('Error while fetching Supabase instructions', e);\n        return `Error while fetching Supabase instructions. Please try again. Error: ${e.message}`;\n      }\n    }\n  });\n};", "startLine": 12, "endLine": 49, "type": "util", "symbols": ["getSupabaseInstructions"], "score": 0.8, "context": "This snippet defines the getSupabaseInstructions tool which fetches Supabase project instructions, integrates with streaming and credit usage tracking, relevant for tool execution flow.", "includesImports": false}, {"filePath": "src/lib/chat/tools/create-file.tool.ts", "content": "export const createFileTool = ({\n                            fileManager,\n                            processImagePlaceholders,\n                            processVideoPlaceholders,\n                            dataStream\n                        }: {\n    fileManager: FileLineManager,\n    dataStream: DataStreamWriter,\n    processImagePlaceholders: (text: string) => Promise<string>,\n    processVideoPlaceholders: (text: string) => Promise<string>\n\n}) => {\n    return tool({\n        description: 'Create a new file',\n        parameters: fileCreateSchema,\n        execute: async ({absolutePath, content}) => {\n\n            // await updateFile({\n            //     files,\n            //     snackBaseId,\n            //     snackId,\n            //     absolutePath,\n            //     contents: content\n            // })\n\n            fileManager.addFiles(absolutePath, content);\n\n            content = await processImagePlaceholders(content);\n            content = await processVideoPlaceholders(content);\n\n            dataStream.writeData({\n                type: 'file-operation',\n                content: {\n                    type: 'create',\n                    absolutePath,\n                    content\n                }\n            });\n            return `Added file ${absolutePath} successfully!`;\n        }\n    })\n}", "startLine": 5, "endLine": 46, "type": "util", "symbols": ["createFileTool"], "score": 0.8, "context": "This snippet defines the createFileTool which creates new files and integrates with streaming and placeholder processing, relevant for tool execution flow.", "includesImports": false}], "additionalFiles": []}}