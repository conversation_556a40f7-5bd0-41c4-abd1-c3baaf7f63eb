{"timestamp": "2025-06-15T13:46:47.588Z", "query": "Find the credit reset bug causing the subscription to not reset properly. Show me the subscription management, billing system, and credit tracking code.", "executionTime": 17329, "snippetsCount": 8, "additionalFilesCount": 0, "totalLines": 311, "snippets": [{"filePath": "src/lib/subscription/credit-usage.ts", "type": "util", "context": "This function updates credit usage for a user subscription, including counting usage in the current billing cycle and updating the subscription record. It is directly related to credit reset and subscription management.", "score": 1, "lines": 58, "startLine": 12, "endLine": 69, "symbols": ["updateCreditUsage"], "preview": "export async function updateCreditUsage(userId: string, operations: CreditOperation[]) {\n    console.log(`[Messages] Updating message usage for user ${userId}`);\n    \n    // Get the latest active subscription\n    const subscription = await db\n..."}, {"filePath": "src/lib/db/schema.ts", "type": "unknown", "context": "The subscriptions table schema defines fields related to credits, creditsUsed, resetDate, and subscription status, which are critical for understanding credit reset and subscription management.", "score": 1, "lines": 39, "startLine": 378, "endLine": 416, "symbols": ["subscriptions table"], "preview": "export const subscriptions = pgTable('Subscription', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  userId: varchar('userId', { length: 255 }).notNull(),\n  status: varchar('status', { length: 50 }).notNull().default('inactive'),\n  planId: varchar('planId', { length: 50 }).notNull().default('free'),\n..."}, {"filePath": "src/lib/credit/CreditUsageTracker.ts", "type": "unknown", "context": "This class tracks credit operations and has a clear method to clear all tracked operations (clear), which is relevant to credit reset logic.", "score": 0.9, "lines": 76, "startLine": 9, "endLine": 84, "symbols": ["CreditUsageTracker"], "preview": "export class CreditUsageTracker {\n    private operations: CreditOperation[] = [];\n    private discountedOperations: DiscountedOperation[] = [];\n\n    /**\n..."}, {"filePath": "src/lib/db/token-consumption.queries.ts", "type": "util", "context": "Function to save token consumption records, including logic to decide if a discount should be applied, which affects credit usage and reset.", "score": 0.9, "lines": 37, "startLine": 36, "endLine": 72, "symbols": ["saveTokenConsumption"], "preview": "export async function saveTokenConsumption(data: TokenConsumptionInput): Promise<TokenConsumption> {\n  const {cacheDiscountPercent, cachingDiscount, inputCost, outputCost, subtotal, totalCost} = await getMessageDetailsFromOpenrouter(data);\n\n  let shouldDiscount = false;\n  if (data.errorId) {\n..."}, {"filePath": "src/lib/db/schema.ts", "type": "unknown", "context": "The tokenConsumption table tracks creditsConsumed, discountedCredits, and discount flags, which are relevant to credit tracking and billing.", "score": 0.8, "lines": 44, "startLine": 319, "endLine": 362, "symbols": ["tokenConsumption table"], "preview": "export const tokenConsumption = pgTable('TokenConsumption', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  model: varchar('model', { length: 50 }).notNull(),\n  totalTimeToken: real('totalTimeToken').notNull(),\n  promptTokens: integer('promptTokens').notNull(),\n..."}, {"filePath": "src/lib/db/queries.ts", "type": "util", "context": "Function to update subscription status on cancel, including updating metadata and resetDate, which may affect credit reset behavior.", "score": 0.8, "lines": 29, "startLine": 1539, "endLine": 1567, "symbols": ["updateSubscriptionOnCancel"], "preview": "export async function updateSubscriptionOnCancel(\n  id: string,\n  isActive: boolean,\n  newMetadata: Record<string, any>,\n  currentMetadata?: Record<string, any> | null\n..."}, {"filePath": "src/lib/db/queries.ts", "type": "util", "context": "Function to get subscription by ID, useful for subscription management and debugging credit reset issues.", "score": 0.7, "lines": 7, "startLine": 1530, "endLine": 1536, "symbols": ["getSubscriptionById"], "preview": "  try {\n    const result = await db.select().from(subscriptionsSchema).where(eq(subscriptionsSchema.id, id)).limit(1);\n    return result[0];\n  } catch (error) {\n    console.error('Failed to get subscription by LemonSqueezy ID from database:', error);\n..."}, {"filePath": "src/lib/db/token-consumption.queries.ts", "type": "util", "context": "Function counts discounted error fixing credits for the day, relevant to credit discount tracking and reset logic.", "score": 0.7, "lines": 21, "startLine": 14, "endLine": 34, "symbols": ["getErrorFixingDiscountsForToday"], "preview": "export async function getErrorFixingDiscountsForToday(userId: string): Promise<number> {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n\n  // Count TokenConsumption records with errorId set and discounted=true from today\n..."}], "additionalFiles": [], "fullResults": {"snippets": [{"filePath": "src/lib/subscription/credit-usage.ts", "content": "export async function updateCreditUsage(userId: string, operations: CreditOperation[]) {\n    console.log(`[Messages] Updating message usage for user ${userId}`);\n    \n    // Get the latest active subscription\n    const subscription = await db\n        .select()\n        .from(subscriptions)\n        .where(eq(subscriptions.userId, userId))\n        .orderBy(desc(subscriptions.updatedAt));\n\n    if (!subscription || subscription.length === 0) {\n        console.log(`[Messages] No subscription found for user ${userId}`);\n        return 0;\n    }\n\n    const sub = subscription.find(sub => sub.status === 'active') || subscription[0];\n    if (!sub) {\n        console.log(`[Messages] No active subscription found for user ${userId}`);\n        return 0;\n    }\n    \n    // Get the billing cycle start date\n    const cycleStartDate = sub.createdAt;\n    console.log(`[Messages] Current billing cycle started at: ${cycleStartDate}`);\n    \n    // Count the number of messages (token consumption records) in the current billing cycle\n    const messageCountResult = await db\n        .select({\n            messageCount: sql<number>`COUNT(*)`\n        })\n        .from(tokenConsumption)\n        .where(\n            and(\n                eq(tokenConsumption.userId, userId),\n                // Use a safer approach that converts dates to ISO strings\n                sql`${tokenConsumption.createdAt} >= ${cycleStartDate instanceof Date ? cycleStartDate.toISOString() : cycleStartDate}`,\n                ne(tokenConsumption.discounted, true)\n            ),\n        );\n    \n    const messagesUsed = messageCountResult[0]?.messageCount || 0;\n    console.log(`[Messages] Total messages used in current cycle: ${messagesUsed}`);\n    \n    // Update the subscription with the message count\n    // Note: We're still using the creditsUsed field for now, but it now represents messages used\n    await db.transaction(async (tx) => {\n        await tx\n            .update(subscriptions)\n            .set({\n                creditsUsed: messagesUsed, // This now represents messages used, not credits\n                updatedAt: new Date(),\n            })\n            .where(eq(subscriptions.id, sub.id));\n    });\n    \n    console.log(`[Messages] Updated subscription ${sub.id} with ${messagesUsed} messages used`);\n    return messagesUsed;\n}", "startLine": 12, "endLine": 69, "type": "util", "symbols": ["updateCreditUsage"], "score": 1, "context": "This function updates credit usage for a user subscription, including counting usage in the current billing cycle and updating the subscription record. It is directly related to credit reset and subscription management.", "includesImports": false}, {"filePath": "src/lib/db/schema.ts", "content": "export const subscriptions = pgTable('Subscription', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  userId: varchar('userId', { length: 255 }).notNull(),\n  status: varchar('status', { length: 50 }).notNull().default('inactive'),\n  planId: varchar('planId', { length: 50 }).notNull().default('free'),\n  subscriptionId: varchar('subscriptionId', { length: 255 }).unique(),\n  credits: integer('credits').notNull().default(50), // Default to free tier credits\n  creditsUsed: integer('creditsUsed').notNull().default(0),\n  isActive: boolean('isActive').default(false),\n  // LemonSqueezy fields\n  lemonSqueezyCustomerId: varchar('lemonSqueezyCustomerId'),\n  lemonSqueezySubscriptionId: varchar('lemonSqueezySubscriptionId'),\n  lemonSqueezyOrderId: varchar('lemonSqueezyOrderId'),\n  lemonSqueezyVariantId: varchar('lemonSqueezyVariantId'),\n  provider: varchar('provider'),\n  // Stripe fields (for compatibility)\n  stripeCustomerId: varchar('stripeCustomerId'),\n  stripeSubscriptionId: varchar('stripeSubscriptionId'),\n  stripePriceId: varchar('stripePriceId'),\n  stripeCurrentPeriodEnd: timestamp('stripeCurrentPeriodEnd'),\n  stripeVariantId: timestamp('stripeVariantId'),\n  // Billing cycle\n  resetDate: timestamp('resetDate'),\n  // Flags\n  isDowngraded: boolean('isDowngraded').default(false),\n  // Additional metadata (cancellation feedback, etc.)\n  metadata: json('metadata').$type<Record<string, any>>(),\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n}, (table) => {\n  return {\n    userIdIdx: index('subscription_userId_idx').on(table.userId),\n    statusIdx: index('subscription_status_idx').on(table.status),\n    userIdUpdatedAtIdx: index('subscription_userId_updatedAt_idx').on(table.userId, table.updatedAt),\n    isActiveIdx: index('subscription_isActive_idx').on(table.isActive)\n  };\n});\n\nexport type Subscription = InferSelectModel<typeof subscriptions>;", "startLine": 378, "endLine": 416, "type": "unknown", "symbols": ["subscriptions table"], "score": 1, "context": "The subscriptions table schema defines fields related to credits, creditsUsed, resetDate, and subscription status, which are critical for understanding credit reset and subscription management.", "includesImports": false}, {"filePath": "src/lib/credit/CreditUsageTracker.ts", "content": "export class CreditUsageTracker {\n    private operations: CreditOperation[] = [];\n    private discountedOperations: DiscountedOperation[] = [];\n\n    /**\n     * Track a credit operation that will be charged to the user\n     * @param type The type of operation\n     * @param count The number of operations (default: 1)\n     */\n    trackOperation(type: CreditOperationType, count: number = 1) {\n        const existingOp = this.operations.find(op => op.type === type);\n        if (existingOp) {\n            existingOp.count += count;\n        } else {\n            this.operations.push({ type, count });\n        }\n    }\n\n    /**\n     * Track a discounted operation that won't be charged to the user\n     * @param type The type of operation\n     * @param reason The reason for the discount (e.g., 'error_fixing')\n     * @param count The number of operations (default: 1)\n     */\n    trackDiscountedOperation(type: CreditOperationType, reason: string, count: number = 1) {\n        const existingOp = this.discountedOperations.find(op => op.type === type && op.reason === reason);\n        if (existingOp) {\n            existingOp.count += count;\n        } else {\n            this.discountedOperations.push({ type, count, reason });\n        }\n    }\n\n    /**\n     * Get all tracked operations that will be charged\n     */\n    getOperations(): CreditOperation[] {\n        return [...this.operations];\n    }\n\n    /**\n     * Get all discounted operations that won't be charged\n     */\n    getDiscountedOperations(): DiscountedOperation[] {\n        return [...this.discountedOperations];\n    }\n\n    /**\n     * Get the total number of credits consumed (charged operations only)\n     */\n    getCreditConsumption(): number {\n        return this.operations.reduce((total, op) => total + op.count, 0);\n    }\n\n    /**\n     * Get the total number of credits saved through discounts\n     */\n    getDiscountedCreditCount(): number {\n        return this.discountedOperations.reduce((total, op) => total + op.count, 0);\n    }\n\n    /**\n     * Get the total potential credit consumption (charged + discounted)\n     */\n    getTotalPotentialConsumption(): number {\n        return this.getCreditConsumption() + this.getDiscountedCreditCount();\n    }\n\n    /**\n     * Clear all tracked operations\n     */\n    clear() {\n        this.operations = [];\n        this.discountedOperations = [];\n    }\n}", "startLine": 9, "endLine": 84, "type": "unknown", "symbols": ["CreditUsageTracker"], "score": 0.9, "context": "This class tracks credit operations and has a clear method to clear all tracked operations (clear), which is relevant to credit reset logic.", "includesImports": false}, {"filePath": "src/lib/db/token-consumption.queries.ts", "content": "export async function saveTokenConsumption(data: TokenConsumptionInput): Promise<TokenConsumption> {\n  const {cacheDiscountPercent, cachingDiscount, inputCost, outputCost, subtotal, totalCost} = await getMessageDetailsFromOpenrouter(data);\n\n  let shouldDiscount = false;\n  if (data.errorId) {\n    try {\n      const count = await getErrorFixingDiscountsForToday(data.userId);\n      shouldDiscount = count < 10 || data.isAutoFixed === true; // Auto-fixed errors are always discounted\n      console.log('count of free error fixes for today', count);\n      console.log('shouldDiscount', shouldDiscount);\n      console.log('isAutoFixed', data.isAutoFixed);\n    } catch (e: unknown) {\n      console.log('Error getting discount credits', e);\n    }\n  }\n\n  const [consumption] = await db\n    .insert(tokenConsumption)\n    .values({\n      ...data,\n      // Store costs as floats directly\n      inputCost: inputCost,\n      outputCost: outputCost,\n      totalCost: totalCost,\n      cachingDiscount: cachingDiscount,\n      subtotal: subtotal,\n      cacheDiscountPercent,\n      creditsConsumed: data.creditsConsumed,\n      discountedCredits: data.discountedCredits || 0,\n      discountReason: data.discountReason || null,\n      errorId: data.errorId,\n      isAutoFixed: data.isAutoFixed || false,\n      discounted: shouldDiscount\n    })\n    .returning();\n  return consumption;\n}", "startLine": 36, "endLine": 72, "type": "util", "symbols": ["saveTokenConsumption"], "score": 0.9, "context": "Function to save token consumption records, including logic to decide if a discount should be applied, which affects credit usage and reset.", "includesImports": false}, {"filePath": "src/lib/db/schema.ts", "content": "export const tokenConsumption = pgTable('TokenConsumption', {\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\n  model: varchar('model', { length: 50 }).notNull(),\n  totalTimeToken: real('totalTimeToken').notNull(),\n  promptTokens: integer('promptTokens').notNull(),\n  completionTokens: integer('completionTokens').notNull(),\n  totalTokens: integer('totalTokens').notNull(),\n  chatId: uuid('chatId')\n    .notNull(),  // No foreign key as specified\n  projectId: uuid('projectId')\n    .notNull(),  // No foreign key as specified\n  messageId: uuid('messageId')\n    .notNull(),  // No foreign key as specified\n  userId: uuid('userId')\n    .notNull()\n    .references(() => user.id, { onDelete: 'cascade' }),  // Add foreign key to User\n  createdAt: timestamp('createdAt').notNull().defaultNow(),\n  updatedAt: timestamp('updatedAt').notNull().defaultNow(),\n  inputCost: real('inputCost').notNull(),  // Store as float\n  outputCost: real('outputCost').notNull(),  // Store as float\n  cachingDiscount: real('cachingDiscount'),\n  cacheDiscountPercent: real('cacheDiscountPercent'),\n  subtotal: real('subtotal'),\n  totalCost: real('totalCost').notNull(),  // Store as float\n  isAnonymous: boolean('isAnonymous').default(false),\n  remoteProvider: varchar(\"remoteProvider\"),\n  remoteProviderId: varchar(\"remoteProviderId\"),\n  creditsConsumed: integer('creditsConsumed'),\n  discountedCredits: integer('discountedCredits'),\n  discountReason: varchar('discountReason', { length: 50 }),\n  errorId: varchar('errorId'),\n  discounted: boolean('discounted').default(false),\n  isAutoFixed: boolean('isAutoFixed').default(false)\n}, (table) => {\n  return {\n    userIdIdx: index('tokenConsumption_userId_idx').on(table.userId),\n    chatIdIdx: index('tokenConsumption_chatId_idx').on(table.chatId),\n    projectIdIdx: index('tokenConsumption_projectId_idx').on(table.projectId),\n    userCreatedAtIdx: index('tokenConsumption_userId_createdAt_idx').on(table.userId, table.createdAt),\n    isAnonymousIdx: index('tokenConsumption_isAnonymous_idx').on(table.isAnonymous)\n  };\n});\n\nexport type TokenConsumption = InferSelectModel<typeof tokenConsumption>;", "startLine": 319, "endLine": 362, "type": "unknown", "symbols": ["tokenConsumption table"], "score": 0.8, "context": "The tokenConsumption table tracks creditsConsumed, discountedCredits, and discount flags, which are relevant to credit tracking and billing.", "includesImports": false}, {"filePath": "src/lib/db/queries.ts", "content": "export async function updateSubscriptionOnCancel(\n  id: string,\n  isActive: boolean,\n  newMetadata: Record<string, any>,\n  currentMetadata?: Record<string, any> | null\n) {\n  try {\n    const combinedMetadata = { ...currentMetadata, ...newMetadata };\n    const endsAtFromMeta = newMetadata.endsAt;\n    const updates = {\n      status: 'cancelled',\n      isActive: isActive,\n      metadata: combinedMetadata,\n      updatedAt: new Date(),\n    }\n    const resetDateValue = endsAtFromMeta;\n    if (resetDateValue) {\n      updates[resetDate] = resetDateValue;\n    }\n\n    await db\n      .update(subscriptionsSchema)\n      .set(updates as any)\n      .where(eq(subscriptionsSchema.id, id));\n  } catch (error) {\n    console.error('Failed to update subscription on cancel in database:', error);\n    throw error;\n  }\n}", "startLine": 1539, "endLine": 1567, "type": "util", "symbols": ["updateSubscriptionOnCancel"], "score": 0.8, "context": "Function to update subscription status on cancel, including updating metadata and resetDate, which may affect credit reset behavior.", "includesImports": false}, {"filePath": "src/lib/db/queries.ts", "content": "  try {\n    const result = await db.select().from(subscriptionsSchema).where(eq(subscriptionsSchema.id, id)).limit(1);\n    return result[0];\n  } catch (error) {\n    console.error('Failed to get subscription by LemonSqueezy ID from database:', error);\n    throw error;\n  }", "startLine": 1530, "endLine": 1536, "type": "util", "symbols": ["getSubscriptionById"], "score": 0.7, "context": "Function to get subscription by ID, useful for subscription management and debugging credit reset issues.", "includesImports": false}, {"filePath": "src/lib/db/token-consumption.queries.ts", "content": "export async function getErrorFixingDiscountsForToday(userId: string): Promise<number> {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n\n  // Count TokenConsumption records with errorId set and discounted=true from today\n  const result = await db\n      .select({\n        count: sql<number>`COUNT(*)`\n      })\n      .from(tokenConsumption)\n      .where(\n          and(\n              eq(tokenConsumption.userId, userId),\n              gte(tokenConsumption.createdAt, today),\n              isNotNull(tokenConsumption.errorId),\n              eq(tokenConsumption.discounted, true)\n          )\n      );\n\n  return Number(result[0]?.count || 0);\n}", "startLine": 14, "endLine": 34, "type": "util", "symbols": ["getErrorFixingDiscountsForToday"], "score": 0.7, "context": "Function counts discounted error fixing credits for the day, relevant to credit discount tracking and reset logic.", "includesImports": false}], "additionalFiles": []}}