{"name": "magically.life", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "npx tsx src/lib/db/migrate && next build", "start": "next start", "lint": "next lint", "shadcn:add": "npx shadcn@latest add", "test": "jest", "db:generate": "drizzle-kit generate", "db:migrate": "npx tsx src/lib/db/migrate.ts", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "test:context-engine": "npx tsx scripts/test-two-stage-context-engine.ts", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "db:up": "drizzle-kit up", "templates:convert": "npx tsx template-converter/src/convert.ts"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.9", "@ai-sdk/openai": "^1.1.15", "@ai-sdk/react": "^1.2.12", "@auth/drizzle-adapter": "^1.7.4", "@babel/standalone": "^7.27.0", "@codemirror/lang-python": "^6.1.7", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.2", "@e2b/code-interpreter": "^1.1.0", "@fastify/cors": "^11.0.1", "@hookform/resolvers": "^4.1.3", "@modelcontextprotocol/sdk": "^1.12.1", "@monaco-editor/react": "^4.6.0", "@msgpack/msgpack": "^3.1.1", "@next/third-parties": "^15.3.1", "@openrouter/ai-sdk-provider": "^0.4.5", "@opentelemetry/api": "^1.9.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.5", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.3.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@react-native-community/cli": "^18.0.0", "@sentry/nextjs": "9.3.0", "@supabase/supabase-js": "^2.48.1", "@tanstack/react-query": "^5.66.7", "@tanstack/react-virtual": "^3.13.9", "@tavily/core": "^0.3.1", "@tremor/react": "^3.18.7", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.4", "@vercel/analytics": "^1.4.1", "@vercel/blob": "^0.27.1", "@vercel/kv": "^3.0.0", "@vercel/mcp-adapter": "^0.9.1", "@vercel/postgres": "^0.10.0", "@xterm/addon-fit": "^0.10.0", "@xterm/xterm": "^5.5.0", "ai": "^4.3.12", "archiver": "^7.0.1", "axios": "^1.8.4", "bcrypt-ts": "^5.0.3", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "codemirror": "^6.0.1", "common-tags": "^1.8.2", "crypto-js": "^4.2.0", "csv-parser": "3.0.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "deep-object-diff": "^1.1.9", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.2", "fast-deep-equal": "^3.1.3", "fastify": "^5.3.1", "framer-motion": "^12.7.4", "fs-extra": "^11.3.0", "gpt-tokenizer": "^3.0.1", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "joi": "^17.13.3", "json5": "^2.2.3", "jsonwebtoken": "^9.0.2", "lemonsqueezy.ts": "^0.1.8", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "marked": "^15.0.8", "ml-regression-simple-linear": "^3.0.1", "mobx": "^6.13.5", "mobx-react-lite": "^4.0.7", "monaco-editor": "^0.52.2", "next": "15.2.0-canary.19", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.4", "node-fetch": "3.3.2", "node-html-parser": "^7.0.1", "papaparse": "5.4.1", "pexels": "^1.4.0", "postgres": "^3.4.5", "posthog-js": "^1.215.6", "posthog-node": "^4.18.0", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.4.0", "prosemirror-markdown": "^1.13.1", "prosemirror-schema-basic": "^1.2.3", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.37.2", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-day-picker": "^9.6.7", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-markdown": "^9.0.3", "react-native-web": "^0.19.13", "react-qr-code": "^2.0.15", "react-resizable-panels": "^3.0.1", "recharts": "^2.15.2", "redis": "^5.1.0", "remark-gfm": "^4.0.0", "resend": "^4.1.2", "resumable-stream": "^2.1.0", "simple-git": "^3.27.0", "snack-sdk": "^6.4.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.7.4", "sucrase": "^3.35.0", "supabase-management-js": "^1.0.0", "swr": "^2.3.2", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "validator": "^13.15.0", "vm2": "^3.9.19", "xterm": "^5.3.0", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-flow-strip-types": "^7.26.5", "@babel/plugin-transform-runtime": "^7.26.8", "@babel/preset-typescript": "^7.27.0", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/archiver": "^6.0.3", "@types/babel__standalone": "^7.1.9", "@types/common-tags": "^1.8.4", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.15", "@types/node": "^20", "@types/puppeteer": "^7.0.4", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "browserslist": "^4.24.4", "drizzle-kit": "^0.30.4", "eslint": "^9", "eslint-config-next": "15.2.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "nexpect": "^0.6.0", "postcss": "^8", "puppeteer": "^24.1.1", "tailwindcss": "^3.4.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.8.2"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}